{"projects": [{"id": "test-proj-001", "user_id": "placeholder-user-id", "project_name": "E-Commerce Platform Modernisierung", "company_name": "TechStart GmbH", "contact_person": "<PERSON>", "contact_email": "<EMAIL>", "contact_phone": "+49 89 ********", "project_description": "**Projektübersicht:**\nModernisierung einer bestehenden E-Commerce-Plattform mit React und Node.js. Mi<PERSON> von Legacy-System zu modernen Technologien.\n\n**Hauptaufgaben:**\n- Frontend-Entwicklung mit React 18 und TypeScript\n- Backend-API-Entwicklung mit Node.js\n- Datenbankoptimierung PostgreSQL\n- CI/CD Pipeline Setup\n\n**Technische Anforderungen:**\n- React, TypeScript, Node.js, PostgreSQL\n- AWS Deployment\n- REST API Design\n- Unit & Integration Tests", "budget_range": "45.000 - 65.000 EUR", "project_start_date": "2024-02-15", "project_end_date": "2024-08-15", "required_skills": ["React", "TypeScript", "Node.js", "PostgreSQL", "AWS", "REST APIs"], "application_date": "2024-01-15", "status": "project_completed", "application_text": "<PERSON>hr gee<PERSON><PERSON> Fr<PERSON> Weber,\n\nIhre Ausschreibung für die E-Commerce Platform Modernisierung hat mein Interesse geweckt. Mit über 8 Jahren Erfahrung in der Full-Stack-Entwicklung und spezieller Expertise in React/Node.js-Projekten bringe ich die perfekten Voraussetzungen mit.\n\nMeine Kernkompetenzen:\n- React 18 & TypeScript (5+ Jahre)\n- Node.js Backend-Entwicklung (6+ Jahre)\n- PostgreSQL Optimierung\n- AWS Cloud-Deployment\n- Agile Projektmanagement\n\nGerne stelle ich Ihnen mein Portfolio vor und bespreche die technischen Details.\n\nBeste Grüße\nMax Mustermann", "notes": "**Projektverlauf:**\n\n*15.01.2024:* Bewerbung eingereicht\n*22.01.2024:* Positive Rückmeldung, technisches Interview vereinbart\n*29.01.2024:* Erfolgreiches Interview, Zusage erhalten\n*15.02.2024:* <PERSON><PERSON><PERSON><PERSON><PERSON>, Team-Onboarding\n*15.08.2024:* Erfolgreiches Projektende, sehr zufriedener Kunde\n\n**Lessons Learned:**\n- Postgres-Optimierung war komplexer als erwartet\n- Kunde sehr technik-affin und kooperativ\n- Mögliche Folgeprojekte in Aussicht", "source": "XING", "listing_url": "https://xing.com/jobs/12345", "work_location_type": "hybrid", "remote_percentage": 70, "work_location_notes": "2 Tage pro Woche im Büro München, Rest remote", "created_at": "2024-01-15T10:00:00Z", "updated_at": "2024-08-15T16:30:00Z"}, {"id": "test-proj-002", "user_id": "placeholder-user-id", "project_name": "Mobile App für Fitness-Tracker", "company_name": "HealthTech Solutions", "contact_person": "Dr. <PERSON>", "contact_email": "<EMAIL>", "contact_phone": "+49 30 98765432", "project_description": "**Projektbeschreibung:**\nEntwicklung einer React Native App für iOS und Android zur Verbindung mit Fitness-Trackern und Gesundheitsdatenanalyse.\n\n**Features:**\n- Bluetooth-Verbindung zu Wearables\n- Datenvisualisierung und Analytics\n- Social Features und Challenges\n- Push-Notifications\n- Offline-Synchronisation\n\n**Tech-Stack:**\n- React Native\n- TypeScript\n- Firebase\n- Bluetooth LE Integration", "budget_range": "35.000 - 50.000 EUR", "project_start_date": "2024-03-01", "project_end_date": "2024-07-31", "required_skills": ["React Native", "TypeScript", "Firebase", "Bluetooth", "Mobile Development"], "application_date": "2024-02-10", "status": "offer_received", "application_text": "<PERSON><PERSON> gee<PERSON><PERSON><PERSON>,\n\nals erfahrener Mobile-Developer mit Fokus auf React Native interessiere ich mich sehr für Ihr Fitness-App-Projekt.\n\nMeine Expertise:\n- React Native (4+ Jahre)\n- Bluetooth LE Integration\n- Firebase Backend-Integration\n- App Store Deployment\n- Healthcare-Domain Erfahrung\n\nFreue mich auf ein Gespräch!\n\nViele Grüße\nMax Mustermann", "notes": "**Status-Updates:**\n\n*10.02.2024:* Bewerbung versendet\n*17.02.2024:* Technisches Interview - sehr gut gelaufen\n*24.02.2024:* <PERSON><PERSON><PERSON> Interview mit CTO\n*03.03.2024:* Zusage erhalten! Vertrag in Bearbeitung\n\n**Notizen:**\n- Sehr innovatives Healthcare-Startup\n- Team aus 12 Entwicklern\n- Agile Entwicklung mit 2-Wochen-Sprints\n- Potential für Langzeit-Zusammenarbeit", "source": "LinkedIn", "listing_url": "https://linkedin.com/jobs/98765", "work_location_type": "remote", "remote_percentage": 100, "work_location_notes": "Vollständig remote, gelegentliche Team-Events in Berlin", "created_at": "2024-02-10T14:20:00Z", "updated_at": "2024-03-03T11:45:00Z"}, {"id": "test-proj-003", "user_id": "placeholder-user-id", "project_name": "Blockchain Supply Chain", "company_name": "LogiChain Technologies", "contact_person": "<PERSON>", "contact_email": "<EMAIL>", "contact_phone": "+49 40 22334455", "project_description": "**Blockchain-Projekt:**\nEntwicklung einer transparenten Supply Chain-Lösung mit Blockchain-Technologie für die Lebensmittelindustrie.\n\n**Funktionen:**\n- Product Traceability\n- Smart Contracts für Lieferungen\n- QR-Code Integration\n- IoT Sensor Data\n- Consumer Transparency App\n\n**Tech-Stack:**\n- Ethereum/Polygon\n- Solidity Smart Contracts\n- Node.js Backend\n- React Frontend\n- Web3.js Integration\n- IPFS für Dokumentenspeicherung", "budget_range": "65.000 - 90.000 EUR", "project_start_date": "2024-10-01", "project_end_date": "2025-03-31", "required_skills": ["Blockchain", "Solidity", "Ethereum", "Web3", "React", "Node.js"], "application_date": "2024-05-15", "status": "interview_scheduled", "application_text": "Dear Mr. <PERSON>,\n\nYour blockchain supply chain project represents exactly the kind of innovative technology I'm passionate about working with.\n\nMy background:\n- Blockchain development (2+ years)\n- Solidity smart contracts\n- Web3 integration experience\n- Full-stack development\n- Supply chain domain knowledge\n\nLooking forward to discussing this exciting opportunity.\n\nBest regards,\n<PERSON>", "notes": "**Re<PERSON><PERSON><PERSON>-Konta<PERSON>:**\n\n*15.05.2024:* Bewerbung eingereicht\n*22.05.2024:* <PERSON><PERSON><PERSON> <PERSON>\n\n**Gespräch mit Recruiterin:**\n- Positive erste Einschätzung\n- Blockchain-Erfahrung ausreichend\n- Supply Chain-<PERSON><PERSON><PERSON><PERSON> von Vorteil\n- Interview mit Tech-Lead geplant\n- Competitive Salary-Range bestätigt\n\n**Nächste Schritte:**\n- Interview am 28.05.2024 um 15:00\n- Blockchain-Wissen auffrischen\n- Supply Chain-Use-Cases studieren", "source": "CryptoJobs", "listing_url": "https://cryptojobs.com/job/67890", "work_location_type": "remote", "remote_percentage": 90, "work_location_notes": "Hamburg-b<PERSON>ert, aber größtenteils remote arbeitend", "created_at": "2024-05-15T16:00:00Z", "updated_at": "2024-05-22T14:30:00Z"}], "activities": [{"id": "activity-001", "project_id": "test-proj-001", "user_id": "placeholder-user-id", "activity_type": "project_created", "description": "Projekt \"E-Commerce Platform Modernisierung\" erstellt", "created_at": "2024-01-15T10:00:00Z"}, {"id": "activity-002", "project_id": "test-proj-001", "user_id": "placeholder-user-id", "activity_type": "application_sent", "description": "Bewerbung versendet", "notes": "Bewerbung über XING eingereicht. Vollständige Unterlagen mit Portfolio gesendet.", "notes_date": "2024-01-15", "created_at": "2024-01-15T10:30:00Z"}, {"id": "activity-003", "project_id": "test-proj-001", "user_id": "placeholder-user-id", "activity_type": "status_changed", "description": "Status: <PERSON>ch nicht beworben → Bewerbung gesendet", "created_at": "2024-01-15T10:30:00Z"}, {"id": "activity-004", "project_id": "test-proj-001", "user_id": "placeholder-user-id", "activity_type": "status_changed", "description": "Status: Bewerbung gesendet → Rückfrage erhalten", "notes": "Positive Rück<PERSON><PERSON><PERSON> von <PERSON>. Technisches Interview vereinbart für 29.01.2024.", "notes_date": "2024-01-22", "created_at": "2024-01-22T14:20:00Z"}, {"id": "activity-005", "project_id": "test-proj-001", "user_id": "placeholder-user-id", "activity_type": "status_changed", "description": "Status: Rückfrage erhalten → Interview durchgeführt", "notes": "Erfolgreiches technisches Interview. Team sehr kompetent, Projekt sehr interessant. Zusage in Aussicht gestellt.", "notes_date": "2024-01-29", "created_at": "2024-01-29T16:45:00Z"}, {"id": "activity-006", "project_id": "test-proj-001", "user_id": "placeholder-user-id", "activity_type": "status_changed", "description": "Status: Interview durchgefü<PERSON><PERSON> → <PERSON><PERSON> er<PERSON>en", "notes": "Zusage erhalten! Vertrag für 6 Monate, Start am 15.02.2024. Rate: 85€/h", "notes_date": "2024-01-30", "created_at": "2024-01-30T11:15:00Z"}, {"id": "activity-007", "project_id": "test-proj-001", "user_id": "placeholder-user-id", "activity_type": "status_changed", "description": "Status: <PERSON><PERSON> → Projekt abgeschlossen", "notes": "Projekt erfolgreich abgeschlossen. Kunde sehr zufrieden. Folgeprojekte in Aussicht.", "notes_date": "2024-08-15", "created_at": "2024-08-15T16:30:00Z"}, {"id": "activity-008", "project_id": "test-proj-002", "user_id": "placeholder-user-id", "activity_type": "project_created", "description": "Projekt \"Mobile App für Fitness-Tracker\" erstellt", "created_at": "2024-02-10T14:20:00Z"}, {"id": "activity-009", "project_id": "test-proj-002", "user_id": "placeholder-user-id", "activity_type": "application_sent", "description": "Bewerbung versendet", "notes": "Bewerbung über LinkedIn eingereicht. Healthcare-Erfahrung betont.", "notes_date": "2024-02-10", "created_at": "2024-02-10T14:30:00Z"}, {"id": "activity-010", "project_id": "test-proj-002", "user_id": "placeholder-user-id", "activity_type": "status_changed", "description": "Status: <PERSON>ch nicht beworben → Bewerbung gesendet", "created_at": "2024-02-10T14:30:00Z"}, {"id": "activity-011", "project_id": "test-proj-002", "user_id": "placeholder-user-id", "activity_type": "status_changed", "description": "Status: Bewerbung gesendet → Interview geplant", "notes": "Erstes technisches Interview am 17.02.2024 um 10:00 vere<PERSON>bart.", "notes_date": "2024-02-12", "created_at": "2024-02-12T09:30:00Z"}, {"id": "activity-012", "project_id": "test-proj-002", "user_id": "placeholder-user-id", "activity_type": "status_changed", "description": "Status: Interview g<PERSON><PERSON> → Interview durchgeführt", "notes": "<PERSON><PERSON><PERSON> Interview sehr gut gelaufen. Zweites Interview mit CTO für 24.02. vereinbart.", "notes_date": "2024-02-17", "created_at": "2024-02-17T11:00:00Z"}, {"id": "activity-013", "project_id": "test-proj-002", "user_id": "placeholder-user-id", "activity_type": "status_changed", "description": "Status: Interview durchgefü<PERSON><PERSON> → <PERSON><PERSON> er<PERSON>en", "notes": "Zusage erhalten! <PERSON>hr gutes Feedback vom CTO. Vertrag wird vorbereitet.", "notes_date": "2024-03-03", "created_at": "2024-03-03T11:45:00Z"}, {"id": "activity-014", "project_id": "test-proj-003", "user_id": "placeholder-user-id", "activity_type": "project_created", "description": "Projekt \"Blockchain Supply Chain\" erstellt", "created_at": "2024-05-15T16:00:00Z"}, {"id": "activity-015", "project_id": "test-proj-003", "user_id": "placeholder-user-id", "activity_type": "application_sent", "description": "Bewerbung versendet", "notes": "Bewerbung über CryptoJobs Portal eingereicht. Blockchain-Portfolio mitgesendet.", "notes_date": "2024-05-15", "created_at": "2024-05-15T16:15:00Z"}, {"id": "activity-016", "project_id": "test-proj-003", "user_id": "placeholder-user-id", "activity_type": "status_changed", "description": "Status: <PERSON><PERSON> nicht beworben → Recruiter-Kontakt", "notes": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>. Positive erste Einschätzung.", "notes_date": "2024-05-22", "created_at": "2024-05-22T14:30:00Z"}, {"id": "activity-017", "project_id": "test-proj-003", "user_id": "placeholder-user-id", "activity_type": "status_changed", "description": "Status: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> → <PERSON> geplant", "notes": "Interview mit Tech-Lead <PERSON> am 28.05.2024 um 15:00 vere<PERSON>bart.", "notes_date": "2024-05-23", "created_at": "2024-05-23T10:15:00Z"}], "calendarEvents": [{"id": "cal-001", "user_id": "placeholder-user-id", "project_id": "test-proj-001", "title": "Projektstart - E-Commerce Platform", "description": "Projektstart bei TechStart GmbH", "start_date": "2024-02-15", "start_time": "09:00", "end_date": "2024-02-15", "end_time": "17:00", "all_day": false, "event_type": "project_start", "color": "#22c55e", "location": "München, TechStart GmbH Büro", "completed": true, "reminder_enabled": true, "reminder_minutes_before": 60, "created_automatically": true, "source_status": "project_start", "created_at": "2024-01-30T11:15:00Z", "updated_at": "2024-02-15T17:00:00Z"}, {"id": "cal-002", "user_id": "placeholder-user-id", "project_id": "test-proj-001", "title": "Projektende - E-Commerce Platform", "description": "Projektabschluss bei TechStart GmbH", "start_date": "2024-08-15", "start_time": "16:00", "end_date": "2024-08-15", "end_time": "18:00", "all_day": false, "event_type": "project_end", "color": "#f59e0b", "location": "München, TechStart GmbH Büro", "completed": true, "reminder_enabled": true, "reminder_minutes_before": 120, "created_automatically": true, "source_status": "project_end", "created_at": "2024-01-30T11:15:00Z", "updated_at": "2024-08-15T18:00:00Z"}, {"id": "cal-003", "user_id": "placeholder-user-id", "project_id": "test-proj-001", "title": "Technisches Interview - TechStart", "description": "Interview mit <PERSON> und dem Entwicklungsteam", "start_date": "2024-01-29", "start_time": "14:00", "end_date": "2024-01-29", "end_time": "15:30", "all_day": false, "event_type": "interview", "color": "#ef4444", "location": "Video-Call (Teams)", "completed": true, "reminder_enabled": true, "reminder_minutes_before": 30, "created_automatically": false, "created_at": "2024-01-22T14:20:00Z", "updated_at": "2024-01-29T15:30:00Z"}, {"id": "cal-004", "user_id": "placeholder-user-id", "project_id": "test-proj-002", "title": "1. Interview - HealthTech Solutions", "description": "Technisches Interview mit Dr. <PERSON>", "start_date": "2024-02-17", "start_time": "10:00", "end_date": "2024-02-17", "end_time": "11:00", "all_day": false, "event_type": "interview", "color": "#ef4444", "location": "Video-Call (Zoom)", "completed": true, "reminder_enabled": true, "reminder_minutes_before": 30, "created_automatically": false, "created_at": "2024-02-12T09:30:00Z", "updated_at": "2024-02-17T11:00:00Z"}, {"id": "cal-005", "user_id": "placeholder-user-id", "project_id": "test-proj-002", "title": "2. Interview - HealthTech CTO", "description": "Interview mit CTO bezüglich Architecture und Team-Fit", "start_date": "2024-02-24", "start_time": "15:00", "end_date": "2024-02-24", "end_time": "16:00", "all_day": false, "event_type": "interview", "color": "#ef4444", "location": "Video-Call (Google Meet)", "completed": true, "reminder_enabled": true, "reminder_minutes_before": 30, "created_automatically": false, "created_at": "2024-02-17T11:00:00Z", "updated_at": "2024-02-24T16:00:00Z"}, {"id": "cal-006", "user_id": "placeholder-user-id", "project_id": "test-proj-002", "title": "Projektstart - HealthTech App", "description": "Projektstart - Mobile App für Fitness-Tracker", "start_date": "2024-03-01", "start_time": "09:00", "end_date": "2024-03-01", "end_time": "17:00", "all_day": false, "event_type": "project_start", "color": "#22c55e", "location": "Remote (Team-Call um 09:00)", "completed": false, "reminder_enabled": true, "reminder_minutes_before": 60, "created_automatically": true, "source_status": "project_start", "created_at": "2024-03-03T11:45:00Z", "updated_at": "2024-03-03T11:45:00Z"}, {"id": "cal-007", "user_id": "placeholder-user-id", "project_id": "test-proj-002", "title": "Projektende - HealthTech App", "description": "Geplantes Projektende - Mobile App für Fitness-Tracker", "start_date": "2024-07-31", "start_time": "16:00", "end_date": "2024-07-31", "end_time": "18:00", "all_day": false, "event_type": "project_end", "color": "#f59e0b", "location": "Remote (Abschluss-Meeting)", "completed": false, "reminder_enabled": true, "reminder_minutes_before": 120, "created_automatically": true, "source_status": "project_end", "created_at": "2024-03-03T11:45:00Z", "updated_at": "2024-03-03T11:45:00Z"}, {"id": "cal-008", "user_id": "placeholder-user-id", "project_id": "test-proj-003", "title": "Interview - Blockchain Supply Chain", "description": "Interview mit Tech-Lead <PERSON>", "start_date": "2024-05-28", "start_time": "15:00", "end_date": "2024-05-28", "end_time": "16:00", "all_day": false, "event_type": "interview", "color": "#ef4444", "location": "Video-Call", "completed": false, "reminder_enabled": true, "reminder_minutes_before": 30, "created_automatically": false, "created_at": "2024-05-23T10:15:00Z", "updated_at": "2024-05-23T10:15:00Z"}, {"id": "cal-009", "user_id": "placeholder-user-id", "project_id": "test-proj-003", "title": "Projektstart - Blockchain Supply Chain", "description": "Geplanter Projektstart bei LogiChain Technologies", "start_date": "2024-10-01", "start_time": "09:00", "end_date": "2024-10-01", "end_time": "17:00", "all_day": false, "event_type": "project_start", "color": "#22c55e", "location": "Hamburg (teilweise remote)", "completed": false, "reminder_enabled": true, "reminder_minutes_before": 1440, "created_automatically": true, "source_status": "project_start", "created_at": "2024-05-15T16:00:00Z", "updated_at": "2024-05-15T16:00:00Z"}, {"id": "cal-010", "user_id": "placeholder-user-id", "project_id": "test-proj-003", "title": "Projektende - Blockchain Supply Chain", "description": "Geplantes Projektende bei LogiChain Technologies", "start_date": "2025-03-31", "start_time": "16:00", "end_date": "2025-03-31", "end_time": "18:00", "all_day": false, "event_type": "project_end", "color": "#f59e0b", "location": "Hamburg (Abschluss vor Ort)", "completed": false, "reminder_enabled": true, "reminder_minutes_before": 2880, "created_automatically": true, "source_status": "project_end", "created_at": "2024-05-15T16:00:00Z", "updated_at": "2024-05-15T16:00:00Z"}, {"id": "cal-011", "user_id": "placeholder-user-id", "project_id": null, "title": "Blockchain Fortbildung", "description": "Online-Kurs: Advanced Solidity Smart Contracts", "start_date": "2024-06-01", "start_time": "10:00", "end_date": "2024-06-01", "end_time": "17:00", "all_day": false, "event_type": "manual", "color": "#3b82f6", "location": "Online", "completed": false, "reminder_enabled": true, "reminder_minutes_before": 60, "created_automatically": false, "created_at": "2024-05-25T09:00:00Z", "updated_at": "2024-05-25T09:00:00Z"}, {"id": "cal-012", "user_id": "placeholder-user-id", "project_id": "test-proj-002", "title": "Nachfassen - HealthTech Vertrag", "description": "Bei <PERSON><PERSON> wegen Vertragsstatus nachfragen", "start_date": "2024-03-10", "start_time": "10:00", "end_date": "2024-03-10", "end_time": "10:30", "all_day": false, "event_type": "follow_up", "color": "#8b5cf6", "location": "Telefonat", "completed": true, "reminder_enabled": true, "reminder_minutes_before": 15, "created_automatically": false, "created_at": "2024-03-08T14:00:00Z", "updated_at": "2024-03-10T10:30:00Z"}], "settings": {"id": "test-settings-001", "user_id": "placeholder-user-id", "full_name": "<PERSON>", "professional_email": "<EMAIL>", "phone": "+49 176 ********", "address": "Musterstraße 42\n80331 München\nDeutschland", "website": "https://maxmustermann.dev", "hourly_rate_eur": 95, "availability_start_date": "2024-12-01", "availability_end_date": "2025-12-31", "availability_hours_per_week": 40, "availability_notes": "Flexibel für Vollzeit- und Teilzeitprojekte. Bevorzuge Remote-Arbeit mit gelegentlichen Vor-Ort-Terminen. Längere Projekte bevorzugt.", "cv_pdf_url": null, "profile_picture_url": null, "created_at": "2024-01-01T00:00:00Z", "updated_at": "2024-08-25T15:30:00Z"}, "exportDate": "2024-08-25T15:30:00Z", "version": "2.0", "totalProjects": 3}