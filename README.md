# 🚀 Lanzr - Freelance Project Management Platform

Eine moderne, KI-gestützte Freelance-Projektmanagement-Anwendung für die professionelle Verwaltung von Bewerbungen, Projekten und Terminen.

## 📱 Hauptfunktionen

### 🎯 Projekt- & Bewerbungsmanagement
- **Dashboard mit KPIs**: Umfassende Projektübersicht mit Filteroptionen und Erfolgskennzahlen
- **Vollständiger Bewerbungszyklus**: Status-Tracking von "Nicht beworben" bis "Projekt abgeschlossen"
- **Activity Timeline**: Automatische Protokollierung aller Projektänderungen mit Zeitstempel
- **Erweiterte Statistiken**: Detaillierte Analyse von Bewerbungsverhalten und Erfolgsquoten
- **Projektdetails**: Umfassende Verwaltung von Projektinformationen, Kontakten und Dokumenten

### 🤖 KI-Integration (Gemini API)
- **Automatische Projektanalyse**: Extrahierung von Projektdaten aus Beschreibungen
- **Personalisierte Bewerbungstexte**: KI-generierte Anschreiben basierend auf CV und Projekt
- **Skill-Matching**: Automatische Bewertung der Projekteignung
- **Intelligente Datenextraktion**: Automatisches Parsen von Projektanforderungen
- **KI-Notizen-Zusammenfassungen**: Timeline-, Insights- und Action-Items-Summaries
- **Projekt-Bewertung**: Automatische Analyse von Projekteignung und Erfolgswahrscheinlichkeit
- **Kontaktdaten-Extraktion**: Automatische Erkennung von Kontaktinformationen
- **Smart Content Generation**: KI-unterstützte Erstellung von Projektbeschreibungen

### 📊 Import/Export System
- **Robustes Import-System**: JSON-Import mit Duplikatserkennung und Überschreibungsoptionen
- **Multi-Format Export**: PDF-Berichte, Excel-Tabellen, JSON-Backups
- **Fortschrittsanzeige**: Echtzeit-Fortschritt für große Datenoperationen
- **Validierung**: Automatische Dateivalidierung (50MB Limit, Typprüfung)
- **Fehlerbehandlung**: Umfassende Fehlererkennung und Benutzerfeedback

### 📅 Kalender & Terminmanagement
- **Projektereignisse**: Automatische Termine für Projektdaten und Deadlines
- **Erinnerungen**: Konfigurierbare Benachrichtigungen für wichtige Termine
- **Timeline-Ansicht**: Chronologische Darstellung aller Aktivitäten
- **Integrierte Kalenderfunktion**: Vollständige Kalenderintegration mit Event-Management

### 📞 Kontakt- & Kundenmanagement
- **Kontaktdatenbank**: Vollständige Verwaltung von Kundenkontakten
- **Kontakthistorie**: Verknüpfung von Kontakten mit Projekten und Aktivitäten
- **Kommunikationsprotokoll**: Nachverfolgung aller Kundeninteraktionen
- **Contact-Center**: Zentrale Verwaltung aller Geschäftskontakte

### 👤 Profil- & Einstellungsmanagement
- **Vollständige Profile**: Name, Kontaktdaten, Stundensatz, Verfügbarkeit
- **CV-Upload**: PDF-Upload für personalisierte KI-Generierung
- **Profilbilder**: Upload von JPG/PNG/WebP (max 5MB)
- **Verfügbarkeitsplanung**: Wöchentliche Stunden und Zeitraum-Definitionen
- **Personalisierte Einstellungen**: Umfassende Anpassung der Benutzeroberfläche

### 🔔 Follow-Up & Benachrichtigungssystem
- **Intelligente Follow-Ups**: Automatische Nachfass-Erinnerungen basierend auf Projektstatus
- **Follow-Up Templates**: Vorgefertigte und anpassbare Nachfass-Vorlagen
- **Follow-Up Analytics**: Detaillierte Analyse von Nachfass-Erfolgsraten und Response-Zeiten
- **Follow-Up Timeline**: Chronologische Darstellung aller Nachfass-Aktivitäten
- **Email-Integration**: Direkte Email-Versendung aus der Anwendung
- **Response-Tracking**: Verfolgung von Kundenantworten und Reaktionen
- **Smart Scheduling**: Intelligente Terminplanung für Follow-Ups
- **Notification-Center**: Zentrale Verwaltung aller Benachrichtigungen
- **Anpassbare Alerts**: Konfigurierbare Benachrichtigungen nach Priorität und Typ
- **Follow-Up History**: Vollständige Historie aller Nachfass-Aktivitäten

### 📈 Analytics & Reporting
- **Detaillierte Statistiken**: Umfassende Analyse von Bewerbungsverhalten
- **Erfolgsmetriken**: KPIs für Bewerbungseffizienz und Projekterfolg
- **Timeline Analytics**: Detaillierte Analyse von Projektverläufen und Zeitdauern
- **Follow-Up Analytics**: Response-Raten, Erfolgsquoten und Timing-Optimierungen
- **Kontakt-Analytics**: Analyse von Kundeninteraktionen und Erfolgsmuster
- **Status-Verlaufsanalyse**: Tracking von Projektfortschritten und Bottlenecks
- **Performance Dashboards**: Interaktive Charts und Visualisierungen
- **Trend-Analyse**: Langzeit-Trends und Entwicklungen
- **Custom Reports**: Konfigurierbare Berichte für spezifische Anforderungen
- **Export-Analytics**: Analyse der Export- und Backup-Aktivitäten

### ⏱️ Zeiterfassung & Projektmanagement
- **Live-Timer**: Echtzeit-Zeiterfassung für aktive Projekte
- **Zeitkategorien**: Kategorisierung von Arbeitszeiten (Development, Meeting, Admin, etc.)
- **Automatisches Tracking**: Smart-Timer mit Pausen-Erkennung
- **Zeiterfassungs-Reports**: Detaillierte Berichte über Arbeitszeiten und Produktivität
- **Stundensatz-Kalkulation**: Automatische Berechnung von Projektkosten
- **Export für Rechnungen**: Zeiterfassungs-Export für Abrechnungszwecke
- **Timer-Historie**: Vollständige Historie aller Zeiterfassungen
- **Produktivitäts-Metriken**: Analyse der Arbeitseffizienz und Zeit-Verteilung
- **Multi-Projekt-Timer**: Gleichzeitige Zeiterfassung für verschiedene Projekte
- **Time-Tracking-Dashboard**: Übersichtliche Darstellung aller Zeiterfassungen

### 📝 Notizen & Dokumentation
- **KI-Notizen-Zusammenfassungen**: Automatische Generierung von Timeline-, Insights- und Action-Items
- **Markdown-Support**: Vollständige Markdown-Unterstützung für formatierte Notizen
- **Notizen-Kategorisierung**: Strukturierte Notizen-Verwaltung nach Kategorien
- **Notizen-Suche**: Volltext-Suche durch alle Projektnotizen
- **Notizen-Export**: Export von Notizen in verschiedene Formate
- **Collaborative Notes**: Geteilte Notizen für Team-Projekte
- **Version History**: Versionierung von Notizen-Änderungen
- **Smart Tagging**: Automatische Tag-Erkennung in Notizen

### 🔐 Sicherheit & Zugriffskontrolle
- **Beta Access Control**: Zugriffscode-System für kontrollierte Beta-Phase
- **Row Level Security**: Sichere Datennutzung auf Datenbankebene
- **Authentifizierung**: Sichere Benutzeranmeldung mit Session-Management
- **Datenschutz**: DSGVO-konforme Datenverarbeitung

### 📱 Mobile-First Design & UI/UX
- **Responsive Design**: Optimiert für alle Gerätegrößen von 320px bis 4K
- **PWA-Funktionalität**: Installierbar als Progressive Web App mit Offline-Support
- **Touch-Optimierung**: Benutzerfreundliche mobile Bedienung mit Swipe-Gesten
- **Dark/Light Mode**: Vollständige Theme-Unterstützung mit System-Synchronisation
- **Accessibility**: WCAG 2.1 AA konform mit Screen-Reader-Unterstützung
- **Micro-Animations**: Flüssige Übergänge und Feedback-Animationen
- **Drag & Drop**: Intuitive Drag & Drop-Funktionalität für Dateien und Elemente
- **Keyboard Navigation**: Vollständige Tastatur-Navigation und Shortcuts
- **Custom Themes**: Anpassbare Farbschemata und Layout-Optionen
- **Loading States**: Intelligente Skeleton-Loader und Progress-Indikatoren

### 🎯 Advanced Dashboard Features
- **Customizable Widgets**: Anpassbare Dashboard-Komponenten
- **Multi-View Layouts**: Grid-, List- und Kanban-Ansichten
- **Advanced Filtering**: Komplexe Filter mit Speicher-Funktionalität
- **Bulk Operations**: Massen-Operationen für Projekte und Kontakte
- **Quick Actions**: Schnellzugriff-Buttons für häufige Aktionen
- **Smart Search**: Intelligente Suche mit Auto-Complete und Suggestions
- **Data Visualization**: Interaktive Charts und Diagramme
- **Real-time Updates**: Live-Updates ohne Seiten-Refresh
- **Bookmark System**: Favoriten-System für wichtige Projekte
- **Recent Activities**: Schnellzugriff auf letzte Aktivitäten

### 🔄 Workflow & Automation
- **Status-Automatisierung**: Automatische Status-Updates basierend auf Aktionen
- **Smart Templates**: Intelligente Vorlagen für wiederkehrende Projekte
- **Workflow-Rules**: Konfigurierbare Geschäftsregeln und Automatisierungen
- **Trigger-System**: Event-basierte Aktionen und Benachrichtigungen
- **Batch Processing**: Automatische Verarbeitung von wiederkehrenden Aufgaben
- **Data Synchronization**: Automatische Synchronisation zwischen Komponenten
- **Smart Defaults**: Intelligente Standardwerte basierend auf Benutzerverhalten
- **Workflow Analytics**: Analyse von Arbeitsabläufen und Optimierungsmöglichkeiten

## 🛠 Technologie-Stack

### Frontend-Architektur
- **React 18** + **TypeScript** - Moderne komponentenbasierte Entwicklung
- **Vite** - Blitzschnelle Entwicklungsumgebung mit Hot Module Replacement
- **Tailwind CSS** - Utility-First CSS-Framework für konsistentes Design
- **shadcn/ui** - Hochwertige, anpassbare UI-Komponentenbibliothek
- **Framer Motion** - Flüssige Animationen und Übergänge
- **React Router** - Clientseitiges Routing mit geschützten Routen

### State Management & Data Fetching
- **TanStack Query** - Erweiterte Server-State-Verwaltung mit Caching
- **React Hook Form** + **Zod** - Type-sichere Formularvalidierung
- **Zustand** - Leichtgewichtige Client-State-Verwaltung
- **React Context** - Globaler State für Authentication und Theme

### Backend & Infrastruktur
- **Supabase** - PostgreSQL-Datenbank mit Auth, Storage und Edge Functions
- **Row Level Security (RLS)** - Sichere Datennutzung auf Datenbankebene
- **Supabase Auth** - Vollständige Authentifizierungslösung
- **Supabase Storage** - Sichere Datei-Uploads (CVs, Profilbilder)
- **Edge Functions** - Serverless Functions für KI-Operationen

### KI & Machine Learning
- **Gemini API** - Google's fortschrittliche KI für Textgenerierung
- **Edge Functions Integration** - Sichere KI-API-Aufrufe
- **Prompt Engineering** - Optimierte KI-Prompts für beste Ergebnisse
- **Content Filtering** - Sichere und relevante KI-Ausgaben

### Supabase Edge Functions
- **analyze-project** - KI-Projektanalyse und Datenextraktion
- **generate-application** - Personalisierte Bewerbungstext-Generierung
- **summarize-notes** - KI-gestützte Notizen-Zusammenfassungen (Timeline, Insights, Action Items)
- **contact-extraction** - Automatische Kontaktdaten-Erkennung
- **project-evaluation** - Bewertung von Projekteignung und Erfolgswahrscheinlichkeit

### Development Tools & Quality Assurance
- **TypeScript** - Vollständige Typsicherheit ohne `any`
- **ESLint** + **Prettier** - Code-Qualitätssicherung und Formatierung
- **Husky** - Git-Hooks für automatische Code-Prüfung
- **Conventional Commits** - Strukturierte Commit-Nachrichten
- **VS Code Extensions** - Empfohlene Entwicklungsumgebung

### Testing & Qualitätssicherung
- **Vitest** - Schnelle Unit-Tests
- **React Testing Library** - Komponententests
- **Playwright** - End-to-End-Tests
- **MSW** - Mock Service Worker für API-Tests

### Build & Deployment
- **Vite Build** - Optimierte Produktions-Builds
- **Vercel** - Automatische Deployments mit CI/CD
- **Environment Variables** - Sichere Konfigurationsverwaltung
- **PWA Support** - Progressive Web App Funktionalitäten

## 🏗 Architektur-Prinzipien

### Client-Side Architecture
- **Komponentenbasierte Architektur** - Modulare, wiederverwendbare React-Komponenten
- **Custom Hooks Pattern** - Geschäftslogik in wiederverwendbaren Hooks
- **Service Layer** - Getrennte Geschäftslogik-Services
- **Type-Safe APIs** - Vollständige TypeScript-Integration

### Database Design
- **Client-Side Logic** - Alle Geschäftslogik in React, keine DB-Funktionen
- **Database Independence** - Einfaches Schema ohne Vendor-Lock-in
- **Normalisierte Struktur** - Effiziente Datenorganisation
- **Activity Logging** - Vollständige Audit-Trail-Funktionalität

### Security First
- **Row Level Security** - Datenbankebene Sicherheit
- **Input Sanitization** - Sichere Behandlung aller Benutzereingaben
- **File Upload Validation** - Umfassende Datei-Sicherheitsprüfungen
- **Environment Variable Security** - Sichere Konfigurationsverwaltung

## 🚀 Installation & Setup

### Voraussetzungen
- **Node.js** (>=18.0.0) - [Installation mit nvm](https://github.com/nvm-sh/nvm)
- **npm** oder **yarn**
- **Git**
- **Supabase Account** - Für Backend-Services

### Lokale Entwicklung

```bash
# Repository klonen
git clone <YOUR_GIT_URL>
cd lanzr-freelance-platform

# Dependencies installieren
npm install

# Environment Variables konfigurieren
cp .env.example .env.local
# .env.local bearbeiten mit Ihren Konfigurationsdaten

# Entwicklungsserver starten
npm run dev
```

### Environment Variables

Erstellen Sie eine `.env.local` Datei:

```env
# Supabase Konfiguration
VITE_SUPABASE_URL=your_supabase_project_url
VITE_SUPABASE_ANON_KEY=your_supabase_anon_key

# KI-Integration (für KI-Features)
GEMINI_API_KEY=your_gemini_api_key

# Beta Access Control (für geschlossene Beta)
VITE_ACCESS_CODE=BETA2025
```

### Verfügbare Scripts

```bash
npm run dev          # Entwicklungsserver mit Hot-Reload
npm run build        # Produktions-Build
npm run build:dev    # Entwicklungs-Build
npm run preview      # Produktions-Build Vorschau
npm run lint         # ESLint Code-Prüfung
npm test            # Test-Suite ausführen
npm run type-check  # TypeScript-Typen prüfen
```

## 📁 Projektstruktur

```
src/
├── components/          # React-Komponenten
│   ├── dashboard/      # Dashboard & Statistiken
│   ├── projects/       # Projektmanagement
│   ├── applications/   # Bewerbungsmanagement
│   ├── contacts/       # Kontaktmanagement
│   ├── calendar/       # Kalenderfunktionen
│   ├── settings/       # Benutzereinstellungen
│   ├── notifications/  # Benachrichtigungssystem
│   ├── layout/         # Layout-Komponenten
│   ├── auth/          # Authentifizierung
│   └── ui/            # Wiederverwendbare UI-Komponenten
├── hooks/              # Custom React Hooks
├── services/           # Geschäftslogik-Services
├── types/              # TypeScript-Typdefinitionen
├── lib/               # Utility-Funktionen
├── contexts/          # React Contexts
├── pages/             # Hauptseiten-Komponenten
└── integrations/      # Externe Service-Integrationen
    └── supabase/      # Supabase Client & Types
```

## 🗄 Datenbankschema

### Haupttabellen

**`freelance_projects`** - Projektdaten
- Vollständige Projektinformationen
- Status-Tracking durch Lebenszyklus
- Kontaktdaten und Bewerbungsstatus
- KI-generierte Inhalte und Analysen

**`user_settings`** - Benutzerprofil
- Persönliche Daten und Kontaktinformationen
- CV-Upload und Profilbild-URLs
- Verfügbarkeit und Stundensatz
- Präferenzen und Konfigurationen

**`project_activities`** - Aktivitätsprotokoll
- Automatische Protokollierung aller Änderungen
- Timeline-Daten für Projekthistorie
- Benutzeraktionen und Systemereignisse

**`calendar_events`** - Kalenderereignisse
- Projektbezogene Termine
- Erinnerungsfunktionalität
- Integration mit Projektmanagement

**`contacts`** - Kontaktmanagement
- Kundenkontakte und Geschäftspartner
- Verknüpfung mit Projekten
- Kommunikationshistorie

**`follow_up_templates`** - Follow-Up Vorlagen
- Vorgefertigte Nachfass-Templates
- Anpassbare Email-Vorlagen
- Template-Kategorisierung

**`follow_up_schedule`** - Follow-Up Terminplanung
- Geplante Nachfass-Aktionen
- Automatische Erinnerungen
- Status-Tracking für Follow-Ups

**`time_entries`** - Zeiterfassung
- Detaillierte Arbeitszeit-Protokolle
- Kategorisierung von Tätigkeiten
- Stundensatz-Berechnungen

**`notifications`** - Benachrichtigungssystem
- System- und User-Benachrichtigungen
- Prioritäts- und Typ-Klassifizierung
- Read/Unread Status-Tracking

### Status-Flow
```
not_applied → application_sent → inquiry_received → 
interview_scheduled → interview_completed → offer_received → 
rejected/project_completed
```

## 🔧 Entwicklungsrichtlinien

### Code-Stil
- **TypeScript First** - Vollständige Typisierung ohne `any`
- **Komponentengetrieben** - Modulare, wiederverwendbare Komponenten
- **Hooks Pattern** - Custom Hooks für Geschäftslogik
- **TanStack Query** - Für alle Server-State-Operationen

### Sprachanforderungen
- **ALLE Code-Kommentare MÜSSEN auf Englisch sein** - Niemals Deutsch oder andere Sprachen
- **ALLE Dokumentation MUSS auf Englisch sein** - Einschließlich README, Inline-Docs
- **Benutzerseitige Texte können auf Deutsch bleiben** - UI-Labels, Fehlermeldungen für deutsche Nutzer

### Architektur-Prinzipien
- **Client-Side Logic** - Alle Geschäftslogik in React
- **Database Independence** - Keine DB-Funktionen oder Trigger
- **Type Safety** - Strikte TypeScript-Konfiguration
- **Security First** - RLS für alle Datenbankoperationen

## 🧪 Testing

### Test-Strategie
- **Unit Tests** mit Vitest für Utility-Funktionen
- **Komponententests** mit React Testing Library
- **Integration Tests** für API-Interaktionen
- **E2E Tests** mit Playwright für kritische User-Flows

### Test-Ausführung
```bash
npm test              # Alle Tests ausführen
npm run test:watch    # Tests im Watch-Modus
npm run test:ui       # Tests mit UI-Interface
npm run test:e2e      # End-to-End-Tests
```

## 📦 Deployment

### Vercel (Empfohlen)
```bash
# Vercel CLI installieren
npm i -g vercel

# Deployment
vercel --prod
```

### Environment Variables
Stellen Sie sicher, dass alle Environment Variables in der Deployment-Plattform konfiguriert sind.

### Build-Optimierung
- **Code Splitting** - Automatische Chunk-Optimierung
- **Tree Shaking** - Entfernung ungenutzten Codes
- **Asset Optimization** - Bildkomprimierung und Caching
- **PWA Optimization** - Service Worker und Offline-Funktionalität

## 🔒 Sicherheit

- **Row Level Security** aktiviert auf allen Tabellen
- **Authentifizierung erforderlich** für alle Operationen
- **Datei-Upload-Validierung** (Größe, Typ, Inhalt)
- **Input-Sanitization** für alle Benutzereingaben
- **Keine sensiblen Daten** im Client-seitigen Code
- **CSRF-Schutz** durch Supabase Auth
- **Rate Limiting** für API-Aufrufe

## 🤝 Contributing

1. **Fork** des Repositories
2. **Feature Branch erstellen** (`git checkout -b feature/amazing-feature`)
3. **Änderungen committen** (`git commit -m 'Add amazing feature'`)
4. **Branch pushen** (`git push origin feature/amazing-feature`)
5. **Pull Request öffnen**

### Code-Qualität
- ESLint-Regeln befolgen
- TypeScript-Fehler beheben
- Komponenten dokumentieren
- Tests für neue Features schreiben

## 📊 Performance

### Optimierungen
- **Lazy Loading** für große Komponenten
- **Memoization** für teure Berechnungen
- **Virtual Scrolling** für große Listen
- **Image Optimization** mit Next.js Image
- **Bundle Splitting** für optimale Ladezeiten

### Monitoring
- **Web Vitals** Tracking
- **Error Monitoring** mit Sentry
- **Performance Analytics** mit Vercel Analytics
- **User Experience** Metriken

## 📄 Lizenz

Dieses Projekt ist unter der MIT-Lizenz lizenziert. Siehe `LICENSE` Datei für Details.

## 🆘 Support

Für Fragen oder Probleme:

1. **GitHub Issues** für Bug-Reports und Feature-Requests
2. **Dokumentation** in `CLAUDE.md` für Entwicklerdetails
3. **Code Review** für komplexe Änderungen
4. **Community Discord** für Diskussionen

---

**Mit ❤️ entwickelt für effizientes Freelance-Management**

*Lanzr - Ihre professionelle Lösung für Freelance-Projektmanagement mit KI-Unterstützung*