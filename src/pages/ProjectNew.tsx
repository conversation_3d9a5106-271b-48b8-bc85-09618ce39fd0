import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { ArrowLeft, Briefcase, Calendar, Euro, Timer } from 'lucide-react';
import { useProjects } from '@/hooks/useProjects';
import { useContacts } from '@/hooks/useContacts';
import { ProjectFormContactSelector } from '@/components/projects/form/ProjectFormContactSelector';
import { toast } from '@/lib/toast';
import type { ProjectInsert, ProjectStatus, ProjectPriority, ProjectType } from '@/types/projects';
import type { Contact } from '@/types/applications';
import { useTranslation } from '@/hooks/useTranslation';
import { useProjectStatusLabel, usePriorityLabel, useProjectTypeLabel } from '@/lib/translations';

const ProjectNew = () => {
  const { t } = useTranslation('forms');
  const tPages = useTranslation('pages').t;
  const tCommon = useTranslation('common').t;
  const tErrors = useTranslation('errors').t;
  const getStatusLabel = useProjectStatusLabel();
  const getPriorityLabel = usePriorityLabel();
  const getTypeLabel = useProjectTypeLabel();
  const navigate = useNavigate();
  const { createProject, isCreating } = useProjects();
  const { contacts, isLoading: contactsLoading } = useContacts();
  
  const [formData, setFormData] = useState<Partial<ProjectInsert>>({
    title: '',
    client_name: '',
    description: '',
    project_type: 'development',
    status: 'starting',
    priority: 'medium',
    hourly_rate: undefined,
    estimated_hours: undefined,
    start_date: undefined,
    planned_end_date: undefined,
    contact_id: undefined,
  });

  // Contact form fields
  const [contactName, setContactName] = useState('');
  const [contactEmail, setContactEmail] = useState('');
  const [contactPhone, setContactPhone] = useState('');
  const [contactCompany, setContactCompany] = useState('');
  const [selectedContactId, setSelectedContactId] = useState('');

  // Contact selection handlers
  const handleContactSelect = (contact: Contact) => {
    setSelectedContactId(contact.id);
    setContactName(contact.name || '');
    setContactEmail(contact.email || '');
    setContactPhone(contact.phone || '');
    setContactCompany(contact.company || '');
    setFormData(prev => ({ 
      ...prev, 
      contact_id: contact.id,
      client_name: contact.company || contact.name || ''
    }));
  };

  const handleContactClear = () => {
    setSelectedContactId('');
    setContactName('');
    setContactEmail('');
    setContactPhone('');
    setContactCompany('');
    setFormData(prev => ({ 
      ...prev, 
      contact_id: undefined 
    }));
  };

  const handleInputChange = (field: keyof ProjectInsert, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.title || !formData.client_name) {
      toast.error(tErrors('general.unexpected_error'), tErrors('validation.title_client_required'));
      return;
    }

    try {
      const projectData: ProjectInsert = {
        title: formData.title,
        client_name: formData.client_name,
        description: formData.description || '',
        project_type: formData.project_type || 'development',
        status: formData.status || 'starting',
        priority: formData.priority || 'medium',
        hourly_rate: formData.hourly_rate,
        estimated_hours: formData.estimated_hours,
        start_date: formData.start_date,
        planned_end_date: formData.planned_end_date,
        contact_id: formData.contact_id,
      };

      await createProject(projectData);
      toast.success(tCommon('messages.project_created'), tCommon('messages.project_created_desc'));
      navigate('/projects');
    } catch (error) {
      console.error('Error creating project:', error);
      toast.error(tErrors('project.save_failed'), tErrors('project.create_failed_desc'));
    }
  };

  const handleCancel = () => {
    navigate('/projects');
  };

  return (
    <div className="container mx-auto px-2 sm:px-6 pb-3 sm:pb-6 max-w-full overflow-x-hidden">
        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-center justify-between mb-6 sm:mb-8 gap-4">
          <div className="min-w-0 flex-1">
            <div className="flex items-center gap-4">
              <Button
                variant="outline"
                size="sm"
                onClick={handleCancel}
                className="flex items-center gap-2"
              >
                <ArrowLeft className="h-4 w-4" />
                {tCommon('actions.back')}
              </Button>
            </div>
          </div>
        </div>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Basic Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Briefcase className="h-5 w-5" />
                {t('sections.basic_information')}
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid gap-4 md:grid-cols-2">
                <div className="space-y-2">
                  <Label htmlFor="title">{t('project.title')} *</Label>
                  <Input
                    id="title"
                    value={formData.title || ''}
                    onChange={(e) => handleInputChange('title', e.target.value)}
                    placeholder={t('placeholders.project_title')}
                    required
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="client_name">{t('project.client_name')} *</Label>
                  <Input
                    id="client_name"
                    value={formData.client_name || ''}
                    onChange={(e) => handleInputChange('client_name', e.target.value)}
                    placeholder={t('placeholders.client_name')}
                    required
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="description">{t('project.description')}</Label>
                <Textarea
                  id="description"
                  value={formData.description || ''}
                  onChange={(e) => handleInputChange('description', e.target.value)}
                  placeholder={t('placeholders.description')}
                  rows={3}
                />
              </div>
            </CardContent>
          </Card>

          {/* Contact Information */}
          <ProjectFormContactSelector
            selectedContactId={selectedContactId}
            contactName={contactName}
            setContactName={setContactName}
            contactEmail={contactEmail}
            setContactEmail={setContactEmail}
            contactPhone={contactPhone}
            setContactPhone={setContactPhone}
            contactCompany={contactCompany}
            setContactCompany={setContactCompany}
            contacts={contacts}
            contactsLoading={contactsLoading}
            onContactSelect={handleContactSelect}
            onContactClear={handleContactClear}
          />

          {/* Project Settings */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Timer className="h-5 w-5" />
                {t('sections.project_settings')}
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid gap-4 md:grid-cols-3">
                <div className="space-y-2">
                  <Label htmlFor="project_type">{t('project.project_type')}</Label>
                  <Select 
                    value={formData.project_type} 
                    onValueChange={(value) => handleInputChange('project_type', value as ProjectType)}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="development">{getTypeLabel('development')}</SelectItem>
                      <SelectItem value="design">{getTypeLabel('design')}</SelectItem>
                      <SelectItem value="consulting">{getTypeLabel('consulting')}</SelectItem>
                      <SelectItem value="marketing">{getTypeLabel('marketing')}</SelectItem>
                      <SelectItem value="other">{getTypeLabel('other')}</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="status">{t('project.status')}</Label>
                  <Select 
                    value={formData.status} 
                    onValueChange={(value) => handleInputChange('status', value as ProjectStatus)}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="starting">{getStatusLabel('starting')}</SelectItem>
                      <SelectItem value="in_progress">{getStatusLabel('in_progress')}</SelectItem>
                      <SelectItem value="on_hold">{getStatusLabel('on_hold')}</SelectItem>
                      <SelectItem value="completing">{getStatusLabel('completing')}</SelectItem>
                      <SelectItem value="completed">{getStatusLabel('completed')}</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="priority">{t('project.priority')}</Label>
                  <Select 
                    value={formData.priority} 
                    onValueChange={(value) => handleInputChange('priority', value as ProjectPriority)}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="low">{getPriorityLabel('low')}</SelectItem>
                      <SelectItem value="medium">{getPriorityLabel('medium')}</SelectItem>
                      <SelectItem value="high">{getPriorityLabel('high')}</SelectItem>
                      <SelectItem value="urgent">{getPriorityLabel('urgent')}</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Financial & Timeline */}
          <div className="grid gap-6 lg:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Euro className="h-5 w-5" />
                  {t('sections.finances')}
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="hourly_rate">{t('project.hourly_rate')}</Label>
                  <Input
                    id="hourly_rate"
                    type="number"
                    step="0.01"
                    min="0"
                    value={formData.hourly_rate || ''}
                    onChange={(e) => handleInputChange('hourly_rate', e.target.value ? parseFloat(e.target.value) : undefined)}
                    placeholder={t('placeholders.currency')}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="estimated_hours">{t('project.estimated_hours')}</Label>
                  <Input
                    id="estimated_hours"
                    type="number"
                    min="0"
                    value={formData.estimated_hours || ''}
                    onChange={(e) => handleInputChange('estimated_hours', e.target.value ? parseInt(e.target.value) : undefined)}
                    placeholder={t('placeholders.hours')}
                  />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Calendar className="h-5 w-5" />
                  {t('sections.timeline')}
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="start_date">{tPages('projects.labels.start_date')}</Label>
                  <Input
                    id="start_date"
                    type="date"
                    value={formData.start_date || ''}
                    onChange={(e) => handleInputChange('start_date', e.target.value || undefined)}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="planned_end_date">{tPages('projects.labels.planned_end_date')}</Label>
                  <Input
                    id="planned_end_date"
                    type="date"
                    value={formData.planned_end_date || ''}
                    onChange={(e) => handleInputChange('planned_end_date', e.target.value || undefined)}
                  />
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Actions */}
          <div className="flex justify-end gap-3 pt-6">
            <Button
              type="button"
              variant="outline"
              onClick={handleCancel}
              disabled={isCreating}
            >
              {tCommon('actions.cancel')}
            </Button>
            <Button type="submit" disabled={isCreating || !formData.title || !formData.client_name}>
              {isCreating ? tCommon('actions.loading') : tCommon('actions.create')}
            </Button>
          </div>
        </form>
    </div>
  );
};

export default ProjectNew;