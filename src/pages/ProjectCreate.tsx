import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { ProjectForm } from '@/components/projects/ProjectForm';
import { StatusUpdateButton } from '@/components/projects/StatusUpdateButton';
import { Button } from '@/components/ui/button';
import { ArrowLeft } from 'lucide-react';
import { toast } from '@/lib/toast';
import { useApplications } from '@/hooks/useApplications';
import { useTranslation } from '@/hooks/useTranslation';
import type { ApplicationStatus } from '@/types/applications';

const ProjectCreate = () => {
  const [currentStatus, setCurrentStatus] = useState<ApplicationStatus>('not_applied');
  const [tempProject, setTempProject] = useState<any>(null);
  const navigate = useNavigate();
  const { createApplication, isUpdating } = useApplications();
  const { t } = useTranslation('applications');

  const handleCreateProject = async (data: any) => {
    try {
      // Include the current status in the project data
      const projectData = { ...data, status: currentStatus };
      await createApplication(projectData);
      toast.success(t('new_project.toasts.created_title'), t('new_project.toasts.created_message'));
      navigate('/applications');
    } catch (error) {
      console.error('Error creating project:', error);
      toast.error(t('new_project.toasts.error_title'), t('new_project.toasts.error_message'));
    }
  };

  const handleStatusUpdate = async (projectId: string, updateData: any, statusNotes?: { notes?: string; notes_date?: string }) => {
    // For create page, we just update the current status
    // The notes will be saved when the project is actually created
    setCurrentStatus(updateData.status);
    if (statusNotes?.notes) {
      // Store notes temporarily to include in project creation
      setTempProject((prev: any) => ({
        ...prev,
        initialStatusNotes: statusNotes
      }));
    }
    toast.info(t('new_project.toasts.status_set_title'), t('new_project.toasts.status_set_message'));
  };

  const handleCancel = () => {
    navigate('/applications');
  };

  return (
    <div className="min-h-screen bg-background">
      <div className="container mx-auto px-2 sm:px-6 pb-3 sm:pb-6 max-w-full overflow-x-hidden">
                {/* Header */}
                <div className="flex flex-col sm:flex-row sm:items-center justify-between mb-6 sm:mb-8 gap-4">
                  <div className="min-w-0 flex-1">
                    <div className="flex items-center gap-4">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={handleCancel}
                        className="flex items-center gap-2"
                      >
                        <ArrowLeft className="h-4 w-4" />
                        {t('new_project.back')}
                      </Button>
                    </div>
                  </div>
                </div>

                {/* Status Management */}
                <div className="bg-muted/20 rounded-lg p-4 border border-border/30 mb-6">
                  <div className="flex items-center gap-2 mb-3">
                    <div className="h-2 w-2 bg-primary rounded-full animate-pulse" />
                    <span className="text-sm font-medium text-foreground">
                      {t('new_project.status_setup.title')}
                    </span>
                  </div>
                  
                  <StatusUpdateButton
                    project={{ 
                      id: 'temp', 
                      status: currentStatus,
                      project_name: t('new_project.status_setup.placeholder_project'),
                      company_name: t('new_project.status_setup.placeholder_company')
                    } as any}
                    onUpdateProject={handleStatusUpdate}
                    isUpdating={isUpdating}
                    className="w-full"
                  />
                </div>

                {/* Form */}
                <ProjectForm 
                  onClose={handleCancel}
                  onSubmit={handleCreateProject}
                  hideStatusField={true}
                />
      </div>
    </div>
  );
};

export default ProjectCreate;