import { useParams, useNavigate } from 'react-router-dom';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { ArrowLeft } from 'lucide-react';
import { useApplication, useApplications } from '@/hooks/useApplications';
import { ProjectForm } from '@/components/projects/ProjectForm';
import type { CreateApplicationData } from '@/types/applications';

const ProjectEdit = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { data: application, isLoading } = useApplication(id!);
  const { updateApplication } = useApplications();

  const handleSubmit = async (data: CreateApplicationData) => {
    if (!id) return;
    
    await updateApplication({ 
      id, 
      updates: data
    });
    
    // Navigate back to application details
    navigate(`/applications/${id}`);
  };

  const handleClose = () => {
    navigate(`/applications/${id}`);
  };

  if (isLoading) {
    return (
      <div className="container mx-auto px-2 sm:px-6 pb-3 sm:pb-6 max-w-full overflow-x-hidden">
        <div className="flex flex-col sm:flex-row sm:items-center justify-between mb-6 sm:mb-8 gap-4">
          <div className="min-w-0 flex-1">
            <h1 className="text-2xl sm:text-3xl font-bold bg-gradient-primary bg-clip-text text-transparent mb-2">
              Lade...
            </h1>
            <p className="text-muted-foreground text-sm sm:text-base">
              Bewerbung wird geladen
            </p>
          </div>
        </div>
        <Card>
          <CardContent className="pt-6">
            <div className="space-y-3">
              {[...Array(5)].map((_, i) => (
                <div key={i} className="h-4 bg-muted rounded animate-pulse" />
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (!application) {
    return (
      <div className="container mx-auto px-2 sm:px-6 pb-3 sm:pb-6 max-w-full overflow-x-hidden">
        <div className="flex flex-col sm:flex-row sm:items-center justify-between mb-6 sm:mb-8 gap-4">
          <div className="min-w-0 flex-1">
            <h1 className="text-2xl sm:text-3xl font-bold bg-gradient-primary bg-clip-text text-transparent mb-2">
              Bewerbung nicht gefunden
            </h1>
            <p className="text-muted-foreground text-sm sm:text-base">
              Die angeforderte Bewerbung existiert nicht
            </p>
          </div>
        </div>
        <Card>
          <CardContent className="pt-6 text-center">
            <p className="text-muted-foreground mb-4">Die Bewerbung konnte nicht gefunden werden.</p>
            <Button onClick={() => navigate('/applications')}>
              <ArrowLeft className="h-4 w-4 mr-2" />
              Zurück zu Bewerbungen
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-2 sm:px-6 pb-3 sm:pb-6 max-w-full overflow-x-hidden">
      <div className="flex flex-col sm:flex-row sm:items-center justify-between mb-6 sm:mb-8 gap-4">
        <div className="min-w-0 flex-1">
          <h1 className="text-2xl sm:text-3xl font-bold bg-gradient-primary bg-clip-text text-transparent mb-2">
            {application.project_name}
          </h1>
        </div>
        <div className="flex items-center gap-2 sm:gap-4">
          <Button variant="outline" onClick={handleClose}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Zurück
          </Button>
        </div>
      </div>
      
      <ProjectForm
        onClose={handleClose}
        initialProject={application}
        onSubmit={handleSubmit}
      />
    </div>
  );
};

export default ProjectEdit;