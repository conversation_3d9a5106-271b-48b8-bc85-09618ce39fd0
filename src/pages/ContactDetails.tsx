import { use<PERSON>ara<PERSON>, useNavigate } from 'react-router-dom';
import { useState, useEffect, useCallback, useMemo } from 'react';
import { useContacts } from '@/hooks/useContacts';
import { useApplications } from '@/hooks/useApplications';
import { useCommunicationStats } from '@/hooks/useContactCommunications';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from '@/components/ui/alert-dialog';
import { Contact, UpdateContactData, ContactAnalytics, ApplicationWithContact, APPLICATION_STATUS_LABELS } from '@/types/applications';
import { CommunicationList, CommunicationSummary } from '@/components/contacts/communications';
import { CommunicationQuickActions } from '@/components/contacts/CommunicationQuickActions';
import { 
  User, 
  Mail, 
  Phone, 
  Building2, 
  Calendar,
  TrendingUp,
  FileText,
  Edit,
  Trash2,
  ExternalLink,
  MessageSquare,
  ArrowLeft,
  Clock
} from 'lucide-react';
import { format } from 'date-fns';
import { de } from 'date-fns/locale';
import { useTranslation } from '@/hooks/useTranslation';

export const ContactDetails = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [analytics, setAnalytics] = useState<ContactAnalytics | null>(null);
  const [isLoadingAnalytics, setIsLoadingAnalytics] = useState(true);
  const [activeTab, setActiveTab] = useState('overview');
  const { t } = useTranslation('contacts');
  
  const { 
    contacts, 
    isLoading: contactsLoading, 
    getContactAnalytics, 
    deleteContact 
  } = useContacts();
  
  const { data: allProjects = [] } = useApplications();
  const { data: commStats } = useCommunicationStats(id || '');
  
  const contact = contacts.find(c => c.id === id);
  
  // Memoize filtered projects to prevent unnecessary re-renders
  const projects = useMemo(() => {
    return allProjects.filter(project => project.contact_id === id);
  }, [allProjects, id]);

  // Memoize the analytics loading function to prevent infinite re-renders
  const loadAnalytics = useCallback(async (contactId: string) => {
    setIsLoadingAnalytics(true);
    try {
      const data = await getContactAnalytics(contactId);
      setAnalytics(data);
    } catch (error) {
      console.error('Error loading analytics:', error);
    } finally {
      setIsLoadingAnalytics(false);
    }
  }, [getContactAnalytics]);

  // Load analytics data with proper dependency management
  useEffect(() => {
    if (contact?.id) {
      loadAnalytics(contact.id);
    }
  }, [contact?.id, loadAnalytics]);

  // Memoize expensive calculations - MUST be before any conditional returns
  const successRate = useMemo(() => {
    if (!contact) return 0;
    return contact.total_projects > 0 
      ? Math.round((contact.successful_projects / contact.total_projects) * 100)
      : 0;
  }, [contact?.total_projects, contact?.successful_projects, contact]);

  const handleDelete = async () => {
    if (contact) {
      const success = await deleteContact(contact.id);
      if (success) {
        navigate('/contacts');
      }
    }
  };

  const handleEdit = () => {
    if (contact) {
      navigate(`/contacts/${contact.id}/edit`);
    }
  };

  if (contactsLoading) {
    return (
      <div className="min-h-screen bg-background">
        <div className="container mx-auto px-2 sm:px-6 pb-3 sm:pb-6 max-w-full overflow-x-hidden">
          <div className="flex items-center justify-center py-12">
            <div className="text-center space-y-4">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
              <div className="text-sm text-muted-foreground">
                {t('loading.contact_data')}
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (!contact) {
    return (
      <div className="min-h-screen bg-background">
        <div className="container mx-auto px-2 sm:px-6 pb-3 sm:pb-6 max-w-full overflow-x-hidden">
          <div className="mb-6 sm:mb-8">
            <h1 className="text-2xl sm:text-3xl font-bold bg-gradient-primary bg-clip-text text-transparent mb-2">
              {t('details.not_found')}
            </h1>
            <p className="text-muted-foreground text-sm sm:text-base">
              {t('details.not_found_desc')}
            </p>
          </div>
          <Card>
            <CardContent className="p-8 text-center">
              <User className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-medium mb-2">{t('details.not_found')}</h3>
              <p className="text-muted-foreground mb-4">
                {t('details.not_found_message')}
              </p>
              <Button onClick={() => navigate('/contacts')} variant="outline">
                <ArrowLeft className="h-4 w-4 mr-2" />
                {t('details.back_to_contacts')}
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background">
      <div className="container mx-auto px-2 sm:px-6 pb-3 sm:pb-6 max-w-full overflow-x-hidden">
          {/* Header with buttons only - exactly like ApplicationEdit */}
          <div className="flex flex-row items-center justify-between mb-6 sm:mb-8 gap-4">
            <div className="flex items-center gap-2">
              <Button variant="outline" onClick={() => navigate('/contacts')}>
                <ArrowLeft className="h-4 w-4 mr-2" />
                {t('details.back')}
              </Button>
            </div>
            <div className="flex items-center gap-2 sm:gap-4">
              {/* Communication Quick Actions */}
              <CommunicationQuickActions 
                contact={contact}
                compact={true}
                onSuccess={() => {
                  // Refresh communication data if needed
                  console.log('Communication created successfully');
                }}
              />
              <Button variant="outline" onClick={handleEdit} className="p-2 sm:px-3 sm:py-2">
                <Edit className="h-4 w-4" />
                <span className="hidden sm:inline sm:ml-2">{t('details.edit')}</span>
              </Button>
              <AlertDialog>
                <AlertDialogTrigger asChild>
                  <Button variant="outline" className="text-red-600 hover:text-red-700 p-2 sm:px-3 sm:py-2">
                    <Trash2 className="h-4 w-4" />
                    <span className="hidden sm:inline sm:ml-2">{t('details.delete')}</span>
                  </Button>
                </AlertDialogTrigger>
                <AlertDialogContent>
                  <AlertDialogHeader>
                    <AlertDialogTitle>{t('delete.confirm_title')}</AlertDialogTitle>
                    <AlertDialogDescription>
                      {t('delete.confirm_message')}
                      {contact.total_projects > 0 && (
                        <>
                          <br />
                          <strong>{t('delete.warning', { count: contact.total_projects })}</strong>
                        </>
                      )}
                    </AlertDialogDescription>
                  </AlertDialogHeader>
                  <AlertDialogFooter>
                    <AlertDialogCancel>{t('delete.cancel')}</AlertDialogCancel>
                    <AlertDialogAction onClick={handleDelete} className="bg-red-600 hover:bg-red-700">
                      {t('delete.delete')}
                    </AlertDialogAction>
                  </AlertDialogFooter>
                </AlertDialogContent>
              </AlertDialog>
            </div>
          </div>

          <div className="space-y-6">
            {/* Contact Header Card */}
            <Card>
              <CardContent className="p-6">
                <div className="flex flex-col sm:flex-row sm:items-center gap-4">
                  <div className="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center flex-shrink-0">
                    <User className="h-8 w-8 text-primary" />
                  </div>
                  <div className="flex-1 min-w-0">
                    <h2 className="text-2xl font-bold mb-1 break-words leading-tight">
                      {contact.name || t('details.unnamed_contact')}
                    </h2>
                    {contact.company && (
                      <div className="flex items-center gap-2 text-muted-foreground mb-2">
                        <Building2 className="h-4 w-4 flex-shrink-0" />
                        <span className="break-words">{contact.company}</span>
                      </div>
                    )}
                    <div className="flex flex-wrap gap-2">
                      <Badge variant="outline" className="text-xs">
                        {t('card.projects_count', { count: contact.total_projects })}
                      </Badge>
                      {contact.successful_projects > 0 && (
                        <Badge variant="outline" className="text-xs bg-green-50 text-green-700 border-green-200">
                          {t('card.success_count', { count: contact.successful_projects })}
                        </Badge>
                      )}
                      <Badge variant="outline" className="text-xs">
                        {successRate}% {t('details.success_rate')}
                      </Badge>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

        {/* Statistics Cards */}
        <div className="grid grid-cols-2 sm:grid-cols-5 gap-3 sm:gap-4">
          <Card className="min-w-0">
            <CardHeader className="pb-2">
              <CardTitle className="text-xs sm:text-sm font-medium flex items-center gap-1 sm:gap-2">
                <FileText className="h-3 w-3 sm:h-4 sm:w-4 text-blue-500 flex-shrink-0" />
                <span className="truncate">{t('details.total_projects')}</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-xl sm:text-2xl font-bold">{contact.total_projects}</div>
              <p className="text-xs text-muted-foreground truncate">{t('details.all_projects_desc')}</p>
            </CardContent>
          </Card>
          
          <Card className="min-w-0">
            <CardHeader className="pb-2">
              <CardTitle className="text-xs sm:text-sm font-medium flex items-center gap-1 sm:gap-2">
                <TrendingUp className="h-3 w-3 sm:h-4 sm:w-4 text-green-500 flex-shrink-0" />
                <span className="truncate">{t('details.success_rate')}</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-xl sm:text-2xl font-bold">{successRate}%</div>
              <p className="text-xs text-muted-foreground truncate">{t('details.successful_projects')}</p>
            </CardContent>
          </Card>
          
          <Card className="min-w-0">
            <CardHeader className="pb-2">
              <CardTitle className="text-xs sm:text-sm font-medium flex items-center gap-1 sm:gap-2">
                <MessageSquare className="h-3 w-3 sm:h-4 sm:w-4 text-purple-500 flex-shrink-0" />
                <span className="truncate">{t('details.last_contact')}</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-xl sm:text-2xl font-bold">
                {commStats?.last_communication_date 
                  ? format(new Date(commStats.last_communication_date), 'dd.MM', { locale: de })
                  : t('details.never')
                }
              </div>
              <p className="text-xs text-muted-foreground truncate">{t('details.last_communication')}</p>
            </CardContent>
          </Card>

          <Card className="min-w-0">
            <CardHeader className="pb-2">
              <CardTitle className="text-xs sm:text-sm font-medium flex items-center gap-1 sm:gap-2">
                <Clock className="h-3 w-3 sm:h-4 sm:w-4 text-emerald-500 flex-shrink-0" />
                <span className="truncate">{t('details.last_activity')}</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-xl sm:text-2xl font-bold">
                {format(new Date(contact.updated_at), 'dd.MM', { locale: de })}
              </div>
              <p className="text-xs text-muted-foreground truncate">{t('details.contact_updated')}</p>
            </CardContent>
          </Card>

          <Card className="min-w-0">
            <CardHeader className="pb-2">
              <CardTitle className="text-xs sm:text-sm font-medium flex items-center gap-1 sm:gap-2">
                <MessageSquare className="h-3 w-3 sm:h-4 sm:w-4 text-orange-500 flex-shrink-0" />
                <span className="truncate">Kommunikationen</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-xl sm:text-2xl font-bold">
                {commStats?.total_communications || 0}
              </div>
              <p className="text-xs text-muted-foreground truncate">Anzahl Gespräche</p>
            </CardContent>
          </Card>
        </div>

        {/* Contact Details */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <User className="h-5 w-5" />
              {t('details.contact_info')}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Contact Information */}
              <div className="space-y-4">
                {contact.email && (
                  <div className="flex items-center gap-3 p-4 bg-muted/50 rounded-lg">
                    <div className="w-10 h-10 bg-blue-100 dark:bg-blue-900/20 rounded-full flex items-center justify-center flex-shrink-0">
                      <Mail className="h-5 w-5 text-blue-600 dark:text-blue-400" />
                    </div>
                    <div className="flex-1 min-w-0">
                      <div className="text-xs text-muted-foreground uppercase tracking-wide font-medium mb-1">
                        {t('details.email')}
                      </div>
                      <div className="font-medium text-sm truncate">
                        {contact.email}
                      </div>
                    </div>
                    <Button variant="ghost" size="sm" asChild className="flex-shrink-0">
                      <a href={`mailto:${contact.email}`} className="text-blue-600 hover:text-blue-700">
                        <ExternalLink className="h-4 w-4" />
                      </a>
                    </Button>
                  </div>
                )}
                
                {contact.phone && (
                  <div className="flex items-center gap-3 p-4 bg-muted/50 rounded-lg">
                    <div className="w-10 h-10 bg-green-100 dark:bg-green-900/20 rounded-full flex items-center justify-center flex-shrink-0">
                      <Phone className="h-5 w-5 text-green-600 dark:text-green-400" />
                    </div>
                    <div className="flex-1 min-w-0">
                      <div className="text-xs text-muted-foreground uppercase tracking-wide font-medium mb-1">
                        {t('details.phone')}
                      </div>
                      <div className="font-medium text-sm">
                        {contact.phone}
                      </div>
                    </div>
                    <Button variant="ghost" size="sm" asChild className="flex-shrink-0">
                      <a href={`tel:${contact.phone}`} className="text-green-600 hover:text-green-700">
                        <ExternalLink className="h-4 w-4" />
                      </a>
                    </Button>
                  </div>
                )}
                
                {contact.company && (
                  <div className="flex items-center gap-3 p-4 bg-muted/50 rounded-lg">
                    <div className="w-10 h-10 bg-purple-100 dark:bg-purple-900/20 rounded-full flex items-center justify-center flex-shrink-0">
                      <Building2 className="h-5 w-5 text-purple-600 dark:text-purple-400" />
                    </div>
                    <div className="flex-1 min-w-0">
                      <div className="text-xs text-muted-foreground uppercase tracking-wide font-medium mb-1">
                        {t('details.company')}
                      </div>
                      <div className="font-medium text-sm">
                        {contact.company}
                      </div>
                    </div>
                  </div>
                )}
              </div>

              {/* Timestamps */}
              <div className="space-y-4">
                <div className="flex items-center gap-3 p-4 bg-muted/50 rounded-lg">
                  <div className="w-10 h-10 bg-orange-100 dark:bg-orange-900/20 rounded-full flex items-center justify-center flex-shrink-0">
                    <Calendar className="h-5 w-5 text-orange-600 dark:text-orange-400" />
                  </div>
                  <div className="flex-1 min-w-0">
                    <div className="text-xs text-muted-foreground uppercase tracking-wide font-medium mb-1">
                      {t('details.created')}
                    </div>
                    <div className="font-medium text-sm">
                      {format(new Date(contact.created_at), 'dd.MM.yyyy HH:mm', { locale: de })}
                    </div>
                  </div>
                </div>

                <div className="flex items-center gap-3 p-4 bg-muted/50 rounded-lg">
                  <div className="w-10 h-10 bg-teal-100 dark:bg-teal-900/20 rounded-full flex items-center justify-center flex-shrink-0">
                    <Calendar className="h-5 w-5 text-teal-600 dark:text-teal-400" />
                  </div>
                  <div className="flex-1 min-w-0">
                    <div className="text-xs text-muted-foreground uppercase tracking-wide font-medium mb-1">
                      {t('details.updated')}
                    </div>
                    <div className="font-medium text-sm">
                      {format(new Date(contact.updated_at), 'dd.MM.yyyy HH:mm', { locale: de })}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Notes Section */}
        {contact.notes && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <MessageSquare className="h-5 w-5" />
                {t('details.notes')}
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="whitespace-pre-wrap text-sm leading-relaxed p-4 bg-muted/30 rounded-lg">
                {contact.notes}
              </div>
            </CardContent>
          </Card>
        )}

        {/* Main Content Tabs */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
          <div className="w-full overflow-x-auto">
            <TabsList className="inline-flex w-auto min-w-full sm:grid sm:grid-cols-4">
              <TabsTrigger value="overview" className="flex-shrink-0 px-2 sm:px-4">{t('tabs.overview')}</TabsTrigger>
              <TabsTrigger value="communications" className="flex-shrink-0 px-2 sm:px-4">{t('tabs.communications')}</TabsTrigger>
              <TabsTrigger value="projects" className="flex-shrink-0 px-2 sm:px-4">{t('tabs.projects', { count: projects.length })}</TabsTrigger>
              <TabsTrigger value="analytics" className="flex-shrink-0 px-2 sm:px-4">{t('tabs.analytics')}</TabsTrigger>
            </TabsList>
          </div>
          
          <TabsContent value="overview" className="space-y-4">
            {/* Desktop: Side-by-side layout, Mobile: Stacked */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 lg:gap-6">
              {/* Communication Summary */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center justify-between">
                    <span className="flex items-center gap-2">
                      <MessageSquare className="h-5 w-5" />
                      {t('overview.recent_communications')}
                    </span>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setActiveTab('communications')}
                    >
                      {t('overview.view_all')}
                    </Button>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <CommunicationSummary
                    contactId={contact.id}
                    contactName={contact.name || contact.company}
                    headless={true}
                  />
                </CardContent>
              </Card>
              
              {/* Recent Projects Preview */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center justify-between">
                    <span className="flex items-center gap-2">
                      <FileText className="h-5 w-5" />
                      {t('overview.recent_projects')}
                    </span>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setActiveTab('projects')}
                    >
                      {t('overview.view_all')}
                    </Button>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  {projects.length > 0 ? (
                    <>
                      <ProjectList projects={projects.slice(0, 3)} />
                      {projects.length > 3 && (
                        <p className="text-sm text-muted-foreground text-center mt-4">
                          {t('details.more_projects', { count: projects.length - 3 })}
                        </p>
                      )}
                    </>
                  ) : (
                    <div className="text-center py-8">
                      <FileText className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                      <p className="text-muted-foreground">{t('projects.no_projects')}</p>
                    </div>
                  )}
                </CardContent>
              </Card>
            </div>
          </TabsContent>
          
          <TabsContent value="communications" className="space-y-4">
            <CommunicationList
              contactId={contact.id}
              contactName={contact.name || contact.company}
              compact={false}
              maxHeight="70vh"
              showAddButton={false}
            />
          </TabsContent>
          
          <TabsContent value="projects" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <FileText className="h-5 w-5" />
                  {t('details.related_projects')}
                </CardTitle>
              </CardHeader>
              <CardContent>
                <Tabs defaultValue="all" className="space-y-4">
                  <TabsList className="w-full grid grid-cols-3">
                    <TabsTrigger value="all">{t('projects.all', { count: projects.length })}</TabsTrigger>
                    <TabsTrigger value="successful">
                      {t('projects.successful', { count: projects.filter(p => p.status === 'offer_received' || p.status === 'project_completed').length })}
                    </TabsTrigger>
                    <TabsTrigger value="active">
                      {t('projects.active', { count: projects.filter(p => !['rejected', 'project_completed'].includes(p.status)).length })}
                    </TabsTrigger>
                  </TabsList>
                  
                  <TabsContent value="all" className="space-y-4">
                    <ProjectList projects={projects} />
                  </TabsContent>
                  
                  <TabsContent value="successful" className="space-y-4">
                    <ProjectList 
                      projects={projects.filter(p => p.status === 'offer_received' || p.status === 'project_completed')} 
                    />
                  </TabsContent>
                  
                  <TabsContent value="active" className="space-y-4">
                    <ProjectList 
                      projects={projects.filter(p => !['rejected', 'project_completed'].includes(p.status))} 
                    />
                  </TabsContent>
                </Tabs>
              </CardContent>
            </Card>
          </TabsContent>
          
          <TabsContent value="analytics" className="space-y-4">
            {/* Enhanced Analytics with Communication Data */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {/* Project Statistics */}
              <Card>
                <CardHeader className="pb-4">
                  <CardTitle className="flex items-center gap-2 text-base">
                    <TrendingUp className="h-4 w-4" />
                    {t('analytics.project_performance')}
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span>{t('details.success_rate')}</span>
                      <span className="font-medium">{successRate}%</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span>{t('details.total_projects')}</span>
                      <span className="font-medium">{contact.total_projects}</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span>{t('analytics.successful')}</span>
                      <span className="font-medium text-green-600">{contact.successful_projects}</span>
                    </div>
                  </div>
                </CardContent>
              </Card>
              
              {/* Communication Statistics */}
              <Card>
                <CardHeader className="pb-4">
                  <CardTitle className="flex items-center gap-2 text-base">
                    <MessageSquare className="h-4 w-4" />
                    {t('analytics.communication')}
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <CommunicationSummary
                    contactId={contact.id}
                    contactName={contact.name || contact.company}
                    className="border-0 shadow-none p-0"
                  />
                </CardContent>
              </Card>
              
              {/* Timeline */}
              <Card>
                <CardHeader className="pb-4">
                  <CardTitle className="flex items-center gap-2 text-base">
                    <Calendar className="h-4 w-4" />
                    {t('analytics.timeline')}
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <div className="text-xs text-muted-foreground space-y-2">
                    <div className="flex justify-between">
                      <span>{t('details.created')}</span>
                      <span>{format(new Date(contact.created_at), 'dd.MM.yyyy', { locale: de })}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>{t('details.updated')}</span>
                      <span>{format(new Date(contact.updated_at), 'dd.MM.yyyy', { locale: de })}</span>
                    </div>
                    {analytics?.recent_applications[0] && (
                      <div className="flex justify-between">
                        <span>{t('analytics.last_project')}</span>
                        <span>{format(new Date(analytics.recent_applications[0].updated_at), 'dd.MM.yyyy', { locale: de })}</span>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>
        </Tabs>
        </div>
      </div>
    </div>
  );
};

// Project List Component
interface ProjectListProps {
  projects: ApplicationWithContact[];
}

const ProjectList = ({ projects }: ProjectListProps) => {
  const { t } = useTranslation('contacts');
  if (projects.length === 0) {
    return (
      <div className="text-center py-8">
        <FileText className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
        <p className="text-muted-foreground">{t('projects.no_projects')}</p>
      </div>
    );
  }

  return (
    <div className="space-y-3">
      {projects.map((project) => (
        <Card key={project.id} className="hover:shadow-sm transition-shadow">
          <CardContent className="p-4">
            <div className="flex items-start justify-between">
              <div className="flex-1 min-w-0">
                <h4 className="font-medium mb-1 break-words leading-tight">{project.project_name}</h4>
                <p className="text-sm text-muted-foreground mb-2 break-words">{project.company_name}</p>
                
                <div className="flex flex-wrap items-center gap-2">
                  <Badge variant="outline" className="text-xs">
                    {APPLICATION_STATUS_LABELS[project.status] || project.status}
                  </Badge>
                  {project.budget_range && (
                    <span className="text-xs text-muted-foreground">{project.budget_range}</span>
                  )}
                </div>
              </div>
              
              <div className="text-xs text-muted-foreground">
                {format(new Date(project.created_at), 'dd.MM.yy', { locale: de })}
              </div>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
};