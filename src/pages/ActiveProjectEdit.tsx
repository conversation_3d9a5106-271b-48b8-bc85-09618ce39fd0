import React, { useState, useEffect } from 'react';
import { usePara<PERSON>, useNavigate } from 'react-router-dom';
import { PageLayout } from '@/components/layout/PageLayout';
import { usePageConfig } from '@/hooks/usePageConfig';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { ArrowLeft, Save, FileText, Settings, DollarSign, Calendar } from 'lucide-react';
import { useProject, useProjects } from '@/hooks/useProjects';
import { useContacts } from '@/hooks/useContacts';
import { ProjectFormContactSelector } from '@/components/projects/form/ProjectFormContactSelector';
import type { ProjectUpdate, ProjectStatus, ProjectPriority, ProjectType } from '@/types/projects';
import type { Contact } from '@/types/applications';
import { useTranslation } from '@/hooks/useTranslation';
import { useProjectStatusLabel, usePriorityLabel, useProjectTypeLabel } from '@/lib/translations';

const ActiveProjectEdit = () => {
  const { t } = useTranslation('forms');
  const getStatusLabel = useProjectStatusLabel();
  const getPriorityLabel = usePriorityLabel();
  const getTypeLabel = useProjectTypeLabel();
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { data: project, isLoading } = useProject(id!);
  const { updateProject, isUpdating } = useProjects();
  const { contacts, isLoading: contactsLoading } = useContacts();
  
  const [formData, setFormData] = useState<Partial<ProjectUpdate>>({});

  // Contact form fields
  const [contactName, setContactName] = useState('');
  const [contactEmail, setContactEmail] = useState('');
  const [contactPhone, setContactPhone] = useState('');
  const [contactCompany, setContactCompany] = useState('');
  const [selectedContactId, setSelectedContactId] = useState('');

  useEffect(() => {
    if (project) {
      setFormData({
        title: project.title,
        client_name: project.client_name,
        description: project.description || '',
        project_type: project.project_type || 'development',
        status: project.status || 'starting',
        priority: project.priority || 'medium',
        hourly_rate: project.hourly_rate,
        estimated_hours: project.estimated_hours,
        start_date: project.start_date,
        planned_end_date: project.planned_end_date,
        actual_end_date: project.actual_end_date,
        contact_id: project.contact_id,
      });

      // Load contact data if contact_id exists
      if (project.contact_id && contacts.length > 0) {
        const contact = contacts.find(c => c.id === project.contact_id);
        if (contact) {
          setSelectedContactId(contact.id);
          setContactName(contact.name || '');
          setContactEmail(contact.email || '');
          setContactPhone(contact.phone || '');
          setContactCompany(contact.company || '');
        }
      }
    }
  }, [project, contacts]);

  // Contact selection handlers
  const handleContactSelect = (contact: Contact) => {
    setSelectedContactId(contact.id);
    setContactName(contact.name || '');
    setContactEmail(contact.email || '');
    setContactPhone(contact.phone || '');
    setContactCompany(contact.company || '');
    setFormData(prev => ({ 
      ...prev, 
      contact_id: contact.id,
      client_name: contact.company || contact.name || ''
    }));
  };

  const handleContactClear = () => {
    setSelectedContactId('');
    setContactName('');
    setContactEmail('');
    setContactPhone('');
    setContactCompany('');
    setFormData(prev => ({ 
      ...prev, 
      contact_id: undefined 
    }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.title || !formData.client_name || !id) {
      return;
    }

    updateProject({ 
      id, 
      updates: formData as ProjectUpdate 
    });
    
    // Navigate back to project details
    navigate(`/projects/${id}`);
  };

  const handleInputChange = (field: keyof ProjectUpdate, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  if (isLoading) {
    return (
      <PageLayout title={t('pages:loading.general')} description={t('pages:loading.project')}>
        <Card>
          <CardContent className="pt-6">
            <div className="space-y-3">
              {[...Array(5)].map((_, i) => (
                <div key={i} className="h-4 bg-muted rounded animate-pulse" />
              ))}
            </div>
          </CardContent>
        </Card>
      </PageLayout>
    );
  }

  if (!project) {
    return (
      <PageLayout title={t('pages:not_found.project_title')} description={t('pages:not_found.project_description')}>
        <Card>
          <CardContent className="pt-6 text-center">
            <p className="text-muted-foreground mb-4">Das Projekt konnte nicht gefunden werden.</p>
            <Button onClick={() => navigate('/projects')}>
              <ArrowLeft className="h-4 w-4 mr-2" />
              Zurück zu aktiven Projekten
            </Button>
          </CardContent>
        </Card>
      </PageLayout>
    );
  }

  return (
    <PageLayout
      title={`${project.title} bearbeiten`}
      description={t('pages:projects.edit_active')}
      headerActions={
        <Button variant="outline" onClick={() => navigate(`/projects/${id}`)}>
          <ArrowLeft className="h-4 w-4 mr-2" />
          Zurück
        </Button>
      }
    >
      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Basic Project Information */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <FileText className="h-5 w-5" />
              Projekt bearbeiten
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid gap-4 md:grid-cols-2">
              <div className="space-y-2">
                <Label htmlFor="title">Projekttitel *</Label>
                <Input
                  id="title"
                  value={formData.title || ''}
                  onChange={(e) => handleInputChange('title', e.target.value)}
                  placeholder="Z.B. Website Redesign"
                  required
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="client_name">Client Name *</Label>
                <Input
                  id="client_name"
                  value={formData.client_name || ''}
                  onChange={(e) => handleInputChange('client_name', e.target.value)}
                  placeholder="Z.B. Musterfirma GmbH"
                  required
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="description">Beschreibung</Label>
              <Textarea
                id="description"
                value={formData.description || ''}
                onChange={(e) => handleInputChange('description', e.target.value)}
                placeholder="Projektbeschreibung..."
                rows={8}
                className="min-h-[200px]"
              />
            </div>
          </CardContent>
        </Card>

        {/* Contact Information */}
        <ProjectFormContactSelector
          selectedContactId={selectedContactId}
          contactName={contactName}
          setContactName={setContactName}
          contactEmail={contactEmail}
          setContactEmail={setContactEmail}
          contactPhone={contactPhone}
          setContactPhone={setContactPhone}
          contactCompany={contactCompany}
          setContactCompany={setContactCompany}
          contacts={contacts}
          contactsLoading={contactsLoading}
          onContactSelect={handleContactSelect}
          onContactClear={handleContactClear}
        />

        {/* Project Settings */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Settings className="h-5 w-5" />
              Projekt-Einstellungen
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid gap-4 md:grid-cols-3">
              <div className="space-y-2">
                <Label htmlFor="project_type">Projekttyp</Label>
                <Select 
                  value={formData.project_type} 
                  onValueChange={(value) => handleInputChange('project_type', value as ProjectType)}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="development">{getTypeLabel('development')}</SelectItem>
                    <SelectItem value="design">{getTypeLabel('design')}</SelectItem>
                    <SelectItem value="consulting">{getTypeLabel('consulting')}</SelectItem>
                    <SelectItem value="marketing">{getTypeLabel('marketing')}</SelectItem>
                    <SelectItem value="other">{getTypeLabel('other')}</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label htmlFor="status">Status</Label>
                <Select 
                  value={formData.status} 
                  onValueChange={(value) => handleInputChange('status', value as ProjectStatus)}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="starting">{getStatusLabel('starting')}</SelectItem>
                    <SelectItem value="in_progress">{getStatusLabel('in_progress')}</SelectItem>
                    <SelectItem value="on_hold">{getStatusLabel('on_hold')}</SelectItem>
                    <SelectItem value="completing">{getStatusLabel('completing')}</SelectItem>
                    <SelectItem value="completed">{getStatusLabel('completed')}</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label htmlFor="priority">Priorität</Label>
                <Select 
                  value={formData.priority} 
                  onValueChange={(value) => handleInputChange('priority', value as ProjectPriority)}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="low">{getPriorityLabel('low')}</SelectItem>
                    <SelectItem value="medium">{getPriorityLabel('medium')}</SelectItem>
                    <SelectItem value="high">{getPriorityLabel('high')}</SelectItem>
                    <SelectItem value="urgent">{getPriorityLabel('urgent')}</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Financial Information */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <DollarSign className="h-5 w-5" />
              Finanzielle Informationen
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <Label htmlFor="hourly_rate">Stundensatz (€)</Label>
              <Input
                id="hourly_rate"
                type="number"
                step="0.01"
                min="0"
                value={formData.hourly_rate || ''}
                onChange={(e) => handleInputChange('hourly_rate', e.target.value ? parseFloat(e.target.value) : undefined)}
                placeholder="0.00"
              />
            </div>
          </CardContent>
        </Card>

        {/* Timeline */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Calendar className="h-5 w-5" />
              Zeitplan
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid gap-4 md:grid-cols-3">
              <div className="space-y-2">
                <Label htmlFor="start_date">Startdatum</Label>
                <Input
                  id="start_date"
                  type="date"
                  value={formData.start_date || ''}
                  onChange={(e) => handleInputChange('start_date', e.target.value || undefined)}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="planned_end_date">Geplantes Enddatum</Label>
                <Input
                  id="planned_end_date"
                  type="date"
                  value={formData.planned_end_date || ''}
                  onChange={(e) => handleInputChange('planned_end_date', e.target.value || undefined)}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="actual_end_date">Tatsächliches Enddatum</Label>
                <Input
                  id="actual_end_date"
                  type="date"
                  value={formData.actual_end_date || ''}
                  onChange={(e) => handleInputChange('actual_end_date', e.target.value || undefined)}
                />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Actions */}
        <div className="flex justify-end gap-3 pt-4">
          <Button
            type="button"
            variant="outline"
            onClick={() => navigate(`/projects/${id}`)}
            disabled={isUpdating}
          >
            Abbrechen
          </Button>
          <Button type="submit" disabled={isUpdating || !formData.title || !formData.client_name}>
            <Save className="h-4 w-4 mr-2" />
            {isUpdating ? 'Speichere...' : 'Speichern'}
          </Button>
        </div>
      </form>
    </PageLayout>
  );
};

export default ActiveProjectEdit;