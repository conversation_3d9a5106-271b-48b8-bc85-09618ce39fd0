import { useState } from 'react';
import { PageLayout } from '@/components/layout/PageLayout';
import { useCurrentPageConfig } from '@/hooks/usePageConfig';
import { useTranslation } from '@/hooks/useTranslation';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card } from '@/components/ui/card';
import { CalendarEventDialog } from '@/components/calendar/CalendarEventDialog';
import { CalendarEventDetailsDialog } from '@/components/calendar/CalendarEventDetailsDialog';
import { useCalendarEvents } from '@/hooks/useCalendarEvents';
import { useApplications } from '@/hooks/useApplications';
import { useProjects } from '@/hooks/useProjects';
import { 
  ChevronLeft, 
  ChevronRight, 
  Plus,
  Calendar as CalendarIcon,
  Clock,
  MapPin,
  User,
  CheckCircle2,
  Circle,
  Grid3X3,
  List
} from 'lucide-react';
import { format, startOfMonth, endOfMonth, eachDayOfInterval, isSameMonth, isSameDay, isToday, addMonths, subMonths, startOfWeek, endOfWeek } from 'date-fns';
import { de, enUS } from 'date-fns/locale';
import type { CalendarEventWithProject } from '@/types/calendar';
import type { CalendarView } from '@/types/calendar';

const Calendar = () => {
  const pageConfig = useCurrentPageConfig();
  const { t, currentLanguage } = useTranslation('calendar');
  const [currentDate, setCurrentDate] = useState(new Date());
  const [view, setView] = useState<CalendarView>('month');
  const [mobileView, setMobileView] = useState<'grid' | 'list'>('list'); // Mobile-specific view state
  const [showEventDialog, setShowEventDialog] = useState(false);
  const [showEventDetailsDialog, setShowEventDetailsDialog] = useState(false);
  const [selectedEvent, setSelectedEvent] = useState<CalendarEventWithProject | null>(null);
  const [selectedDate, setSelectedDate] = useState<Date | null>(null);
  const [selectedDayEvents, setSelectedDayEvents] = useState<CalendarEventWithProject[]>([]);
  
  const { events, isLoading: eventsLoading, toggleEventCompletion, isToggling } = useCalendarEvents();
  const { data: applications } = useApplications();
  const { projects } = useProjects();

  // Get the appropriate date-fns locale based on current language
  const dateLocale = currentLanguage === 'en' ? enUS : de;

  const handlePreviousMonth = () => {
    setCurrentDate(subMonths(currentDate, 1));
  };

  const handleNextMonth = () => {
    setCurrentDate(addMonths(currentDate, 1));
  };

  const handleTodayClick = () => {
    setCurrentDate(new Date());
  };

  const handleDateClick = (date: Date) => {
    setSelectedDate(date);
    setShowEventDialog(true);
  };

  const handleEventClick = (event: CalendarEventWithProject) => {
    setSelectedEvent(event);
    setShowEventDetailsDialog(true);
  };

  const handleToggleEventCompletion = async (event: CalendarEventWithProject) => {
    try {
      await toggleEventCompletion({
        eventId: event.id,
        completed: !event.completed
      });
    } catch (error) {
      console.error('Error toggling event completion:', error);
    }
  };

  // Generate calendar days
  const monthStart = startOfMonth(currentDate);
  const monthEnd = endOfMonth(currentDate);
  // Start from the beginning of the week that contains the first day of the month
  const startDate = startOfWeek(monthStart, { weekStartsOn: 1 }); // Monday = 1
  // End at the end of the week that contains the last day of the month
  const endDate = endOfWeek(monthEnd, { weekStartsOn: 1 });
  const days = eachDayOfInterval({ start: startDate, end: endDate });

  // Get events for a specific date
  const getEventsForDate = (date: Date) => {
    return events.filter(event => isSameDay(new Date(event.start_date), date));
  };

  // Translate event types using i18n
  const translateEventType = (eventType: string) => {
    return t(`event_types.${eventType}`) || eventType;
  };

  return (
    <PageLayout
      title={pageConfig.title}
      description={pageConfig.description}
      headerActions={
        <Button
          onClick={() => {
            setSelectedDate(new Date());
            setShowEventDialog(true);
          }}
          size="sm"
          className="flex items-center gap-2"
        >
          <Plus className="h-4 w-4" />
          <span className="hidden sm:inline">{t('new_event')}</span>
          <span className="sm:hidden">{t('new')}</span>
        </Button>
      }
    >
          {/* Calendar Navigation */}
          <div className="w-full min-w-0 mb-6">
            {/* Mobile Navigation - Stack vertically */}
            <div className="sm:hidden space-y-3">
              <div className="flex items-center justify-between min-w-0 overflow-x-hidden">
                <div className="flex items-center gap-1 min-w-0 flex-1 overflow-x-hidden">
                  <div className="flex items-center gap-1 flex-shrink-0">
                    <Button
                      onClick={handlePreviousMonth}
                      variant="outline"
                      size="sm"
                      className="px-1.5 min-w-0 flex-shrink-0"
                    >
                      <ChevronLeft className="h-4 w-4" />
                    </Button>
                    <Button
                      onClick={handleNextMonth}
                      variant="outline"
                      size="sm"
                      className="px-1.5 min-w-0 flex-shrink-0"
                    >
                      <ChevronRight className="h-4 w-4" />
                    </Button>
                  </div>
                  <h2 className="text-base sm:text-lg font-semibold truncate min-w-0 ml-1">
                    {format(currentDate, 'MMM yyyy', { locale: dateLocale })}
                  </h2>
                </div>
                <Button
                  onClick={handleTodayClick}
                  variant="outline"
                  size="sm"
                  className="px-2 flex-shrink-0 ml-2"
                >
                  {t('navigation.today')}
                </Button>
              </div>
              
              {/* Mobile view toggle - left aligned */}
              <div className="flex justify-start overflow-x-hidden">
                <div className="flex items-center gap-0.5 bg-muted p-0.5 rounded-lg min-w-0">
                  <Button
                    onClick={() => setMobileView('list')}
                    variant={mobileView === 'list' ? 'default' : 'ghost'}
                    size="sm"
                    className="px-1.5 min-w-0 flex-shrink-0"
                  >
                    <List className="h-4 w-4" />
                  </Button>
                  <Button
                    onClick={() => setMobileView('grid')}
                    variant={mobileView === 'grid' ? 'default' : 'ghost'}
                    size="sm"
                    className="px-1.5 min-w-0 flex-shrink-0"
                  >
                    <Grid3X3 className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </div>
            
            {/* Desktop Navigation */}
            <div className="hidden sm:flex items-center justify-between">
              <div className="flex items-center gap-4">
                <div className="flex items-center gap-2">
                  <Button
                    onClick={handlePreviousMonth}
                    variant="outline"
                    size="sm"
                  >
                    <ChevronLeft className="h-4 w-4" />
                  </Button>
                  <Button
                    onClick={handleNextMonth}
                    variant="outline"
                    size="sm"
                  >
                    <ChevronRight className="h-4 w-4" />
                  </Button>
                </div>
                <h2 className="text-xl font-semibold">
                  {format(currentDate, 'MMMM yyyy', { locale: dateLocale })}
                </h2>
              </div>
              <Button
                onClick={handleTodayClick}
                variant="outline"
                size="sm"
                className="flex-shrink-0"
              >
                {t('navigation.today')}
              </Button>
            </div>
          </div>

          {/* Mobile List View (default on small screens) */}
          <div className={`sm:hidden overflow-x-hidden w-full ${mobileView === 'list' ? 'block' : 'hidden'}`}>
            <div className="space-y-4 w-full overflow-x-hidden">
              {days
                .filter(day => isSameMonth(day, currentDate))
                .map((day) => {
                  const dayEvents = getEventsForDate(day);
                  const isCurrentDay = isToday(day);
                  
                  if (dayEvents.length === 0) return null;
                  
                  return (
                    <Card key={day.toString()} className={`p-3 sm:p-4 w-full overflow-x-hidden min-w-0 ${isCurrentDay ? 'ring-2 ring-primary' : ''}`}>
                      <div className="flex items-center mb-3 min-w-0 overflow-x-hidden">
                        <div className={`flex items-center gap-2 sm:gap-3 min-w-0 flex-1 overflow-x-hidden`}>
                          <div className={`
                            flex items-center justify-center w-8 h-8 sm:w-10 sm:h-10 text-xs sm:text-sm font-medium rounded-lg flex-shrink-0
                            ${isCurrentDay ? 'bg-primary text-primary-foreground' : 'bg-muted'}
                          `}>
                            {format(day, 'd')}
                          </div>
                          <div className="min-w-0 flex-1 overflow-x-hidden">
                            <div className="font-medium text-sm sm:text-base truncate">{format(day, 'EEEE', { locale: dateLocale })}</div>
                            <div className="text-xs sm:text-sm text-muted-foreground truncate">
                              {format(day, currentLanguage === 'en' ? 'MM/dd/yyyy' : 'dd.MM.yyyy', { locale: dateLocale })}
                            </div>
                          </div>
                        </div>
                      </div>
                      
                      <div className="space-y-2 w-full overflow-x-hidden">
                        {dayEvents.map((event) => (
                          <div
                            key={event.id}
                            onClick={() => handleEventClick(event)}
                            className={`
                              p-2 sm:p-3 rounded-lg cursor-pointer hover:opacity-80 transition-all
                              flex items-center gap-2 sm:gap-3 min-h-[44px] sm:min-h-[48px] w-full overflow-x-hidden min-w-0
                              ${event.completed ? 'opacity-60' : ''}
                            `}
                            style={{ 
                              backgroundColor: event.color + '15',
                              borderLeft: `3px solid ${event.color}`
                            }}
                          >
                            <div
                              className="w-2.5 h-2.5 sm:w-3 sm:h-3 rounded-full flex-shrink-0"
                              style={{ backgroundColor: event.color }}
                            />
                            <div className="flex-1 min-w-0 overflow-x-hidden">
                              <div className={`font-medium text-sm sm:text-base truncate break-words ${event.completed ? 'line-through' : ''}`}>
                                {event.title}
                              </div>
                              <div className="flex flex-col sm:flex-row sm:items-center gap-1 sm:gap-4 mt-1 text-xs sm:text-sm text-muted-foreground min-w-0">
                                {event.start_time && (
                                  <div className="flex items-center gap-1 flex-shrink-0">
                                    <Clock className="h-3 w-3" />
                                    <span className="whitespace-nowrap">
                                      {event.start_time}
                                      {event.end_time && ` - ${event.end_time}`}
                                    </span>
                                  </div>
                                )}
                                {event.location && (
                                  <div className="flex items-center gap-1 min-w-0 overflow-x-hidden">
                                    <MapPin className="h-3 w-3 flex-shrink-0" />
                                    <span className="truncate break-words">{event.location}</span>
                                  </div>
                                )}
                              </div>
                            </div>
                            <Button
                              size="sm"
                              variant="ghost"
                              onClick={(e) => {
                                e.stopPropagation();
                                handleToggleEventCompletion(event);
                              }}
                              disabled={isToggling}
                              className="flex-shrink-0"
                            >
                              {event.completed ? (
                                <CheckCircle2 className="h-4 w-4 text-success" />
                              ) : (
                                <Circle className="h-4 w-4" />
                              )}
                            </Button>
                          </div>
                        ))}
                      </div>
                    </Card>
                  );
                })
              }
            </div>
          </div>
          
          {/* Mobile Grid View (compact) */}
          <div className={`sm:hidden w-full overflow-x-hidden ${mobileView === 'grid' ? 'block' : 'hidden'}`}>
            <Card className="overflow-hidden w-full max-w-full">
              {/* Compact Weekday Headers */}
              <div className="grid grid-cols-7 border-b bg-muted/30 w-full overflow-x-hidden">
                {[
                  t('weekdays.short.monday'),
                  t('weekdays.short.tuesday'),
                  t('weekdays.short.wednesday'),
                  t('weekdays.short.thursday'),
                  t('weekdays.short.friday'),
                  t('weekdays.short.saturday'),
                  t('weekdays.short.sunday')
                ].map((day) => (
                  <div key={day} className="p-1 sm:p-2 text-center text-xs font-medium text-muted-foreground min-w-0 overflow-x-hidden">
                    <span className="truncate">{day}</span>
                  </div>
                ))}
              </div>

              {/* Compact Calendar Days */}
              <div className="grid grid-cols-7 w-full overflow-x-hidden">
                {days.map((day) => {
                  const dayEvents = getEventsForDate(day);
                  const isCurrentMonth = isSameMonth(day, currentDate);
                  const isCurrentDay = isToday(day);

                  return (
                    <div
                      key={day.toString()}
                      className={`
                        min-h-[50px] sm:min-h-[60px] p-0.5 sm:p-1 border-r last:border-r-0 border-b cursor-pointer
                        hover:bg-muted/50 transition-colors relative min-w-0 overflow-x-hidden
                        ${!isCurrentMonth ? 'bg-muted/20 text-muted-foreground' : ''}
                        ${isCurrentDay ? 'bg-primary/5 border-primary/20' : ''}
                      `}
                      onClick={() => handleDateClick(day)}
                    >
                      <div className={`
                        flex items-center justify-center w-5 h-5 sm:w-6 sm:h-6 text-xs font-medium mb-1 mx-auto flex-shrink-0
                        ${isCurrentDay ? 'bg-primary text-primary-foreground rounded-full' : ''}
                      `}>
                        {format(day, 'd')}
                      </div>
                      
                      {/* Event indicators (dots) */}
                      {dayEvents.length > 0 && (
                        <div className="flex flex-wrap gap-0.5 justify-center min-w-0 overflow-hidden px-0.5">
                          {dayEvents.slice(0, 3).map((event) => (
                            <div
                              key={event.id}
                              className={`w-1 h-1 sm:w-1.5 sm:h-1.5 rounded-full flex-shrink-0 ${event.completed ? 'opacity-50' : ''}`}
                              style={{ backgroundColor: event.color }}
                              onClick={(e) => {
                                e.stopPropagation();
                                handleEventClick(event);
                              }}
                            />
                          ))}
                          {dayEvents.length > 3 && (
                            <div className="w-1 h-1 sm:w-1.5 sm:h-1.5 rounded-full bg-muted-foreground flex-shrink-0" />
                          )}
                        </div>
                      )}
                    </div>
                  );
                })}
              </div>
            </Card>
          </div>
          
          {/* Desktop Calendar Grid (hidden on mobile) */}
          <div className="hidden sm:block">
            <Card className="overflow-hidden">
              {/* Weekday Headers */}
              <div className="grid grid-cols-7 border-b">
                {[
                  t('weekdays.short.monday'),
                  t('weekdays.short.tuesday'),
                  t('weekdays.short.wednesday'),
                  t('weekdays.short.thursday'),
                  t('weekdays.short.friday'),
                  t('weekdays.short.saturday'),
                  t('weekdays.short.sunday')
                ].map((day) => (
                  <div key={day} className="p-4 text-center text-sm font-medium text-muted-foreground border-r last:border-r-0">
                    {day}
                  </div>
                ))}
              </div>

              {/* Calendar Days */}
              <div className="grid grid-cols-7">
                {days.map((day) => {
                  const dayEvents = getEventsForDate(day);
                  const isCurrentMonth = isSameMonth(day, currentDate);
                  const isCurrentDay = isToday(day);

                  return (
                    <div
                      key={day.toString()}
                      className={`
                        min-h-[120px] p-2 border-r last:border-r-0 border-b cursor-pointer
                        hover:bg-muted/50 transition-colors
                        ${!isCurrentMonth ? 'bg-muted/20 text-muted-foreground' : ''}
                        ${isCurrentDay ? 'bg-primary/5 border-primary/20' : ''}
                      `}
                      onClick={() => handleDateClick(day)}
                    >
                      <div className={`
                        flex items-center justify-center w-8 h-8 text-sm font-medium mb-2
                        ${isCurrentDay ? 'bg-primary text-primary-foreground rounded-full' : ''}
                      `}>
                        {format(day, 'd')}
                      </div>
                      
                      {/* Events for this day */}
                      <div className="space-y-1">
                        {dayEvents.slice(0, 3).map((event) => (
                          <div
                            key={event.id}
                            onClick={(e) => {
                              e.stopPropagation();
                              handleEventClick(event);
                            }}
                            className={`
                              text-xs p-1 rounded cursor-pointer hover:opacity-80
                              transition-opacity flex items-center gap-1
                              ${event.completed ? 'opacity-60 line-through' : ''}
                            `}
                            style={{ 
                              backgroundColor: event.color + '20',
                              borderLeft: `3px solid ${event.color}`
                            }}
                          >
                            <div
                              className="w-2 h-2 rounded-full flex-shrink-0"
                              style={{ backgroundColor: event.color }}
                            />
                            <span className="truncate flex-1">{event.title}</span>
                            {event.start_time && (
                              <Clock className="h-3 w-3 flex-shrink-0" />
                            )}
                          </div>
                        ))}
                        {dayEvents.length > 3 && (
                          <div className="text-xs text-muted-foreground text-center py-1">
                            +{dayEvents.length - 3} {t('navigation.more_events')}
                          </div>
                        )}
                      </div>
                    </div>
                  );
                })}
              </div>
            </Card>
          </div>

          {/* Selected Day Events Section */}
          {selectedDate && selectedDayEvents.length > 0 && (
            <div className="mt-6 sm:mt-8 w-full overflow-x-hidden">
              <h3 className="text-base sm:text-lg font-semibold mb-3 sm:mb-4 flex items-center gap-2">
                <CalendarIcon className="h-4 w-4 sm:h-5 sm:w-5 flex-shrink-0" />
                <span className="truncate">
                  {t('navigation.events_on')} {format(selectedDate, currentLanguage === 'en' ? 'MM/dd/yyyy' : 'dd.MM.yyyy', { locale: dateLocale })}
                </span>
              </h3>
              <div className="space-y-3 w-full overflow-x-hidden">
                {selectedDayEvents.map((event) => (
                  <Card
                    key={event.id}
                    className="p-3 sm:p-4 hover:shadow-md transition-shadow cursor-pointer w-full overflow-x-hidden min-w-0"
                    onClick={() => handleEventClick(event)}
                  >
                    <div className="flex items-start justify-between gap-2 sm:gap-3 min-w-0">
                      <div className="flex-1 min-w-0 w-full overflow-x-hidden">
                        <div className="flex items-center gap-2 mb-2 min-w-0">
                          <div
                            className="w-2.5 h-2.5 sm:w-3 sm:h-3 rounded-full flex-shrink-0"
                            style={{ backgroundColor: event.color }}
                          />
                          <h4 className={`font-medium text-sm sm:text-base truncate break-words min-w-0 ${event.completed ? 'line-through opacity-60' : ''}`}>
                            {event.title}
                          </h4>
                          <Badge variant="secondary" className="text-xs flex-shrink-0">
                            {translateEventType(event.event_type)}
                          </Badge>
                        </div>
                        
                        <div className="space-y-1 text-xs sm:text-sm text-muted-foreground w-full overflow-x-hidden">
                          {event.start_time && (
                            <div className="flex items-center gap-2 min-w-0">
                              <Clock className="h-3 w-3 sm:h-4 sm:w-4 flex-shrink-0" />
                              <span className="whitespace-nowrap">
                                {event.start_time}
                                {event.end_time && ` - ${event.end_time}`}
                              </span>
                            </div>
                          )}
                          {event.location && (
                            <div className="flex items-center gap-2 min-w-0 overflow-x-hidden">
                              <MapPin className="h-3 w-3 sm:h-4 sm:w-4 flex-shrink-0" />
                              <span className="truncate break-words">{event.location}</span>
                            </div>
                          )}
                          {event.project && (
                            <div className="flex items-center gap-2 min-w-0 overflow-x-hidden">
                              <User className="h-3 w-3 sm:h-4 sm:w-4 flex-shrink-0" />
                              <span className="truncate break-words">{event.project.title} - {event.project.client_name}</span>
                            </div>
                          )}
                        </div>
                      </div>
                      
                      <Button
                        size="sm"
                        variant="ghost"
                        onClick={(e) => {
                          e.stopPropagation();
                          handleToggleEventCompletion(event);
                        }}
                        disabled={isToggling}
                        className="flex-shrink-0 px-1 sm:px-2"
                      >
                        {event.completed ? (
                          <CheckCircle2 className="h-3.5 w-3.5 sm:h-4 sm:w-4 text-success" />
                        ) : (
                          <Circle className="h-3.5 w-3.5 sm:h-4 sm:w-4" />
                        )}
                      </Button>
                    </div>
                  </Card>
                ))}
              </div>
            </div>
          )}

          {/* Today's Events Section */}
          <div className="mt-6 sm:mt-8 w-full overflow-x-hidden">
            <h3 className="text-base sm:text-lg font-semibold mb-3 sm:mb-4 flex items-center gap-2">
              <CalendarIcon className="h-4 w-4 sm:h-5 sm:w-5 flex-shrink-0" />
              <span className="truncate">{t('navigation.today_events')}</span>
            </h3>
            <div className="space-y-3 w-full overflow-x-hidden">
              {getEventsForDate(new Date()).length === 0 ? (
                <Card className="p-4 sm:p-6 text-center w-full overflow-x-hidden">
                  <CalendarIcon className="h-10 w-10 sm:h-12 sm:w-12 mx-auto text-muted-foreground mb-3" />
                  <p className="text-sm sm:text-base text-muted-foreground">{t('navigation.no_events_today')}</p>
                </Card>
              ) : (
                getEventsForDate(new Date()).map((event) => (
                  <Card
                    key={event.id}
                    className="p-3 sm:p-4 hover:shadow-md transition-shadow cursor-pointer w-full overflow-x-hidden min-w-0"
                    onClick={() => handleEventClick(event)}
                  >
                    <div className="flex items-start justify-between gap-2 sm:gap-3 min-w-0 overflow-x-hidden">
                      <div className="flex-1 min-w-0 overflow-x-hidden">
                        <div className="flex items-center gap-2 mb-2 flex-wrap min-w-0">
                          <div
                            className="w-2.5 h-2.5 sm:w-3 sm:h-3 rounded-full flex-shrink-0"
                            style={{ backgroundColor: event.color }}
                          />
                          <h4 className={`font-medium text-sm sm:text-base truncate break-words min-w-0 ${event.completed ? 'line-through opacity-60' : ''}`}>
                            {event.title}
                          </h4>
                          <Badge variant="secondary" className="text-xs flex-shrink-0">
                            {translateEventType(event.event_type)}
                          </Badge>
                        </div>
                        
                        <div className="space-y-1 text-xs sm:text-sm text-muted-foreground w-full overflow-x-hidden">
                          {event.start_time && (
                            <div className="flex items-center gap-2 min-w-0">
                              <Clock className="h-3 w-3 sm:h-4 sm:w-4 flex-shrink-0" />
                              <span className="whitespace-nowrap">
                                {event.start_time}
                                {event.end_time && ` - ${event.end_time}`}
                              </span>
                            </div>
                          )}
                          {event.location && (
                            <div className="flex items-center gap-2 min-w-0 overflow-x-hidden">
                              <MapPin className="h-3 w-3 sm:h-4 sm:w-4 flex-shrink-0" />
                              <span className="truncate break-words">{event.location}</span>
                            </div>
                          )}
                          {event.project && (
                            <div className="flex items-center gap-2 min-w-0 overflow-x-hidden">
                              <User className="h-3 w-3 sm:h-4 sm:w-4 flex-shrink-0" />
                              <span className="truncate break-words">{event.project.title} - {event.project.client_name}</span>
                            </div>
                          )}
                        </div>
                      </div>
                      
                      <Button
                        size="sm"
                        variant="ghost"
                        onClick={(e) => {
                          e.stopPropagation();
                          handleToggleEventCompletion(event);
                        }}
                        disabled={isToggling}
                        className="flex-shrink-0 px-1 sm:px-2"
                      >
                        {event.completed ? (
                          <CheckCircle2 className="h-3.5 w-3.5 sm:h-4 sm:w-4 text-success" />
                        ) : (
                          <Circle className="h-3.5 w-3.5 sm:h-4 sm:w-4" />
                        )}
                      </Button>
                    </div>
                  </Card>
                ))
              )}
            </div>
          </div>
      {/* Event Creation Dialog */}
      <CalendarEventDialog
        isOpen={showEventDialog}
        onClose={() => {
          setShowEventDialog(false);
          setSelectedDate(null);
        }}
        selectedDate={selectedDate}
        applications={applications || []}
        projects={projects || []}
      />

      {/* Event Details Dialog */}
      <CalendarEventDetailsDialog
        isOpen={showEventDetailsDialog}
        onClose={() => {
          setShowEventDetailsDialog(false);
          setSelectedEvent(null);
        }}
        event={selectedEvent}
        applications={applications || []}
        projects={projects || []}
      />
    </PageLayout>
  );
};

export default Calendar;