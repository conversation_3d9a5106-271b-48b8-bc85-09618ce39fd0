import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { PageLayout } from '@/components/layout/PageLayout';
import { useCurrentPageConfig } from '@/hooks/usePageConfig';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuSeparator, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { Separator } from '@/components/ui/separator';
import { 
  Bell, 
  MoreHorizontal, 
  CheckCircle, 
  CheckCheck,
  Trash2, 
  Filter,
  Inbox,
  Clock,
  AlertTriangle,
  Mail,
  User,
  Calendar,
  Circle
} from 'lucide-react';
import { useNotifications, useUnreadNotifications } from '@/hooks/useNotifications';
import { NotificationFilters } from '@/components/notifications/NotificationFilters';
import type { NotificationFilters as NotificationFiltersType, NotificationType } from '@/types/notifications';
import { format, isToday, isYesterday } from 'date-fns';
import { de } from 'date-fns/locale';
import { cn } from '@/lib/utils';
import { useTranslation } from '@/hooks/useTranslation';

// Base notification type configuration (without labels)
const NOTIFICATION_TYPE_BASE_CONFIG = {
  follow_up_due: {
    icon: Mail,
    color: 'text-orange-600 dark:text-orange-400',
    bgColor: 'bg-orange-50 dark:bg-orange-950/20',
    borderColor: 'border-orange-200 dark:border-orange-800',
  },
  follow_up_overdue: {
    icon: AlertTriangle,
    color: 'text-red-600 dark:text-red-400',
    bgColor: 'bg-red-50 dark:bg-red-950/20',
    borderColor: 'border-red-200 dark:border-red-800',
  },
  application_reminder: {
    icon: User,
    color: 'text-blue-600 dark:text-blue-400',
    bgColor: 'bg-blue-50 dark:bg-blue-950/20',
    borderColor: 'border-blue-200 dark:border-blue-800',
  },
  interview_reminder: {
    icon: Calendar,
    color: 'text-green-600 dark:text-green-400',
    bgColor: 'bg-green-50 dark:bg-green-950/20',
    borderColor: 'border-green-200 dark:border-green-800',
  },
  calendar_event: {
    icon: Clock,
    color: 'text-purple-600 dark:text-purple-400',
    bgColor: 'bg-purple-50 dark:bg-purple-950/20',
    borderColor: 'border-purple-200 dark:border-purple-800',
  },
  system_update: {
    icon: Bell,
    color: 'text-gray-600 dark:text-gray-400',
    bgColor: 'bg-gray-50 dark:bg-gray-950/20',
    borderColor: 'border-gray-200 dark:border-gray-800',
  },
  general: {
    icon: Bell,
    color: 'text-blue-600 dark:text-blue-400',
    bgColor: 'bg-blue-50 dark:bg-blue-950/20',
    borderColor: 'border-blue-200 dark:border-blue-800',
  }
} as const;

export default function Notifications() {
  const pageConfig = useCurrentPageConfig();
  const [filters, setFilters] = useState<NotificationFiltersType>({});
  const [showFilters, setShowFilters] = useState(false);
  const navigate = useNavigate();
  const { t } = useTranslation('common');
  
  const unreadCount = useUnreadNotifications();
  const { notifications: allNotifications, loading, markAllAsRead, clearAll, markAsRead, markAsUnread } = useNotifications(filters);

  // Dynamic notification stats with translations
  const NOTIFICATION_STATS = [
    {
      id: 'total',
      label: t('notifications.stats.total'),
      icon: Inbox,
      color: 'blue'
    },
    {
      id: 'unread',
      label: t('notifications.stats.unread'),
      icon: Bell,
      color: 'green'
    },
    {
      id: 'urgent',
      label: t('notifications.stats.urgent'),
      icon: AlertTriangle,
      color: 'purple'
    },
    {
      id: 'recent',
      label: t('notifications.stats.recent'),
      icon: Clock,
      color: 'orange'
    }
  ];


  const groupNotificationsByDate = (notifications: typeof allNotifications) => {
    const grouped = new Map<string, { notifications: typeof allNotifications; isToday: boolean; isYesterday: boolean }>();
    
    notifications.forEach(notification => {
      const date = new Date(notification.created_at);
      let dateKey: string;
      const isDateToday = isToday(date);
      const isDateYesterday = isYesterday(date);
      
      if (isDateToday) {
        dateKey = t('notifications.today');
      } else if (isDateYesterday) {
        dateKey = t('notifications.yesterday');
      } else {
        dateKey = format(date, 'dd. MMMM yyyy', { locale: de });
      }
      
      if (!grouped.has(dateKey)) {
        grouped.set(dateKey, { notifications: [], isToday: isDateToday, isYesterday: isDateYesterday });
      }
      grouped.get(dateKey)!.notifications.push(notification);
    });
    
    return Array.from(grouped.entries()).map(([date, data]) => ({
      date,
      notifications: data.notifications,
      isToday: data.isToday,
      isYesterday: data.isYesterday
    }));
  };

  const getNotificationTypeConfig = (type: NotificationType) => {
    const baseConfig = NOTIFICATION_TYPE_BASE_CONFIG[type] || NOTIFICATION_TYPE_BASE_CONFIG.general;
    return {
      ...baseConfig,
      label: t(`notifications.types.${type}` as any) || t('notifications.types.general')
    };
  };

  const handleNotificationClick = (notification: typeof allNotifications[0]) => {
    // Mark as read if unread
    if (!notification.read_at) {
      markAsRead(notification.id);
    }
    
    // Navigate to action URL if available
    if (notification.action_url) {
      navigate(notification.action_url);
    }
  };

  // Calculate stats
  const todayNotifications = allNotifications.filter(n => isToday(new Date(n.created_at)));
  const urgentNotifications = allNotifications.filter(n => n.priority === 'urgent');
  
  const stats = {
    total: allNotifications.length,
    unread: unreadCount,
    urgent: urgentNotifications.length,
    recent: todayNotifications.length
  };

  return (
    <PageLayout
      title={pageConfig.title}
      description={pageConfig.description}
      headerActions={
        <div className="flex items-center gap-1 sm:gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => setShowFilters(!showFilters)}
            className={cn("hidden sm:flex px-2 sm:px-3", showFilters && "bg-muted")}
          >
            <Filter className="h-4 w-4 mr-1 sm:mr-2" />
            <span className="hidden sm:inline">{t('actions.filter')}</span>
          </Button>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" size="sm" className="hidden sm:flex px-2">
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem onClick={markAllAsRead} disabled={unreadCount === 0}>
                <CheckCircle className="h-4 w-4 mr-2" />
                {t('notifications.mark_all_as_read')}
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem onClick={clearAll} className="text-destructive">
                <Trash2 className="h-4 w-4 mr-2" />
                {t('notifications.clear_all')}
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      }
    >
        <div className="space-y-6 w-full overflow-x-hidden min-w-0">
          {/* Statistics Cards */}
          <div className="grid grid-cols-2 lg:grid-cols-4 gap-2 sm:gap-3 w-full overflow-x-hidden">
            {NOTIFICATION_STATS.map((stat) => {
              const Icon = stat.icon;
              const value = stats[stat.id as keyof typeof stats];
              const color = stat.color;
              
              return (
                <Card key={stat.id} className="min-w-0 w-full overflow-x-hidden">
                  <CardHeader className="pb-2 p-3 sm:p-4">
                    <CardTitle className="text-xs sm:text-sm font-medium flex items-center gap-1 sm:gap-2 min-w-0">
                      <Icon className={`h-3 w-3 sm:h-4 sm:w-4 text-${color}-500 flex-shrink-0`} />
                      <span className="truncate min-w-0">{stat.label}</span>
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="p-3 sm:p-4 pt-0">
                    <div className="text-lg sm:text-xl font-bold truncate">{value}</div>
                    <p className="text-xs text-muted-foreground truncate">
                      {stat.id === 'total' && t('notifications.stats.messages')}
                      {stat.id === 'unread' && t('notifications.stats.not_read')}
                      {stat.id === 'urgent' && t('notifications.stats.important')}
                      {stat.id === 'recent' && t('notifications.stats.from_today')}
                    </p>
                  </CardContent>
                </Card>
              );
            })}
          </div>

          {/* Filters */}
          {showFilters && (
            <NotificationFilters
              filters={filters}
              onChange={setFilters}
              onClose={() => setShowFilters(false)}
            />
          )}

          {/* Main Content */}
          <Card className="min-h-[600px] min-w-0 w-full overflow-x-hidden">
            <CardHeader className="pb-4 w-full overflow-x-hidden">
              <div className="flex items-center justify-between gap-2 min-w-0 overflow-x-hidden">
                <CardTitle className="flex items-center gap-2 min-w-0 text-base sm:text-lg flex-1 overflow-x-hidden">
                  <Bell className="h-4 w-4 sm:h-5 sm:w-5 flex-shrink-0" />
                  <span className="truncate min-w-0">{t('notifications.all_notifications')}</span>
                  {unreadCount > 0 && (
                    <Badge variant="secondary" className="bg-orange-500/20 text-orange-700 dark:text-orange-300 border-orange-200 dark:border-orange-800 flex-shrink-0 text-xs">
                      {unreadCount} {t('notifications.new')}
                    </Badge>
                  )}
                </CardTitle>
                
                {/* Mobile Actions */}
                <div className="flex items-center gap-1 sm:hidden flex-shrink-0">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setShowFilters(!showFilters)}
                    className={cn("px-1.5 min-w-0", showFilters && "bg-muted")}
                  >
                    <Filter className="h-4 w-4" />
                  </Button>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="sm" className="px-1.5 min-w-0">
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem onClick={markAllAsRead} disabled={unreadCount === 0}>
                        <CheckCircle className="h-4 w-4 mr-2" />
                        {t('notifications.mark_all_as_read')}
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={clearAll} className="text-destructive">
                        <Trash2 className="h-4 w-4 mr-2" />
                        {t('notifications.clear_all')}
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
              </div>
            </CardHeader>
            
            <CardContent className="w-full overflow-x-hidden min-w-0">
              {loading ? (
                <div className="space-y-4">
                  {[...Array(5)].map((_, i) => (
                    <div key={i} className="flex items-start gap-4">
                      <div className="h-10 w-10 bg-muted rounded-full" />
                      <div className="flex-1 space-y-2">
                        <div className="h-4 bg-muted rounded w-3/4" />
                        <div className="h-3 bg-muted rounded w-1/2" />
                      </div>
                    </div>
                  ))}
                </div>
              ) : allNotifications.length === 0 ? (
                <div className="flex flex-col items-center justify-center py-12 sm:py-16 text-center w-full overflow-x-hidden">
                  <div className="relative mb-4 sm:mb-6">
                    <div className="absolute inset-0 bg-gradient-to-br from-blue-500/20 to-purple-500/20 rounded-full blur-xl" />
                    <div className="relative rounded-full bg-gradient-to-br from-blue-500/10 to-purple-500/10 p-6 sm:p-8">
                      <Bell className="h-12 w-12 sm:h-16 sm:w-16 text-muted-foreground/40" />
                    </div>
                  </div>
                  <div className="space-y-3 sm:space-y-4 w-full max-w-md px-4">
                    <h3 className="text-xl sm:text-2xl font-semibold bg-gradient-primary bg-clip-text text-transparent">
                      {t('notifications.all_done')}
                    </h3>
                    <p className="text-sm sm:text-base text-muted-foreground leading-relaxed break-words">
                      {t('notifications.all_caught_up')}
                    </p>
                  </div>
                </div>
              ) : (
                <div className="space-y-3 w-full overflow-x-hidden">
                  {groupNotificationsByDate(allNotifications).map(({ date, notifications, isToday, isYesterday }) => (
                    <div key={date} className="w-full overflow-x-hidden">
                      <div className="flex items-center gap-2 sm:gap-3 mb-3 min-w-0 overflow-x-hidden">
                        <div className={cn(
                          "flex items-center gap-1 sm:gap-2 px-2 sm:px-3 py-1 rounded-full text-xs font-medium flex-shrink-0",
                          isToday 
                            ? "bg-green-500/20 text-green-700 dark:text-green-300" 
                            : isYesterday 
                            ? "bg-blue-500/20 text-blue-700 dark:text-blue-300"
                            : "bg-muted/50 text-muted-foreground"
                        )}>
                          {isToday && <Clock className="h-3 w-3 flex-shrink-0" />}
                          {isYesterday && <Calendar className="h-3 w-3 flex-shrink-0" />}
                          <span className="font-medium truncate">{date}</span>
                          <Badge variant="secondary" className="text-xs bg-background/50 flex-shrink-0">
                            {notifications.length}
                          </Badge>
                        </div>
                        <Separator className="flex-1 min-w-0" />
                      </div>
                      <div className="space-y-2 sm:space-y-3 w-full overflow-x-hidden">
                        {notifications.map((notification) => (
                          <div 
                            key={notification.id} 
                            onClick={() => handleNotificationClick(notification)}
                            className={cn(
                              "flex items-start gap-2 sm:gap-3 p-2 sm:p-3 rounded-lg border transition-colors min-w-0 w-full overflow-x-hidden",
                              notification.read_at 
                                ? "bg-muted/20 hover:bg-muted/40 cursor-pointer" 
                                : "bg-primary/5 hover:bg-primary/10 cursor-pointer border-primary/20 relative"
                            )}
                          >
                            {!notification.read_at && (
                              <div className="absolute left-1 top-3 w-2 h-2 bg-primary rounded-full flex-shrink-0" />
                            )}
                            <div className={cn("flex-1 min-w-0 overflow-x-hidden", !notification.read_at && "pl-3")}>
                              <h4 className={cn(
                                "text-sm truncate break-words",
                                notification.read_at ? "font-medium" : "font-semibold"
                              )}>
                                {notification.title}
                              </h4>
                              <p className="text-xs text-muted-foreground break-words line-clamp-2">
                                {notification.message}
                              </p>
                              <p className="text-xs text-muted-foreground mt-1">
                                {format(new Date(notification.created_at), 'HH:mm', { locale: de })}
                              </p>
                            </div>
                            <div className="flex flex-col sm:flex-row items-end sm:items-center gap-1 sm:gap-2 flex-shrink-0">
                              {notification.priority === 'urgent' && (
                                <Badge variant="secondary" className="text-xs bg-red-500/20 text-red-700 dark:text-red-300 flex-shrink-0">
                                  {t('notifications.urgent')}
                                </Badge>
                              )}
                              <DropdownMenu>
                                <DropdownMenuTrigger asChild onClick={(e) => e.stopPropagation()}>
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    className="h-7 w-7 sm:h-8 sm:w-8 p-0 flex-shrink-0"
                                  >
                                    {notification.read_at ? (
                                      <CheckCheck className="h-3 w-3 sm:h-4 sm:w-4 text-green-500" />
                                    ) : (
                                      <CheckCircle className="h-3 w-3 sm:h-4 sm:w-4" />
                                    )}
                                  </Button>
                                </DropdownMenuTrigger>
                                <DropdownMenuContent align="end" onClick={(e) => e.stopPropagation()}>
                                  {!notification.read_at ? (
                                    <DropdownMenuItem onClick={(e) => {
                                      e.stopPropagation();
                                      markAsRead(notification.id);
                                    }}>
                                      <CheckCircle className="h-4 w-4 mr-2" />
                                      {t('notifications.mark_as_read')}
                                    </DropdownMenuItem>
                                  ) : (
                                    <DropdownMenuItem onClick={(e) => {
                                      e.stopPropagation();
                                      markAsUnread(notification.id);
                                    }}>
                                      <Circle className="h-4 w-4 mr-2" />
                                      {t('notifications.mark_as_unread')}
                                    </DropdownMenuItem>
                                  )}
                                </DropdownMenuContent>
                              </DropdownMenu>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </div>
    </PageLayout>
  );
}