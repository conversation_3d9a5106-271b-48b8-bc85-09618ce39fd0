import React, { useState } from 'react';
import { PageLayout } from '@/components/layout/PageLayout';
import { useCurrentPageConfig } from '@/hooks/usePageConfig';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Download, 
  Calendar,
  BarChart3,
  PieChart,
  TrendingUp,
  Clock,
  FileText,
  FileSpreadsheet,
  ChevronDown,
  CalendarRange
} from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { useProjects } from '@/hooks/useProjects';
import { useTimeTracking } from '@/hooks/useTimeTracking';
import { format, startOfWeek, endOfWeek, startOfMonth, endOfMonth, parseISO } from 'date-fns';
import { de } from 'date-fns/locale';
import { TimeTrackingExportService } from '@/services/timeTrackingExportService';
import { toast } from '@/lib/toast';
import { useTranslation } from '@/hooks/useTranslation';

const ActiveProjectsReports = () => {
  const pageConfig = useCurrentPageConfig();
  const { t: tPages } = useTranslation('pages');
  const { data: projects = [] } = useProjects();
  const { entries, todayHours, thisWeekHours } = useTimeTracking();
  const [selectedPeriod, setSelectedPeriod] = useState<string>('this-week');
  const [selectedProject, setSelectedProject] = useState<string>('all');
  const [customStartDate, setCustomStartDate] = useState<string>('');
  const [customEndDate, setCustomEndDate] = useState<string>('');

  // Filter entries based on selected period
  const getFilteredEntries = () => {
    const now = new Date();
    let startDate: Date;
    let endDate: Date = now;

    switch (selectedPeriod) {
      case 'today':
        startDate = new Date(now.getFullYear(), now.getMonth(), now.getDate());
        break;
      case 'this-week':
        startDate = startOfWeek(now, { locale: de });
        endDate = endOfWeek(now, { locale: de });
        break;
      case 'this-month':
        startDate = startOfMonth(now);
        endDate = endOfMonth(now);
        break;
      case 'custom':
        if (customStartDate && customEndDate) {
          startDate = parseISO(customStartDate);
          endDate = new Date(parseISO(customEndDate));
          endDate.setHours(23, 59, 59, 999); // Include the entire end date
        } else {
          // Fallback to this week if custom dates are not set
          startDate = startOfWeek(now, { locale: de });
          endDate = endOfWeek(now, { locale: de });
        }
        break;
      default:
        startDate = startOfWeek(now, { locale: de });
    }

    return entries.filter(entry => {
      const entryDate = new Date(entry.start_time);
      const inPeriod = entryDate >= startDate && entryDate <= endDate;
      const matchesProject = selectedProject === 'all' || entry.project_id === selectedProject;
      return inPeriod && matchesProject;
    });
  };

  const filteredEntries = getFilteredEntries();
  
  // Calculate statistics
  const totalHours = filteredEntries.reduce((sum, entry) => sum + (entry.calculated_duration || 0), 0) / 60;
  const billableHours = filteredEntries
    .filter(entry => entry.billable)
    .reduce((sum, entry) => sum + (entry.calculated_duration || 0), 0) / 60;

  // Group by category
  const categoryStats = filteredEntries.reduce((acc, entry) => {
    const category = entry.category || 'other';
    const duration = entry.calculated_duration || 0;
    acc[category] = (acc[category] || 0) + duration;
    return acc;
  }, {} as Record<string, number>);

  // Group by project
  const projectStats = filteredEntries.reduce((acc, entry) => {
    const projectId = entry.project_id;
    const project = projects.find(p => p.id === projectId);
    const projectName = project?.title || 'Unknown Project';
    const duration = entry.calculated_duration || 0;
    acc[projectName] = (acc[projectName] || 0) + duration;
    return acc;
  }, {} as Record<string, number>);

  // Group by day for trend
  const dailyStats = filteredEntries.reduce((acc, entry) => {
    const day = format(new Date(entry.start_time), 'yyyy-MM-dd');
    const duration = entry.calculated_duration || 0;
    acc[day] = (acc[day] || 0) + duration;
    return acc;
  }, {} as Record<string, number>);

  const formatHours = (minutes: number) => {
    return `${(minutes / 60).toFixed(1)}h`;
  };

  // Export handlers
  const handleExportExcel = () => {
    try {
      const exportData = {
        entries: filteredEntries,
        projects,
        period: getPeriodLabel(),
        totalHours,
        billableHours
      };
      
      TimeTrackingExportService.exportToExcel(exportData);
      toast.success(tPages('reports.messages.excel_success'), tPages('reports.messages.excel_success_desc'));
    } catch (error) {
      console.error('Excel export error:', error);
      toast.error(tPages('reports.messages.excel_error'), tPages('reports.messages.excel_error_desc'));
    }
  };

  const handleExportPDF = () => {
    try {
      const exportData = {
        entries: filteredEntries,
        projects,
        period: getPeriodLabel(),
        totalHours,
        billableHours
      };
      
      TimeTrackingExportService.exportToPDF(exportData);
      toast.success(tPages('reports.messages.pdf_success'), tPages('reports.messages.pdf_success_desc'));
    } catch (error) {
      console.error('PDF export error:', error);
      toast.error(tPages('reports.messages.pdf_error'), tPages('reports.messages.pdf_error_desc'));
    }
  };

  const getPeriodLabel = () => {
    switch (selectedPeriod) {
      case 'today':
        return tPages('reports.filter.today');
      case 'this-week':
        return tPages('reports.filter.this_week');
      case 'this-month':
        return tPages('reports.filter.this_month');
      case 'custom':
        if (customStartDate && customEndDate) {
          const start = format(parseISO(customStartDate), 'dd.MM.yyyy', { locale: de });
          const end = format(parseISO(customEndDate), 'dd.MM.yyyy', { locale: de });
          return `${start} - ${end}`;
        }
        return tPages('reports.filter.custom');
      default:
        return tPages('reports.filter.this_week');
    }
  };

  return (
    <PageLayout
      title={pageConfig.title}
      description={pageConfig.description}
      headerActions={
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="outline" size="sm" className="shrink-0">
              <Download className="h-4 w-4" />
              <span className="hidden sm:inline sm:ml-2">{tPages('reports.export_menu.export')}</span>
              <ChevronDown className="h-4 w-4 ml-1 sm:ml-2" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuItem onClick={handleExportExcel}>
              <FileSpreadsheet className="h-4 w-4 mr-2" />
              {tPages('reports.export_menu.as_excel')}
            </DropdownMenuItem>
            <DropdownMenuItem onClick={handleExportPDF}>
              <FileText className="h-4 w-4 mr-2" />
              {tPages('reports.export_menu.as_pdf')}
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      }
    >
        <div className="space-y-6">
      {/* Filter Controls */}
      <Card className="mb-6">
        <CardHeader className="pb-4">
          <CardTitle className="text-lg flex items-center gap-2">
            <BarChart3 className="h-5 w-5" />
            {tPages('reports.filter.title')}
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Period and Project Selection */}
          <div className="grid gap-4 sm:grid-cols-2">
            <div className="space-y-2">
              <Label className="text-sm font-medium flex items-center gap-2">
                <Calendar className="h-4 w-4" />
                {tPages('reports.filter.period')}
              </Label>
              <Select value={selectedPeriod} onValueChange={setSelectedPeriod}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="today">{tPages('reports.filter.today')}</SelectItem>
                  <SelectItem value="this-week">{tPages('reports.filter.this_week')}</SelectItem>
                  <SelectItem value="this-month">{tPages('reports.filter.this_month')}</SelectItem>
                  <SelectItem value="custom">{tPages('reports.filter.custom')}</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label className="text-sm font-medium">{tPages('reports.filter.project')}</Label>
              <Select value={selectedProject} onValueChange={setSelectedProject}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">{tPages('reports.filter.all_projects')}</SelectItem>
                  {projects.map((project) => (
                    <SelectItem key={project.id} value={project.id}>
                      {project.title}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Custom Date Range */}
          {selectedPeriod === 'custom' && (
            <div className="space-y-2">
              <Label className="text-sm font-medium flex items-center gap-2">
                <CalendarRange className="h-4 w-4" />
                {tPages('reports.filter.date_range')}
              </Label>
              <div className="grid gap-3 sm:grid-cols-2">
                <div className="space-y-1">
                  <Label htmlFor="start-date" className="text-xs text-muted-foreground">{tPages('reports.filter.from')}</Label>
                  <Input
                    id="start-date"
                    type="date"
                    value={customStartDate}
                    onChange={(e) => setCustomStartDate(e.target.value)}
                  />
                </div>
                <div className="space-y-1">
                  <Label htmlFor="end-date" className="text-xs text-muted-foreground">{tPages('reports.filter.to')}</Label>
                  <Input
                    id="end-date"
                    type="date"
                    value={customEndDate}
                    onChange={(e) => setCustomEndDate(e.target.value)}
                  />
                </div>
              </div>
            </div>
          )}

        </CardContent>
      </Card>

      {/* Quick Stats */}
      <div className="grid grid-cols-2 gap-4 lg:grid-cols-4 mb-6">
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center space-x-2">
              <Clock className="h-4 w-4 text-muted-foreground" />
              <span className="text-sm font-medium">{tPages('reports.stats.total_hours')}</span>
            </div>
            <div className="text-2xl font-bold">{formatHours(totalHours * 60)}</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center space-x-2">
              <TrendingUp className="h-4 w-4 text-muted-foreground" />
              <span className="text-sm font-medium">{tPages('reports.stats.billable')}</span>
            </div>
            <div className="text-2xl font-bold text-green-600">{formatHours(billableHours * 60)}</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center space-x-2">
              <BarChart3 className="h-4 w-4 text-muted-foreground" />
              <span className="text-sm font-medium">{tPages('reports.stats.sessions')}</span>
            </div>
            <div className="text-2xl font-bold">{filteredEntries.length}</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center space-x-2">
              <PieChart className="h-4 w-4 text-muted-foreground" />
              <span className="text-sm font-medium">{tPages('reports.stats.projects')}</span>
            </div>
            <div className="text-2xl font-bold">{Object.keys(projectStats).length}</div>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="overview" className="space-y-6">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview" className="text-xs sm:text-sm">
            <span className="hidden sm:inline">{tPages('reports.tabs.overview')}</span>
            <span className="sm:hidden">{tPages('reports.tabs.overview_short')}</span>
          </TabsTrigger>
          <TabsTrigger value="details" className="text-xs sm:text-sm">
            <span className="hidden sm:inline">{tPages('reports.tabs.details')}</span>
            <span className="sm:hidden">{tPages('reports.tabs.details_short')}</span>
          </TabsTrigger>
          <TabsTrigger value="projects" className="text-xs sm:text-sm">
            <span className="hidden sm:inline">{tPages('reports.tabs.by_project')}</span>
            <span className="sm:hidden">{tPages('reports.tabs.by_project_short')}</span>
          </TabsTrigger>
          <TabsTrigger value="categories" className="text-xs sm:text-sm">
            <span className="hidden sm:inline">{tPages('reports.tabs.by_category')}</span>
            <span className="sm:hidden">{tPages('reports.tabs.by_category_short')}</span>
          </TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          <div className="grid gap-6 lg:grid-cols-2">
            {/* Daily Breakdown */}
            <Card>
              <CardHeader>
                <CardTitle>{tPages('reports.overview.daily_breakdown')}</CardTitle>
              </CardHeader>
              <CardContent>
                {Object.keys(dailyStats).length === 0 ? (
                  <p className="text-muted-foreground text-center py-8">
                    {tPages('reports.overview.no_data_period')}
                  </p>
                ) : (
                  <div className="space-y-3">
                    {Object.entries(dailyStats)
                      .sort(([a], [b]) => b.localeCompare(a))
                      .map(([day, minutes]) => (
                        <div key={day} className="flex items-center justify-between">
                          <span className="text-sm">
                            {format(new Date(day), 'EEE, dd.MM.yyyy', { locale: de })}
                          </span>
                          <Badge variant="outline">
                            {formatHours(minutes)}
                          </Badge>
                        </div>
                      ))}
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Recent Sessions */}
            <Card>
              <CardHeader>
                <CardTitle>{tPages('reports.overview.recent_sessions')}</CardTitle>
              </CardHeader>
              <CardContent>
                {filteredEntries.length === 0 ? (
                  <p className="text-muted-foreground text-center py-8">
                    {tPages('reports.overview.no_sessions_period')}
                  </p>
                ) : (
                  <div className="space-y-3">
                    {filteredEntries.slice(0, 8).map((entry) => {
                      const project = projects.find(p => p.id === entry.project_id);
                      return (
                        <div key={entry.id} className="flex items-center justify-between text-sm">
                          <div>
                            <p className="font-medium">{project?.title || 'Unknown'}</p>
                            <p className="text-muted-foreground capitalize">
                              {entry.category} • {format(new Date(entry.start_time), 'HH:mm')}
                            </p>
                          </div>
                          <Badge variant="outline" className="text-xs">
                            {entry.formatted_duration}
                          </Badge>
                        </div>
                      );
                    })}
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="details" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>{tPages('reports.details.title')}</CardTitle>
              <CardDescription>
                {tPages('reports.details.description')}
              </CardDescription>
            </CardHeader>
            <CardContent>
              {filteredEntries.length === 0 ? (
                <p className="text-muted-foreground text-center py-8">
                  {tPages('reports.details.no_entries')}
                </p>
              ) : (
                <div className="space-y-6">
                  {/* Group entries by day */}
                  {Object.entries(
                    filteredEntries.reduce((acc, entry) => {
                      const day = format(new Date(entry.start_time), 'yyyy-MM-dd');
                      if (!acc[day]) acc[day] = [];
                      acc[day].push(entry);
                      return acc;
                    }, {} as Record<string, typeof filteredEntries>)
                  )
                    .sort(([a], [b]) => b.localeCompare(a))
                    .map(([day, dayEntries]) => {
                      const dayTotal = dayEntries.reduce((sum, entry) => sum + (entry.calculated_duration || 0), 0);
                      const dayBillable = dayEntries
                        .filter(entry => entry.billable)
                        .reduce((sum, entry) => sum + (entry.calculated_duration || 0), 0);
                      
                      return (
                        <div key={day} className="border rounded-lg p-4">
                          <div className="flex items-center justify-between mb-4">
                            <h3 className="text-lg font-semibold">
                              {format(new Date(day), 'EEEE, dd.MM.yyyy', { locale: de })}
                            </h3>
                            <div className="flex items-center gap-4">
                              <Badge variant="outline">
                                {tPages('reports.details.total_label')}: {formatHours(dayTotal)}
                              </Badge>
                              <Badge variant="default">
                                {tPages('reports.details.billable_label')}: {formatHours(dayBillable)}
                              </Badge>
                            </div>
                          </div>
                          
                          <div className="space-y-2">
                            {dayEntries
                              .sort((a, b) => new Date(a.start_time).getTime() - new Date(b.start_time).getTime())
                              .map((entry) => {
                                const project = projects.find(p => p.id === entry.project_id);
                                return (
                                  <div key={entry.id} className="grid grid-cols-12 gap-4 items-center py-2 px-3 hover:bg-accent rounded">
                                    <div className="col-span-2 text-sm">
                                      {format(new Date(entry.start_time), 'HH:mm')}
                                      {entry.end_time && (
                                        <span className="text-muted-foreground">
                                          {' - ' + format(new Date(entry.end_time), 'HH:mm')}
                                        </span>
                                      )}
                                    </div>
                                    <div className="col-span-3">
                                      <p className="font-medium text-sm">{project?.title || 'Unknown Project'}</p>
                                      <p className="text-xs text-muted-foreground">{project?.client_name}</p>
                                    </div>
                                    <div className="col-span-2">
                                      <Badge variant="outline" className="text-xs capitalize">
                                        {entry.category}
                                      </Badge>
                                    </div>
                                    <div className="col-span-3">
                                      {entry.description && (
                                        <p className="text-sm text-muted-foreground truncate">
                                          {entry.description}
                                        </p>
                                      )}
                                    </div>
                                    <div className="col-span-1 text-center">
                                      {entry.billable && (
                                        <Badge variant="secondary" className="text-xs">€</Badge>
                                      )}
                                    </div>
                                    <div className="col-span-1 text-right">
                                      <Badge variant="outline" className="text-xs">
                                        {entry.formatted_duration}
                                      </Badge>
                                    </div>
                                  </div>
                                );
                              })}
                          </div>
                        </div>
                      );
                    })}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="projects" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>{tPages('reports.by_project.title')}</CardTitle>
            </CardHeader>
            <CardContent>
              {Object.keys(projectStats).length === 0 ? (
                <p className="text-muted-foreground text-center py-8">
                  {tPages('reports.by_project.no_data')}
                </p>
              ) : (
                <div className="space-y-4">
                  {Object.entries(projectStats)
                    .sort(([, a], [, b]) => b - a)
                    .map(([projectName, minutes]) => (
                      <div key={projectName} className="flex items-center justify-between">
                        <div className="flex-1">
                          <p className="font-medium">{projectName}</p>
                          <div className="w-full bg-secondary rounded-full h-2">
                            <div 
                              className="bg-primary h-2 rounded-full"
                              style={{ 
                                width: `${(minutes / Math.max(...Object.values(projectStats))) * 100}%` 
                              }}
                            />
                          </div>
                        </div>
                        <Badge variant="outline" className="ml-4">
                          {formatHours(minutes)}
                        </Badge>
                      </div>
                    ))}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="categories" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>{tPages('reports.by_category.title')}</CardTitle>
            </CardHeader>
            <CardContent>
              {Object.keys(categoryStats).length === 0 ? (
                <p className="text-muted-foreground text-center py-8">
                  {tPages('reports.by_category.no_data')}
                </p>
              ) : (
                <div className="space-y-4">
                  {Object.entries(categoryStats)
                    .sort(([, a], [, b]) => b - a)
                    .map(([category, minutes]) => (
                      <div key={category} className="flex items-center justify-between">
                        <div className="flex-1">
                          <p className="font-medium capitalize">{category}</p>
                          <div className="w-full bg-secondary rounded-full h-2">
                            <div 
                              className="bg-primary h-2 rounded-full"
                              style={{ 
                                width: `${(minutes / Math.max(...Object.values(categoryStats))) * 100}%` 
                              }}
                            />
                          </div>
                        </div>
                        <Badge variant="outline" className="ml-4">
                          {formatHours(minutes)}
                        </Badge>
                      </div>
                    ))}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
        </div>
    </PageLayout>
  );
};

export default ActiveProjectsReports;