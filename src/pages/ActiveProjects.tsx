import React, { useState } from 'react';
import { PageLayout } from '@/components/layout/PageLayout';
import { useCurrentPageConfig } from '@/hooks/usePageConfig';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Progress } from '@/components/ui/progress';
import { Skeleton } from '@/components/ui/skeleton';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { Plus, Clock, FolderO<PERSON>, ArrowRight, Bar<PERSON>hart3, <PERSON>r, MoreH<PERSON>zontal, Trash2, <PERSON>, Eye } from 'lucide-react';
import { useApplications } from '@/hooks/useApplications';
import { useProjects } from '@/hooks/useProjects';
import { useTimeTracking, useTimerState } from '@/hooks/useTimeTracking';
import { useNavigate } from 'react-router-dom';
import type { ProjectWithStats } from '@/types/projects';
import { 
  PROJECT_STATUS_LABELS, 
  PROJECT_TYPE_LABELS 
} from '@/types/projects';
import { useTranslation } from '@/hooks/useTranslation';
import { useProjectStatusLabel, useProjectTypeLabel } from '@/lib/translations';

const ActiveProjects = () => {
  const navigate = useNavigate();
  const pageConfig = useCurrentPageConfig();
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [projectToDelete, setProjectToDelete] = useState<string | null>(null);
  const { t: tPages } = useTranslation('pages');
  const getStatusLabel = useProjectStatusLabel();
  const getTypeLabel = useProjectTypeLabel();
  
  // Hooks for data fetching
  const { data: applications = [] } = useApplications();
  const { 
    data: projects = [], 
    isLoading: isLoadingProjects,
    importProject,
    deleteProject,
    isImporting,
    isDeleting 
  } = useProjects();
  const { 
    todayHours, 
    thisWeekHours
  } = useTimeTracking();
  const timerState = useTimerState();

  // Filter projects that can be imported (exclude already imported ones)
  const importedProjectIds = projects
    .filter(ap => ap.source_application_id)
    .map(ap => ap.source_application_id);
  
  const projectsToImport = applications.filter(
    project => project.status === 'offer_received' && 
    !importedProjectIds.includes(project.id)
  );

  const handleImportProject = (projectId: string) => {
    importProject(projectId);
  };

  // Timer controls removed - only status display

  const handleDeleteProject = (projectId: string) => {
    setProjectToDelete(projectId);
    setDeleteDialogOpen(true);
  };

  const confirmDeleteProject = () => {
    if (projectToDelete) {
      deleteProject(projectToDelete);
      setDeleteDialogOpen(false);
      setProjectToDelete(null);
    }
  };

  const handleViewProject = (project: ProjectWithStats) => {
    // Navigate to project details using the current route structure
    navigate(`/projects/${project.id}`);
  };

  const handleEditProject = (project: ProjectWithStats) => {
    // Navigate to project edit using the current route structure
    navigate(`/projects/edit/${project.id}`);
  };

  const ImportSection = () => {
    if (projectsToImport.length === 0) {
      return null;
    }

    return (
      <Card className="mb-6 border-dashed border-2 border-primary/20 bg-primary/5">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FolderOpen className="h-5 w-5" />
            {tPages('projects.import_section.title')}
          </CardTitle>
          <CardDescription>
            {tPages('projects.import_section.description')}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {projectsToImport.map((project) => (
              <div
                key={project.id}
                className="flex items-center justify-between p-3 bg-background rounded-lg border"
              >
                <div className="flex-1">
                  <h4 className="font-medium">{project.project_name}</h4>
                  <p className="text-sm text-muted-foreground">
                    {project.client_name || tPages('projects.import_section.no_client_name')}
                  </p>
                  {project.hourly_rate_eur && (
                    <p className="text-sm text-green-600">
                      €{project.hourly_rate_eur}/h
                    </p>
                  )}
                </div>
                <Button
                  onClick={() => handleImportProject(project.id)}
                  disabled={isImporting}
                  className="ml-4"
                >
                  <ArrowRight className="h-4 w-4 mr-2" />
                  {isImporting ? tPages('projects.import_section.importing') : tPages('projects.import_section.import')}
                </Button>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  };

  const EmptyActiveProjects = () => (
    <Card className="text-center py-12">
      <CardContent>
        <div className="mx-auto max-w-sm">
          <Clock className="mx-auto h-16 w-16 text-muted-foreground mb-4" />
          <h3 className="text-lg font-semibold mb-2">{tPages('projects.empty_state.title')}</h3>
          <p className="text-muted-foreground mb-6">
            {tPages('projects.empty_state.description')}
          </p>
          <Button onClick={() => navigate('/projects/new')}>
            <Plus className="h-4 w-4 mr-2" />
            {tPages('projects.empty_state.new_project')}
          </Button>
        </div>
      </CardContent>
    </Card>
  );

  const ActiveProjectCard = ({ project }: { project: ProjectWithStats }) => {
    const formatDate = (dateString: string | null) => {
      if (!dateString) return null;
      return new Date(dateString).toLocaleDateString('de-DE', {
        day: '2-digit',
        month: '2-digit',
        year: 'numeric'
      });
    };

    return (
      <Card className="hover:shadow-md transition-shadow cursor-pointer group">
        <CardHeader>
          <div className="flex items-start justify-between">
            <div className="flex-1" onClick={() => handleViewProject(project)}>
              <CardTitle className="text-lg group-hover:text-primary transition-colors">
                {project.title}
              </CardTitle>
              <CardDescription>{project.client_name}</CardDescription>
            </div>
            <div className="flex items-center gap-2">
              <Badge variant="secondary">
                {getStatusLabel(project.status)}
              </Badge>
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                    <MoreHorizontal className="h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuItem onClick={() => handleViewProject(project)}>
                    <Eye className="h-4 w-4 mr-2" />
                    {tPages('projects.card.view_details')}
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => handleEditProject(project)}>
                    <Edit className="h-4 w-4 mr-2" />
                    {tPages('projects.card.edit')}
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => handleDeleteProject(project.id)}>
                    <Trash2 className="h-4 w-4 mr-2" />
                    {tPages('projects.card.delete')}
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {/* Project Info Row 1 */}
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <span className="text-muted-foreground">{tPages('projects.card.type')}</span>
                <div className="font-medium">{getTypeLabel(project.project_type)}</div>
              </div>
              {project.hourly_rate && (
                <div>
                  <span className="text-muted-foreground">{tPages('projects.card.hourly_rate')}</span>
                  <div className="font-medium text-green-600">€{project.hourly_rate}/h</div>
                </div>
              )}
            </div>

            {/* Dates Row */}
            {(project.start_date || project.planned_end_date) && (
              <div className="grid grid-cols-2 gap-4 text-sm">
                {project.start_date && (
                  <div>
                    <span className="text-muted-foreground">{tPages('projects.card.start')}</span>
                    <div className="font-medium">{formatDate(project.start_date)}</div>
                  </div>
                )}
                {project.planned_end_date && (
                  <div>
                    <span className="text-muted-foreground">{tPages('projects.card.planned_end')}</span>
                    <div className="font-medium">{formatDate(project.planned_end_date)}</div>
                  </div>
                )}
              </div>
            )}

            {/* Total Hours */}
            <div className="flex items-center justify-between text-sm">
              <span className="text-muted-foreground">{tPages('projects.card.total_hours')}</span>
              <span className="font-medium text-blue-600">
                {project.total_hours || 0}h
              </span>
            </div>

            {/* Timer Status */}
            <Separator />
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium">{tPages('projects.card.time_tracking')}</span>
              <Badge 
                variant={project.is_timer_running && timerState.currentProject?.id === project.id ? "default" : "outline"} 
                className="text-xs"
              >
                {project.is_timer_running && timerState.currentProject?.id === project.id ? (
                  <>
                    <Timer className="h-3 w-3 mr-1" />
                    {tPages('projects.card.timer_running')}
                  </>
                ) : (
                  tPages('projects.card.hours_total', { hours: project.total_hours || 0 })
                )}
              </Badge>
            </div>
            {project.is_timer_running && timerState.currentProject?.id === project.id && (
              <div className="text-xs text-muted-foreground text-center">
                {tPages('projects.card.current_session', { time: timerState.formattedElapsedTime })}
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    );
  };

  // Loading skeleton component
  const ProjectSkeleton = () => (
    <Card>
      <CardHeader>
        <Skeleton className="h-6 w-3/4" />
        <Skeleton className="h-4 w-1/2" />
      </CardHeader>
      <CardContent>
        <div className="space-y-3">
          <Skeleton className="h-4 w-full" />
          <Skeleton className="h-4 w-2/3" />
          <Skeleton className="h-8 w-full" />
        </div>
      </CardContent>
    </Card>
  );

  return (
    <PageLayout
      title={pageConfig.title}
      description={pageConfig.description}
      headerActions={
        <Button onClick={() => navigate('/projects/new')}>
          <Plus className="h-4 w-4 mr-2" />
          {tPages('projects.create_new')}
        </Button>
      }
    >
      <ImportSection />

      {/* Statistics Cards */}
      <div className="grid grid-cols-2 gap-4 lg:grid-cols-4 mb-6">
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center space-x-2">
              <BarChart3 className="h-4 w-4 text-muted-foreground" />
              <span className="text-sm font-medium">{tPages('projects.stats.today')}</span>
            </div>
            <div className="text-2xl font-bold">{timerState.realTimeTodayHours || todayHours}h</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center space-x-2">
              <BarChart3 className="h-4 w-4 text-muted-foreground" />
              <span className="text-sm font-medium">{tPages('projects.stats.this_week')}</span>
            </div>
            <div className="text-2xl font-bold">{timerState.realTimeWeekHours || thisWeekHours}h</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center space-x-2">
              <FolderOpen className="h-4 w-4 text-muted-foreground" />
              <span className="text-sm font-medium">{tPages('projects.stats.active_projects')}</span>
            </div>
            <div className="text-2xl font-bold">{projects.length}</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center space-x-2">
              <Timer className="h-4 w-4 text-muted-foreground" />
              <span className="text-sm font-medium">{tPages('projects.stats.timer_status')}</span>
            </div>
            <div className="text-2xl font-bold">
              {timerState.isRunning ? (
                <Badge variant="default">
                  {timerState.formattedElapsedTime}
                </Badge>
              ) : (
                <Badge variant="outline">{tPages('projects.stats.stopped')}</Badge>
              )}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Projects Grid */}
      {isLoadingProjects ? (
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
          {[...Array(3)].map((_, i) => (
            <ProjectSkeleton key={i} />
          ))}
        </div>
      ) : projects.length === 0 ? (
        <EmptyActiveProjects />
      ) : (
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
          {projects.map((project) => (
            <ActiveProjectCard key={project.id} project={project} />
          ))}
        </div>
      )}


      <AlertDialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>{tPages('projects.delete_dialog.title')}</AlertDialogTitle>
            <AlertDialogDescription>
              {tPages('projects.delete_dialog.description')}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>{tPages('projects.delete_dialog.cancel')}</AlertDialogCancel>
            <AlertDialogAction
              onClick={confirmDeleteProject}
              disabled={isDeleting}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              {isDeleting ? tPages('projects.delete_dialog.deleting') : tPages('projects.delete_dialog.delete')}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
    </AlertDialog>
    </PageLayout>
  );
};

export default ActiveProjects;