import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useCurrentPageConfig } from '@/hooks/usePageConfig';
import { useApplications } from '@/hooks/useApplications';
import { useProjects } from '@/hooks/useProjects';
import { useTimeTracking } from '@/hooks/useTimeTracking';
import { useProjectActivities } from '@/hooks/useProjectActivities';
import { useFollowUpSchedule } from '@/hooks/useFollowUpSchedule';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { PageLayout } from '@/components/layout/PageLayout';
import { ActivitiesModal } from '@/components/dashboard/ActivitiesModal';
import { format } from 'date-fns';
import { de, enUS } from 'date-fns/locale';
import ReactMarkdown from 'react-markdown';
import { useTranslation } from 'react-i18next';

import { 
  Plus, 
  FolderOpen, 
  Building2,
  Clock,
  Euro,
  TrendingUp,
  ArrowRight,
  Calendar,
  Timer,
  Activity,
  Bell
} from 'lucide-react';
import { FollowUpNotifications } from '@/components/followup/FollowUpNotifications';

import { APPLICATION_STATUS_BADGE_CLASSES } from '@/types/applications';
import { useApplicationStatusLabel, useActivityLabel } from '@/lib/translations';
import type { Project } from '@/types/projects';
import { PROJECT_STATUS_LABELS, PROJECT_STATUS_BADGE_CLASSES } from '@/types/projects';
import type { ProjectActivity } from '@/types/applications';
import { ALL_ACTIVITY_LABELS, ALL_ACTIVITY_COLORS } from '@/types/shared';

export const CombinedDashboard = () => {
  const navigate = useNavigate();
  const pageConfig = useCurrentPageConfig();
  const { t, i18n } = useTranslation('common');
  const tPages = useTranslation('pages').t;
  const getStatusLabel = useApplicationStatusLabel();
  const getActivityLabel = useActivityLabel();
  
  // Get the appropriate date-fns locale based on current language
  const dateLocale = i18n.language === 'en' ? enUS : de;
  const [showActivitiesModal, setShowActivitiesModal] = useState(false);
  const { data: applications = [], isLoading: applicationsLoading, error: applicationsError } = useApplications();
  const { projects = [], isLoading: projectsLoading, error: projectsError } = useProjects();
  const { todayHours, weeklyHours } = useTimeTracking();
  const { data: allActivities = [], isLoading: activitiesLoading } = useProjectActivities();
  const { scheduledFollowUps } = useFollowUpSchedule();
  

  const handleCreateApplication = () => {
    navigate('/applications/new');
  };

  const handleCreateProject = () => {
    navigate('/projects/new');
  };

  const handleViewAllApplications = () => {
    navigate('/applications');
  };

  const handleViewAllProjects = () => {
    navigate('/projects');
  };

  // Calculate KPIs
  const openApplications = applications.filter(app => 
    ['application_sent', 'inquiry_received', 'interview_scheduled', 'interview_completed'].includes(app.status)
  ).length;
  const activeProjects = projects.filter(project => 
    project.status === 'in_progress'
  ).length;
  const activeFollowUps = scheduledFollowUps.filter(followUp => 
    followUp.status === 'scheduled'
  ).length;
  
  // Get recent items (last 5)
  const recentApplications = applications.slice(0, 5);
  const recentProjects = projects.slice(0, 5);
  const recentActivities = allActivities.slice(0, 5);

  if (applicationsLoading || projectsLoading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto"></div>
          <p className="mt-4 text-muted-foreground">{t('loading_states.dashboard')}</p>
        </div>
      </div>
    );
  }

  // Show error state if there are errors
  if (applicationsError || projectsError) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="text-center">
          <p className="text-destructive mb-2">{t('error_states.data_loading')}</p>
          <p className="text-sm text-muted-foreground">
            {applicationsError?.message || projectsError?.message || t('error_states.unknown_error')}
          </p>
        </div>
      </div>
    );
  }

  return (
    <PageLayout
      title={pageConfig.title}
      description={pageConfig.description}
    >

      {/* KPI Cards - moved to top */}
        <div className="grid grid-cols-2 sm:grid-cols-4 gap-2 sm:gap-3 w-full overflow-x-hidden mb-6">
            <Card className="min-w-0 w-full overflow-x-hidden">
              <CardHeader className="pb-2">
                <CardTitle className="text-xs sm:text-sm font-medium flex items-center gap-1 sm:gap-2">
                  <Building2 className="h-3 w-3 sm:h-4 sm:w-4 text-blue-500 flex-shrink-0" />
                  <span className="truncate">{tPages('dashboard.kpi_cards.running_applications')}</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-xl sm:text-2xl font-bold">{openApplications}</div>
                <p className="text-xs text-muted-foreground truncate">{tPages('dashboard.kpi_cards.actively_running')}</p>
              </CardContent>
            </Card>
            
            <Card className="min-w-0">
              <CardHeader className="pb-2">
                <CardTitle className="text-xs sm:text-sm font-medium flex items-center gap-1 sm:gap-2">
                  <FolderOpen className="h-3 w-3 sm:h-4 sm:w-4 text-green-500 flex-shrink-0" />
                  <span className="truncate">{tPages('dashboard.kpi_cards.active_projects')}</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-xl sm:text-2xl font-bold">{activeProjects}</div>
                <p className="text-xs text-muted-foreground truncate">{tPages('dashboard.kpi_cards.in_processing')}</p>
              </CardContent>
            </Card>

            <Card className="min-w-0">
              <CardHeader className="pb-2">
                <CardTitle className="text-xs sm:text-sm font-medium flex items-center gap-1 sm:gap-2">
                  <Clock className="h-3 w-3 sm:h-4 sm:w-4 text-orange-500 flex-shrink-0" />
                  <span className="truncate">{tPages('dashboard.worked_today')}</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-xl sm:text-2xl font-bold">{todayHours || 0}h</div>
                <p className="text-xs text-muted-foreground truncate">{tPages('dashboard.kpi_cards.hours_today')}</p>
              </CardContent>
            </Card>

            <Card className="min-w-0">
              <CardHeader className="pb-2">
                <CardTitle className="text-xs sm:text-sm font-medium flex items-center gap-1 sm:gap-2">
                  <Bell className="h-3 w-3 sm:h-4 sm:w-4 text-purple-500 flex-shrink-0" />
                  <span className="truncate">{tPages('dashboard.kpi_cards.followups_count')}</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-xl sm:text-2xl font-bold">{activeFollowUps || 0}</div>
                <p className="text-xs text-muted-foreground truncate">{tPages('dashboard.kpi_cards.scheduled')}</p>
              </CardContent>
            </Card>
        </div>

        {/* Follow-up Notifications */}
        <div className="mb-6 w-full overflow-x-hidden">
          <FollowUpNotifications compact={true} />
        </div>

        <div className="space-y-6 w-full overflow-x-hidden min-w-0">
          {/* Main Content Grid */}
          <div className="grid gap-4 sm:gap-6 lg:grid-cols-2">
            {/* Recent Applications */}
            <Card className="min-w-0">
              <CardHeader className="pb-4">
                <div className="flex items-center justify-between gap-2">
                  <CardTitle className="flex items-center gap-2 min-w-0 text-base sm:text-lg">
                    <Building2 className="h-4 w-4 sm:h-5 sm:w-5 flex-shrink-0" />
                    <span className="truncate">{tPages('dashboard.recent_applications')}</span>
                  </CardTitle>
                  <Button 
                    variant="ghost" 
                    size="sm" 
                    onClick={handleViewAllApplications}
                    className="text-xs flex-shrink-0"
                  >
                    <span className="hidden sm:inline">{tPages('dashboard.show_all')}</span>
                    <span className="sm:hidden">{tPages('dashboard.show_all_short')}</span>
                    <ArrowRight className="h-3 w-3 ml-1" />
                  </Button>
                </div>
              </CardHeader>
              <CardContent>
                {recentApplications.length === 0 ? (
                  <p className="text-muted-foreground text-sm">{tPages('dashboard.no_applications')}</p>
                ) : (
                  <div className="space-y-3 w-full overflow-x-hidden">
                    {recentApplications.map((application) => (
                      <div 
                        key={application.id} 
                        className="flex items-center gap-2 p-2 sm:p-3 rounded-lg border bg-muted/20 hover:bg-muted/40 cursor-pointer transition-colors w-full overflow-x-hidden min-w-0"
                        onClick={() => navigate(`/applications/${application.id}`)}
                      >
                        <div className="flex-1 min-w-0 overflow-x-hidden">
                          <h4 className="font-medium text-sm truncate break-words">{application.project_name}</h4>
                          <p className="text-xs text-muted-foreground truncate break-words">{application.company_name}</p>
                        </div>
                        <div className="flex-shrink-0">
                          <Badge 
                            variant="outline"
                            className={`text-xs ${APPLICATION_STATUS_BADGE_CLASSES[application.status]}`}
                          >
                            {getStatusLabel(application.status)}
                          </Badge>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Recent Projects */}
            <Card className="min-w-0">
              <CardHeader className="pb-4">
                <div className="flex items-center justify-between gap-2">
                  <CardTitle className="flex items-center gap-2 min-w-0 text-base sm:text-lg">
                    <FolderOpen className="h-4 w-4 sm:h-5 sm:w-5 flex-shrink-0" />
                    <span className="truncate">{tPages('dashboard.recent_projects')}</span>
                  </CardTitle>
                  <Button 
                    variant="ghost" 
                    size="sm" 
                    onClick={handleViewAllProjects}
                    className="text-xs flex-shrink-0"
                  >
                    <span className="hidden sm:inline">{tPages('dashboard.show_all')}</span>
                    <span className="sm:hidden">{tPages('dashboard.show_all_short')}</span>
                    <ArrowRight className="h-3 w-3 ml-1" />
                  </Button>
                </div>
              </CardHeader>
              <CardContent>
                {recentProjects.length === 0 ? (
                  <p className="text-muted-foreground text-sm">{tPages('dashboard.no_projects')}</p>
                ) : (
                  <div className="space-y-3 w-full overflow-x-hidden">
                    {recentProjects.map((project: Project) => (
                      <div 
                        key={project.id} 
                        className="flex items-center gap-2 p-2 sm:p-3 rounded-lg border bg-muted/20 hover:bg-muted/40 cursor-pointer transition-colors w-full overflow-x-hidden min-w-0"
                        onClick={() => navigate(`/projects/${project.id}`)}
                      >
                        <div className="flex-1 min-w-0 overflow-x-hidden">
                          <h4 className="font-medium text-sm truncate break-words">{project.title}</h4>
                          <p className="text-xs text-muted-foreground truncate break-words">{project.client_name}</p>
                        </div>
                        <div className="flex-shrink-0">
                          <Badge 
                            variant="outline"
                            className={`text-xs ${PROJECT_STATUS_BADGE_CLASSES[project.status]}`}
                          >
                            {PROJECT_STATUS_LABELS[project.status]}
                          </Badge>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>
          </div>

          {/* Activity Timeline */}
          <Card className="min-w-0">
            <CardHeader className="pb-4">
              <div className="flex items-center justify-between gap-2">
                <CardTitle className="flex items-center gap-2 min-w-0 text-sm sm:text-base">
                  <Activity className="h-4 w-4 sm:h-5 sm:w-5 flex-shrink-0" />
                  <span className="truncate">{tPages('dashboard.recent_activities')}</span>
                </CardTitle>
                <Button 
                  variant="ghost" 
                  size="sm" 
                  onClick={() => setShowActivitiesModal(true)}
                  className="text-xs flex-shrink-0"
                >
                  <span className="hidden sm:inline">{tPages('dashboard.show_all')}</span>
                  <span className="sm:hidden">{tPages('dashboard.show_all_short')}</span>
                  <ArrowRight className="h-3 w-3 ml-1" />
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              {recentActivities.length === 0 ? (
                <p className="text-muted-foreground text-sm">{tPages('dashboard.no_activities')}</p>
              ) : (
                <div className="space-y-4 w-full overflow-x-hidden min-w-0">
                  {recentActivities.map((activity: ProjectActivity) => {
                    // Find project/application name from already loaded data
                    let projectName = t('project_labels.unknown');
                    let navigateUrl = '';
                    
                    // Check if it's an application activity or follow-up activity
                    if (activity.activity_type.includes('application') || activity.activity_type.includes('followup')) {
                      const application = applications.find(app => app.id === activity.project_id);
                      projectName = application?.project_name || t('project_labels.application');
                      navigateUrl = `/applications/${activity.project_id}`;
                    } else {
                      // It's a project activity
                      const project = projects.find(proj => proj.id === activity.project_id);
                      projectName = project?.title || t('project_labels.project');
                      navigateUrl = `/projects/${activity.project_id}`;
                    }
                    
                    return (
                      <div 
                        key={activity.id} 
                        className="flex gap-2 sm:gap-3 pb-3 border-b border-border/40 last:border-0 last:pb-0 cursor-pointer hover:bg-muted/20 rounded-lg p-2 -m-2 transition-colors w-full overflow-x-hidden min-w-0"
                        onClick={() => navigate(navigateUrl)}
                      >
                        <div className="flex-shrink-0 w-6 h-6 sm:w-8 sm:h-8 rounded-full bg-muted flex items-center justify-center">
                          <Activity className="h-3 w-3 sm:h-4 sm:w-4 text-muted-foreground" />
                        </div>
                        <div className="flex-1 min-w-0 overflow-x-hidden">
                          <div className="flex flex-col sm:flex-row sm:items-start gap-1 sm:gap-2 mb-1 w-full min-w-0 overflow-x-hidden">
                            <div className="flex-shrink-0 w-fit">
                              <Badge 
                                variant="outline" 
                                className={`text-xs ${ALL_ACTIVITY_COLORS[activity.activity_type as keyof typeof ALL_ACTIVITY_COLORS] || 'text-muted-foreground'}`}
                              >
                                {getActivityLabel(activity.activity_type)}
                              </Badge>
                            </div>
                            <span className="text-xs text-muted-foreground truncate flex-shrink min-w-0">
                              {format(new Date(activity.created_at), 'dd.MM.yyyy HH:mm', { locale: de })}
                            </span>
                          </div>
                          <p className="text-sm font-medium text-primary mb-1 truncate">
                            {projectName}
                          </p>
                          <p className="text-sm text-foreground break-words mobile-text-safe" style={{
                            display: '-webkit-box',
                            WebkitLineClamp: 2,
                            WebkitBoxOrient: 'vertical',
                            overflow: 'hidden',
                            wordBreak: 'break-word',
                            overflowWrap: 'break-word'
                          }}>
                            {activity.description}
                          </p>
                          {activity.notes && (
                            <div className="text-xs text-muted-foreground mt-1 break-words mobile-text-safe prose prose-xs max-w-none" style={{
                              display: '-webkit-box',
                              WebkitLineClamp: 2,
                              WebkitBoxOrient: 'vertical',
                              overflow: 'hidden',
                              wordBreak: 'break-word',
                              overflowWrap: 'break-word'
                            }}>
                              <ReactMarkdown 
                                components={{
                                  // Remove margins and ensure inline rendering
                                  p: ({children}) => <span>{children}</span>,
                                  h1: ({children}) => <strong className="font-semibold">{children}</strong>,
                                  h2: ({children}) => <strong className="font-semibold">{children}</strong>,
                                  h3: ({children}) => <strong className="font-medium">{children}</strong>,
                                  strong: ({children}) => <strong className="font-semibold">{children}</strong>,
                                  em: ({children}) => <em className="italic">{children}</em>,
                                  code: ({children}) => <code className="bg-muted px-1 py-0.5 rounded text-xs">{children}</code>,
                                  // Disable block elements for inline display
                                  ul: ({children}) => <span>{children}</span>,
                                  ol: ({children}) => <span>{children}</span>,
                                  li: ({children}) => <span>• {children} </span>,
                                  blockquote: ({children}) => <span className="italic">"{children}"</span>,
                                }}
                              >
                                {activity.notes}
                              </ReactMarkdown>
                            </div>
                          )}
                        </div>
                      </div>
                    );
                  })}
                </div>
              )}
            </CardContent>
          </Card>

          {/* Quick Actions */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-base sm:text-lg">
                <TrendingUp className="h-4 w-4 sm:h-5 sm:w-5" />
                {tPages('dashboard.quick_actions.title')}
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 sm:grid-cols-4 gap-2 sm:gap-3">
                <Button 
                  variant="outline" 
                  size="sm" 
                  onClick={() => navigate('/applications/new')}
                  className="flex items-center justify-center gap-1 sm:gap-2 min-w-0 mobile-button-safe"
                >
                  <Plus className="h-3 w-3 sm:h-4 sm:w-4 flex-shrink-0" />
                  <span className="truncate text-xs sm:text-sm">{tPages('dashboard.quick_actions.new_application')}</span>
                </Button>
                <Button 
                  variant="outline" 
                  size="sm" 
                  onClick={() => navigate('/projects/new')}
                  className="flex items-center justify-center gap-1 sm:gap-2 min-w-0 mobile-button-safe"
                >
                  <FolderOpen className="h-3 w-3 sm:h-4 sm:w-4 flex-shrink-0" />
                  <span className="truncate text-xs sm:text-sm">{tPages('dashboard.quick_actions.new_project')}</span>
                </Button>
                <Button 
                  variant="outline" 
                  size="sm" 
                  onClick={() => navigate('/projects/timer')}
                  className="flex items-center justify-center gap-1 sm:gap-2 min-w-0 mobile-button-safe"
                >
                  <Timer className="h-3 w-3 sm:h-4 sm:w-4 flex-shrink-0" />
                  <span className="truncate text-xs sm:text-sm">{tPages('dashboard.quick_actions.start_timer')}</span>
                </Button>
                <Button 
                  variant="outline" 
                  size="sm" 
                  onClick={() => navigate('/calendar')}
                  className="flex items-center justify-center gap-1 sm:gap-2 min-w-0 mobile-button-safe"
                >
                  <Calendar className="h-3 w-3 sm:h-4 sm:w-4 flex-shrink-0" />
                  <span className="truncate text-xs sm:text-sm">{tPages('dashboard.quick_actions.calendar')}</span>
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Activities Modal */}
        <ActivitiesModal
          isOpen={showActivitiesModal}
          onClose={() => setShowActivitiesModal(false)}
          activities={allActivities}
          isLoading={activitiesLoading}
          applications={applications}
          projects={projects}
        />
    </PageLayout>
  );
};

export default CombinedDashboard;