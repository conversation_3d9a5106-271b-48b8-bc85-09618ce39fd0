import { PageLayout } from '@/components/layout/PageLayout';
import { Statistics as StatisticsComponent } from '@/components/dashboard/Statistics';
import { useCurrentPageConfig } from '@/hooks/usePageConfig';

const Statistics = () => {
  const pageConfig = useCurrentPageConfig();
  
  return (
    <PageLayout
      title={pageConfig.title}
      description={pageConfig.description}
    >
      <StatisticsComponent />
    </PageLayout>
  );
};

export default Statistics;