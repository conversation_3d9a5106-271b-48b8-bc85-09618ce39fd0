import { useEffect, useState } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { supabase } from '@/integrations/supabase/client';
import { toast } from '@/lib/toast';
import { Loader2, CheckCircle, XCircle } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';

const AuthCallback = () => {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const [status, setStatus] = useState<'loading' | 'success' | 'error'>('loading');
  const [message, setMessage] = useState('Authentifizierung wird verarbeitet...');

  useEffect(() => {
    const handleAuthCallback = async () => {
      try {
        // First check if user is already authenticated
        const { data: { session: existingSession } } = await supabase.auth.getSession();
        
        if (existingSession) {
          // User is already authenticated, redirect to main app
          setStatus('success');
          setMessage('Sie sind bereits angemeldet!');
          setTimeout(() => {
            navigate('/', { replace: true });
          }, 1000);
          return;
        }

        // Get the authorization code from URL parameters
        const code = searchParams.get('code');
        const error = searchParams.get('error');
        const errorDescription = searchParams.get('error_description');

        // Handle error from OAuth provider
        if (error) {
          throw new Error(errorDescription || error);
        }

        // Handle missing code - but check for fragment-based flow first
        if (!code) {
          // Check if we have tokens in the URL fragment (implicit flow)
          const hash = window.location.hash;
          if (hash.includes('access_token=') || hash.includes('id_token=')) {
            // Let Supabase handle the implicit flow automatically
            setStatus('success');
            setMessage('Anmeldung erfolgreich!');
            setTimeout(() => {
              navigate('/', { replace: true });
            }, 1000);
            return;
          }
          throw new Error('Kein Autorisierungscode erhalten');
        }

        setMessage('Sitzung wird erstellt...');

        // Exchange the code for a session
        const { data, error: exchangeError } = await supabase.auth.exchangeCodeForSession(code);

        if (exchangeError) {
          throw exchangeError;
        }

        if (!data.session || !data.user) {
          throw new Error('Sitzung konnte nicht erstellt werden');
        }

        // Check if this is a new user and create user settings if needed
        const isNewUser = !data.user.email_confirmed_at || 
                          data.user.created_at === data.user.updated_at;

        if (isNewUser && data.user.user_metadata) {
          try {
            const { error: settingsError } = await supabase
              .from('user_settings')
              .insert([{
                user_id: data.user.id,
                full_name: data.user.user_metadata.full_name || 
                          data.user.user_metadata.name || 
                          data.user.email?.split('@')[0] || 'Google User'
              }]);

            // Note: We don't throw here if settings creation fails
            // User registration is still considered successful
            if (settingsError) {
              console.warn('Failed to create user settings:', settingsError);
            }
          } catch (settingsError) {
            console.warn('Error creating user settings:', settingsError);
          }
        }

        setStatus('success');
        setMessage('Anmeldung erfolgreich!');
        
        toast.success(
          'Google-Anmeldung erfolgreich', 
          isNewUser ? 'Willkommen bei Lanzr!' : 'Willkommen zurück!'
        );

        // Small delay to show success message, then redirect
        setTimeout(() => {
          navigate('/', { replace: true });
        }, 1500);

      } catch (error: unknown) {
        console.error('Auth callback error:', error);
        setStatus('error');
        setMessage('Authentifizierung fehlgeschlagen');
        
        const errorMessage = error instanceof Error ? error.message : 'Ein unerwarteter Fehler ist aufgetreten.';
        toast.error('Anmeldung fehlgeschlagen', errorMessage);

        // Redirect to auth page after showing error
        setTimeout(() => {
          navigate('/auth', { replace: true });
        }, 3000);
      }
    };

    handleAuthCallback();
  }, [navigate, searchParams]);

  const getIcon = () => {
    switch (status) {
      case 'loading':
        return <Loader2 className="w-12 h-12 animate-spin text-primary" />;
      case 'success':
        return <CheckCircle className="w-12 h-12 text-green-500" />;
      case 'error':
        return <XCircle className="w-12 h-12 text-red-500" />;
    }
  };

  const getStatusColor = () => {
    switch (status) {
      case 'loading':
        return 'text-muted-foreground';
      case 'success':
        return 'text-green-600 dark:text-green-400';
      case 'error':
        return 'text-red-600 dark:text-red-400';
    }
  };

  return (
    <div className="min-h-screen relative overflow-hidden">
      {/* Animated Background - same as Auth page */}
      <div className="absolute inset-0 bg-gradient-to-br from-background via-background to-muted">
        <div className="absolute inset-0 bg-[radial-gradient(circle_at_20%_80%,_hsl(var(--primary))_0%,_transparent_50%),_radial-gradient(circle_at_80%_20%,_hsl(var(--primary-glow))_0%,_transparent_50%)] opacity-20" />
        <div className="absolute inset-0 bg-grid-white/[0.02] bg-[size:50px_50px]" />
      </div>

      {/* Main Content */}
      <div className="relative z-10 min-h-screen flex items-center justify-center p-4">
        <Card className="w-full max-w-md backdrop-blur-sm bg-card/80 border-0 shadow-2xl shadow-black/10 dark:shadow-black/20">
          <CardContent className="p-12">
            <div className="text-center space-y-6">
              {/* Icon */}
              <div className="flex justify-center">
                {getIcon()}
              </div>

              {/* Message */}
              <div className="space-y-2">
                <h2 className="text-xl font-semibold">
                  {status === 'loading' && 'Authentifizierung läuft...'}
                  {status === 'success' && 'Erfolgreich angemeldet!'}
                  {status === 'error' && 'Anmeldung fehlgeschlagen'}
                </h2>
                <p className={`text-sm ${getStatusColor()}`}>
                  {message}
                </p>
              </div>

              {/* Additional info for error state */}
              {status === 'error' && (
                <p className="text-xs text-muted-foreground">
                  Sie werden automatisch zur Anmeldeseite weitergeleitet...
                </p>
              )}

              {/* Additional info for success state */}
              {status === 'success' && (
                <p className="text-xs text-muted-foreground">
                  Sie werden automatisch weitergeleitet...
                </p>
              )}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default AuthCallback;