import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useCurrentPageConfig } from '@/hooks/usePageConfig';
import { useFollowUpSchedule } from '@/hooks/useFollowUpSchedule';
import { FollowUpService } from '@/services/followUpService';
import { withFollowUpErrorHandling } from '@/lib/errorHandling';
import { useUserSettings } from '@/hooks/useUserSettings';
import { supabase } from '@/integrations/supabase/client';
import { PageLayout } from '@/components/layout/PageLayout';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuSeparator, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { Skeleton } from '@/components/ui/skeleton';
import { Bell, Clock, AlertTriangle, CheckCircle, XCircle, Mail, Plus, Trash2, MoreHorizontal } from 'lucide-react';
import { format } from 'date-fns';
import { de, enUS } from 'date-fns/locale';
import { useTranslation } from 'react-i18next';
import { formatFollowUpDateTime } from '@/lib/dateUtils';
import type { FollowUpNotification } from '@/types/followup';
import { useQuery } from '@tanstack/react-query';
import { FollowUpDeleteDialog } from '@/components/followup/FollowUpDeleteDialog';
import { FollowUpAnalytics } from '@/components/followup/FollowUpAnalytics';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useFollowUpHistory } from '@/hooks/useFollowUpHistory';

export const FollowUps = () => {
  const navigate = useNavigate();
  const pageConfig = useCurrentPageConfig();
  const { t, i18n } = useTranslation('followup');
  const dateLocale = i18n.language === 'de' ? de : enUS;
  const { markAsSent, dismissFollowUp, deleteScheduledFollowUp, isDeleting, error } = useFollowUpSchedule();
  const { history, markAsResponded, unmarkResponse, isLoading: isHistoryLoading } = useFollowUpHistory();
  const { settings } = useUserSettings();
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [followUpToDelete, setFollowUpToDelete] = useState<FollowUpNotification | null>(null);
  const [activeTab, setActiveTab] = useState('followups');

  // Fetch all follow-ups for overview
  const {
    data: allFollowUps = [],
    isLoading,
    error: followUpsError
  } = useQuery({
    queryKey: ['allFollowUps'],
    queryFn: async (): Promise<FollowUpNotification[]> => {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error('User not authenticated');

      return await withFollowUpErrorHandling(
        () => FollowUpService.getAllFollowUps(user.id),
        'FollowUps.getAllFollowUps'
      );
    },
    refetchInterval: 5 * 60 * 1000, // Refresh every 5 minutes
  });

  const handleOpenInEmailClient = async (followUp: FollowUpNotification) => {
    try {
      // Get the full follow-up with template details
      const { data: fullFollowUp, error } = await supabase
        .from('follow_up_schedule')
        .select(`
          *,
          template:follow_up_templates(*),
          application:project_applications(
            *,
            contact:contacts(*)
          )
        `)
        .eq('id', followUp.id)
        .single();

      if (error) {
        console.error('Error fetching follow-up data:', error);
        throw error;
      }

      if (!fullFollowUp?.template || !fullFollowUp?.application) {
        console.error('Could not load follow-up template or application data');
        throw new Error('Missing template or application data');
      }

      // Create template variables
      const templateVariables = {
        project_name: fullFollowUp.application.project_name || followUp.project_name,
        contact_person: fullFollowUp.application.contact?.name || 'Sehr geehrte Damen und Herren',
        company_name: fullFollowUp.application.company_name || followUp.company_name,
        user_name: settings?.full_name || 'Ihr Name',
        trigger_days: fullFollowUp.template.trigger_days || 7,
        application_date: fullFollowUp.application.application_date || fullFollowUp.application.created_at || '',
        interview_date: fullFollowUp.application.interview_date || ''
      };

      // Generate personalized email content
      const personalizedSubject = FollowUpService.personalizeTemplate(
        fullFollowUp.template.subject,
        templateVariables
      );
      const personalizedBody = FollowUpService.personalizeTemplate(
        fullFollowUp.template.body,
        templateVariables
      );

      const recipientEmail = fullFollowUp.application.contact?.email || '';
      const mailtoLink = FollowUpService.generateMailtoLink(
        recipientEmail,
        personalizedSubject,
        personalizedBody
      );
      
      window.open(mailtoLink, '_blank');
    } catch (error) {
      console.error('Error generating personalized email:', error);
      // Fallback to simple email
      const subject = `Follow-up: ${followUp.project_name}`;
      const body = `Hallo,\\n\\nich wollte mich nach dem Stand meiner Bewerbung für ${followUp.project_name} bei ${followUp.company_name} erkundigen.\\n\\nVielen Dank für Ihr Feedback.\\n\\nBeste Grüße\\n${settings?.full_name || 'Ihr Name'}`;
      
      const mailtoLink = FollowUpService.generateMailtoLink('', subject, body);
      window.open(mailtoLink, '_blank');
    }
  };

  const handleMarkAsSent = (followUpId: string, followUp: FollowUpNotification) => {
    const subject = `Follow-up: ${followUp.project_name}`;
    const body = `Hallo,\\n\\nich wollte mich nach dem Stand meiner Bewerbung für ${followUp.project_name} bei ${followUp.company_name} erkundigen.\\n\\nVielen Dank für Ihr Feedback.\\n\\nBeste Grüße\\n${settings?.full_name || 'Ihr Name'}`;
    
    markAsSent({
      followUpId,
      actualSubject: subject,
      actualBody: body
    });
  };

  const handleDeleteClick = (followUp: FollowUpNotification) => {
    setFollowUpToDelete(followUp);
    setDeleteDialogOpen(true);
  };

  const handleDeleteConfirm = () => {
    if (followUpToDelete) {
      deleteScheduledFollowUp(followUpToDelete.id);
      setDeleteDialogOpen(false);
      setFollowUpToDelete(null);
    }
  };

  const handleDeleteCancel = () => {
    setDeleteDialogOpen(false);
    setFollowUpToDelete(null);
  };

  const getFollowUpStatus = (notification: FollowUpNotification) => {
    const now = new Date();
    const scheduledDate = new Date(notification.scheduled_date);
    
    // Check if it's the same day
    const nowDate = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    const scheduleOnlyDate = new Date(scheduledDate.getFullYear(), scheduledDate.getMonth(), scheduledDate.getDate());
    const isSameDay = nowDate.getTime() === scheduleOnlyDate.getTime();
    
    const diffMs = scheduledDate.getTime() - now.getTime();
    const diffDays = Math.floor((scheduleOnlyDate.getTime() - nowDate.getTime()) / (1000 * 60 * 60 * 24));
    
    if (diffMs > 0) {
      // Still upcoming
      if (isSameDay) {
        // Due today but not yet overdue
        return {
          status: 'due',
          text: t('page.status.due_today'),
          color: 'orange',
          icon: AlertTriangle
        };
      } else {
        // Future days
        const days = Math.abs(diffDays);
        return {
          status: 'upcoming',
          text: t('page.status.upcoming_days', { days, plural: days !== 1 ? 'en' : '' }),
          color: 'blue',
          icon: Clock
        };
      }
    } else {
      // Past due time
      if (isSameDay) {
        // Overdue today
        return {
          status: 'overdue',
          text: t('page.status.overdue'),
          color: 'red',
          icon: AlertTriangle
        };
      } else {
        // Overdue by multiple days
        const overdueDays = Math.abs(diffDays);
        return {
          status: 'overdue',
          text: t('page.status.overdue_days', { days: overdueDays, plural: overdueDays !== 1 ? 'e' : '' }),
          color: 'red',
          icon: AlertTriangle
        };
      }
    }
  };

  // Group follow-ups by status
  const overdueFollowUps = allFollowUps.filter(followUp => getFollowUpStatus(followUp).status === 'overdue');
  const dueFollowUps = allFollowUps.filter(followUp => getFollowUpStatus(followUp).status === 'due');
  const upcomingFollowUps = allFollowUps.filter(followUp => getFollowUpStatus(followUp).status === 'upcoming');

  // If there's an error (like 403/400), don't render anything
  if (error || followUpsError) {
    return (
      <PageLayout
        title={pageConfig.title}
        description={t('page.system_unavailable')}
      >
        <div className="text-center py-12">
          <XCircle className="w-12 h-12 mx-auto mb-4 text-muted-foreground" />
          <p className="text-muted-foreground">{t('page.system_unavailable_message')}</p>
        </div>
      </PageLayout>
    );
  }

  if (isLoading) {
    return (
      <PageLayout
        title={pageConfig.title}
        description={t('page.loading_followups')}
      >
        <div className="space-y-4">
          {[...Array(3)].map((_, i) => (
            <Card key={i}>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div className="space-y-2 flex-1">
                    <Skeleton className="h-4 w-[250px]" />
                    <Skeleton className="h-3 w-[200px]" />
                  </div>
                  <Skeleton className="h-8 w-20" />
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </PageLayout>
    );
  }

  return (
    <PageLayout
      title={pageConfig.title}
      description={pageConfig.description}
    >

        {/* Tabs for Follow-ups and Analytics */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="followups">{t('page.tabs.scheduled')}</TabsTrigger>
            <TabsTrigger value="sent">{t('page.tabs.sent')}</TabsTrigger>
            <TabsTrigger value="analytics">{t('page.tabs.analytics')}</TabsTrigger>
          </TabsList>

          <TabsContent value="followups" className="space-y-6">
        {/* Summary Cards */}
        <div className="grid grid-cols-3 gap-1 sm:gap-4 w-full overflow-x-hidden">
          <Card className="min-w-0">
            <CardHeader className="pb-1 sm:pb-2">
              <CardTitle className="text-xs sm:text-sm font-medium flex flex-col sm:flex-row sm:items-center gap-1 sm:gap-2">
                <AlertTriangle className="h-3 w-3 sm:h-4 sm:w-4 text-red-500 flex-shrink-0 mx-auto sm:mx-0" />
                <span className="truncate text-center sm:text-left text-xs sm:text-sm">{t('page.status_cards.overdue')}</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="pt-1 sm:pt-3">
              <div className="text-lg sm:text-2xl font-bold text-red-600 text-center sm:text-left">{overdueFollowUps.length}</div>
            </CardContent>
          </Card>
          
          <Card className="min-w-0">
            <CardHeader className="pb-1 sm:pb-2">
              <CardTitle className="text-xs sm:text-sm font-medium flex flex-col sm:flex-row sm:items-center gap-1 sm:gap-2">
                <AlertTriangle className="h-3 w-3 sm:h-4 sm:w-4 text-orange-500 flex-shrink-0 mx-auto sm:mx-0" />
                <span className="truncate text-center sm:text-left text-xs sm:text-sm">{t('page.status_cards.due_today')}</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="pt-1 sm:pt-3">
              <div className="text-lg sm:text-2xl font-bold text-orange-600 text-center sm:text-left">{dueFollowUps.length}</div>
            </CardContent>
          </Card>
          
          <Card className="min-w-0">
            <CardHeader className="pb-1 sm:pb-2">
              <CardTitle className="text-xs sm:text-sm font-medium flex flex-col sm:flex-row sm:items-center gap-1 sm:gap-2">
                <Clock className="h-3 w-3 sm:h-4 sm:w-4 text-blue-500 flex-shrink-0 mx-auto sm:mx-0" />
                <span className="truncate text-center sm:text-left text-xs sm:text-sm">{t('page.status_cards.upcoming')}</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="pt-1 sm:pt-3">
              <div className="text-lg sm:text-2xl font-bold text-blue-600 text-center sm:text-left">{upcomingFollowUps.length}</div>
            </CardContent>
          </Card>
        </div>

        {/* All Follow-ups List */}
        {allFollowUps.length === 0 ? (
          <Card>
            <CardContent className="py-12">
              <div className="text-center">
                <CheckCircle className="w-12 h-12 mx-auto mb-4 text-green-500 opacity-50" />
                <h3 className="text-lg font-semibold mb-2">{t('page.empty_states.no_scheduled_title')}</h3>
                <p className="text-muted-foreground">
                  {t('page.empty_states.no_scheduled_message')}
                </p>
              </div>
            </CardContent>
          </Card>
        ) : (
          <div className="space-y-2 sm:space-y-3 w-full overflow-x-hidden">
            {allFollowUps.map((followUp) => {
              const status = getFollowUpStatus(followUp);
              const StatusIcon = status.icon;
              
              return (
                <Card key={followUp.id} className="hover:shadow-md transition-shadow min-w-0">
                  <CardContent className="p-3 sm:p-4">
                    <div 
                      onClick={() => navigate(`/applications/${followUp.application_id}`)}
                      className="flex items-center justify-between gap-2 sm:gap-4 cursor-pointer min-w-0"
                    >
                      {/* Left: Follow-up Details */}
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center gap-2 sm:gap-3 mb-1 sm:mb-2">
                          <h4 className="font-semibold text-sm sm:text-base truncate">
                            {followUp.project_name || t('page.labels.project')}
                          </h4>
                          <Badge 
                            variant={status.status === 'overdue' ? 'destructive' : 'secondary'} 
                            className={`text-xs flex-shrink-0 ${
                              status.status === 'upcoming' ? 'bg-blue-500/20 text-blue-700 dark:text-blue-300' :
                              status.status === 'due' ? 'bg-orange-500/20 text-orange-700 dark:text-orange-300' :
                              ''
                            }`}
                          >
                            <StatusIcon className="w-3 h-3 mr-1" />
                            <span className="truncate">{status.text}</span>
                          </Badge>
                        </div>
                        
                        <div className="flex flex-col sm:flex-row sm:items-center gap-0 sm:gap-4 text-xs sm:text-sm text-muted-foreground">
                          <span className="truncate">{followUp.company_name || t('page.labels.unknown_company')}</span>
                          <span className="hidden sm:inline">•</span>
                          <span className="truncate">{followUp.template_name || t('page.labels.followup')}</span>
                          <span className="hidden sm:inline">•</span>
                          <span className="truncate">
                            {t('page.labels.scheduled')} {formatFollowUpDateTime(followUp.scheduled_date)}
                          </span>
                        </div>
                      </div>

                      {/* Right: Action Buttons */}
                      <div className="flex items-center gap-1 flex-shrink-0">
                        {/* Desktop: All 4 buttons visible */}
                        <div className="hidden sm:flex items-center gap-1">
                          <TooltipProvider>
                            {/* Email Button */}
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    handleOpenInEmailClient(followUp);
                                  }}
                                  className="h-8 w-8 p-0 hover:bg-blue-500/20 text-blue-600 hover:text-blue-700"
                                >
                                  <Mail className="w-4 h-4" />
                                </Button>
                              </TooltipTrigger>
                              <TooltipContent>
                                <p>{t('page.actions.open_email')}</p>
                              </TooltipContent>
                            </Tooltip>
                            
                            {/* Mark as Sent Button */}
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    handleMarkAsSent(followUp.id, followUp);
                                  }}
                                  className="h-8 w-8 p-0 hover:bg-green-500/20 text-green-600 hover:text-green-700"
                                >
                                  <CheckCircle className="w-4 h-4" />
                                </Button>
                              </TooltipTrigger>
                              <TooltipContent>
                                <p>{t('page.actions.mark_as_sent')}</p>
                              </TooltipContent>
                            </Tooltip>
                            
                            {/* Delete Button */}
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    handleDeleteClick(followUp);
                                  }}
                                  className="h-8 w-8 p-0 hover:bg-red-500/20 text-muted-foreground hover:text-red-600"
                                >
                                  <Trash2 className="w-4 h-4" />
                                </Button>
                              </TooltipTrigger>
                              <TooltipContent>
                                <p>{t('page.actions.delete')}</p>
                              </TooltipContent>
                            </Tooltip>
                            
                            {/* Dismiss Button */}
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    dismissFollowUp(followUp.id);
                                  }}
                                  className="h-8 w-8 p-0 hover:bg-orange-500/20 text-muted-foreground hover:text-orange-600"
                                >
                                  <XCircle className="w-4 h-4" />
                                </Button>
                              </TooltipTrigger>
                              <TooltipContent>
                                <p>{t('page.actions.dismiss')}</p>
                              </TooltipContent>
                            </Tooltip>
                          </TooltipProvider>
                        </div>

                        {/* Mobile: Dropdown with 3 dots */}
                        <div className="sm:hidden">
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={(e) => e.stopPropagation()}
                                className="h-8 w-8 p-0 hover:bg-muted"
                              >
                                <MoreHorizontal className="w-4 h-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end" onClick={(e) => e.stopPropagation()}>
                              <DropdownMenuItem
                                onClick={(e) => {
                                  e.stopPropagation();
                                  handleOpenInEmailClient(followUp);
                                }}
                                className="text-blue-600 focus:text-blue-700"
                              >
                                <Mail className="w-4 h-4 mr-2" />
                                {t('page.actions.open_email')}
                              </DropdownMenuItem>
                              <DropdownMenuItem
                                onClick={(e) => {
                                  e.stopPropagation();
                                  handleMarkAsSent(followUp.id, followUp);
                                }}
                                className="text-green-600 focus:text-green-700"
                              >
                                <CheckCircle className="w-4 h-4 mr-2" />
                                {t('page.actions.mark_as_sent')}
                              </DropdownMenuItem>
                              <DropdownMenuItem
                                onClick={(e) => {
                                  e.stopPropagation();
                                  dismissFollowUp(followUp.id);
                                }}
                                className="text-orange-600 focus:text-orange-700"
                              >
                                <XCircle className="w-4 h-4 mr-2" />
                                {t('page.actions.dismiss')}
                              </DropdownMenuItem>
                              <DropdownMenuSeparator />
                              <DropdownMenuItem
                                onClick={(e) => {
                                  e.stopPropagation();
                                  handleDeleteClick(followUp);
                                }}
                                className="text-red-600 focus:text-red-700"
                              >
                                <Trash2 className="w-4 h-4 mr-2" />
                                {t('page.actions.delete')}
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              );
            })}
          </div>
        )}
          </TabsContent>

          <TabsContent value="sent" className="space-y-6">
            {/* Sent Follow-ups List */}
            {isHistoryLoading ? (
              <div className="space-y-4">
                {[...Array(3)].map((_, i) => (
                  <Card key={i}>
                    <CardContent className="p-6">
                      <div className="flex items-center justify-between">
                        <div className="space-y-2 flex-1">
                          <Skeleton className="h-4 w-[250px]" />
                          <Skeleton className="h-3 w-[200px]" />
                        </div>
                        <Skeleton className="h-8 w-20" />
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            ) : history.length === 0 ? (
              <Card>
                <CardContent className="py-12">
                  <div className="text-center">
                    <Mail className="w-12 h-12 mx-auto mb-4 text-blue-500 opacity-50" />
                    <h3 className="text-lg font-semibold mb-2">{t('page.empty_states.no_sent_title')}</h3>
                    <p className="text-muted-foreground">
                      {t('page.empty_states.no_sent_message')}
                    </p>
                  </div>
                </CardContent>
              </Card>
            ) : (
              <div className="space-y-2 sm:space-y-3 w-full overflow-x-hidden">
                {history.map((followUp) => (
                  <Card key={followUp.id} className="hover:shadow-md transition-shadow min-w-0">
                    <CardContent className="p-3 sm:p-4">
                      <div 
                        onClick={() => navigate(`/applications/${followUp.application_id}`)}
                        className="flex items-center justify-between gap-2 sm:gap-4 cursor-pointer min-w-0"
                      >
                        {/* Left: Follow-up Details */}
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center gap-2 sm:gap-3 mb-1 sm:mb-2">
                            <h4 className="font-semibold text-sm sm:text-base truncate">
                              {followUp.application?.project_name || t('page.labels.project')}
                            </h4>
                            <Badge 
                              variant={followUp.response_received ? 'default' : 'secondary'}
                              className={`text-xs flex-shrink-0 ${
                                followUp.response_received 
                                  ? 'bg-green-500/20 text-green-700 dark:text-green-300' 
                                  : 'bg-gray-500/20 text-gray-700 dark:text-gray-300'
                              }`}
                            >
                              {followUp.response_received ? (
                                <>
                                  <CheckCircle className="w-3 h-3 mr-1" />
                                  <span className="truncate">{t('page.labels.responded')}</span>
                                </>
                              ) : (
                                <>
                                  <Clock className="w-3 h-3 mr-1" />
                                  <span className="truncate">{t('page.labels.waiting_for_response')}</span>
                                </>
                              )}
                            </Badge>
                          </div>
                          
                          <div className="flex flex-col sm:flex-row sm:items-center gap-0 sm:gap-4 text-xs sm:text-sm text-muted-foreground">
                            <span className="truncate">{followUp.application?.company_name || t('page.labels.unknown_company')}</span>
                            <span className="hidden sm:inline">•</span>
                            <span className="truncate">
                              {t('page.labels.sent')} {format(new Date(followUp.sent_at), 'dd.MM.yyyy HH:mm', { locale: dateLocale })}
                            </span>
                            {followUp.response_received && followUp.response_date && (
                              <>
                                <span className="hidden sm:inline">•</span>
                                <span className="truncate text-green-600">
                                  {t('page.labels.response')} {format(new Date(followUp.response_date), 'dd.MM.yyyy', { locale: dateLocale })}
                                </span>
                              </>
                            )}
                          </div>
                        </div>

                        {/* Right: Action Buttons */}
                        <div className="flex items-center gap-1 flex-shrink-0">
                          {/* Desktop: Show buttons directly */}
                          <div className="hidden sm:flex items-center gap-1">
                            <TooltipProvider>
                              {/* Response Received Button */}
                              {!followUp.response_received ? (
                                <Tooltip>
                                  <TooltipTrigger asChild>
                                    <Button
                                      variant="ghost"
                                      size="sm"
                                      onClick={(e) => {
                                        e.stopPropagation();
                                        markAsResponded.mutate({ historyId: followUp.id });
                                      }}
                                      className="h-8 w-8 p-0 hover:bg-green-500/20 text-green-600 hover:text-green-700"
                                    >
                                      <CheckCircle className="w-4 h-4" />
                                    </Button>
                                  </TooltipTrigger>
                                  <TooltipContent>
                                    <p>{t('page.actions.mark_responded')}</p>
                                  </TooltipContent>
                                </Tooltip>
                              ) : (
                                <Tooltip>
                                  <TooltipTrigger asChild>
                                    <Button
                                      variant="ghost"
                                      size="sm"
                                      onClick={(e) => {
                                        e.stopPropagation();
                                        unmarkResponse.mutate(followUp.id);
                                      }}
                                      className="h-8 w-8 p-0 hover:bg-orange-500/20 text-orange-600 hover:text-orange-700"
                                    >
                                      <XCircle className="w-4 h-4" />
                                    </Button>
                                  </TooltipTrigger>
                                  <TooltipContent>
                                    <p>{t('page.actions.unmark_response')}</p>
                                  </TooltipContent>
                                </Tooltip>
                              )}
                            </TooltipProvider>
                          </div>

                          {/* Mobile: Dropdown */}
                          <div className="sm:hidden">
                            <DropdownMenu>
                              <DropdownMenuTrigger asChild>
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={(e) => e.stopPropagation()}
                                  className="h-8 w-8 p-0 hover:bg-muted"
                                >
                                  <MoreHorizontal className="w-4 h-4" />
                                </Button>
                              </DropdownMenuTrigger>
                              <DropdownMenuContent align="end" onClick={(e) => e.stopPropagation()}>
                                {!followUp.response_received ? (
                                  <DropdownMenuItem
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      markAsResponded.mutate({ historyId: followUp.id });
                                    }}
                                    className="text-green-600 focus:text-green-700"
                                  >
                                    <CheckCircle className="w-4 h-4 mr-2" />
                                    {t('page.actions.mark_responded')}
                                  </DropdownMenuItem>
                                ) : (
                                  <DropdownMenuItem
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      unmarkResponse.mutate(followUp.id);
                                    }}
                                    className="text-orange-600 focus:text-orange-700"
                                  >
                                    <XCircle className="w-4 h-4 mr-2" />
                                    {t('page.actions.unmark_response')}
                                  </DropdownMenuItem>
                                )}
                              </DropdownMenuContent>
                            </DropdownMenu>
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            )}
          </TabsContent>

          <TabsContent value="analytics" className="space-y-6">
            <FollowUpAnalytics />
          </TabsContent>
        </Tabs>
        
        {/* Delete Confirmation Dialog */}
      <FollowUpDeleteDialog
        isOpen={deleteDialogOpen}
        onClose={handleDeleteCancel}
        onConfirm={handleDeleteConfirm}
        followUp={followUpToDelete}
        isDeleting={isDeleting}
      />
    </PageLayout>
  );
};

export default FollowUps;