import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { supabase } from '@/integrations/supabase/client';
import { AccessControl } from '@/lib/access-control';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import { toast } from '@/lib/toast';
import { AuthStorage } from '@/lib/auth-storage';
import { GoogleAuthButton } from '@/components/auth/GoogleAuthButton';
import { useTranslation } from '@/hooks/useTranslation';
import { 
  Loader2, 
  User, 
  Mail, 
  Lock, 
  UserPlus, 
  Eye, 
  EyeOff,
  Sparkles,
  ArrowRight,
  Shield
} from 'lucide-react';

const Auth = () => {
  const { t } = useTranslation('forms');
  const tPages = useTranslation('pages').t;
  const tCommon = useTranslation('common').t;
  const tErrors = useTranslation('errors').t;
  const [loading, setLoading] = useState(false);
  const [activeTab, setActiveTab] = useState<'login' | 'register'>('login');

  // Login form state
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [stayLoggedIn, setStayLoggedIn] = useState(() => AuthStorage.isPersistentSession());

  // Registration form state
  const [registerEmail, setRegisterEmail] = useState('');
  const [registerPassword, setRegisterPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [fullName, setFullName] = useState('');
  const [showRegisterPassword, setShowRegisterPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);

  const navigate = useNavigate();

  // Handle stay logged in toggle
  const handleStayLoggedInChange = (checked: boolean) => {
    setStayLoggedIn(checked);
    if (checked) {
      AuthStorage.enablePersistentSession();
    } else {
      AuthStorage.enableTemporarySession();
    }
  };

  useEffect(() => {
    // Check access first
    if (!AccessControl.hasAccess()) {
      navigate('/access', { replace: true });
      return;
    }

    // Check if user is already logged in
    const checkUser = async () => {
      const { data: { session } } = await supabase.auth.getSession();
      if (session) {
        navigate('/');
      }
    };
    checkUser();

    // Listen for auth changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange((event, session) => {
      if (session) {
        navigate('/');
      }
    });

    return () => subscription.unsubscribe();
  }, [navigate]);

  const handleSignIn = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);

    try {
      // Set storage type based on "stay logged in" checkbox
      if (stayLoggedIn) {
        AuthStorage.enablePersistentSession();
      } else {
        AuthStorage.enableTemporarySession();
      }

      const { error } = await supabase.auth.signInWithPassword({
        email,
        password,
      });

      if (error) throw error;

      toast.success(tCommon('messages.login_success'), tCommon('messages.welcome_back'));
    } catch (error: any) {
      toast.error(tCommon('messages.login_failed'), error.message);
    } finally {
      setLoading(false);
    }
  };

  const handleSignUp = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);

    // Validation
    if (registerPassword !== confirmPassword) {
      toast.error(tErrors('validation.passwords_no_match'), tErrors('validation.passwords_no_match_desc'));
      setLoading(false);
      return;
    }

    if (registerPassword.length < 6) {
      toast.error(tErrors('validation.password_too_short'), tErrors('validation.password_min_length'));
      setLoading(false);
      return;
    }

    try {
      // Create user account (email verification is now disabled)
      const { data: authData, error: authError } = await supabase.auth.signUp({
        email: registerEmail,
        password: registerPassword,
        options: {
          data: {
            full_name: fullName,
          }
        }
      });

      if (authError) throw authError;

      if (!authData.user || !authData.session) {
        throw new Error('User creation failed');
      }

      // Create user settings if full name provided
      if (fullName.trim()) {
        const { error: settingsError } = await supabase
          .from('user_settings')
          .insert([{
            user_id: authData.user.id,
            full_name: fullName
          }]);

        if (settingsError) {
          // User settings creation failed, but registration was successful
          // This is not critical for the registration flow
        }
      }

      toast.success(tCommon('messages.registration_success'), tCommon('messages.welcome_lanzr'));

    } catch (error: any) {
      toast.error(tCommon('messages.registration_failed'), error.message);
    } finally {
      setLoading(false);
    }
  };


  return (
    <div className="min-h-screen relative overflow-hidden">
      {/* Background */}
      <div className="absolute inset-0 bg-gradient-to-br from-background via-background to-muted">
        <div className="absolute inset-0 bg-[radial-gradient(circle_at_20%_80%,_hsl(var(--primary))_0%,_transparent_50%),_radial-gradient(circle_at_80%_20%,_hsl(var(--primary-glow))_0%,_transparent_50%)] opacity-20" />
        <div className="absolute inset-0 bg-grid-white/[0.02] bg-[size:50px_50px]" />
        
        {/* Static Elements */}
        <div className="absolute top-20 left-20 w-72 h-72 bg-primary/10 rounded-full blur-3xl" />
        <div className="absolute bottom-20 right-20 w-96 h-96 bg-primary-glow/10 rounded-full blur-3xl" />
      </div>

      {/* Main Content */}
      <div className="relative z-10 min-h-screen flex items-center justify-center p-4 sm:p-6">
        <div className="w-full max-w-sm mx-auto">
          {/* Header */}
          <div className="text-center mb-4">
            <div className="inline-flex items-center justify-center mb-3">
              <img src="/logo.png" alt="Lanzr" className="h-10 sm:h-12 object-contain dark:hidden" />
              <img src="/logo_dark.png" alt="Lanzr" className="h-10 sm:h-12 object-contain hidden dark:block" />
            </div>
            <p className="text-muted-foreground text-sm sm:text-base">
              {tPages('auth.subtitle')}
            </p>
          </div>

          {/* Auth Card */}
          <Card className="backdrop-blur-sm bg-card/80 border-0 shadow-2xl shadow-black/10 dark:shadow-black/20">
            <CardContent className="p-0">
              {/* Tab Switcher */}
              <div className="flex rounded-t-lg overflow-hidden">
                <button
                  type="button"
                  onClick={() => setActiveTab('login')}
                  className={`flex-1 px-6 py-4 text-center font-medium transition-all duration-300 ${
                    activeTab === 'login'
                      ? 'bg-gradient-to-r from-primary to-primary-glow text-primary-foreground shadow-lg'
                      : 'bg-muted hover:bg-muted/80 text-muted-foreground hover:text-foreground'
                  }`}
                >
                  <div className="flex items-center justify-center gap-2">
                    <User className="w-4 h-4" />
                    {t('auth.login')}
                  </div>
                </button>
                <button
                  type="button"
                  onClick={() => setActiveTab('register')}
                  className={`flex-1 px-6 py-4 text-center font-medium transition-all duration-300 ${
                    activeTab === 'register'
                      ? 'bg-gradient-to-r from-primary to-primary-glow text-primary-foreground shadow-lg'
                      : 'bg-muted hover:bg-muted/80 text-muted-foreground hover:text-foreground'
                  }`}
                >
                  <div className="flex items-center justify-center gap-2">
                    <UserPlus className="w-4 h-4" />
                    {t('auth.register')}
                  </div>
                </button>
              </div>

              {/* Form Content */}
              <div className="p-4 sm:p-6">
                {activeTab === 'login' ? (
                  <form onSubmit={handleSignIn} className="space-y-4 sm:space-y-6">
                    <div className="space-y-3 sm:space-y-4">
                      {/* Email Field */}
                      <div className="space-y-2">
                        <Label htmlFor="email" className="text-sm font-medium flex items-center gap-2">
                          <Mail className="w-4 h-4 text-primary" />
                          {t('auth.email')}
                        </Label>
                        <div className="relative">
                          <Input
                            id="email"
                            type="email"
                            value={email}
                            onChange={(e) => setEmail(e.target.value)}
                            className="pl-4 pr-4 py-2 sm:py-3 h-10 sm:h-12 border-2 focus:border-primary transition-colors"
                            placeholder={t('auth.email_placeholder')}
                            required
                          />
                        </div>
                      </div>

                      {/* Password Field */}
                      <div className="space-y-2">
                        <Label htmlFor="password" className="text-sm font-medium flex items-center gap-2">
                          <Lock className="w-4 h-4 text-primary" />
                          {t('auth.password')}
                        </Label>
                        <div className="relative">
                          <Input
                            id="password"
                            type={showPassword ? 'text' : 'password'}
                            value={password}
                            onChange={(e) => setPassword(e.target.value)}
                            className="pl-4 pr-12 py-2 sm:py-3 h-10 sm:h-12 border-2 focus:border-primary transition-colors"
                            placeholder={t('auth.password_placeholder')}
                            required
                          />
                          <button
                            type="button"
                            onClick={() => setShowPassword(!showPassword)}
                            className="absolute right-3 top-1/2 -translate-y-1/2 text-muted-foreground hover:text-foreground transition-colors"
                          >
                            {showPassword ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                          </button>
                        </div>
                      </div>

                      {/* Stay Logged In */}
                      <div className="flex items-center space-x-3 pt-2">
                        <Checkbox
                          id="stay-logged-in"
                          checked={stayLoggedIn}
                          onCheckedChange={(checked) => handleStayLoggedInChange(checked as boolean)}
                        />
                        <Label 
                          htmlFor="stay-logged-in" 
                          className="text-sm cursor-pointer"
                        >
                          {t('auth.stay_logged_in')}
                        </Label>
                      </div>
                    </div>

                    {/* Login Button */}
                    <Button
                      type="submit"
                      disabled={loading}
                      className="w-full h-12 text-base font-medium bg-gradient-to-r from-primary to-primary-glow hover:from-primary-glow hover:to-primary transition-all duration-300 shadow-lg shadow-primary/25 hover:shadow-primary/40 hover:scale-[1.02] active:scale-[0.98]"
                    >
                      {loading ? (
                        <Loader2 className="w-5 h-5 animate-spin" />
                      ) : (
                        <div className="flex items-center gap-2">
                          {t('auth.login')}
                          <ArrowRight className="w-4 h-4" />
                        </div>
                      )}
                    </Button>

                    {/* Divider */}
                    <div className="relative my-6">
                      <div className="absolute inset-0 flex items-center">
                        <span className="w-full border-t border-muted-foreground/20" />
                      </div>
                      <div className="relative flex justify-center text-xs uppercase">
                        <span className="bg-card px-3 text-muted-foreground">
                          {tCommon('common.or')}
                        </span>
                      </div>
                    </div>

                    {/* Google Sign In Button */}
                    <GoogleAuthButton mode="signin" />
                  </form>
                ) : (
                  <form onSubmit={handleSignUp} className="space-y-6">
                    <div className="space-y-4">
                      {/* Full Name Field */}
                      <div className="space-y-2">
                        <Label htmlFor="full-name" className="text-sm font-medium flex items-center gap-2">
                          <User className="w-4 h-4 text-primary" />
                          {t('auth.full_name')}
                        </Label>
                        <Input
                          id="full-name"
                          type="text"
                          value={fullName}
                          onChange={(e) => setFullName(e.target.value)}
                          className="pl-4 pr-4 py-3 h-12 border-2 focus:border-primary transition-colors"
                          placeholder={t('auth.full_name_placeholder')}
                        />
                      </div>

                      {/* Email Field */}
                      <div className="space-y-2">
                        <Label htmlFor="register-email" className="text-sm font-medium flex items-center gap-2">
                          <Mail className="w-4 h-4 text-primary" />
                          {t('auth.email')}
                        </Label>
                        <Input
                          id="register-email"
                          type="email"
                          value={registerEmail}
                          onChange={(e) => setRegisterEmail(e.target.value)}
                          className="pl-4 pr-4 py-3 h-12 border-2 focus:border-primary transition-colors"
                          placeholder="<EMAIL>"
                          required
                        />
                      </div>

                      {/* Password Field */}
                      <div className="space-y-2">
                        <Label htmlFor="register-password" className="text-sm font-medium flex items-center gap-2">
                          <Lock className="w-4 h-4 text-primary" />
                          {t('auth.password')}
                        </Label>
                        <div className="relative">
                          <Input
                            id="register-password"
                            type={showRegisterPassword ? 'text' : 'password'}
                            value={registerPassword}
                            onChange={(e) => setRegisterPassword(e.target.value)}
                            className="pl-4 pr-12 py-2 sm:py-3 h-10 sm:h-12 border-2 focus:border-primary transition-colors"
                            placeholder={t('auth.password_min_chars')}
                            required
                            minLength={6}
                          />
                          <button
                            type="button"
                            onClick={() => setShowRegisterPassword(!showRegisterPassword)}
                            className="absolute right-3 top-1/2 -translate-y-1/2 text-muted-foreground hover:text-foreground transition-colors"
                          >
                            {showRegisterPassword ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                          </button>
                        </div>
                      </div>

                      {/* Confirm Password Field */}
                      <div className="space-y-2">
                        <Label htmlFor="confirm-password" className="text-sm font-medium flex items-center gap-2">
                          <Lock className="w-4 h-4 text-primary" />
                          {t('auth.confirm_password')}
                        </Label>
                        <div className="relative">
                          <Input
                            id="confirm-password"
                            type={showConfirmPassword ? 'text' : 'password'}
                            value={confirmPassword}
                            onChange={(e) => setConfirmPassword(e.target.value)}
                            className="pl-4 pr-12 py-2 sm:py-3 h-10 sm:h-12 border-2 focus:border-primary transition-colors"
                            placeholder={t('auth.confirm_password_placeholder')}
                            required
                            minLength={6}
                          />
                          <button
                            type="button"
                            onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                            className="absolute right-3 top-1/2 -translate-y-1/2 text-muted-foreground hover:text-foreground transition-colors"
                          >
                            {showConfirmPassword ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                          </button>
                        </div>
                      </div>
                    </div>

                    {/* Register Button */}
                    <Button
                      type="submit"
                      disabled={loading}
                      className="w-full h-12 text-base font-medium bg-gradient-to-r from-primary to-primary-glow hover:from-primary-glow hover:to-primary transition-all duration-300 shadow-lg shadow-primary/25 hover:shadow-primary/40 hover:scale-[1.02] active:scale-[0.98]"
                    >
                      {loading ? (
                        <Loader2 className="w-5 h-5 animate-spin" />
                      ) : (
                        <div className="flex items-center gap-2">
                          {t('auth.register')}
                          <ArrowRight className="w-4 h-4" />
                        </div>
                      )}
                    </Button>

                    {/* Divider */}
                    <div className="relative my-6">
                      <div className="absolute inset-0 flex items-center">
                        <span className="w-full border-t border-muted-foreground/20" />
                      </div>
                      <div className="relative flex justify-center text-xs uppercase">
                        <span className="bg-card px-3 text-muted-foreground">
                          {tCommon('common.or')}
                        </span>
                      </div>
                    </div>

                    {/* Google Sign Up Button */}
                    <GoogleAuthButton mode="signup" />
                  </form>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Footer */}
          <div className="text-center mt-6 text-sm text-muted-foreground">
            <p>{tCommon('footer.copyright')}</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Auth;