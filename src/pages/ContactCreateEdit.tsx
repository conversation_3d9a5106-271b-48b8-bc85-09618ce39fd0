import { useParams, useNavigate } from 'react-router-dom';
import { useContacts } from '@/hooks/useContacts';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { ContactForm } from '@/components/contacts/ContactForm';
import { CreateContactData, UpdateContactData } from '@/types/applications';
import { ArrowLeft, User, UserPlus } from 'lucide-react';
import { useTranslation } from 'react-i18next';

export const ContactCreateEdit = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const isEditing = Boolean(id);
  const { t } = useTranslation(['pages', 'common', 'forms']);
  
  const { 
    contacts, 
    isLoading: contactsLoading, 
    createContact, 
    updateContact 
  } = useContacts();
  
  const contact = isEditing ? contacts.find(c => c.id === id) : undefined;

  const handleSubmit = async (data: CreateContactData | UpdateContactData) => {
    try {
      if (isEditing && contact) {
        // Update existing contact
        const success = await updateContact(data as UpdateContactData);
        if (success) {
          navigate(`/contacts/${contact.id}`);
        }
      } else {
        // Create new contact
        const newContact = await createContact(data as CreateContactData);
        if (newContact) {
          navigate(`/contacts/${newContact.id}`);
        }
      }
    } catch (error) {
      console.error('Error saving contact:', error);
    }
  };

  const handleCancel = () => {
    if (isEditing && contact) {
      navigate(`/contacts/${contact.id}`);
    } else {
      navigate('/contacts');
    }
  };

  if (contactsLoading) {
    return (
      <div className="min-h-screen bg-background">
        <div className="container mx-auto px-2 sm:px-6 pb-3 sm:pb-6 max-w-full overflow-x-hidden">
          <div className="flex items-center justify-center py-12">
            <div className="text-center space-y-4">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
              <div className="text-sm text-muted-foreground">
                {isEditing ? t('pages:contact.loading_contact') : t('pages:loading.general')}
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (isEditing && !contact) {
    return (
      <div className="min-h-screen bg-background">
        <div className="container mx-auto px-2 sm:px-6 pb-3 sm:pb-6 max-w-full overflow-x-hidden">
          <div className="mb-6 sm:mb-8">
            <h1 className="text-2xl sm:text-3xl font-bold bg-gradient-primary bg-clip-text text-transparent mb-2">
              {t('pages:not_found.contact_title')}
            </h1>
            <p className="text-muted-foreground text-sm sm:text-base">
              {t('pages:not_found.contact_description')}
            </p>
          </div>
          <Card>
            <CardContent className="p-8 text-center">
              <User className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-medium mb-2">{t('pages:not_found.contact_title')}</h3>
              <p className="text-muted-foreground mb-4">
                {t('pages:not_found.contact_message')}
              </p>
              <Button onClick={() => navigate('/contacts')} variant="outline">
                <ArrowLeft className="h-4 w-4 mr-2" />
                {t('common:navigation.back_to_contacts')}
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  const headerActions = (
    <Button variant="outline" onClick={handleCancel} className="w-full sm:w-auto">
      <ArrowLeft className="h-4 w-4 mr-2" />
      {t('common:actions.back')}
    </Button>
  );

  return (
    <div className="min-h-screen bg-background">
      <div className="container mx-auto px-2 sm:px-6 pb-3 sm:pb-6 max-w-full overflow-x-hidden">
          {/* Header */}
          <div className="flex flex-col sm:flex-row sm:items-center justify-between mb-6 sm:mb-8 gap-4">
            <div className="min-w-0 flex-1">
              <h1 className="text-2xl sm:text-3xl font-bold bg-gradient-primary bg-clip-text text-transparent mb-2">
                {isEditing ? t('pages:contact.edit_title') : t('pages:contact.create_title')}
              </h1>
              <p className="text-muted-foreground text-sm sm:text-base">
                {isEditing 
                  ? (contact?.name 
                    ? t('pages:contact.edit_description', { name: contact.name })
                    : t('pages:contact.edit_description_default')
                   )
                  : t('pages:contact.create_description')
                }
              </p>
            </div>
            <div className="flex-shrink-0">
              {headerActions}
            </div>
          </div>

          {/* Form Container - Full width for both create and edit */}
          <div className="max-w-full">
        <Card>
          <CardContent className="pt-6">
            <div className="space-y-6">
              {/* Form Instructions */}
              <div className="bg-blue-50 dark:bg-blue-950/20 p-4 rounded-lg border border-blue-200 dark:border-blue-800">
                <p className="text-sm text-blue-800 dark:text-blue-200">
                  <strong>{t('common:note')}:</strong> {t('pages:contact.note')}
                  Alle anderen Felder sind optional und können später hinzugefügt werden.
                </p>
              </div>

              <ContactForm
                contact={contact}
                onSubmit={handleSubmit}
                onCancel={handleCancel}
                isLoading={contactsLoading}
              />
            </div>
          </CardContent>
        </Card>

            {/* Additional Information Card for New Contacts */}
            {!isEditing && (
              <Card className="mt-6">
                <CardContent className="p-4">
                  <h4 className="font-medium mb-2 flex items-center gap-2">
                    <User className="h-4 w-4" />
                    Tipps für Kontakte
                  </h4>
                  <ul className="text-sm text-muted-foreground space-y-1">
                    <li>• Vervollständige die Kontaktdaten für bessere Organisation</li>
                    <li>• Notizen helfen dir dabei, wichtige Details zu speichern</li>
                    <li>• Projekte werden automatisch mit Kontakten verknüpft</li>
                    <li>• Du kannst Kontakte später jederzeit bearbeiten</li>
                  </ul>
                </CardContent>
              </Card>
            )}
          </div>
      </div>
    </div>
  );
};