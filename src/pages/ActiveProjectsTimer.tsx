import React, { useState } from 'react';
import { PageLayout } from '@/components/layout/PageLayout';
import { useCurrentPageConfig } from '@/hooks/usePageConfig';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { Separator } from '@/components/ui/separator';
import { 
  Play, 
  Pause, 
  Square, 
  Clock, 
  Timer as TimerIcon,
  Calendar
} from 'lucide-react';
import { useProjects } from '@/hooks/useProjects';
import { useTimeTracking, useTimerState } from '@/hooks/useTimeTracking';
import type { TimeEntryCategory } from '@/types/projects';
import { PROJECT_STATUS_LABELS } from '@/types/projects';
import { useTranslation } from 'react-i18next';
import { useProjectStatusLabel } from '@/lib/translations';

const ActiveProjectsTimer = () => {
  const pageConfig = useCurrentPageConfig();
  const { data: projects = [] } = useProjects();
  const { t } = useTranslation(['timer', 'common']);
  const tTimer = useTranslation('timer').t;
  const getStatusLabel = useProjectStatusLabel();
  const { 
    startTimer, 
    stopTimer, 
    addManualEntry,
    todayHours, 
    thisWeekHours,
    entries,
    todayEntries,
    isCreating 
  } = useTimeTracking();
  const timerState = useTimerState();

  const [selectedProject, setSelectedProject] = useState<string>('');
  const [selectedCategory, setSelectedCategory] = useState<TimeEntryCategory>('development');
  const [description, setDescription] = useState('');

  const handleStartTimer = () => {
    if (!selectedProject) return;
    startTimer(selectedProject, selectedCategory, description);
    setDescription('');
  };

  const handleStopTimer = () => {
    if (timerState.timerId) {
      stopTimer(timerState.timerId);
    }
  };

  const formatTime = (seconds: number): string => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;
    
    if (hours === 0) {
      return `${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
    } else {
      return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
    }
  };

  // Today's entries are now provided by the hook for consistency

  const activeProject = projects.find(p => p.id === timerState.currentProject?.id);

  return (
    <PageLayout
      title={pageConfig.title}
      description={pageConfig.description}
    >
        <div className="space-y-6">
      <div className="grid gap-6 lg:grid-cols-3">
        {/* Timer Section - Takes 2 columns */}
        <div className="lg:col-span-2 space-y-6">
          {/* Current Timer Display */}
          <Card className="text-center">
            <CardHeader>
              <div className="flex items-center justify-center gap-2 mb-4">
                <TimerIcon className="h-6 w-6" />
                <CardTitle className="text-2xl">
                  {timerState.isRunning ? tTimer('timer_running') : tTimer('timer_stopped')}
                </CardTitle>
              </div>
              {timerState.isRunning && activeProject && (
                <div className="mb-4">
                  <Badge variant="default" className="text-lg px-4 py-2">
                    {activeProject.title}
                  </Badge>
                  <p className="text-muted-foreground mt-2">
                    {activeProject.client_name}
                  </p>
                </div>
              )}
            </CardHeader>
            <CardContent>
              {/* Large Timer Display */}
              <div className="text-6xl font-mono font-bold mb-8 text-primary">
                {timerState.isRunning ? formatTime(timerState.elapsedTimeInSeconds) : '00:00'}
              </div>

              {/* Timer Controls */}
              {timerState.isRunning ? (
                <div className="flex gap-4 justify-center">
                  <Button
                    size="lg"
                    onClick={handleStopTimer}
                    variant="destructive"
                    disabled={isCreating}
                  >
                    <Square className="h-5 w-5 mr-2" />
                    {tTimer('stop')}
                  </Button>
                </div>
              ) : (
                <div className="space-y-4">
                  {/* Project Selection */}
                  <div className="max-w-md mx-auto">
                    <Select value={selectedProject} onValueChange={setSelectedProject}>
                      <SelectTrigger>
                        <SelectValue placeholder={tTimer('placeholders.select_project')} />
                      </SelectTrigger>
                      <SelectContent>
                        {projects.map((project) => (
                          <SelectItem key={project.id} value={project.id}>
                            <div className="flex items-center gap-2">
                              <span className="font-medium">{project.title}</span>
                              <span className="text-muted-foreground text-sm">
                                ({project.client_name})
                              </span>
                            </div>
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  {/* Category Selection */}
                  <div className="max-w-md mx-auto">
                    <Select value={selectedCategory} onValueChange={(value) => setSelectedCategory(value as TimeEntryCategory)}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="development">{tTimer('categories.development')}</SelectItem>
                        <SelectItem value="meetings">{tTimer('categories.meetings')}</SelectItem>
                        <SelectItem value="documentation">{tTimer('categories.documentation')}</SelectItem>
                        <SelectItem value="design">{tTimer('categories.design')}</SelectItem>
                        <SelectItem value="testing">{tTimer('categories.testing')}</SelectItem>
                        <SelectItem value="communication">{tTimer('categories.communication')}</SelectItem>
                        <SelectItem value="research">{tTimer('categories.research')}</SelectItem>
                        <SelectItem value="other">{tTimer('categories.other')}</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  {/* Description */}
                  <div className="max-w-md mx-auto">
                    <Textarea
                      placeholder={tTimer('description_optional')}
                      value={description}
                      onChange={(e) => setDescription(e.target.value)}
                      rows={2}
                    />
                  </div>

                  {/* Start Button */}
                  <Button
                    size="lg"
                    onClick={handleStartTimer}
                    disabled={!selectedProject || isCreating}
                    className="px-8"
                  >
                    <Play className="h-5 w-5 mr-2" />
                    {tTimer('start')}
                  </Button>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Project Selection Card */}
          {!timerState.isRunning && (
            <Card>
              <CardHeader>
                <CardTitle>{tTimer('projects_title')}</CardTitle>
                <CardDescription>
                  {tTimer('projects_description')}
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid gap-3 md:grid-cols-2">
                  {projects.map((project) => (
                    <Card key={project.id} className="cursor-pointer hover:bg-accent transition-colors">
                      <CardContent className="p-4">
                        <div className="flex items-center justify-between">
                          <div className="flex-1">
                            <h4 className="font-medium text-sm">{project.title}</h4>
                            <p className="text-xs text-muted-foreground">{project.client_name}</p>
                            <div className="flex items-center gap-2 mt-2">
                              <Badge variant="outline" className="text-xs">
                                {project.total_hours || 0}h
                              </Badge>
                              {project.status && (
                                <Badge variant="secondary" className="text-xs">
                                  {getStatusLabel(project.status)}
                                </Badge>
                              )}
                            </div>
                          </div>
                          <Button 
                            size="sm" 
                            onClick={() => {
                              setSelectedProject(project.id);
                              // Auto-scroll to timer controls
                              window.scrollTo({ top: 0, behavior: 'smooth' });
                            }}
                          >
                            {tTimer('select_button')}
                          </Button>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
                {projects.length === 0 && (
                  <div className="text-center py-8 text-muted-foreground">
                    <Clock className="h-8 w-8 mx-auto mb-2 opacity-50" />
                    <p>{tTimer('no_active_projects')}</p>
                  </div>
                )}
              </CardContent>
            </Card>
          )}
        </div>

        {/* Sidebar - Stats and Today's Sessions */}
        <div className="space-y-6">
          {/* Today's Stats */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Calendar className="h-5 w-5" />
                {tTimer('today')}
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-primary mb-2">
                {timerState.realTimeTodayHours}h
              </div>
              <p className="text-muted-foreground text-sm">
                {tTimer('entries_count', { count: todayEntries.length })}
              </p>
              <Separator className="my-4" />
              <div className="text-sm">
                <p className="text-muted-foreground mb-1">{tTimer('this_week')}</p>
                <p className="font-semibold">{timerState.realTimeWeekHours}h</p>
              </div>
            </CardContent>
          </Card>

          {/* Today's Sessions */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">{tTimer('today_sessions')}</CardTitle>
            </CardHeader>
            <CardContent>
              {todayEntries.length === 0 ? (
                <p className="text-muted-foreground text-center py-4">
                  {tTimer('no_entries_today')}
                </p>
              ) : (
                <div className="space-y-3">
                  {todayEntries.slice(0, 5).map((entry) => {
                    // For running timer, show real-time duration
                    const isCurrentRunningTimer = entry.is_running && timerState.timerId === entry.id;
                    const displayDuration = isCurrentRunningTimer 
                      ? timerState.formattedElapsedTime 
                      : entry.formatted_duration;
                    const project = projects.find(p => p.id === entry.project_id);
                    
                    return (
                      <div key={entry.id} className="flex items-center justify-between text-sm">
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center gap-2 mb-1">
                            <p className="font-medium text-xs">{project?.title || 'Unknown Project'}</p>
                            {isCurrentRunningTimer && (
                              <Badge variant="default" className="text-xs px-1 py-0">
                                {tTimer('running')}
                              </Badge>
                            )}
                          </div>
                          <p className="text-muted-foreground text-xs capitalize">
                            {entry.category}
                          </p>
                          {entry.description && (
                            <p className="text-muted-foreground text-xs truncate">
                              {entry.description}
                            </p>
                          )}
                        </div>
                        <Badge variant="outline" className="text-xs ml-2">
                          {displayDuration}
                        </Badge>
                      </div>
                    );
                  })}
                  {todayEntries.length > 5 && (
                    <p className="text-muted-foreground text-xs text-center pt-2">
                      {tTimer('more_entries', { count: todayEntries.length - 5 })}
                    </p>
                  )}
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>
        </div>
    </PageLayout>
  );
};

export default ActiveProjectsTimer;