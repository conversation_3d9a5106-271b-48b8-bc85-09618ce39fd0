import React from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { 
  ArrowLeft, 
  Edit, 
  Clock, 
  Calendar,
  User,
  Building2,
  DollarSign,
  Target,
  Briefcase,
  Timer,
  ExternalLink
} from 'lucide-react';
import { useProject } from '@/hooks/useProjects';
import { useTimeTracking } from '@/hooks/useTimeTracking';
import { useContacts } from '@/hooks/useContacts';
import { 
  PROJECT_STATUS_LABELS, 
  PROJECT_PRIORITY_LABELS,
  PROJECT_TYPE_LABELS 
} from '@/types/projects';
import { MarkdownRenderer } from '@/components/ui/markdown-renderer';
import { format } from 'date-fns';
import { de } from 'date-fns/locale';

const ActiveProjectDetails = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { data: project, isLoading } = useProject(id!);
  const { entries } = useTimeTracking(id);
  const { contacts } = useContacts();

  if (isLoading) {
    return (
      <div className="container mx-auto px-2 sm:px-6 pb-3 sm:pb-6 max-w-full overflow-x-hidden">
        <div className="mb-6 sm:mb-8">
          <h1 className="text-2xl sm:text-3xl font-bold bg-gradient-primary bg-clip-text text-transparent mb-2">
            Lade...
          </h1>
          <p className="text-muted-foreground text-sm sm:text-base">
            Projektdetails werden geladen
          </p>
        </div>
        <div className="space-y-6">
          {[...Array(3)].map((_, i) => (
            <Card key={i}>
              <CardContent className="pt-6">
                <div className="space-y-3">
                  <div className="h-4 bg-muted rounded animate-pulse" />
                  <div className="h-4 bg-muted rounded animate-pulse w-2/3" />
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  if (!project) {
    return (
      <div className="container mx-auto px-2 sm:px-6 pb-3 sm:pb-6 max-w-full overflow-x-hidden">
        <div className="mb-6 sm:mb-8">
          <h1 className="text-2xl sm:text-3xl font-bold bg-gradient-primary bg-clip-text text-transparent mb-2">
            Projekt nicht gefunden
          </h1>
          <p className="text-muted-foreground text-sm sm:text-base">
            Das angeforderte Projekt existiert nicht
          </p>
        </div>
        <Card>
          <CardContent className="pt-6 text-center">
            <p className="text-muted-foreground mb-4">Das Projekt konnte nicht gefunden werden.</p>
            <Button onClick={() => navigate('/projects')}>
              <ArrowLeft className="h-4 w-4 mr-2" />
              Zurück zu aktiven Projekten
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  const projectEntries = entries.filter(entry => entry.project_id === project.id);
  const totalMinutes = projectEntries.reduce((sum, entry) => sum + (entry.calculated_duration || 0), 0);
  const totalHours = Math.round((totalMinutes / 60) * 100) / 100;

  return (
    <div className="container mx-auto px-2 sm:px-6 pb-3 sm:pb-6 max-w-full overflow-x-hidden">
        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-center justify-between mb-6 sm:mb-8 gap-4">
          <div className="min-w-0 flex-1">
            <h1 className="text-2xl sm:text-3xl font-bold bg-gradient-primary bg-clip-text text-transparent mb-2">
              {project.title}
            </h1>
            <p className="text-muted-foreground text-sm sm:text-base">
              Aktives Projekt • {project.client_name}
            </p>
          </div>
          <div className="flex justify-between sm:justify-end sm:gap-2 items-center w-full sm:w-auto">
            <Button variant="outline" onClick={() => navigate('/projects')}>
              <ArrowLeft className="h-4 w-4 mr-2" />
              <span className="hidden sm:inline">Zurück</span>
            </Button>
            <Button onClick={() => navigate(`/projects/edit/${project.id}`)}>
              <Edit className="h-4 w-4 mr-2" />
              Bearbeiten
            </Button>
          </div>
        </div>
      <div className="grid gap-6 lg:grid-cols-3">
        {/* Main Content - 2/3 width */}
        <div className="lg:col-span-2 space-y-6">
          {/* Project Overview */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Briefcase className="h-5 w-5" />
                Projektübersicht
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid gap-4 md:grid-cols-2">
                <div className="flex items-center gap-3">
                  <User className="h-4 w-4 text-muted-foreground" />
                  <div>
                    <p className="text-sm font-medium">Client</p>
                    <p className="text-sm text-muted-foreground">{project.client_name}</p>
                  </div>
                </div>
                
                <div className="flex items-center gap-3">
                  <Building2 className="h-4 w-4 text-muted-foreground" />
                  <div>
                    <p className="text-sm font-medium">Projekttyp</p>
                    <p className="text-sm text-muted-foreground">{PROJECT_TYPE_LABELS[project.project_type]}</p>
                  </div>
                </div>

                <div className="flex items-center gap-3">
                  <Target className="h-4 w-4 text-muted-foreground" />
                  <div>
                    <p className="text-sm font-medium">Status</p>
                    <Badge variant="secondary">{PROJECT_STATUS_LABELS[project.status]}</Badge>
                  </div>
                </div>

                <div className="flex items-center gap-3">
                  <Timer className="h-4 w-4 text-muted-foreground" />
                  <div>
                    <p className="text-sm font-medium">Priorität</p>
                    <Badge variant={project.priority === 'urgent' ? 'destructive' : 'outline'}>
                      {PROJECT_PRIORITY_LABELS[project.priority]}
                    </Badge>
                  </div>
                </div>
              </div>

              {project.description && (
                <>
                  <Separator />
                  <div>
                    <p className="text-sm font-medium mb-2">Beschreibung</p>
                    <div className="text-sm text-muted-foreground">
                      <MarkdownRenderer 
                        content={project.description}
                        className="text-sm [&_p]:text-muted-foreground [&_strong]:text-foreground [&_li]:text-muted-foreground"
                      />
                    </div>
                  </div>
                </>
              )}
              
              {/* Source Project Info - minimalistic integration */}
              {project.source_project_id && (
                <>
                  <Separator />
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium">Ursprungsprojekt</p>
                      <p className="text-xs text-muted-foreground">Aus Freelance-Bewerbung importiert</p>
                    </div>
                    <Button 
                      variant="ghost" 
                      size="sm"
                      onClick={() => navigate(`/project/${project.source_project_id}`)}
                      className="text-xs"
                    >
                      <ExternalLink className="h-3 w-3 mr-1" />
                      Anzeigen
                    </Button>
                  </div>
                </>
              )}
            </CardContent>
          </Card>

          {/* Timeline & Financial */}
          <div className="grid gap-6 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Calendar className="h-5 w-5" />
                  Zeitplan
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                {project.start_date && (
                  <div>
                    <p className="text-sm font-medium">Startdatum</p>
                    <p className="text-sm text-muted-foreground">
                      {format(new Date(project.start_date), 'dd.MM.yyyy', { locale: de })}
                    </p>
                  </div>
                )}
                {project.planned_end_date && (
                  <div>
                    <p className="text-sm font-medium">Geplantes Ende</p>
                    <p className="text-sm text-muted-foreground">
                      {format(new Date(project.planned_end_date), 'dd.MM.yyyy', { locale: de })}
                    </p>
                  </div>
                )}
                {project.actual_end_date && (
                  <div>
                    <p className="text-sm font-medium">Tatsächliches Ende</p>
                    <p className="text-sm text-muted-foreground">
                      {format(new Date(project.actual_end_date), 'dd.MM.yyyy', { locale: de })}
                    </p>
                  </div>
                )}
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <DollarSign className="h-5 w-5" />
                  Finanzen
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                {project.hourly_rate && (
                  <div>
                    <p className="text-sm font-medium">Stundensatz</p>
                    <p className="text-sm text-green-600 font-semibold">
                      €{project.hourly_rate}/Stunde
                    </p>
                  </div>
                )}
                <div>
                  <p className="text-sm font-medium">Gesamte Stunden</p>
                  <p className="text-sm text-blue-600 font-semibold">{totalHours}h</p>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Sidebar - 1/3 width */}
        <div className="space-y-6">
          {/* Time Tracking & Recent Entries Combined */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Clock className="h-5 w-5" />
                Zeit-Tracking
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Summary */}
              <div className="text-center space-y-2">
                <div className="text-3xl font-bold text-primary">{totalHours}h</div>
                <p className="text-sm text-muted-foreground">
                  {projectEntries.length} Einträge insgesamt
                </p>
              </div>

              {/* Recent Entries */}
              <Separator />
              <div>
                <h4 className="text-sm font-medium mb-3">Letzte Einträge</h4>
                {projectEntries.length === 0 ? (
                  <p className="text-muted-foreground text-center py-4 text-sm">
                    Noch keine Zeiteinträge
                  </p>
                ) : (
                  <div className="space-y-3">
                    {projectEntries.slice(0, 5).map((entry) => (
                      <div key={entry.id} className="flex items-center justify-between text-sm">
                        <div>
                          <p className="font-medium capitalize">{entry.category}</p>
                          <p className="text-muted-foreground text-xs">
                            {format(new Date(entry.start_time), 'dd.MM. HH:mm', { locale: de })}
                          </p>
                        </div>
                        <Badge variant="outline" className="text-xs">
                          {entry.formatted_duration}
                        </Badge>
                      </div>
                    ))}
                    {projectEntries.length > 5 && (
                      <p className="text-muted-foreground text-xs text-center pt-2">
                        +{projectEntries.length - 5} weitere Einträge
                      </p>
                    )}
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Contact Information */}
          {project.contact_id && (() => {
            const contact = contacts.find(c => c.id === project.contact_id);
            return contact ? (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <User className="h-5 w-5" />
                    Kontakt
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  {contact.name && (
                    <div>
                      <p className="text-sm font-medium">Name</p>
                      <p className="text-sm text-muted-foreground">{contact.name}</p>
                    </div>
                  )}
                  {contact.email && (
                    <div>
                      <p className="text-sm font-medium">E-Mail</p>
                      <a 
                        href={`mailto:${contact.email}`}
                        className="text-sm text-primary hover:underline"
                      >
                        {contact.email}
                      </a>
                    </div>
                  )}
                  {contact.phone && (
                    <div>
                      <p className="text-sm font-medium">Telefon</p>
                      <a 
                        href={`tel:${contact.phone}`}
                        className="text-sm text-primary hover:underline"
                      >
                        {contact.phone}
                      </a>
                    </div>
                  )}
                  {contact.company && contact.company !== project.client_name && (
                    <div>
                      <p className="text-sm font-medium">Unternehmen</p>
                      <p className="text-sm text-muted-foreground">{contact.company}</p>
                    </div>
                  )}
                  {contact.notes && (
                    <div>
                      <p className="text-sm font-medium">Notizen</p>
                      <p className="text-sm text-muted-foreground whitespace-pre-wrap">{contact.notes}</p>
                    </div>
                  )}
                </CardContent>
              </Card>
            ) : null;
          })()}

        </div>
      </div>
    </div>
  );
};

export default ActiveProjectDetails;