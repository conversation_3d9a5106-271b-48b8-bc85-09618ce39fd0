import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { toast } from '@/lib/toast';
import { AccessControl } from '@/lib/access-control';
import { 
  Loader2, 
  Shield, 
  Key,
  ArrowRight,
  Lock
} from 'lucide-react';

const AccessCode = () => {
  const [code, setCode] = useState('');
  const [loading, setLoading] = useState(false);
  const navigate = useNavigate();

  useEffect(() => {
    // If user already has access, redirect to auth
    if (AccessControl.hasAccess()) {
      navigate('/auth', { replace: true });
    }
  }, [navigate]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!code.trim()) {
      toast.error('Code erforderlich', 'Bitte geben Sie den Zugriffscode ein.');
      return;
    }

    setLoading(true);

    // Small delay for better UX
    await new Promise(resolve => setTimeout(resolve, 800));

    const isValid = AccessControl.validateAndGrantAccess(code);

    if (isValid) {
      toast.success('Zugriff gewährt', 'Willkommen zur Beta-Version von Lanzr!');
      navigate('/auth', { replace: true });
    } else {
      toast.error('Ungültiger Code', 'Der eingegebene Zugriffscode ist nicht korrekt.');
      setCode(''); // Clear the input
    }

    setLoading(false);
  };

  return (
    <div className="min-h-screen relative overflow-hidden">
      {/* Background */}
      <div className="absolute inset-0 bg-gradient-to-br from-background via-background to-muted">
        <div className="absolute inset-0 bg-[radial-gradient(circle_at_20%_80%,_hsl(var(--primary))_0%,_transparent_50%),_radial-gradient(circle_at_80%_20%,_hsl(var(--primary-glow))_0%,_transparent_50%)] opacity-20" />
        <div className="absolute inset-0 bg-grid-white/[0.02] bg-[size:50px_50px]" />
        
        {/* Static Elements */}
        <div className="absolute top-20 left-20 w-72 h-72 bg-primary/10 rounded-full blur-3xl" />
        <div className="absolute bottom-20 right-20 w-96 h-96 bg-primary-glow/10 rounded-full blur-3xl" />
      </div>

      {/* Main Content */}
      <div className="relative z-10 min-h-screen flex items-center justify-center p-4 sm:p-6">
        <div className="w-full max-w-sm mx-auto">
          {/* Header */}
          <div className="text-center mb-6">
            <div className="inline-flex items-center justify-center mb-4">
              <img src="/logo.png" alt="Lanzr" className="h-12 object-contain dark:hidden" />
              <img src="/logo_dark.png" alt="Lanzr" className="h-12 object-contain hidden dark:block" />
            </div>
          </div>

          {/* Access Code Card */}
          <Card className="backdrop-blur-sm bg-card/80 border-0 shadow-2xl shadow-black/10 dark:shadow-black/20">
            <CardHeader className="text-center pb-3">
              <CardTitle className="flex items-center justify-center gap-2 text-lg">
                <Lock className="w-4 h-4 text-primary" />
                Zugriffscode erforderlich
              </CardTitle>
              <CardDescription className="text-xs">
                Diese Anwendung befindet sich derzeit in der Beta-Phase. 
                Nur autorisierte Nutzer haben Zugriff.
              </CardDescription>
            </CardHeader>
            <CardContent className="px-4 pb-4">
              <form onSubmit={handleSubmit} className="space-y-4">
                {/* Code Input Field */}
                <div className="space-y-2">
                  <Label htmlFor="access-code" className="text-sm font-medium flex items-center gap-2">
                    <Key className="w-4 h-4 text-primary" />
                    Zugriffscode
                  </Label>
                  <div className="relative">
                    <Input
                      id="access-code"
                      type="text"
                      value={code}
                      onChange={(e) => setCode(e.target.value.toUpperCase())}
                      className="pl-4 pr-4 py-2 h-10 border-2 focus:border-primary transition-colors text-center text-base font-mono tracking-wider"
                      placeholder="XXXX-XXXX"
                      required
                      maxLength={20}
                      autoComplete="off"
                      disabled={loading}
                    />
                  </div>
                  <p className="text-xs text-muted-foreground text-center">
                    Der Code wird automatisch in Großbuchstaben umgewandelt
                  </p>
                </div>

                {/* Submit Button */}
                <Button
                  type="submit"
                  disabled={loading || !code.trim()}
                  className="w-full h-10 text-sm font-medium bg-gradient-to-r from-primary to-primary-glow hover:from-primary-glow hover:to-primary transition-all duration-300 shadow-lg shadow-primary/25"
                >
                  {loading ? (
                    <div className="flex items-center gap-2">
                      <Loader2 className="w-4 h-4 animate-spin" />
                      Überprüfung läuft...
                    </div>
                  ) : (
                    <div className="flex items-center gap-2">
                      Zugang freischalten
                      <ArrowRight className="w-4 h-4" />
                    </div>
                  )}
                </Button>
              </form>

              {/* Info Section */}
              <div className="mt-4 p-3 bg-muted/50 rounded-lg">
                <div className="flex items-start gap-2">
                  <Shield className="w-4 h-4 text-primary flex-shrink-0 mt-0.5" />
                  <div className="space-y-0.5">
                    <h4 className="font-medium text-xs">Beta-Programm</h4>
                    <p className="text-xs text-muted-foreground">
                      Haben Sie keinen Zugriffscode? Kontaktieren Sie das Lanzr-Team 
                      für eine Beta-Einladung.
                    </p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Footer */}
          <div className="text-center mt-6 text-sm text-muted-foreground">
            <p>© 2025 Lanzr. Professionelles Projektmanagement für Freelancer.</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AccessCode;