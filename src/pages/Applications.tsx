import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useApplications } from '@/hooks/useApplications';
import { usePagination } from '@/hooks/usePagination';
import { PageLayout } from '@/components/layout/PageLayout';
import { useCurrentPageConfig } from '@/hooks/usePageConfig';

import { ProjectCard } from '@/components/projects/ProjectCard';
import { DashboardStats } from '@/components/dashboard/DashboardStats';
import { ProjectTimeline } from '@/components/projects/ProjectTimeline';
import { ApplicationWithContact, ApplicationUpdateHandler } from '@/types/applications';
import { useTranslation } from 'react-i18next';
import { useApplicationStatusLabel, usePriorityLabel } from '@/lib/translations';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { MarkdownRenderer } from '@/components/ui/markdown-renderer';
import { Plus, Search, Filter, User, Mail, Phone, Building2 } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { ProjectPagination } from '@/components/ui/project-pagination';
import { APPLICATION_STATUS_LABELS, APPLICATION_STATUS_COLORS, WORK_LOCATION_LABELS, WORK_LOCATION_COLORS } from '@/types/applications';
import { format } from 'date-fns';
import { de } from 'date-fns/locale';

const Applications = () => {
  const navigate = useNavigate();
  const pageConfig = useCurrentPageConfig();
  const { t } = useTranslation(['forms', 'common']);
  const tPages = useTranslation('pages').t;
  const getStatusLabel = useApplicationStatusLabel();
  const getPriorityLabel = usePriorityLabel();
  const { 
    data: applications = [], 
    isLoading,
    updateApplication,
    deleteApplication,
    isUpdating,
    isDeleting
  } = useApplications();
  const [viewingApplication, setViewingApplication] = useState<ApplicationWithContact | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');

  const filteredApplications = applications.filter(application => {
    const searchLower = searchTerm.toLowerCase();
    const matchesSearch = searchTerm === '' || 
      application.project_name.toLowerCase().includes(searchLower) ||
      application.company_name.toLowerCase().includes(searchLower) ||
      application.project_description?.toLowerCase().includes(searchLower) ||
      application.budget_range?.toLowerCase().includes(searchLower) ||
      application.source?.toLowerCase().includes(searchLower) ||
      application.notes?.toLowerCase().includes(searchLower) ||
      application.work_location_notes?.toLowerCase().includes(searchLower) ||
      application.required_skills?.some(skill => skill.toLowerCase().includes(searchLower)) ||
      // Search in contact data
      application.contact?.name?.toLowerCase().includes(searchLower) ||
      application.contact?.email?.toLowerCase().includes(searchLower) ||
      application.contact?.phone?.toLowerCase().includes(searchLower) ||
      application.contact?.company?.toLowerCase().includes(searchLower);
    const matchesStatus = statusFilter === 'all' || application.status === statusFilter;
    return matchesSearch && matchesStatus;
  });

  const pagination = usePagination(filteredApplications, { itemsPerPage: 6 });

  const handleCreateApplication = () => {
    navigate('/applications/new');
  };

  const handleEditApplication = (application: ApplicationWithContact) => {
    navigate(`/applications/edit/${application.id}`);
  };

  const handleViewApplication = (application: ApplicationWithContact) => {
    navigate(`/applications/${application.id}`);
  };

  const handleStatusUpdate: ApplicationUpdateHandler = async (applicationId, updateData, statusNotes) => {
    try {
      // Status notes should NOT be saved to project_applications table
      // They are only for activity logging in project_activities table
      await updateApplication({ 
        id: applicationId, 
        updates: updateData, 
        statusNotes: statusNotes 
      });
    } catch (error) {
      console.error('Error updating application status:', error);
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto"></div>
          <p className="mt-4 text-muted-foreground">{tPages('loading.applications')}</p>
        </div>
      </div>
    );
  }

  return (
    <PageLayout
      title={pageConfig.title}
      description={pageConfig.description}
      headerActions={
        <Button onClick={handleCreateApplication} className="flex items-center gap-2">
          <Plus className="h-4 w-4" />
          {tPages('applications.new_project_button')}
        </Button>
      }
    >

        <DashboardStats projects={applications} />

        {/* Search and Filter */}
        <div className="mt-6">
          {/* Mobile: Single row with search and filter icon */}
          <div className="flex items-center gap-3 sm:hidden">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
              <Input
                placeholder={t('forms:search.projects')}
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-9"
              />
            </div>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline" size="icon" className="flex-shrink-0">
                  <Filter className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-48">
                <DropdownMenuItem 
                  onClick={() => setStatusFilter('all')}
                  className={statusFilter === 'all' ? 'bg-accent' : ''}
                >
                  {tPages('applications.all_status')}
                </DropdownMenuItem>
                {Object.entries(APPLICATION_STATUS_LABELS).map(([value, label]) => (
                  <DropdownMenuItem 
                    key={value} 
                    onClick={() => setStatusFilter(value)}
                    className={statusFilter === value ? 'bg-accent' : ''}
                  >
                    {getStatusLabel(value)}
                  </DropdownMenuItem>
                ))}
              </DropdownMenuContent>
            </DropdownMenu>
          </div>

          {/* Desktop: Original layout */}
          <div className="hidden sm:flex sm:flex-row sm:items-center gap-4">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
              <Input
                placeholder={t('forms:search.projects_detailed')}
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-9"
              />
            </div>
            <div className="flex items-center gap-2">
              <Filter className="h-4 w-4 text-muted-foreground" />
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger className="w-[180px]">
                  <SelectValue placeholder={t('forms:search.all_status')} />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">{tPages('applications.all_status')}</SelectItem>
                  {Object.entries(APPLICATION_STATUS_LABELS).map(([value, label]) => (
                    <SelectItem key={value} value={value}>
                      {getStatusLabel(value)}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
        </div>


        {/* Applications Grid */}
        <div className="mt-6">
        {filteredApplications.length === 0 ? (
          <div className="text-center py-12">
            <div className="mx-auto w-24 h-24 bg-muted rounded-full flex items-center justify-center mb-4">
              <Building2 className="h-12 w-12 text-muted-foreground" />
            </div>
            <h3 className="text-lg font-semibold mb-2">
              {searchTerm || statusFilter !== 'all' ? tPages('applications.no_projects_found') : tPages('applications.no_projects_yet')}
            </h3>
            <p className="text-muted-foreground mb-4">
              {searchTerm || statusFilter !== 'all' 
                ? tPages('applications.try_different_search')
                : tPages('applications.create_first_project')
              }
            </p>
            {(!searchTerm && statusFilter === 'all') && (
              <Button onClick={handleCreateApplication}>
                <Plus className="h-4 w-4 mr-2" />
                {tPages('applications.create_new_project')}
              </Button>
            )}
          </div>
        ) : (
          <>
            <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
              {pagination.paginatedItems.map((application) => (
                <ProjectCard
                  key={application.id}
                  project={application}
                  onEdit={handleEditApplication}
                  onView={handleViewApplication}
                  onDelete={(id) => deleteApplication(id)}
                  onUpdateProject={handleStatusUpdate}
                  isUpdating={isUpdating}
                  isDeleting={isDeleting}
                />
              ))}
            </div>
            
            <div className="flex justify-center sm:justify-end mt-6 mb-8">
              <ProjectPagination 
            currentPage={pagination.currentPage}
            totalPages={pagination.totalPages}
            totalItems={pagination.totalItems}
            startItemIndex={pagination.startItemIndex}
            endItemIndex={pagination.endItemIndex}
            hasNextPage={pagination.hasNextPage}
            hasPreviousPage={pagination.hasPreviousPage}
            onPageChange={pagination.goToPage}
            onNextPage={pagination.goToNextPage}
            onPreviousPage={pagination.goToPreviousPage}
            onFirstPage={pagination.goToFirstPage}
            onLastPage={pagination.goToLastPage}
          />
            </div>
          </>
        )}
        </div>

        {/* Application Details Modal */}
        <Dialog open={!!viewingApplication} onOpenChange={() => setViewingApplication(null)}>
          <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle>{viewingApplication?.project_name}</DialogTitle>
            </DialogHeader>
            {viewingApplication && (
              <div className="space-y-6">
                <div className="grid gap-4 md:grid-cols-2">
                  <div>
                    <h4 className="font-semibold mb-2">{tPages('applications.project_details')}</h4>
                    <div className="space-y-2">
                      <div className="flex items-center gap-2">
                        <Building2 className="h-4 w-4 text-muted-foreground" />
                        <span className="font-medium">{viewingApplication.company_name}</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <Badge 
                          variant="secondary"
                          style={{
                            backgroundColor: APPLICATION_STATUS_COLORS[viewingApplication.status],
                            color: 'white'
                          }}
                        >
                          {getStatusLabel(viewingApplication.status)}
                        </Badge>
                      </div>
                      {viewingApplication.budget_range && (
                        <p className="text-sm">
                          <span className="font-medium">{tPages('applications.budget')}:</span> {viewingApplication.budget_range}
                        </p>
                      )}
                    </div>
                  </div>

                  {viewingApplication.contact && (
                    <div>
                      <h4 className="font-semibold mb-2">{tPages('applications.contact')}</h4>
                      <div className="space-y-2">
                        {viewingApplication.contact.name && (
                          <div className="flex items-center gap-2">
                            <User className="h-4 w-4 text-muted-foreground" />
                            <span>{viewingApplication.contact.name}</span>
                          </div>
                        )}
                        {viewingApplication.contact.email && (
                          <div className="flex items-center gap-2">
                            <Mail className="h-4 w-4 text-muted-foreground" />
                            <span>{viewingApplication.contact.email}</span>
                          </div>
                        )}
                        {viewingApplication.contact.phone && (
                          <div className="flex items-center gap-2">
                            <Phone className="h-4 w-4 text-muted-foreground" />
                            <span>{viewingApplication.contact.phone}</span>
                          </div>
                        )}
                      </div>
                    </div>
                  )}
                </div>

                {viewingApplication.project_description && (
                  <div>
                    <h4 className="font-semibold mb-2">{tPages('applications.description')}</h4>
                    <MarkdownRenderer content={viewingApplication.project_description} />
                  </div>
                )}

                {viewingApplication.required_skills && viewingApplication.required_skills.length > 0 && (
                  <div>
                    <h4 className="font-semibold mb-2">{tPages('applications.required_skills')}</h4>
                    <div className="flex flex-wrap gap-2">
                      {viewingApplication.required_skills.map((skill, index) => (
                        <Badge key={index} variant="outline">{skill}</Badge>
                      ))}
                    </div>
                  </div>
                )}

                <ProjectTimeline projectId={viewingApplication.id} />
              </div>
            )}
          </DialogContent>
        </Dialog>
    </PageLayout>
  );
};

export default Applications;