import { usePara<PERSON>, useNavigate } from 'react-router-dom';
import { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '@/components/ui/accordion';
import { Separator } from '@/components/ui/separator';
import { useApplications } from '@/hooks/useApplications';
import { ProjectTimeline } from '@/components/projects/ProjectTimeline';
import { StatusNotesDialog } from '@/components/projects/StatusNotesDialog';
import { StatusUpdateButton } from '@/components/projects/StatusUpdateButton';
import { DeleteConfirmationDialog } from '@/components/projects/DeleteConfirmationDialog';
import {
  ArrowLeft,
  Edit2,
  Trash2,
  Calendar,
  MapPin,
  Clock,
  Euro,
  User,
  Briefcase,
  Globe,
  Mail,
  Phone,
  ExternalLink,
  Activity,
  TrendingUp,
  CheckCircle,
  AlertCircle,
  Building,
  Code,
  FileText,
  Target,
  Plus,
  Sparkles
} from 'lucide-react';
import { APPLICATION_STATUS_COLORS, APPLICATION_STATUS_LABELS, ApplicationStatus } from '@/types/applications';
import { FollowUpTimeline } from '@/components/followup/FollowUpTimeline';
import { FollowUpScheduler } from '@/components/followup/FollowUpScheduler';
import { FollowUpDetailsCard } from '@/components/followup/FollowUpDetailsCard';
import { CommunicationQuickActions } from '@/components/contacts/CommunicationQuickActions';
import { MatchScoreIndicator } from '@/components/ui/match-score-indicator';
import { useMatchScore } from '@/hooks/useMatchScore';
import { format } from 'date-fns';
import { de } from 'date-fns/locale';
import ReactMarkdown from 'react-markdown';
import { useTranslation } from '@/hooks/useTranslation';
import { useApplicationStatusLabel } from '@/lib/translations';

export default function ProjectDetails() {
  const { id } = useParams();
  const navigate = useNavigate();
  const [statusDialogOpen, setStatusDialogOpen] = useState(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [newStatus, setNewStatus] = useState<ApplicationStatus>('not_applied');
  
  const { data: applications = [], isLoading: applicationsLoading, updateApplication, deleteApplication, isUpdating, isDeleting, refetch } = useApplications();
  const { calculateMatchScore, isCalculating } = useMatchScore();
  const { t: tPages } = useTranslation('pages');
  const getStatusLabel = useApplicationStatusLabel();
  
  // Get logical next statuses based on current status
  const getNextStatuses = (currentStatus: ApplicationStatus): ApplicationStatus[] => {
    const statusFlow: Record<ApplicationStatus, ApplicationStatus[]> = {
      'not_applied': ['recommended', 'recruiter_contacted', 'application_sent'],
      'recommended': ['recruiter_contacted', 'application_sent'],
      'recruiter_contacted': ['application_sent', 'rejected'],
      'application_sent': ['inquiry_received', 'interview_scheduled', 'rejected'],
      'inquiry_received': ['interview_scheduled', 'rejected'],
      'interview_scheduled': ['interview_completed', 'rejected'],
      'interview_completed': ['offer_received', 'rejected'],
      'offer_received': ['project_completed', 'rejected'],
      'rejected': [], // Terminal status
      'project_completed': [] // Terminal status
    };
    
    // Type guard to ensure we have a valid status
    if (!currentStatus || !(currentStatus in statusFlow)) {
      console.warn('Invalid application status:', currentStatus);
      return [];
    }
    
    return statusFlow[currentStatus];
  };
  
  const application = applications?.find(p => p.id === id);

  // Show loading state while applications are being fetched
  if (applicationsLoading) {
    return (
      <div className="flex-1 p-6">
        <div className="max-w-4xl mx-auto">
          <div className="text-center py-12">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
            <p className="text-muted-foreground">{tPages('application_details.loading_application')}</p>
          </div>
        </div>
      </div>
    );
  }

  // Show not found only after loading is complete
  if (!application) {
    return (
      <div className="flex-1 p-6">
        <div className="max-w-4xl mx-auto">
          <div className="text-center py-12">
            <AlertCircle className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <h2 className="text-2xl font-semibold mb-2">{tPages('application_details.application_not_found')}</h2>
            <p className="text-muted-foreground mb-6">
              {tPages('application_details.application_not_found_description')}
            </p>
            <Button onClick={() => navigate('/applications')} variant="outline">
              <ArrowLeft className="h-4 w-4 mr-2" />
              {tPages('application_details.back_to_overview')}
            </Button>
          </div>
        </div>
      </div>
    );
  }

  const handleStatusChange = (status: ApplicationStatus) => {
    setNewStatus(status);
    setStatusDialogOpen(true);
  };

  const handleStatusConfirm = async (notes?: string, notesDate?: string) => {
    try {
      await updateApplication({ 
        id: application.id, 
        updates: { 
          status: newStatus
        },
        statusNotes: notes || notesDate ? {
          notes,
          notes_date: notesDate
        } : undefined
      });
      setStatusDialogOpen(false);
    } catch (error) {
      console.error('Error updating application status:', error);
      // Keep dialog open on error
    }
  };

  const handleDelete = () => {
    setDeleteDialogOpen(true);
  };

  const handleDeleteConfirm = async () => {
    await deleteApplication(application.id);
    navigate('/applications');
    setDeleteDialogOpen(false);
  };

  const handleDeleteCancel = () => {
    setDeleteDialogOpen(false);
  };

  const formatDate = (dateString?: string) => {
    if (!dateString) return tPages('application_details.not_specified');
    return format(new Date(dateString), 'dd. MMMM yyyy', { locale: de });
  };

  const handleCalculateMatchScore = async () => {
    const result = await calculateMatchScore(application.id, true);
    if (result) {
      // Refetch applications to get updated match score
      refetch();
    }
  };

  return (
    <div className="min-h-screen bg-background">
        <div className="container mx-auto px-2 sm:px-6 pb-3 sm:pb-6 max-w-full overflow-x-hidden">
          {/* Header */}
          <div className="flex items-center justify-between mb-6 sm:mb-8">
            <Button 
              variant="ghost" 
              onClick={() => navigate('/applications')}
              className="flex items-center gap-2"
            >
              <ArrowLeft className="h-4 w-4" />
              {tPages('application_details.back_to_overview')}
            </Button>
            <div className="flex gap-2">
              <Button 
                onClick={() => navigate(`/applications/edit/${application.id}`)}
                variant="outline"
                className="flex items-center gap-0 sm:gap-2 p-2 sm:px-4"
              >
                <Edit2 className="h-4 w-4" />
                <span className="hidden sm:inline">{tPages('application_details.edit')}</span>
              </Button>
              <Button 
                onClick={handleDelete}
                variant="destructive"
                disabled={isDeleting}
                className="flex items-center gap-0 sm:gap-2 p-2 sm:px-4"
              >
                <Trash2 className="h-4 w-4" />
                <span className="hidden sm:inline">{tPages('application_details.delete')}</span>
              </Button>
            </div>
          </div>

          {/* Modern Hero Section */}
          <div className="mb-6 sm:mb-8">
            {/* Main Card */}
            <div className="bg-card rounded-xl border shadow-sm overflow-hidden">
              
              {/* Header Bar */}
              <div className="bg-muted/30 px-4 sm:px-6 py-3 border-b">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <Badge 
                      className={`${APPLICATION_STATUS_COLORS[application.status]} px-3 py-1 text-xs font-medium`}
                    >
                      {getStatusLabel(application.status)}
                    </Badge>
                  </div>
                  
                  {/* Match Score - Always visible, clickable */}
                  <div 
                    className="cursor-pointer active:scale-95 transition-transform"
                    onClick={handleCalculateMatchScore}
                    disabled={isCalculating}
                  >
                    <MatchScoreIndicator 
                      score={application.match_score || null} 
                      size="sm"
                      showLabel={false}
                    />
                  </div>
                </div>
              </div>

              {/* Content */}
              <div className="p-4 sm:p-6">
                <div className="space-y-4">
                  {/* Title */}
                  <div>
                    <h1 className="text-xl sm:text-2xl lg:text-3xl font-bold text-foreground leading-tight">
                      {application.project_name}
                    </h1>
                  </div>

                  {/* Info Grid */}
                  <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
                    {/* Company - Most important base info */}
                    <div className="flex items-center gap-3 p-3 bg-muted/50 rounded-lg">
                      <div className="w-8 h-8 bg-primary/10 rounded-lg flex items-center justify-center flex-shrink-0">
                        <Building className="h-4 w-4 text-primary" />
                      </div>
                      <div className="min-w-0">
                        <div className="text-xs text-muted-foreground uppercase tracking-wide">{tPages('application_details.company')}</div>
                        <div className="text-sm font-medium text-foreground truncate">
                          {application.company_name}
                        </div>
                      </div>
                    </div>

                    {/* Application Date - When applied */}
                    {application.application_date && (
                      <div className="flex items-center gap-3 p-3 bg-muted/50 rounded-lg">
                        <div className="w-8 h-8 bg-blue-500/10 rounded-lg flex items-center justify-center flex-shrink-0">
                          <Calendar className="h-4 w-4 text-blue-600 dark:text-blue-400" />
                        </div>
                        <div className="min-w-0">
                          <div className="text-xs text-muted-foreground uppercase tracking-wide">{tPages('application_details.application')}</div>
                          <div className="text-sm font-medium text-foreground">
                            {format(new Date(application.application_date), 'dd.MM.yyyy')}
                          </div>
                        </div>
                      </div>
                    )}

                    {/* Start Date - Project timeline begins */}
                    {application.project_start_date && (
                      <div className="flex items-center gap-3 p-3 bg-muted/50 rounded-lg">
                        <div className="w-8 h-8 bg-green-500/10 rounded-lg flex items-center justify-center flex-shrink-0">
                          <Clock className="h-4 w-4 text-green-600 dark:text-green-400" />
                        </div>
                        <div className="min-w-0">
                          <div className="text-xs text-muted-foreground uppercase tracking-wide">{tPages('application_details.start')}</div>
                          <div className="text-sm font-medium text-foreground">
                            {format(new Date(application.project_start_date), 'dd.MM.yyyy')}
                          </div>
                        </div>
                      </div>
                    )}

                    {/* End Date - Project timeline ends */}
                    {application.project_end_date && (
                      <div className="flex items-center gap-3 p-3 bg-muted/50 rounded-lg">
                        <div className="w-8 h-8 bg-orange-500/10 rounded-lg flex items-center justify-center flex-shrink-0">
                          <CheckCircle className="h-4 w-4 text-orange-600 dark:text-orange-400" />
                        </div>
                        <div className="min-w-0">
                          <div className="text-xs text-muted-foreground uppercase tracking-wide">{tPages('application_details.end')}</div>
                          <div className="text-sm font-medium text-foreground">
                            {format(new Date(application.project_end_date), 'dd.MM.yyyy')}
                          </div>
                        </div>
                      </div>
                    )}

                    {/* Budget - Financial information */}
                    {application.budget_range && (
                      <div className="flex items-center gap-3 p-3 bg-muted/50 rounded-lg">
                        <div className="w-8 h-8 bg-emerald-500/10 rounded-lg flex items-center justify-center flex-shrink-0">
                          <Euro className="h-4 w-4 text-emerald-600 dark:text-emerald-400" />
                        </div>
                        <div className="min-w-0">
                          <div className="text-xs text-muted-foreground uppercase tracking-wide">{tPages('application_details.budget')}</div>
                          <div className="text-sm font-medium text-foreground truncate">
                            {application.budget_range}
                          </div>
                        </div>
                      </div>
                    )}

                    {/* Work Location - How the work is done */}
                    {application.work_location_type && (
                      <div className="flex items-center gap-3 p-3 bg-muted/50 rounded-lg">
                        <div className="w-8 h-8 bg-purple-500/10 rounded-lg flex items-center justify-center flex-shrink-0">
                          <MapPin className="h-4 w-4 text-purple-600 dark:text-purple-400" />
                        </div>
                        <div className="min-w-0">
                          <div className="text-xs text-muted-foreground uppercase tracking-wide">{tPages('application_details.work_location')}</div>
                          <div className="text-sm font-medium text-foreground truncate">
                            {application.work_location_type === 'remote' && tPages('application_details.remote')}
                            {application.work_location_type === 'onsite' && tPages('application_details.onsite')}
                            {application.work_location_type === 'hybrid' && tPages('application_details.hybrid', { percentage: application.remote_percentage || 0 })}
                            {application.work_location_type === 'flexible' && tPages('application_details.flexible')}
                          </div>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Main Content Grid */}
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 sm:gap-8">
            {/* Left Column - Application Details */}
            <div className="lg:col-span-2 space-y-6 order-1 lg:order-1">
              {/* Application Information - Accordion Layout */}
              <Card className="shadow-sm">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <FileText className="h-5 w-5" />
                    {tPages('application_details.application_information')}
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <Accordion type="multiple" defaultValue={["description"]} className="w-full">
                    {application.project_description && (
                      <AccordionItem value="description">
                        <AccordionTrigger className="flex items-center justify-between hover:no-underline text-left w-full">
                          <div className="flex items-center gap-2">
                            <Target className="h-4 w-4" />
                            <span>{tPages('application_details.project_description')}</span>
                          </div>
                        </AccordionTrigger>
                        <AccordionContent>
                          <div className="bg-muted/30 rounded-lg p-4 mt-2">
                            <div className="text-sm leading-relaxed prose prose-sm dark:prose-invert max-w-none">
                              <ReactMarkdown
                                disallowedElements={['script', 'iframe', 'object', 'embed', 'form', 'input']}
                                unwrapDisallowed={true}
                                components={{
                                  strong: ({ children }) => <strong className="font-bold text-foreground">{children}</strong>,
                                  h1: ({ children }) => <h1 className="text-lg font-bold mb-2 text-foreground">{children}</h1>,
                                  h2: ({ children }) => <h2 className="text-base font-bold mb-2 text-foreground">{children}</h2>,
                                  h3: ({ children }) => <h3 className="text-sm font-bold mb-1 text-foreground">{children}</h3>,
                                  p: ({ children }) => <p className="mb-2 text-foreground last:mb-0">{children}</p>,
                                  ul: ({ children }) => <ul className="list-disc pl-4 mb-2 space-y-1">{children}</ul>,
                                  ol: ({ children }) => <ol className="list-decimal pl-4 mb-2 space-y-1">{children}</ol>,
                                  li: ({ children }) => <li className="text-foreground">{children}</li>,
                                  hr: () => <hr className="my-4 border-border" />,
                                  code: ({ children }) => <code className="bg-muted px-1 py-0.5 rounded text-xs font-mono">{children}</code>,
                                  pre: ({ children }) => <pre className="bg-muted p-3 rounded-lg overflow-x-auto text-xs">{children}</pre>,
                                  a: ({ children, href }) => (
                                    <a 
                                      href={href} 
                                      target="_blank" 
                                      rel="noopener noreferrer"
                                      className="text-primary hover:underline"
                                    >
                                      {children}
                                    </a>
                                  )
                                }}
                              >
                                {application.project_description}
                              </ReactMarkdown>
                            </div>
                          </div>
                        </AccordionContent>
                      </AccordionItem>
                    )}

                    {application.application_text && (
                      <AccordionItem value="application">
                        <AccordionTrigger className="flex items-center justify-between hover:no-underline text-left w-full">
                          <div className="flex items-center gap-2">
                            <FileText className="h-4 w-4" />
                            <span>{tPages('application_details.application_text')}</span>
                          </div>
                        </AccordionTrigger>
                        <AccordionContent>
                          <div className="bg-muted/30 rounded-lg p-4 mt-2">
                            <div className="text-sm leading-relaxed prose prose-sm dark:prose-invert max-w-none">
                              <ReactMarkdown
                                disallowedElements={['script', 'iframe', 'object', 'embed', 'form', 'input']}
                                unwrapDisallowed={true}
                                components={{
                                  strong: ({ children }) => <strong className="font-bold text-foreground">{children}</strong>,
                                  h1: ({ children }) => <h1 className="text-lg font-bold mb-2 text-foreground">{children}</h1>,
                                  h2: ({ children }) => <h2 className="text-base font-bold mb-2 text-foreground">{children}</h2>,
                                  h3: ({ children }) => <h3 className="text-sm font-bold mb-1 text-foreground">{children}</h3>,
                                  p: ({ children }) => <p className="mb-2 text-foreground last:mb-0">{children}</p>,
                                  ul: ({ children }) => <ul className="list-disc pl-4 mb-2 space-y-1">{children}</ul>,
                                  ol: ({ children }) => <ol className="list-decimal pl-4 mb-2 space-y-1">{children}</ol>,
                                  li: ({ children }) => <li className="text-foreground">{children}</li>,
                                  hr: () => <hr className="my-4 border-border" />,
                                  code: ({ children }) => <code className="bg-muted px-1 py-0.5 rounded text-xs font-mono">{children}</code>,
                                  pre: ({ children }) => <pre className="bg-muted p-3 rounded-lg overflow-x-auto text-xs">{children}</pre>,
                                  a: ({ children, href }) => (
                                    <a 
                                      href={href} 
                                      target="_blank" 
                                      rel="noopener noreferrer"
                                      className="text-primary hover:underline"
                                    >
                                      {children}
                                    </a>
                                  )
                                }}
                              >
                                {application.application_text}
                              </ReactMarkdown>
                            </div>
                          </div>
                        </AccordionContent>
                      </AccordionItem>
                    )}


                    {/* Skills & Match Analysis Section in Accordion */}
                    {((application.required_skills && application.required_skills.length > 0) || (application.match_score && application.match_reasoning)) && (
                      <AccordionItem value="skills-match">
                        <AccordionTrigger className="flex items-center justify-between hover:no-underline text-left w-full">
                          <div className="flex items-center gap-2">
                            <Code className="h-4 w-4" />
                            <span>{tPages('application_details.skills_match_analysis')}</span>
                          </div>
                        </AccordionTrigger>
                        <AccordionContent>
                          <div className="space-y-4 mt-2">
                            {/* Skills */}
                            {application.required_skills && application.required_skills.length > 0 && (
                              <div>
                                <h5 className="font-medium mb-3 flex items-center gap-2">
                                  <Code className="h-4 w-4" />
                                  {tPages('application_details.required_skills')}
                                </h5>
                                <div className="flex flex-wrap gap-2">
                                  {application.required_skills.map((skill, index) => (
                                    <Badge key={index} variant="secondary" className="text-xs">
                                      {skill}
                                    </Badge>
                                  ))}
                                </div>
                              </div>
                            )}

                            {/* Match Score Reasoning */}
                            {application.match_score && application.match_reasoning && (
                              <div>
                                <h5 className="font-medium mb-3 flex items-center gap-2">
                                  <Sparkles className="h-4 w-4" />
                                  {tPages('application_details.skill_match_analysis')}
                                </h5>
                                <div className="bg-muted/30 rounded-lg p-4">
                                  <div className="text-sm leading-relaxed prose prose-sm dark:prose-invert max-w-none">
                                    <ReactMarkdown
                                      disallowedElements={['script', 'iframe', 'object', 'embed', 'form', 'input']}
                                      unwrapDisallowed={true}
                                      components={{
                                        h2: ({ children }) => <h2 className="text-base font-bold mb-3 text-foreground">{children}</h2>,
                                        h3: ({ children }) => <h3 className="text-sm font-bold mb-2 text-foreground">{children}</h3>,
                                        ul: ({ children }) => <ul className="list-none space-y-1 mb-3">{children}</ul>,
                                        li: ({ children }) => <li className="text-foreground">{children}</li>,
                              strong: ({ children }) => <strong className="font-bold text-foreground">{children}</strong>,
                              p: ({ children }) => <p className="mb-2 text-foreground last:mb-0">{children}</p>,
                            }}
                          >
                                      {application.match_reasoning}
                                    </ReactMarkdown>
                                  </div>
                                </div>
                              </div>
                            )}
                          </div>
                        </AccordionContent>
                      </AccordionItem>
                    )}

                    {/* Notes Section in Accordion */}
                    {application.notes && (
                      <AccordionItem value="notes">
                        <AccordionTrigger className="flex items-center justify-between hover:no-underline text-left w-full">
                          <div className="flex items-center gap-2">
                            <FileText className="h-4 w-4" />
                            <span>{tPages('application_details.notes')}</span>
                          </div>
                        </AccordionTrigger>
                        <AccordionContent>
                          <div className="bg-muted/30 rounded-lg p-4 mt-2">
                            <div className="text-sm leading-relaxed prose prose-sm dark:prose-invert max-w-none">
                              <ReactMarkdown
                                disallowedElements={['script', 'iframe', 'object', 'embed', 'form', 'input']}
                                unwrapDisallowed={true}
                            components={{
                              // Custom styling for markdown elements
                              strong: ({ children }) => <strong className="font-bold text-foreground">{children}</strong>,
                              h1: ({ children }) => <h1 className="text-lg font-bold mb-2 text-foreground">{children}</h1>,
                              h2: ({ children }) => <h2 className="text-base font-bold mb-2 text-foreground">{children}</h2>,
                              h3: ({ children }) => <h3 className="text-sm font-bold mb-1 text-foreground">{children}</h3>,
                              p: ({ children }) => <p className="mb-2 text-foreground last:mb-0">{children}</p>,
                              ul: ({ children }) => <ul className="list-disc pl-4 mb-2 space-y-1">{children}</ul>,
                              ol: ({ children }) => <ol className="list-decimal pl-4 mb-2 space-y-1">{children}</ol>,
                              li: ({ children }) => <li className="text-foreground">{children}</li>,
                              hr: () => <hr className="my-4 border-border" />,
                              code: ({ children }) => <code className="bg-muted px-1 py-0.5 rounded text-xs font-mono">{children}</code>,
                              pre: ({ children }) => <pre className="bg-muted p-3 rounded-lg overflow-x-auto text-xs">{children}</pre>,
                              a: ({ children, href }) => (
                                <a 
                                  href={href} 
                                  target="_blank" 
                                  rel="noopener noreferrer"
                                  className="text-primary hover:underline"
                                >
                                  {children}
                                </a>
                              )
                            }}
                          >
                                {application.notes}
                              </ReactMarkdown>
                            </div>
                          </div>
                        </AccordionContent>
                      </AccordionItem>
                    )}

                  </Accordion>
                </CardContent>
              </Card>

              {/* Activity Timeline - Desktop: in left column, Mobile: in right column after contact */}
              <div className="hidden lg:block">
                <ProjectTimeline 
                  projectId={application.id} 
                  projectName={application.project_name}
                />
              </div>
            </div>

            {/* Right Column - Contact & Actions */}
            <div className="space-y-6 order-2 lg:order-2">
              {/* Contact Information */}
              <Card className="shadow-sm">
                <CardHeader>
                  <CardTitle className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <User className="h-5 w-5" />
                      {tPages('application_details.contact_data')}
                    </div>
                    {application.contact && (
                      <CommunicationQuickActions 
                        contact={application.contact}
                        compact={true}
                        showLabel={false}
                        projectId={application.id}
                        projectName={application.project_name}
                        onSuccess={() => {
                          // Refresh data if needed
                          console.log('Communication created successfully');
                        }}
                      />
                    )}
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  {application.contact?.name && (
                    <div className="flex items-center gap-3">
                      <User className="h-4 w-4 text-muted-foreground" />
                      <button
                        onClick={() => navigate(`/contacts/${application.contact_id}`)}
                        className="text-sm text-primary hover:underline cursor-pointer text-left"
                      >
                        {application.contact.name}
                      </button>
                    </div>
                  )}
                  {application.contact?.email && (
                    <div className="flex items-center gap-3">
                      <Mail className="h-4 w-4 text-muted-foreground" />
                      <a 
                        href={`mailto:${application.contact.email}`}
                        className="text-sm text-primary hover:underline"
                      >
                        {application.contact.email}
                      </a>
                    </div>
                  )}
                  {application.contact?.phone && (
                    <div className="flex items-center gap-3">
                      <Phone className="h-4 w-4 text-muted-foreground" />
                      <a 
                        href={`tel:${application.contact.phone}`}
                        className="text-sm text-primary hover:underline"
                      >
                        {application.contact.phone}
                      </a>
                    </div>
                  )}
                  {application.listing_url && (
                    <div className="flex items-center gap-3">
                      <Globe className="h-4 w-4 text-muted-foreground" />
                      <a 
                        href={application.listing_url}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-sm text-primary hover:underline flex items-center gap-1"
                      >
                        {tPages('application_details.job_posting')}
                        <ExternalLink className="h-3 w-3" />
                      </a>
                    </div>
                  )}
                  {application.application_url && (
                    <div className="flex items-center gap-3">
                      <Globe className="h-4 w-4 text-muted-foreground" />
                      <a 
                        href={application.application_url}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-sm text-primary hover:underline flex items-center gap-1"
                      >
                        {tPages('application_details.application_portal')}
                        <ExternalLink className="h-3 w-3" />
                      </a>
                    </div>
                  )}
                  {application.source && (
                    <div className="flex items-center gap-3">
                      <Activity className="h-4 w-4 text-muted-foreground" />
                      <span className="text-sm text-muted-foreground">
                        {tPages('application_details.source', { source: application.source })}
                      </span>
                    </div>
                  )}
                </CardContent>
              </Card>

              {/* Activity Timeline - Mobile: after contact, Desktop: in left column */}
              <div className="lg:hidden">
                <ProjectTimeline 
                  projectId={application.id} 
                  projectName={application.project_name}
                />
              </div>

              {/* Quick Actions */}
              <Card className="shadow-sm">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <TrendingUp className="h-5 w-5" />
                    {tPages('application_details.actions')}
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <Button 
                    onClick={() => navigate('/calendar')}
                    variant="outline" 
                    className="w-full justify-start"
                  >
                    <Calendar className="h-4 w-4 mr-2" />
                    {tPages('application_details.open_calendar')}
                  </Button>
                  
                  {application.status !== 'rejected' && application.status !== 'project_completed' && (
                    <div className="space-y-3">
                      <Separator />
                      
                      {/* Status Update Dropdown */}
                      <div className="space-y-2">
                        <p className="text-xs text-muted-foreground font-medium">{tPages('application_details.status_change')}</p>
                        <StatusUpdateButton
                          project={application}
                          onUpdateProject={(id, updates, statusNotes) => 
                            updateApplication({ id, updates, statusNotes })
                          }
                          isUpdating={isUpdating}
                          className="text-sm"
                        />
                      </div>
                      
                      {/* Next Possible Statuses as Quick Buttons */}
                      <div className="space-y-2">
                        <p className="text-xs text-muted-foreground">{tPages('application_details.next_possible_status')}</p>
                        <div className="grid grid-cols-1 gap-1">
                          {getNextStatuses(application.status).length > 0 ? (
                            getNextStatuses(application.status).map((status) => (
                              <Button
                                key={status}
                                onClick={() => handleStatusChange(status)}
                                variant="ghost"
                                size="sm"
                                className="justify-start text-xs h-8"
                                disabled={isUpdating}
                                aria-label={`Status zu ${getStatusLabel(status)} ändern`}
                              >
                                <CheckCircle className="h-3 w-3 mr-2" />
                                {getStatusLabel(status)}
                              </Button>
                            ))
                          ) : (
                            <p className="text-xs text-muted-foreground italic">
                              {tPages('application_details.no_further_status_changes')}
                            </p>
                          )}
                        </div>
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>

              {/* Follow-up Management */}
              <FollowUpDetailsCard application={application} />
          </div>
        </div>

        {/* Status Change Dialog */}
        <StatusNotesDialog
          isOpen={statusDialogOpen}
          onClose={() => setStatusDialogOpen(false)}
          onConfirm={handleStatusConfirm}
          currentStatus={application.status}
          newStatus={newStatus}
          isLoading={isUpdating}
          projectName={application.project_name}
          companyName={application.company_name}
        />

        <DeleteConfirmationDialog
          isOpen={deleteDialogOpen}
          onClose={handleDeleteCancel}
          onConfirm={handleDeleteConfirm}
          projectName={application.project_name}
          companyName={application.company_name}
          isDeleting={isDeleting}
        />
      </div>
    </div>
  );
}