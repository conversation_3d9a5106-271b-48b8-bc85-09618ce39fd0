export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export type Database = {
  // Allows to automatically instantiate createClient with right options
  // instead of createClient<Database, { PostgrestVersion: 'XX' }>(URL, KEY)
  __InternalSupabase: {
    PostgrestVersion: "12.2.3 (519615d)"
  }
  public: {
    Tables: {
      calendar_events: {
        Row: {
          all_day: boolean
          color: string | null
          completed: boolean
          created_at: string
          created_automatically: boolean
          description: string | null
          end_date: string | null
          end_time: string | null
          event_type: string
          id: string
          location: string | null
          project_id: string | null
          reference_id: string | null
          reference_type: string | null
          reminder_enabled: boolean
          reminder_minutes_before: number | null
          source_status: string | null
          start_date: string
          start_time: string | null
          title: string
          updated_at: string
          user_id: string
        }
        Insert: {
          all_day?: boolean
          color?: string | null
          completed?: boolean
          created_at?: string
          created_automatically?: boolean
          description?: string | null
          end_date?: string | null
          end_time?: string | null
          event_type?: string
          id?: string
          location?: string | null
          project_id?: string | null
          reference_id?: string | null
          reference_type?: string | null
          reminder_enabled?: boolean
          reminder_minutes_before?: number | null
          source_status?: string | null
          start_date: string
          start_time?: string | null
          title: string
          updated_at?: string
          user_id: string
        }
        Update: {
          all_day?: boolean
          color?: string | null
          completed?: boolean
          created_at?: string
          created_automatically?: boolean
          description?: string | null
          end_date?: string | null
          end_time?: string | null
          event_type?: string
          id?: string
          location?: string | null
          project_id?: string | null
          reference_id?: string | null
          reference_type?: string | null
          reminder_enabled?: boolean
          reminder_minutes_before?: number | null
          source_status?: string | null
          start_date?: string
          start_time?: string | null
          title?: string
          updated_at?: string
          user_id?: string
        }
        Relationships: []
      }
      contact_communications: {
        Row: {
          communication_date: string
          communication_type: string
          contact_id: string
          created_at: string | null
          duration_minutes: number | null
          id: string
          is_project_related: boolean | null
          notes: string
          project_id: string | null
          subject: string | null
          summarized_notes: string | null
          updated_at: string | null
          user_id: string
        }
        Insert: {
          communication_date: string
          communication_type: string
          contact_id: string
          created_at?: string | null
          duration_minutes?: number | null
          id?: string
          is_project_related?: boolean | null
          notes: string
          project_id?: string | null
          subject?: string | null
          summarized_notes?: string | null
          updated_at?: string | null
          user_id: string
        }
        Update: {
          communication_date?: string
          communication_type?: string
          contact_id?: string
          created_at?: string | null
          duration_minutes?: number | null
          id?: string
          is_project_related?: boolean | null
          notes?: string
          project_id?: string | null
          subject?: string | null
          summarized_notes?: string | null
          updated_at?: string | null
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "contact_communications_contact_id_fkey"
            columns: ["contact_id"]
            isOneToOne: false
            referencedRelation: "contacts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "contact_communications_project_id_fkey"
            columns: ["project_id"]
            isOneToOne: false
            referencedRelation: "project_applications"
            referencedColumns: ["id"]
          },
        ]
      }
      contacts: {
        Row: {
          company: string | null
          created_at: string | null
          email: string | null
          id: string
          name: string | null
          notes: string | null
          phone: string | null
          successful_projects: number | null
          total_projects: number | null
          updated_at: string | null
          user_id: string
        }
        Insert: {
          company?: string | null
          created_at?: string | null
          email?: string | null
          id?: string
          name?: string | null
          notes?: string | null
          phone?: string | null
          successful_projects?: number | null
          total_projects?: number | null
          updated_at?: string | null
          user_id: string
        }
        Update: {
          company?: string | null
          created_at?: string | null
          email?: string | null
          id?: string
          name?: string | null
          notes?: string | null
          phone?: string | null
          successful_projects?: number | null
          total_projects?: number | null
          updated_at?: string | null
          user_id?: string
        }
        Relationships: []
      }
      follow_up_history: {
        Row: {
          application_id: string | null
          body: string
          created_at: string | null
          id: string
          response_date: string | null
          response_received: boolean | null
          sent_at: string
          subject: string
          template_id: string | null
          user_id: string | null
        }
        Insert: {
          application_id?: string | null
          body: string
          created_at?: string | null
          id?: string
          response_date?: string | null
          response_received?: boolean | null
          sent_at: string
          subject: string
          template_id?: string | null
          user_id?: string | null
        }
        Update: {
          application_id?: string | null
          body?: string
          created_at?: string | null
          id?: string
          response_date?: string | null
          response_received?: boolean | null
          sent_at?: string
          subject?: string
          template_id?: string | null
          user_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "follow_up_history_application_id_fkey"
            columns: ["application_id"]
            isOneToOne: false
            referencedRelation: "project_applications"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "follow_up_history_template_id_fkey"
            columns: ["template_id"]
            isOneToOne: false
            referencedRelation: "follow_up_templates"
            referencedColumns: ["id"]
          },
        ]
      }
      follow_up_schedule: {
        Row: {
          application_id: string | null
          created_at: string | null
          id: string
          response_received: boolean | null
          scheduled_date: string
          sent_at: string | null
          status: string | null
          template_id: string | null
          user_id: string | null
        }
        Insert: {
          application_id?: string | null
          created_at?: string | null
          id?: string
          response_received?: boolean | null
          scheduled_date: string
          sent_at?: string | null
          status?: string | null
          template_id?: string | null
          user_id?: string | null
        }
        Update: {
          application_id?: string | null
          created_at?: string | null
          id?: string
          response_received?: boolean | null
          scheduled_date?: string
          sent_at?: string | null
          status?: string | null
          template_id?: string | null
          user_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "follow_up_schedule_application_id_fkey"
            columns: ["application_id"]
            isOneToOne: false
            referencedRelation: "project_applications"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "follow_up_schedule_template_id_fkey"
            columns: ["template_id"]
            isOneToOne: false
            referencedRelation: "follow_up_templates"
            referencedColumns: ["id"]
          },
        ]
      }
      follow_up_templates: {
        Row: {
          body: string
          created_at: string | null
          id: string
          is_active: boolean | null
          name: string
          status_trigger: string
          subject: string
          trigger_days: number
          updated_at: string | null
          user_id: string | null
        }
        Insert: {
          body: string
          created_at?: string | null
          id?: string
          is_active?: boolean | null
          name: string
          status_trigger: string
          subject: string
          trigger_days?: number
          updated_at?: string | null
          user_id?: string | null
        }
        Update: {
          body?: string
          created_at?: string | null
          id?: string
          is_active?: boolean | null
          name?: string
          status_trigger?: string
          subject?: string
          trigger_days?: number
          updated_at?: string | null
          user_id?: string | null
        }
        Relationships: []
      }
      notifications: {
        Row: {
          action_label: string | null
          action_url: string | null
          created_at: string | null
          dismissed_at: string | null
          expires_at: string | null
          id: string
          message: string
          metadata: Json | null
          priority: string
          read_at: string | null
          related_entity_id: string | null
          status: string
          title: string
          type: string
          user_id: string
        }
        Insert: {
          action_label?: string | null
          action_url?: string | null
          created_at?: string | null
          dismissed_at?: string | null
          expires_at?: string | null
          id?: string
          message: string
          metadata?: Json | null
          priority?: string
          read_at?: string | null
          related_entity_id?: string | null
          status?: string
          title: string
          type: string
          user_id: string
        }
        Update: {
          action_label?: string | null
          action_url?: string | null
          created_at?: string | null
          dismissed_at?: string | null
          expires_at?: string | null
          id?: string
          message?: string
          metadata?: Json | null
          priority?: string
          read_at?: string | null
          related_entity_id?: string | null
          status?: string
          title?: string
          type?: string
          user_id?: string
        }
        Relationships: []
      }
      project_activities: {
        Row: {
          activity_type: string
          created_at: string
          description: string
          id: string
          notes: string | null
          notes_date: string | null
          project_id: string
          user_id: string
        }
        Insert: {
          activity_type: string
          created_at?: string
          description: string
          id?: string
          notes?: string | null
          notes_date?: string | null
          project_id: string
          user_id: string
        }
        Update: {
          activity_type?: string
          created_at?: string
          description?: string
          id?: string
          notes?: string | null
          notes_date?: string | null
          project_id?: string
          user_id?: string
        }
        Relationships: []
      }
      project_applications: {
        Row: {
          application_date: string | null
          application_text: string | null
          budget_range: string | null
          company_name: string
          contact_id: string | null
          created_at: string
          id: string
          interview_date: string | null
          interview_time: string | null
          listing_url: string | null
          match_calculated_at: string | null
          match_reasoning: string | null
          match_score: number | null
          notes: string | null
          project_description: string | null
          project_end_date: string | null
          project_name: string
          project_start_date: string | null
          remote_percentage: number | null
          required_skills: string[] | null
          source: string | null
          status: string
          updated_at: string
          user_id: string
          work_location_notes: string | null
          work_location_type: string | null
        }
        Insert: {
          application_date?: string | null
          application_text?: string | null
          budget_range?: string | null
          company_name: string
          contact_id?: string | null
          created_at?: string
          id?: string
          interview_date?: string | null
          interview_time?: string | null
          listing_url?: string | null
          match_calculated_at?: string | null
          match_reasoning?: string | null
          match_score?: number | null
          notes?: string | null
          project_description?: string | null
          project_end_date?: string | null
          project_name: string
          project_start_date?: string | null
          remote_percentage?: number | null
          required_skills?: string[] | null
          source?: string | null
          status?: string
          updated_at?: string
          user_id: string
          work_location_notes?: string | null
          work_location_type?: string | null
        }
        Update: {
          application_date?: string | null
          application_text?: string | null
          budget_range?: string | null
          company_name?: string
          contact_id?: string | null
          created_at?: string
          id?: string
          interview_date?: string | null
          interview_time?: string | null
          listing_url?: string | null
          match_calculated_at?: string | null
          match_reasoning?: string | null
          match_score?: number | null
          notes?: string | null
          project_description?: string | null
          project_end_date?: string | null
          project_name?: string
          project_start_date?: string | null
          remote_percentage?: number | null
          required_skills?: string[] | null
          source?: string | null
          status?: string
          updated_at?: string
          user_id?: string
          work_location_notes?: string | null
          work_location_type?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "project_applications_contact_id_fkey"
            columns: ["contact_id"]
            isOneToOne: false
            referencedRelation: "contacts"
            referencedColumns: ["id"]
          },
        ]
      }
      projects: {
        Row: {
          actual_end_date: string | null
          client_name: string
          contact_id: string | null
          created_at: string
          description: string | null
          estimated_hours: number | null
          hourly_rate: number | null
          id: string
          planned_end_date: string | null
          priority: string | null
          project_type: string | null
          source_application_id: string | null
          start_date: string | null
          status: string | null
          title: string
          updated_at: string
          user_id: string
        }
        Insert: {
          actual_end_date?: string | null
          client_name: string
          contact_id?: string | null
          created_at?: string
          description?: string | null
          estimated_hours?: number | null
          hourly_rate?: number | null
          id?: string
          planned_end_date?: string | null
          priority?: string | null
          project_type?: string | null
          source_application_id?: string | null
          start_date?: string | null
          status?: string | null
          title: string
          updated_at?: string
          user_id: string
        }
        Update: {
          actual_end_date?: string | null
          client_name?: string
          contact_id?: string | null
          created_at?: string
          description?: string | null
          estimated_hours?: number | null
          hourly_rate?: number | null
          id?: string
          planned_end_date?: string | null
          priority?: string | null
          project_type?: string | null
          source_application_id?: string | null
          start_date?: string | null
          status?: string | null
          title?: string
          updated_at?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "projects_source_application_id_fkey"
            columns: ["source_application_id"]
            isOneToOne: false
            referencedRelation: "project_applications"
            referencedColumns: ["id"]
          },
        ]
      }
      time_entries: {
        Row: {
          billable: boolean | null
          category: string | null
          created_at: string
          description: string | null
          duration_minutes: number | null
          end_time: string | null
          id: string
          is_running: boolean | null
          project_id: string
          start_time: string
          updated_at: string
          user_id: string
        }
        Insert: {
          billable?: boolean | null
          category?: string | null
          created_at?: string
          description?: string | null
          duration_minutes?: number | null
          end_time?: string | null
          id?: string
          is_running?: boolean | null
          project_id: string
          start_time: string
          updated_at?: string
          user_id: string
        }
        Update: {
          billable?: boolean | null
          category?: string | null
          created_at?: string
          description?: string | null
          duration_minutes?: number | null
          end_time?: string | null
          id?: string
          is_running?: boolean | null
          project_id?: string
          start_time?: string
          updated_at?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "time_entries_project_id_fkey"
            columns: ["project_id"]
            isOneToOne: false
            referencedRelation: "projects"
            referencedColumns: ["id"]
          },
        ]
      }
      user_settings: {
        Row: {
          address: string | null
          availability_end_date: string | null
          availability_hours_per_week: number | null
          availability_notes: string | null
          availability_start_date: string | null
          created_at: string
          cv_pdf_url: string | null
          full_name: string | null
          hourly_rate_eur: number | null
          id: string
          phone: string | null
          professional_email: string | null
          profile_picture_url: string | null
          updated_at: string
          user_id: string
          website: string | null
        }
        Insert: {
          address?: string | null
          availability_end_date?: string | null
          availability_hours_per_week?: number | null
          availability_notes?: string | null
          availability_start_date?: string | null
          created_at?: string
          cv_pdf_url?: string | null
          full_name?: string | null
          hourly_rate_eur?: number | null
          id?: string
          phone?: string | null
          professional_email?: string | null
          profile_picture_url?: string | null
          updated_at?: string
          user_id: string
          website?: string | null
        }
        Update: {
          address?: string | null
          availability_end_date?: string | null
          availability_hours_per_week?: number | null
          availability_notes?: string | null
          availability_start_date?: string | null
          created_at?: string
          cv_pdf_url?: string | null
          full_name?: string | null
          hourly_rate_eur?: number | null
          id?: string
          phone?: string | null
          professional_email?: string | null
          profile_picture_url?: string | null
          updated_at?: string
          user_id?: string
          website?: string | null
        }
        Relationships: []
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      check_notification_cron_setup: {
        Args: Record<PropertyKey, never>
        Returns: {
          is_configured: boolean
          secret_name: string
        }[]
      }
      global_search: {
        Args: { result_limit?: number; search_query: string; user_uuid: string }
        Returns: {
          created_at: string
          description: string
          entity_id: string
          entity_type: string
          highlight: string
          rank: number
          subtitle: string
          title: string
          url_path: string
        }[]
      }
      quick_search: {
        Args: { result_limit?: number; search_query: string; user_uuid: string }
        Returns: {
          entity_id: string
          entity_type: string
          rank: number
          subtitle: string
          title: string
          url_path: string
        }[]
      }
      unaccent: {
        Args: { "": string }
        Returns: string
      }
      unaccent_init: {
        Args: { "": unknown }
        Returns: unknown
      }
      uuid_generate_v1: {
        Args: Record<PropertyKey, never>
        Returns: string
      }
      uuid_generate_v1mc: {
        Args: Record<PropertyKey, never>
        Returns: string
      }
      uuid_generate_v3: {
        Args: { name: string; namespace: string }
        Returns: string
      }
      uuid_generate_v4: {
        Args: Record<PropertyKey, never>
        Returns: string
      }
      uuid_generate_v5: {
        Args: { name: string; namespace: string }
        Returns: string
      }
      uuid_nil: {
        Args: Record<PropertyKey, never>
        Returns: string
      }
      uuid_ns_dns: {
        Args: Record<PropertyKey, never>
        Returns: string
      }
      uuid_ns_oid: {
        Args: Record<PropertyKey, never>
        Returns: string
      }
      uuid_ns_url: {
        Args: Record<PropertyKey, never>
        Returns: string
      }
      uuid_ns_x500: {
        Args: Record<PropertyKey, never>
        Returns: string
      }
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}

type DatabaseWithoutInternals = Omit<Database, "__InternalSupabase">

type DefaultSchema = DatabaseWithoutInternals[Extract<keyof Database, "public">]

export type Tables<
  DefaultSchemaTableNameOrOptions extends
    | keyof (DefaultSchema["Tables"] & DefaultSchema["Views"])
    | { schema: keyof DatabaseWithoutInternals },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof (DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
        DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Views"])
    : never = never,
> = DefaultSchemaTableNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? (DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
      DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Views"])[TableName] extends {
      Row: infer R
    }
    ? R
    : never
  : DefaultSchemaTableNameOrOptions extends keyof (DefaultSchema["Tables"] &
        DefaultSchema["Views"])
    ? (DefaultSchema["Tables"] &
        DefaultSchema["Views"])[DefaultSchemaTableNameOrOptions] extends {
        Row: infer R
      }
      ? R
      : never
    : never

export type TablesInsert<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof DatabaseWithoutInternals },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Insert: infer I
    }
    ? I
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Insert: infer I
      }
      ? I
      : never
    : never

export type TablesUpdate<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof DatabaseWithoutInternals },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Update: infer U
    }
    ? U
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Update: infer U
      }
      ? U
      : never
    : never

export type Enums<
  DefaultSchemaEnumNameOrOptions extends
    | keyof DefaultSchema["Enums"]
    | { schema: keyof DatabaseWithoutInternals },
  EnumName extends DefaultSchemaEnumNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"]
    : never = never,
> = DefaultSchemaEnumNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"][EnumName]
  : DefaultSchemaEnumNameOrOptions extends keyof DefaultSchema["Enums"]
    ? DefaultSchema["Enums"][DefaultSchemaEnumNameOrOptions]
    : never

export type CompositeTypes<
  PublicCompositeTypeNameOrOptions extends
    | keyof DefaultSchema["CompositeTypes"]
    | { schema: keyof DatabaseWithoutInternals },
  CompositeTypeName extends PublicCompositeTypeNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"]
    : never = never,
> = PublicCompositeTypeNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"][CompositeTypeName]
  : PublicCompositeTypeNameOrOptions extends keyof DefaultSchema["CompositeTypes"]
    ? DefaultSchema["CompositeTypes"][PublicCompositeTypeNameOrOptions]
    : never

export const Constants = {
  public: {
    Enums: {},
  },
} as const
