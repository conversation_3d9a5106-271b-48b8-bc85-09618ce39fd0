import { NotificationService } from './notificationService';
import { supabase } from '@/integrations/supabase/client';

export class CalendarNotificationService {
  private static processedNotifications = new Set<string>();

  /**
   * Check for upcoming calendar events and create notifications
   */
  static async checkCalendarReminders(): Promise<void> {
    try {
      const { data: user } = await supabase.auth.getUser();
      if (!user.user) return;

      const now = new Date();
      const tomorrow = new Date();
      tomorrow.setDate(tomorrow.getDate() + 1);

      // Get calendar events for today and tomorrow
      const { data: events, error } = await supabase
        .from('calendar_events')
        .select('*')
        .eq('user_id', user.user.id)
        .gte('start_date', now.toISOString().split('T')[0])
        .lte('start_date', tomorrow.toISOString().split('T')[0])
        .order('start_date', { ascending: true })
        .order('start_time', { ascending: true });

      if (error) {
        console.error('Error fetching calendar events:', error);
        return;
      }

      if (!events?.length) return;

      for (const event of events) {
        const eventDateTime = this.getEventDateTime(event.start_date, event.start_time);
        if (!eventDateTime) continue;

        const minutesUntil = Math.floor((eventDateTime.getTime() - now.getTime()) / (1000 * 60));
        
        // Create notifications at different intervals
        const shouldNotify = this.shouldCreateNotification(event.id, minutesUntil);
        
        if (shouldNotify) {
          NotificationService.createCalendarEventNotification(
            event.id,
            event.title,
            event.start_date,
            event.start_time,
            minutesUntil
          );
          
          // Mark as processed for this time window
          const notificationKey = this.getNotificationKey(event.id, minutesUntil);
          this.processedNotifications.add(notificationKey);
        }
      }
    } catch (error) {
      console.error('Error checking calendar reminders:', error);
    }
  }

  /**
   * Determine if we should create a notification based on timing
   */
  private static shouldCreateNotification(eventId: string, minutesUntil: number): boolean {
    // Create notifications at: 24 hours, 1 hour, 15 minutes, and 5 minutes before
    const notificationWindows = [
      { minutes: 24 * 60, key: '24h' },  // 24 hours
      { minutes: 60, key: '1h' },        // 1 hour  
      { minutes: 15, key: '15m' },       // 15 minutes
      { minutes: 5, key: '5m' }          // 5 minutes
    ];

    for (const window of notificationWindows) {
      // Check if we're within 5 minutes of this notification window
      if (Math.abs(minutesUntil - window.minutes) <= 2) {
        const notificationKey = `${eventId}-${window.key}`;
        return !this.processedNotifications.has(notificationKey);
      }
    }

    return false;
  }

  /**
   * Get notification key for tracking processed notifications
   */
  private static getNotificationKey(eventId: string, minutesUntil: number): string {
    if (minutesUntil >= 24 * 60 - 5) return `${eventId}-24h`;
    if (minutesUntil >= 60 - 5) return `${eventId}-1h`;
    if (minutesUntil >= 15 - 2) return `${eventId}-15m`;
    if (minutesUntil >= 5 - 2) return `${eventId}-5m`;
    return `${eventId}-now`;
  }

  /**
   * Combine event date and time into a Date object
   */
  private static getEventDateTime(eventDate: string, eventTime?: string): Date | null {
    try {
      if (eventTime) {
        // Combine date and time
        const dateTime = new Date(`${eventDate}T${eventTime}`);
        return isNaN(dateTime.getTime()) ? null : dateTime;
      } else {
        // All-day event - use start of day
        const date = new Date(`${eventDate}T00:00:00`);
        return isNaN(date.getTime()) ? null : date;
      }
    } catch (error) {
      console.error('Error parsing event date/time:', error);
      return null;
    }
  }

  /**
   * Clear processed notifications cache
   */
  static clearProcessedNotifications(): void {
    this.processedNotifications.clear();
  }

  /**
   * Remove specific processed notification
   */
  static removeProcessedNotification(key: string): void {
    this.processedNotifications.delete(key);
  }
}