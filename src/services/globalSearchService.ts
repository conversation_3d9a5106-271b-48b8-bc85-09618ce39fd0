import { supabase } from '@/integrations/supabase/client';
import DOMPurify from 'dompurify';
import type { SearchResult } from '@/types/search';
import { SEARCH_CONFIG } from '@/types/search';
import { 
  SearchError, 
  NetworkError, 
  ValidationError, 
  ErrorCodes, 
  createSearchError, 
  createNetworkError, 
  createValidationError 
} from '@/types/errors';

export class GlobalSearchService {
  
  // Comprehensive search across all entities
  static async globalSearch(
    query: string, 
    limit: number = SEARCH_CONFIG.MAX_RESULTS
  ): Promise<SearchResult[]> {
    try {
      // Validate input
      if (!query || typeof query !== 'string') {
        throw createValidationError('Search query is required', 'query', query);
      }

      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        throw createSearchError(
          'User authentication required', 
          ErrorCodes.SEARCH_PERMISSION_DENIED
        );
      }

      const sanitizedQuery = this.sanitizeQuery(query);
      if (!this.isValidQuery(sanitizedQuery)) {
        if (sanitizedQuery.length < SEARCH_CONFIG.MIN_QUERY_LENGTH) {
          throw createSearchError(
            `Query too short. Minimum ${SEARCH_CONFIG.MIN_QUERY_LENGTH} characters required.`,
            ErrorCodes.SEARCH_QUERY_TOO_SHORT,
            { query: sanitizedQuery, minLength: SEARCH_CONFIG.MIN_QUERY_LENGTH }
          );
        }
        throw createSearchError(
          'Invalid search query format',
          ErrorCodes.SEARCH_QUERY_INVALID,
          { originalQuery: query, sanitizedQuery }
        );
      }

      const { data, error } = await supabase.rpc('global_search', {
        search_query: sanitizedQuery,
        user_uuid: user.id,
        result_limit: limit
      });

      if (error) {
        console.error('Global search database error:', error);
        throw createSearchError(
          'Database search failed',
          ErrorCodes.SEARCH_DATABASE_ERROR,
          error
        );
      }

      return data || [];
    } catch (error) {
      // Re-throw our custom errors
      if (error instanceof SearchError || error instanceof ValidationError) {
        throw error;
      }
      
      // Handle network/connection errors
      if (error instanceof Error && error.message.includes('fetch')) {
        throw createNetworkError(
          'Network connection failed',
          undefined,
          error
        );
      }
      
      // Handle unexpected errors
      console.error('GlobalSearchService.globalSearch unexpected error:', error);
      throw createSearchError(
        'Unexpected search error occurred',
        ErrorCodes.SEARCH_DATABASE_ERROR,
        error
      );
    }
  }


  // Group search results by entity type
  static groupSearchResults(results: SearchResult[]): Record<string, SearchResult[]> {
    return results.reduce((groups, result) => {
      const type = result.entity_type;
      if (!groups[type]) {
        groups[type] = [];
      }
      groups[type].push(result);
      return groups;
    }, {} as Record<string, SearchResult[]>);
  }

  // Enhanced input validation and sanitization for SQL injection prevention
  static sanitizeQuery(query: string): string {
    if (!query || typeof query !== 'string') {
      return '';
    }
    
    // Remove dangerous characters and limit length
    const sanitized = query
      .trim()
      .slice(0, SEARCH_CONFIG.MAX_QUERY_LENGTH)
      // Remove SQL injection patterns
      .replace(/[<>'"`;\\]/g, '') // Remove HTML tags, quotes, semicolons, backslashes
      .replace(/\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|UNION|SCRIPT)\b/gi, '') // Remove SQL keywords
      .replace(/--.*$/gm, '') // Remove SQL comments
      .replace(/\/\*[\s\S]*?\*\//g, '') // Remove multi-line SQL comments
      .replace(/\s+/g, ' ') // Normalize whitespace
      .trim();
    
    // Validate the sanitized query
    if (sanitized.length < SEARCH_CONFIG.MIN_QUERY_LENGTH) {
      return '';
    }
    
    // Additional validation: only allow alphanumeric, spaces, and common punctuation
    if (!/^[a-zA-Z0-9\s\-_@.]+$/.test(sanitized)) {
      // If it contains invalid characters, return empty string
      return '';
    }
    
    return sanitized;
  }


  // Check if search query is valid
  static isValidQuery(query: string): boolean {
    const sanitized = this.sanitizeQuery(query);
    return sanitized.length >= SEARCH_CONFIG.MIN_QUERY_LENGTH;
  }

  // Generate search suggestions based on query
  static generateSearchSuggestions(query: string): string[] {
    const suggestions: string[] = [];
    
    // Add common search patterns
    if (query.length >= 1) {
      suggestions.push(
        `Projekte mit "${query}"`,
        `Kontakte mit "${query}"`,
        `Bewerbungen bei "${query}"`
      );
    }

    return suggestions.slice(0, 3);
  }

  // Sanitize HTML content to prevent XSS attacks
  static sanitizeHTML(html: string): string {
    if (typeof window === 'undefined') {
      // Server-side fallback - strip all HTML tags
      return html.replace(/<[^>]*>/g, '');
    }
    
    // Client-side sanitization with DOMPurify
    return DOMPurify.sanitize(html, {
      ALLOWED_TAGS: ['b', 'strong', 'em', 'i', 'mark'],
      ALLOWED_ATTR: [],
      KEEP_CONTENT: true,
    });
  }

  // Safe highlight formatting with HTML sanitization
  static formatHighlight(highlight: string, fallback: string = ''): string {
    if (!highlight || highlight.trim() === '') {
      return this.sanitizeHTML(fallback);
    }
    
    // Sanitize the highlight content before returning
    return this.sanitizeHTML(highlight);
  }

  // Track search analytics (for future enhancement)
  static async trackSearchAnalytics(query: string, resultsCount: number): Promise<void> {
    try {
      // Could store search analytics for improving search quality
      // For now, just console log in development
      if (process.env.NODE_ENV === 'development') {
        console.log(`Search: "${query}" returned ${resultsCount} results`);
      }
    } catch (error) {
      // Fail silently for analytics
      console.warn('Search analytics tracking failed:', error);
    }
  }
}