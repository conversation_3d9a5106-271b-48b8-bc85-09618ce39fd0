import { supabase } from '@/integrations/supabase/client';

/**
 * Service for cleaning up notifications when status changes occur
 * This handles reactive cleanup only - all time-based notifications are handled by Edge Function
 */
export class NotificationCleanupService {
  
  /**
   * Cancel all notifications related to an application when status changes to completed/rejected
   */
  static async cancelApplicationNotifications(applicationId: string): Promise<void> {
    try {
      const { error } = await supabase
        .from('notifications')
        .update({ dismissed_at: new Date().toISOString() })
        .or(`related_entity_id.eq.${applicationId},metadata->>application_id.eq.${applicationId}`)
        .is('dismissed_at', null);

      if (error) {
        console.error('Error canceling application notifications:', error);
      } else {
        console.log(`Canceled notifications for application ${applicationId}`);
      }
    } catch (error) {
      console.error('Error in cancelApplicationNotifications:', error);
    }
  }

  /**
   * Cancel notifications related to a specific follow-up when it's sent/deleted/dismissed
   */
  static async cancelFollowUpNotifications(followUpId: string): Promise<void> {
    try {
      const { error } = await supabase
        .from('notifications')
        .update({ dismissed_at: new Date().toISOString() })
        .or(`related_entity_id.eq.${followUpId},metadata->>follow_up_id.eq.${followUpId}`)
        .is('dismissed_at', null);

      if (error) {
        console.error('Error canceling follow-up notifications:', error);
      } else {
        console.log(`Canceled notifications for follow-up ${followUpId}`);
      }
    } catch (error) {
      console.error('Error in cancelFollowUpNotifications:', error);
    }
  }

  /**
   * Cancel interview reminders when interview is completed/cancelled
   */
  static async cancelInterviewNotifications(applicationId: string): Promise<void> {
    try {
      const { error } = await supabase
        .from('notifications')
        .update({ dismissed_at: new Date().toISOString() })
        .eq('type', 'interview_reminder')
        .contains('metadata', { application_id: applicationId })
        .is('dismissed_at', null);

      if (error) {
        console.error('Error canceling interview notifications:', error);
      } else {
        console.log(`Canceled interview notifications for application ${applicationId}`);
      }
    } catch (error) {
      console.error('Error in cancelInterviewNotifications:', error);
    }
  }

  /**
   * Cancel suggestion notifications when user plans a follow-up 
   * (hybrid logic: manual planning overrides auto-suggestions)
   */
  static async cancelFollowUpSuggestions(applicationId: string): Promise<void> {
    try {
      const { error } = await supabase
        .from('notifications')
        .update({ dismissed_at: new Date().toISOString() })
        .in('type', ['follow_up_suggestion', 'follow_up_suggestion_urgent'])
        .contains('metadata', { application_id: applicationId })
        .is('dismissed_at', null);

      if (error) {
        console.error('Error canceling follow-up suggestions:', error);
      } else {
        console.log(`Canceled follow-up suggestions for application ${applicationId}`);
      }
    } catch (error) {
      console.error('Error in cancelFollowUpSuggestions:', error);
    }
  }

  /**
   * Cancel calendar event notifications when event is deleted/completed
   */
  static async cancelCalendarEventNotifications(eventId: string): Promise<void> {
    try {
      const { error } = await supabase
        .from('notifications')
        .update({ dismissed_at: new Date().toISOString() })
        .eq('type', 'calendar_event')
        .contains('metadata', { event_id: eventId })
        .is('dismissed_at', null);

      if (error) {
        console.error('Error canceling calendar event notifications:', error);
      } else {
        console.log(`Canceled calendar event notifications for event ${eventId}`);
      }
    } catch (error) {
      console.error('Error in cancelCalendarEventNotifications:', error);
    }
  }
}