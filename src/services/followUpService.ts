import { supabase } from '@/integrations/supabase/client';
import { parseFollowUpDateForCalendar, parseFollowUpTime } from '@/lib/dateUtils';
import { createAppError, logError, tryCatch } from '@/lib/errorHandling';
import { ActivityService } from './activityService';
import type { 
  FollowUpTemplate, 
  ScheduledFollowUp, 
  FollowUpHistory,
  TemplateVariables,
  FollowUpAnalytics,
  FollowUpNotification,
  ApplicationWithContact
} from '@/types/followup';
import type { CreateCalendarEventData } from '@/types/calendar';
import { EVENT_TYPE_COLORS } from '@/types/calendar';

export class FollowUpService {
  
  // Template personalization - replace placeholders with actual data
  static personalizeTemplate(template: string, variables: TemplateVariables): string {
    return template
      .replace(/\{project_name\}/g, variables.project_name)
      .replace(/\{contact_person\}/g, variables.contact_person)
      .replace(/\{company_name\}/g, variables.company_name)
      .replace(/\{user_name\}/g, variables.user_name)
      .replace(/\{trigger_days\}/g, variables.trigger_days.toString())
      .replace(/\{application_date\}/g, variables.application_date)
      .replace(/\{interview_date\}/g, variables.interview_date || 'N/A');
  }

  // Helper method to create standardized template variables
  private static createTemplateVariables(
    application: ApplicationWithContact, 
    template: FollowUpTemplate, 
    userContext?: string
  ): TemplateVariables {
    return {
      project_name: application.project_name,
      contact_person: application?.contact?.name || 'Sehr geehrte Damen und Herren',
      company_name: application.company_name,
      user_name: userContext || '',
      trigger_days: template.trigger_days,
      application_date: application.application_date || '',
      interview_date: application.interview_date || ''
    };
  }

  // Generate mailto: link for email client integration
  static generateMailtoLink(
    to: string,
    subject: string,
    body: string,
    cc?: string,
    bcc?: string
  ): string {
    const params = new URLSearchParams();
    params.append('subject', subject);
    params.append('body', body);
    
    if (cc) params.append('cc', cc);
    if (bcc) params.append('bcc', bcc);
    
    return `mailto:${encodeURIComponent(to)}?${params.toString()}`;
  }

  // Create follow-up from template and application data
  static async createFollowUpFromTemplate(
    applicationId: string,
    templateId: string,
    customScheduleDate?: string
  ): Promise<ScheduledFollowUp> {
    // Get current user
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) throw new Error('User not authenticated');

    // Get template and application data
    const { data: template, error: templateError } = await supabase
      .from('follow_up_templates')
      .select('*')
      .eq('id', templateId)
      .single();

    if (templateError) throw templateError;

    const { data: application, error: appError } = await supabase
      .from('project_applications')
      .select(`
        *,
        contact:contacts(name, email, phone)
      `)
      .eq('id', applicationId)
      .single();

    if (appError) throw appError;

    // Calculate scheduled date
    const scheduledDate = customScheduleDate || 
      new Date(Date.now() + template.trigger_days * 24 * 60 * 60 * 1000).toISOString();

    // Create scheduled follow-up
    const { data, error } = await supabase
      .from('follow_up_schedule')
      .insert({
        user_id: user.id,  // Add user_id for RLS policy
        application_id: applicationId,
        template_id: templateId,
        scheduled_date: scheduledDate,
        status: 'scheduled'
      })
      .select('*')
      .single();

    if (error) throw error;

    // Create corresponding calendar event for the follow-up
    try {
      const followUpDate = parseFollowUpDateForCalendar(scheduledDate);
      const followUpTime = parseFollowUpTime(scheduledDate);
      
      // Get user data for calendar template personalization
      let userName = '';
      try {
        const { data: userSettings } = await supabase
          .from('user_settings')
          .select('full_name')
          .eq('user_id', user.id)
          .single();
        userName = userSettings?.full_name || '';
      } catch (error) {
        console.warn('Could not load user name for follow-up personalization:', error);
      }

      // Personalize the template subject and body for the calendar event
      const templateVariables = this.createTemplateVariables(application, template, userName);

      const personalizedSubject = this.personalizeTemplate(template.subject, templateVariables);
      const personalizedBody = this.personalizeTemplate(template.body, templateVariables);
      
      const eventData: CreateCalendarEventData = {
        reference_id: applicationId,
        reference_type: 'application',
        title: `Follow-up: ${application.project_name}`,
        description: `Follow-up für "${application.project_name}" bei ${application.company_name}\n\nTemplate: ${template.name}\nSubject: ${personalizedSubject}\n\nBody:\n${personalizedBody}`,
        start_date: followUpDate,
        start_time: followUpTime,
        all_day: false,
        event_type: 'follow_up',
        color: EVENT_TYPE_COLORS.follow_up,
        created_automatically: true,
        source_status: 'follow_up_scheduled',
        reminder_enabled: true,
        reminder_minutes_before: 30, // 30 minute reminder
      };

      const [calendarResult, calendarError] = await tryCatch(
        async () => {
          const { data, error } = await supabase
            .from('calendar_events')
            .insert([{
              ...eventData,
              user_id: user.id,
            }]);
          if (error) throw error;
          return data;
        },
        'FollowUpService.createScheduledFollowUp.calendarEvent'
      );

      if (calendarError) {
        logError(calendarError, 'Calendar event creation during follow-up scheduling');
        // We don't throw here to avoid breaking follow-up creation
        // but we log the error properly for monitoring
      }
    } catch (error) {
      const appError = createAppError(error, 'FollowUpService.createScheduledFollowUp.calendarIntegration');
      logError(appError, 'Calendar integration failed - follow-up created without calendar event');
      // Calendar errors are non-critical - follow-up creation should continue
      // TODO: Add monitoring/alerting for calendar integration failures
    }

    // Log activity for follow-up creation with personalized template content
    try {
      // Personalize the template for activity logging (ActivityService handles user auth internally)
      const activityTemplateVariables = this.createTemplateVariables(application, template);

      const personalizedSubject = this.personalizeTemplate(template.subject, activityTemplateVariables);
      const personalizedBody = this.personalizeTemplate(template.body, activityTemplateVariables);
      
      const templateContent = `**Template:** ${template.name}
**Geplant für:** ${new Date(scheduledDate).toLocaleDateString('de-DE')}

**Betreff:**
${personalizedSubject}

**Inhalt:**
${personalizedBody}`;

      await ActivityService.logActivity(
        applicationId,
        'followup_scheduled',
        `Follow-up "${template.name}" für ${application.project_name} bei ${application.company_name} geplant`,
        templateContent
      );
    } catch (activityError) {
      console.error('Failed to log follow-up creation activity:', activityError);
      // Activity logging is non-critical - continue with follow-up creation
      // TODO: Add monitoring/alerting for activity logging failures
    }

    return {
      ...data,
      template,
      application: {
        id: application.id,
        project_name: application.project_name,
        contact_person: application?.contact?.name || 'Sehr geehrte Damen und Herren',
        company_name: application.company_name,
        status: application.status
      }
    } as ScheduledFollowUp;
  }

  // Get all scheduled follow-ups for user
  static async getScheduledFollowUps(userId: string): Promise<ScheduledFollowUp[]> {
    const { data: followUps, error } = await supabase
      .from('follow_up_schedule')
      .select(`
        *,
        template:follow_up_templates(*),
        application:project_applications(
          id,
          project_name,
          company_name,
          status,
          contact:contacts(name, email, phone)
        )
      `)
      .eq('user_id', userId)
      .order('scheduled_date', { ascending: true });

    if (error) throw error;
    if (!followUps || followUps.length === 0) return [];

    // Transform the data to match the expected interface
    return followUps.map(followUp => ({
      ...followUp,
      application: followUp.application ? {
        id: followUp.application.id,
        project_name: followUp.application.project_name,
        contact_person: followUp.application.contact?.name || 'Sehr geehrte Damen und Herren',
        company_name: followUp.application.company_name,
        status: followUp.application.status
      } : null
    })) as ScheduledFollowUp[];
  }

  // Get due follow-ups (today and overdue)
  static async getDueFollowUps(userId: string): Promise<FollowUpNotification[]> {
    const today = new Date();
    const todayEnd = new Date(today);  
    todayEnd.setHours(23, 59, 59, 999); // End of today
    
    const { data: followUps, error } = await supabase
      .from('follow_up_schedule')
      .select(`
        *,
        template:follow_up_templates(name),
        application:project_applications(
          id,
          project_name,
          company_name,
          status
        )
      `)
      .eq('user_id', userId)
      .eq('status', 'scheduled')
      .lte('scheduled_date', todayEnd.toISOString()) // All items due today or before (overdue)
      .order('scheduled_date', { ascending: true });

    if (error) throw error;
    if (!followUps || followUps.length === 0) return [];

    return followUps.map(item => {
      const scheduledDate = new Date(item.scheduled_date);
      const now = new Date();
      const isOverdue = scheduledDate < now;
      const daysOverdue = isOverdue ? 
        Math.floor((now.getTime() - scheduledDate.getTime()) / (1000 * 60 * 60 * 24)) : 
        undefined;

      return {
        id: item.id,
        application_id: item.application_id,
        template_name: item.template?.name || 'Follow-up',
        project_name: item.application?.project_name || 'Unbekanntes Projekt',
        company_name: item.application?.company_name || 'Unbekannte Firma',
        scheduled_date: item.scheduled_date,
        is_overdue: isOverdue,
        days_overdue: daysOverdue
      };
    });
  }

  // Get all follow-ups for overview page
  static async getAllFollowUps(userId: string): Promise<FollowUpNotification[]> {
    const { data: followUps, error } = await supabase
      .from('follow_up_schedule')
      .select(`
        *,
        template:follow_up_templates(name),
        application:project_applications(
          id,
          project_name,
          company_name,
          status
        )
      `)
      .eq('user_id', userId)
      .eq('status', 'scheduled')
      .order('scheduled_date', { ascending: true });

    if (error) throw error;
    if (!followUps || followUps.length === 0) return [];

    return followUps.map(item => {
      const scheduledDate = new Date(item.scheduled_date);
      const now = new Date();
      const isOverdue = scheduledDate < now;
      const daysOverdue = isOverdue ? 
        Math.floor((now.getTime() - scheduledDate.getTime()) / (1000 * 60 * 60 * 24)) : 
        undefined;

      return {
        id: item.id,
        application_id: item.application_id,
        template_name: item.template?.name || 'Follow-up',
        project_name: item.application?.project_name || 'Unbekanntes Projekt',
        company_name: item.application?.company_name || 'Unbekannte Firma',
        scheduled_date: item.scheduled_date,
        is_overdue: isOverdue,
        days_overdue: daysOverdue
      };
    });
  }

  // Update follow-up calendar event
  static async updateFollowUpCalendarEvent(
    applicationId: string,
    oldScheduledDate: string,
    newScheduledDate: string,
    template: FollowUpTemplate,
    application: ApplicationWithContact
  ): Promise<void> {
    try {
      const oldDate = parseFollowUpDateForCalendar(oldScheduledDate);
      const newDate = parseFollowUpDateForCalendar(newScheduledDate);
      const newTime = parseFollowUpTime(newScheduledDate);

      // Get current user for personalization
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) return;

      // Get user data for personalization
      let userName = '';
      try {
        const { data: userSettings } = await supabase
          .from('user_settings')
          .select('full_name')
          .eq('user_id', user.id)
          .single();
        userName = userSettings?.full_name || '';
      } catch (error) {
        console.warn('Could not load user name for follow-up personalization:', error);
      }

      // Personalize the template
      const templateVariables = this.createTemplateVariables(application, template, userName);

      const personalizedSubject = this.personalizeTemplate(template.subject, templateVariables);
      const personalizedBody = this.personalizeTemplate(template.body, templateVariables);

      // Update the calendar event
      const [result, error] = await tryCatch(
        async () => {
          const { data, error } = await supabase
            .from('calendar_events')
            .update({
              title: `Follow-up: ${application.project_name}`,
              description: `Follow-up für "${application.project_name}" bei ${application.company_name}\n\nTemplate: ${template.name}\nSubject: ${personalizedSubject}\n\nBody:\n${personalizedBody}`,
              start_date: newDate,
              start_time: newTime,
              updated_at: new Date().toISOString()
            })
            .eq('reference_id', applicationId)
            .eq('reference_type', 'application')
            .eq('event_type', 'follow_up')
            .eq('start_date', oldDate)
            .eq('source_status', 'follow_up_scheduled');
          
          if (error) throw error;
          return data;
        },
        'FollowUpService.updateFollowUpCalendarEvent'
      );

      if (error) {
        logError(error, 'Calendar event update during follow-up modification');
        // Calendar update errors should not prevent follow-up updates
      }
    } catch (error) {
      const appError = createAppError(error, 'FollowUpService.updateFollowUpCalendarEvent.fallback');
      logError(appError, 'Calendar integration update');
    }
  }

  // Delete follow-up calendar event
  static async deleteFollowUpCalendarEvent(applicationId: string, scheduledDate: string): Promise<void> {
    const [result, error] = await tryCatch(
      async () => {
        const { data, error } = await supabase
          .from('calendar_events')
          .delete()
          .eq('reference_id', applicationId)
          .eq('reference_type', 'application')
          .eq('event_type', 'follow_up');
        
        if (error) throw error;
        return data;
      },
      'FollowUpService.deleteFollowUpCalendarEvent'
    );

    if (error) {
      logError(error, 'Calendar event deletion during follow-up removal');
      // Calendar deletion errors should not prevent follow-up deletion
    }
  }

  // Mark follow-up as sent and create history entry
  static async markFollowUpAsSent(
    followUpId: string,
    actualSubject: string,
    actualBody: string
  ): Promise<void> {
    // Get the follow-up to move to history with application details for activity logging
    const { data: followUp, error: getError } = await supabase
      .from('follow_up_schedule')
      .select(`
        *,
        template:follow_up_templates(*),
        application:project_applications(
          *,
          contact:contacts(name, email, phone)
        )
      `)
      .eq('id', followUpId)
      .single();

    if (getError) throw getError;

    // Delete corresponding calendar event since follow-up is now sent
    if (followUp) {
      await this.deleteFollowUpCalendarEvent(followUp.application_id, followUp.scheduled_date);
    }

    // Update follow-up status
    const { error: updateError } = await supabase
      .from('follow_up_schedule')
      .update({
        status: 'sent',
        sent_at: new Date().toISOString()
      })
      .eq('id', followUpId);

    if (updateError) throw updateError;

    // Create history entry
    const { error: historyError } = await supabase
      .from('follow_up_history')
      .insert({
        user_id: followUp.user_id,
        application_id: followUp.application_id,
        template_id: followUp.template_id,
        sent_at: new Date().toISOString(),
        subject: actualSubject,
        body: actualBody,
        response_received: false
      });

    if (historyError) throw historyError;

    // Log activity in the project timeline
    if (followUp.application_id && followUp.application) {
      const templateName = followUp.template?.name || 'Follow-up';
      const projectName = followUp.application.project_name || 'Projekt';
      const companyName = followUp.application.company_name || 'Unbekannte Firma';
      
      // Personalize the template content if it contains placeholders
      let personalizedSubject = actualSubject;
      let personalizedBody = actualBody;
      
      // Check if the content contains placeholders and personalize if needed
      if (actualSubject.includes('{') && actualBody.includes('{') && followUp.template) {
        const templateVariables = this.createTemplateVariables(followUp.application, followUp.template);
        
        personalizedSubject = this.personalizeTemplate(actualSubject, templateVariables);
        personalizedBody = this.personalizeTemplate(actualBody, templateVariables);
      }
      
      const sentContent = `**Template:** ${templateName}
**Gesendet am:** ${new Date().toLocaleDateString('de-DE')} um ${new Date().toLocaleTimeString('de-DE')}

**Gesendeter Betreff:**
${personalizedSubject}

**Gesendeter Inhalt:**
${personalizedBody}`;
      
      try {
        await ActivityService.logActivity(
          followUp.application_id,
          'followup_marked_as_sent',
          `Follow-up "${templateName}" für ${projectName} bei ${companyName} als gesendet markiert`,
          sentContent
        );
      } catch (activityError) {
        console.error('Failed to log follow-up sent activity:', activityError);
        // Activity logging is non-critical - continue with marking follow-up as sent
        // TODO: Add monitoring/alerting for activity logging failures
      }
    }
  }

  // Get follow-up analytics
  static async getFollowUpAnalytics(userId: string): Promise<FollowUpAnalytics> {
    const { data: history, error } = await supabase
      .from('follow_up_history')
      .select(`
        *,
        template:follow_up_templates(name, trigger_days)
      `)
      .eq('user_id', userId);

    if (error) throw error;

    const totalSent = history.length;
    const totalResponses = history.filter(h => h.response_received).length;
    const responseRate = totalSent > 0 ? (totalResponses / totalSent) * 100 : 0;

    // Calculate average response time
    const responseTimes = history
      .filter(h => h.response_received && h.response_date)
      .map(h => {
        const sentDate = new Date(h.sent_at);
        const responseDate = new Date(h.response_date!);
        return Math.floor((responseDate.getTime() - sentDate.getTime()) / (1000 * 60 * 60 * 24));
      });

    const avgResponseTime = responseTimes.length > 0 ? 
      responseTimes.reduce((sum, days) => sum + days, 0) / responseTimes.length : 0;

    // Success by template
    const templateStats = new Map();
    history.forEach(h => {
      const templateId = h.template_id;
      const templateName = h.template?.name || 'Unknown';
      
      if (!templateStats.has(templateId)) {
        templateStats.set(templateId, {
          template_id: templateId,
          template_name: templateName,
          sent_count: 0,
          response_count: 0
        });
      }
      
      const stats = templateStats.get(templateId);
      stats.sent_count++;
      if (h.response_received) stats.response_count++;
    });

    const successByTemplate = Array.from(templateStats.values()).map(stats => ({
      ...stats,
      response_rate: stats.sent_count > 0 ? (stats.response_count / stats.sent_count) * 100 : 0
    }));

    // Success by timing
    const timingStats = new Map();
    history.forEach(h => {
      const triggerDays = h.template?.trigger_days || 0;
      
      if (!timingStats.has(triggerDays)) {
        timingStats.set(triggerDays, {
          trigger_days: triggerDays,
          sent_count: 0,
          response_count: 0
        });
      }
      
      const stats = timingStats.get(triggerDays);
      stats.sent_count++;
      if (h.response_received) stats.response_count++;
    });

    const successByTiming = Array.from(timingStats.values()).map(stats => ({
      ...stats,
      response_rate: stats.sent_count > 0 ? (stats.response_count / stats.sent_count) * 100 : 0
    }));

    return {
      total_sent: totalSent,
      response_rate: responseRate,
      avg_response_time_days: avgResponseTime,
      success_by_template: successByTemplate,
      success_by_timing: successByTiming
    };
  }

  // Delete follow-up and log activity
  static async deleteFollowUpWithLogging(followUpId: string): Promise<void> {
    // Get follow-up details with all necessary information for activity logging
    const { data: followUp, error: getError } = await supabase
      .from('follow_up_schedule')
      .select(`
        *,
        template:follow_up_templates(name),
        application:project_applications(id, project_name, company_name)
      `)
      .eq('id', followUpId)
      .single();

    if (getError) throw getError;

    // Delete corresponding calendar event
    if (followUp) {
      await this.deleteFollowUpCalendarEvent(followUp.application_id, followUp.scheduled_date);
    }

    // Delete the follow-up from database
    const { error: deleteError } = await supabase
      .from('follow_up_schedule')
      .delete()
      .eq('id', followUpId);

    if (deleteError) throw deleteError;

    // Log activity for follow-up deletion
    if (followUp.application_id && followUp.application) {
      const templateName = followUp.template?.name || 'Follow-up';
      const projectName = followUp.application.project_name || 'Projekt';
      const companyName = followUp.application.company_name || 'Unbekannte Firma';
      const scheduledDate = new Date(followUp.scheduled_date).toLocaleDateString('de-DE');
      
      const deletionContent = `**Template:** ${templateName}
**War geplant für:** ${scheduledDate}
**Status:** ${followUp.status === 'scheduled' ? 'Geplant' : followUp.status}
**Löschgrund:** Manuell gelöscht`;

      try {
        await ActivityService.logActivity(
          followUp.application_id,
          'followup_deleted',
          `Follow-up "${templateName}" für ${projectName} bei ${companyName} gelöscht`,
          deletionContent
        );
      } catch (activityError) {
        console.error('Failed to log follow-up deletion activity:', activityError);
        // Don't throw error to avoid breaking the deletion process
      }
    }
  }

  // Auto-schedule follow-ups when application status changes
  static async autoScheduleFollowUps(
    applicationId: string,
    newStatus: string,
    userId: string
  ): Promise<ScheduledFollowUp[]> {
    // Get active templates for this status trigger
    const { data: templates, error } = await supabase
      .from('follow_up_templates')
      .select('*')
      .eq('user_id', userId)
      .eq('status_trigger', newStatus)
      .eq('is_active', true);

    if (error) throw error;

    const createdFollowUps: ScheduledFollowUp[] = [];

    for (const template of templates) {
      try {
        const followUp = await this.createFollowUpFromTemplate(applicationId, template.id);
        createdFollowUps.push(followUp);
      } catch (error) {
        console.error(`Failed to create follow-up for template ${template.id}:`, error);
      }
    }

    return createdFollowUps;
  }
}