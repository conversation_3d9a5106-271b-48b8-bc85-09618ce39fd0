import { supabase } from '@/integrations/supabase/client';
import { ExportData } from '@/types/export';
import { Application, ApplicationActivity } from '@/types/applications';
import { Project } from '@/types/projects';
import { CalendarEvent } from '@/types/calendar';
import { ActivityService } from './activityService';
import { createProjectDateEvents } from './calendarService';

export interface ImportProgress {
  current: number;
  total: number;
  percentage: number;
  itemName?: string; // Can be project name or application name
}

export interface ImportResult {
  success: boolean;
  successCount: number;
  errorCount: number;
  errors: string[];
  activitiesImported?: number;
  calendarEventsImported?: number;
  duplicatesFound?: number;
  duplicatesOverwritten?: number;
}

export interface ImportOptions {
  overwriteDuplicates?: boolean; // true = überschreiben, false = Fehler werfen
}

export class ImportService {
  private static readonly MAX_ITEMS_PER_IMPORT = 1000;
  private static readonly MAX_ACTIVITIES_PER_IMPORT = 10000;
  private static readonly MAX_CALENDAR_EVENTS_PER_IMPORT = 5000;
  
  /**
   * Validate import data for applications or projects
   */
  static async validateImportData(data: any, type: 'applications' | 'projects' = 'applications'): Promise<ExportData> {
    if (!data || typeof data !== 'object') {
      throw new Error('Ungültiges Datenformat');
    }

    const dataKey = type === 'applications' ? 'applications' : 'projects';
    const legacyKey = type === 'applications' ? 'projects' : 'data'; // Support legacy formats

    // Check for data in multiple possible locations
    let items = data[dataKey] || data[legacyKey] || data.data;
    
    if (!items || !Array.isArray(items)) {
      throw new Error(`Keine ${type === 'applications' ? 'Bewerbungen' : 'Projekte'} in der Datei gefunden`);
    }

    if (items.length === 0) {
      throw new Error(`Die Datei enthält keine ${type === 'applications' ? 'Bewerbungen' : 'Projekte'}`);
    }

    // Check import size limits
    if (items.length > this.MAX_ITEMS_PER_IMPORT) {
      throw new Error(`Zu viele ${type === 'applications' ? 'Bewerbungen' : 'Projekte'}: ${items.length}. Maximum: ${this.MAX_ITEMS_PER_IMPORT}`);
    }

    // Validate activities if present
    if (data.activities && Array.isArray(data.activities)) {
      if (data.activities.length > this.MAX_ACTIVITIES_PER_IMPORT) {
        throw new Error(`Zu viele Aktivitäten: ${data.activities.length}. Maximum: ${this.MAX_ACTIVITIES_PER_IMPORT}`);
      }
    }

    // Validate calendar events if present
    if (data.calendarEvents && Array.isArray(data.calendarEvents)) {
      if (data.calendarEvents.length > this.MAX_CALENDAR_EVENTS_PER_IMPORT) {
        throw new Error(`Zu viele Kalendereinträge: ${data.calendarEvents.length}. Maximum: ${this.MAX_CALENDAR_EVENTS_PER_IMPORT}`);
      }
    }

    return {
      [dataKey]: items,
      activities: data.activities || [],
      calendarEvents: data.calendarEvents || [],
      exportDate: data.exportDate || new Date().toISOString(),
      version: data.version || '1.0'
    };
  }

  /**
   * Import applications with progress tracking and duplicate handling
   */
  static async importApplications(
    data: ExportData,
    options: ImportOptions = {},
    onProgress?: (progress: ImportProgress) => void
  ): Promise<ImportResult> {
    const result: ImportResult = {
      success: true,
      successCount: 0,
      errorCount: 0,
      errors: [],
      duplicatesFound: 0,
      duplicatesOverwritten: 0
    };

    try {
      const { data: { session } } = await supabase.auth.getSession();
      if (!session?.user) {
        throw new Error('Nicht authentifiziert');
      }

      const applications = data.applications || data.projects || []; // Support legacy format
      const total = applications.length;

      // Check for existing applications to detect duplicates
      const { data: existingApps } = await supabase
        .from('project_applications') // Updated table name
        .select('id, project_name, company_name')
        .eq('user_id', session.user.id);

      for (let i = 0; i < applications.length; i++) {
        const app = applications[i];
        
        onProgress?.({
          current: i + 1,
          total,
          percentage: Math.round(((i + 1) / total) * 100),
          itemName: app.project_name
        });

        try {
          // Check for duplicates
          const isDuplicate = existingApps?.some(existing => 
            existing.project_name === app.project_name && 
            existing.company_name === app.company_name
          );

          if (isDuplicate && !options.overwriteDuplicates) {
            result.duplicatesFound = (result.duplicatesFound || 0) + 1;
            result.errors.push(`Duplicate gefunden: ${app.project_name} (${app.company_name})`);
            result.errorCount++;
            continue;
          }

          // Prepare application data for new table structure
          const applicationData = {
            user_id: session.user.id,
            project_name: app.project_name,
            company_name: app.company_name,
            project_description: app.project_description,
            budget_range: app.budget_range,
            project_start_date: app.project_start_date,
            project_end_date: app.project_end_date,
            required_skills: app.required_skills,
            application_date: app.application_date,
            status: app.status || 'not_applied',
            application_text: app.application_text,
            notes: app.notes,
            source: app.source,
            listing_url: app.listing_url,
            work_location_type: app.work_location_type,
            remote_percentage: app.remote_percentage,
            work_location_notes: app.work_location_notes,
            contact_id: app.contact_id,
          };

          if (isDuplicate && options.overwriteDuplicates) {
            // Update existing application
            const existingApp = existingApps?.find(existing => 
              existing.project_name === app.project_name && 
              existing.company_name === app.company_name
            );

            const { error } = await supabase
              .from('project_applications') // Updated table name
              .update(applicationData)
              .eq('id', existingApp!.id);

            if (error) throw error;
            result.duplicatesOverwritten = (result.duplicatesOverwritten || 0) + 1;
          } else {
            // Create new application
            const { data: newApp, error } = await supabase
              .from('project_applications') // Updated table name
              .insert([applicationData])
              .select()
              .single();

            if (error) throw error;

            // Create calendar events if dates are present
            if (newApp && (app.project_start_date || app.project_end_date)) {
              await createProjectDateEvents(
                newApp.id,
                app.project_name,
                app.company_name,
                app.project_start_date,
                app.project_end_date,
                'application' // reference_type for applications
              );
            }
          }

          result.successCount++;
        } catch (error) {
          console.error(`Error importing application ${app.project_name}:`, error);
          result.errors.push(`${app.project_name}: ${error instanceof Error ? error.message : 'Unbekannter Fehler'}`);
          result.errorCount++;
        }
      }

      result.success = result.errorCount === 0;
      return result;
    } catch (error) {
      console.error('Import error:', error);
      result.success = false;
      result.errors.push(error instanceof Error ? error.message : 'Unbekannter Fehler beim Import');
      return result;
    }
  }

  /**
   * Import projects with progress tracking and duplicate handling
   */
  static async importProjects(
    data: ExportData,
    options: ImportOptions = {},
    onProgress?: (progress: ImportProgress) => void
  ): Promise<ImportResult> {
    const result: ImportResult = {
      success: true,
      successCount: 0,
      errorCount: 0,
      errors: [],
      duplicatesFound: 0,
      duplicatesOverwritten: 0
    };

    try {
      const { data: { session } } = await supabase.auth.getSession();
      if (!session?.user) {
        throw new Error('Nicht authentifiziert');
      }

      const projects = data.projects || [];
      const total = projects.length;

      // Check for existing projects to detect duplicates
      const { data: existingProjects } = await supabase
        .from('projects') // Updated table name
        .select('id, title, client_name')
        .eq('user_id', session.user.id);

      for (let i = 0; i < projects.length; i++) {
        const project = projects[i];
        
        onProgress?.({
          current: i + 1,
          total,
          percentage: Math.round(((i + 1) / total) * 100),
          itemName: project.title
        });

        try {
          // Check for duplicates
          const isDuplicate = existingProjects?.some(existing => 
            existing.title === project.title && 
            existing.client_name === project.client_name
          );

          if (isDuplicate && !options.overwriteDuplicates) {
            result.duplicatesFound = (result.duplicatesFound || 0) + 1;
            result.errors.push(`Duplicate gefunden: ${project.title} (${project.client_name})`);
            result.errorCount++;
            continue;
          }

          // Prepare project data for new table structure
          const projectData = {
            user_id: session.user.id,
            title: project.title,
            client_name: project.client_name,
            description: project.description,
            project_type: project.project_type || 'development',
            hourly_rate: project.hourly_rate,
            estimated_hours: project.estimated_hours,
            start_date: project.start_date,
            planned_end_date: project.planned_end_date,
            actual_end_date: project.actual_end_date,
            status: project.status || 'starting',
            priority: project.priority || 'medium',
            contact_id: project.contact_id,
            source_application_id: project.source_application_id, // Updated field name
          };

          if (isDuplicate && options.overwriteDuplicates) {
            // Update existing project
            const existingProject = existingProjects?.find(existing => 
              existing.title === project.title && 
              existing.client_name === project.client_name
            );

            const { error } = await supabase
              .from('projects') // Updated table name
              .update(projectData)
              .eq('id', existingProject!.id);

            if (error) throw error;
            result.duplicatesOverwritten = (result.duplicatesOverwritten || 0) + 1;
          } else {
            // Create new project
            const { data: newProject, error } = await supabase
              .from('projects') // Updated table name
              .insert([projectData])
              .select()
              .single();

            if (error) throw error;

            // Create calendar events if dates are present
            if (newProject && (project.start_date || project.planned_end_date)) {
              await createProjectDateEvents(
                newProject.id,
                project.title,
                project.client_name,
                project.start_date,
                project.planned_end_date,
                'project' // reference_type for projects
              );
            }
          }

          result.successCount++;
        } catch (error) {
          console.error(`Error importing project ${project.title}:`, error);
          result.errors.push(`${project.title}: ${error instanceof Error ? error.message : 'Unbekannter Fehler'}`);
          result.errorCount++;
        }
      }

      result.success = result.errorCount === 0;
      return result;
    } catch (error) {
      console.error('Import error:', error);
      result.success = false;
      result.errors.push(error instanceof Error ? error.message : 'Unbekannter Fehler beim Import');
      return result;
    }
  }

  /**
   * Legacy import method for backward compatibility
   */
  static async importFreelanceProjects(
    data: ExportData,
    options: ImportOptions = {},
    onProgress?: (progress: ImportProgress) => void
  ): Promise<ImportResult> {
    return this.importApplications(data, options, onProgress);
  }

  /**
   * Detect file type and import accordingly
   */
  static async autoImport(
    data: any,
    options: ImportOptions = {},
    onProgress?: (progress: ImportProgress) => void
  ): Promise<ImportResult> {
    try {
      // Try to detect the type of data
      if (data.type === 'applications' || data.applications) {
        const validatedData = await this.validateImportData(data, 'applications');
        return this.importApplications(validatedData, options, onProgress);
      } else if (data.type === 'projects' || data.projects) {
        const validatedData = await this.validateImportData(data, 'projects');
        return this.importProjects(validatedData, options, onProgress);
      } else {
        // Default to applications for legacy support
        const validatedData = await this.validateImportData(data, 'applications');
        return this.importApplications(validatedData, options, onProgress);
      }
    } catch (error) {
      return {
        success: false,
        successCount: 0,
        errorCount: 1,
        errors: [error instanceof Error ? error.message : 'Unbekannter Fehler beim Import']
      };
    }
  }
}