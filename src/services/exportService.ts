import jsPDF from 'jspdf';
import * as XLSX from 'xlsx';
import { Application } from '@/types/applications';
import { Project } from '@/types/projects';
import { UserSettings } from '@/types/settings';
import {
  ExportData,
  ExportOptions,
  ExportResult,
  PDFExportConfig,
  ExcelExportConfig,
  DEFAULT_PDF_CONFIG,
  DEFAULT_EXCEL_CONFIG
} from '@/types/export';
import { APPLICATION_STATUS_LABELS, WORK_LOCATION_LABELS } from '@/types/applications';
import { PROJECT_STATUS_LABELS } from '@/types/projects';
import { format } from 'date-fns';
import { de } from 'date-fns/locale';

export class ExportService {
  /**
   * Generate PDF export of applications or projects
   */
  static async generatePDF(
    data: Application[] | Project[],
    settings?: UserSettings | null,
    options: Partial<ExportOptions> = {},
    config: Partial<PDFExportConfig> = {},
    type: 'applications' | 'projects' = 'applications'
  ): Promise<ExportResult> {
    try {
      // Validate input
      if (!data || data.length === 0) {
        return {
          success: false,
          error: type === 'applications' ? 'Keine Bewerbungen zum Exportieren vorhanden.' : 'Keine Projekte zum Exportieren vorhanden.',
        };
      }

      const pdfConfig = { ...DEFAULT_PDF_CONFIG, ...config };
      const doc = new jsPDF();
      
      // Set document properties
      const title = type === 'applications' ? 'Bewerbungsübersicht' : 'Projektübersicht';
      doc.setProperties({
        title: title,
        author: pdfConfig.author,
        subject: `${title} - Export`,
        keywords: type === 'applications' ? 'Bewerbungen, Export' : 'Projekte, Export',
      });

      let yPosition = pdfConfig.margins.top;
      const pageWidth = doc.internal.pageSize.width;
      const contentWidth = pageWidth - pdfConfig.margins.left - pdfConfig.margins.right;

      // Add header
      doc.setFontSize(20);
      doc.setFont('helvetica', 'bold');
      doc.text(title, pdfConfig.margins.left, yPosition);
      yPosition += 15;

      // Add export info
      doc.setFontSize(10);
      doc.setFont('helvetica', 'normal');
      const exportDate = format(new Date(), 'dd.MM.yyyy HH:mm', { locale: de });
      doc.text(`Exportiert am: ${exportDate}`, pdfConfig.margins.left, yPosition);
      yPosition += 10;

      if (settings?.name) {
        doc.text(`Erstellt von: ${settings.name}`, pdfConfig.margins.left, yPosition);
        yPosition += 10;
      }

      doc.text(`Anzahl ${type === 'applications' ? 'Bewerbungen' : 'Projekte'}: ${data.length}`, pdfConfig.margins.left, yPosition);
      yPosition += 15;

      // Add data
      data.forEach((item, index) => {
        // Check if we need a new page
        if (yPosition > doc.internal.pageSize.height - 40) {
          doc.addPage();
          yPosition = pdfConfig.margins.top;
        }

        // Item header
        doc.setFontSize(12);
        doc.setFont('helvetica', 'bold');
        
        if (type === 'applications') {
          const app = item as Application;
          doc.text(`${index + 1}. ${app.project_name}`, pdfConfig.margins.left, yPosition);
          yPosition += 8;
          
          doc.setFontSize(10);
          doc.setFont('helvetica', 'normal');
          doc.text(`Unternehmen: ${app.company_name}`, pdfConfig.margins.left + 5, yPosition);
          yPosition += 6;
          doc.text(`Status: ${APPLICATION_STATUS_LABELS[app.status]}`, pdfConfig.margins.left + 5, yPosition);
          yPosition += 6;
          
          if (app.budget_range) {
            doc.text(`Budget: ${app.budget_range}`, pdfConfig.margins.left + 5, yPosition);
            yPosition += 6;
          }
          
          if (app.application_date) {
            doc.text(`Bewerbungsdatum: ${format(new Date(app.application_date), 'dd.MM.yyyy', { locale: de })}`, pdfConfig.margins.left + 5, yPosition);
            yPosition += 6;
          }
        } else {
          const project = item as Project;
          doc.text(`${index + 1}. ${project.title}`, pdfConfig.margins.left, yPosition);
          yPosition += 8;
          
          doc.setFontSize(10);
          doc.setFont('helvetica', 'normal');
          doc.text(`Client: ${project.client_name}`, pdfConfig.margins.left + 5, yPosition);
          yPosition += 6;
          doc.text(`Status: ${PROJECT_STATUS_LABELS[project.status]}`, pdfConfig.margins.left + 5, yPosition);
          yPosition += 6;
          
          if (project.hourly_rate) {
            doc.text(`Stundensatz: €${project.hourly_rate}`, pdfConfig.margins.left + 5, yPosition);
            yPosition += 6;
          }
          
          if (project.start_date) {
            doc.text(`Startdatum: ${format(new Date(project.start_date), 'dd.MM.yyyy', { locale: de })}`, pdfConfig.margins.left + 5, yPosition);
            yPosition += 6;
          }
        }

        yPosition += 5; // Space between items
      });

      // Generate and download
      const fileName = `${type === 'applications' ? 'bewerbungen' : 'projekte'}_export_${format(new Date(), 'yyyy-MM-dd')}.pdf`;
      doc.save(fileName);

      return {
        success: true,
        fileName,
        recordCount: data.length,
      };
    } catch (error) {
      console.error('PDF export error:', error);
      return {
        success: false,
        error: `Fehler beim PDF-Export: ${error instanceof Error ? error.message : 'Unbekannter Fehler'}`,
      };
    }
  }

  /**
   * Generate Excel export of applications or projects
   */
  static async generateExcel(
    data: Application[] | Project[],
    settings?: UserSettings | null,
    options: Partial<ExportOptions> = {},
    config: Partial<ExcelExportConfig> = {},
    type: 'applications' | 'projects' = 'applications'
  ): Promise<ExportResult> {
    try {
      // Validate input
      if (!data || data.length === 0) {
        return {
          success: false,
          error: type === 'applications' ? 'Keine Bewerbungen zum Exportieren vorhanden.' : 'Keine Projekte zum Exportieren vorhanden.',
        };
      }

      const excelConfig = { ...DEFAULT_EXCEL_CONFIG, ...config };
      
      // Prepare data for Excel
      let worksheetData: any[] = [];
      
      if (type === 'applications') {
        const applications = data as Application[];
        worksheetData = applications.map(app => ({
          'Projekt Name': app.project_name,
          'Unternehmen': app.company_name,
          'Status': APPLICATION_STATUS_LABELS[app.status],
          'Budget': app.budget_range || '',
          'Bewerbungsdatum': app.application_date ? format(new Date(app.application_date), 'dd.MM.yyyy', { locale: de }) : '',
          'Projektstart': app.project_start_date ? format(new Date(app.project_start_date), 'dd.MM.yyyy', { locale: de }) : '',
          'Projektende': app.project_end_date ? format(new Date(app.project_end_date), 'dd.MM.yyyy', { locale: de }) : '',
          'Arbeitsort': app.work_location_type ? WORK_LOCATION_LABELS[app.work_location_type] : '',
          'Remote %': app.remote_percentage || '',
          'Quelle': app.source || '',
          'Notizen': app.notes || '',
          'Erstellt': format(new Date(app.created_at), 'dd.MM.yyyy HH:mm', { locale: de })
        }));
      } else {
        const projects = data as Project[];
        worksheetData = projects.map(project => ({
          'Titel': project.title,
          'Client': project.client_name,
          'Status': PROJECT_STATUS_LABELS[project.status],
          'Stundensatz': project.hourly_rate ? `€${project.hourly_rate}` : '',
          'Geschätzte Stunden': project.estimated_hours || '',
          'Startdatum': project.start_date ? format(new Date(project.start_date), 'dd.MM.yyyy', { locale: de }) : '',
          'Geplantes Ende': project.planned_end_date ? format(new Date(project.planned_end_date), 'dd.MM.yyyy', { locale: de }) : '',
          'Tatsächliches Ende': project.actual_end_date ? format(new Date(project.actual_end_date), 'dd.MM.yyyy', { locale: de }) : '',
          'Priorität': project.priority,
          'Projekttyp': project.project_type,
          'Beschreibung': project.description || '',
          'Erstellt': format(new Date(project.created_at), 'dd.MM.yyyy HH:mm', { locale: de })
        }));
      }

      // Create workbook
      const wb = XLSX.utils.book_new();
      const ws = XLSX.utils.json_to_sheet(worksheetData);

      // Auto-size columns
      const colWidths = Object.keys(worksheetData[0] || {}).map(key => ({
        wch: Math.max(key.length, 15)
      }));
      ws['!cols'] = colWidths;

      // Add worksheet to workbook
      const sheetName = type === 'applications' ? 'Bewerbungen' : 'Projekte';
      XLSX.utils.book_append_sheet(wb, ws, sheetName);

      // Generate and download
      const fileName = `${type === 'applications' ? 'bewerbungen' : 'projekte'}_export_${format(new Date(), 'yyyy-MM-dd')}.xlsx`;
      XLSX.writeFile(wb, fileName);

      return {
        success: true,
        fileName,
        recordCount: data.length,
      };
    } catch (error) {
      console.error('Excel export error:', error);
      return {
        success: false,
        error: `Fehler beim Excel-Export: ${error instanceof Error ? error.message : 'Unbekannter Fehler'}`,
      };
    }
  }

  /**
   * Generate JSON backup of applications or projects
   */
  static async generateJSONBackup(
    data: Application[] | Project[],
    settings?: UserSettings | null,
    type: 'applications' | 'projects' = 'applications'
  ): Promise<ExportResult> {
    try {
      if (!data || data.length === 0) {
        return {
          success: false,
          error: type === 'applications' ? 'Keine Bewerbungen zum Exportieren vorhanden.' : 'Keine Projekte zum Exportieren vorhanden.',
        };
      }

      const backupData = {
        exportDate: new Date().toISOString(),
        exportedBy: settings?.name || 'Unbekannt',
        type: type,
        version: '1.0',
        data: data
      };

      const jsonString = JSON.stringify(backupData, null, 2);
      const blob = new Blob([jsonString], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      
      const fileName = `${type === 'applications' ? 'bewerbungen' : 'projekte'}_backup_${format(new Date(), 'yyyy-MM-dd')}.json`;
      
      // Download file
      const a = document.createElement('a');
      a.href = url;
      a.download = fileName;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);

      return {
        success: true,
        fileName,
        recordCount: data.length,
      };
    } catch (error) {
      console.error('JSON backup error:', error);
      return {
        success: false,
        error: `Fehler beim JSON-Backup: ${error instanceof Error ? error.message : 'Unbekannter Fehler'}`,
      };
    }
  }

  /**
   * Legacy method for backward compatibility with FreelanceProject
   */
  static async exportFreelanceProjects(
    projects: any[],
    format: 'pdf' | 'excel' | 'json' = 'excel',
    settings?: UserSettings | null
  ): Promise<ExportResult> {
    switch (format) {
      case 'pdf':
        return this.generatePDF(projects, settings, {}, {}, 'applications');
      case 'excel':
        return this.generateExcel(projects, settings, {}, {}, 'applications');
      case 'json':
        return this.generateJSONBackup(projects, settings, 'applications');
      default:
        return {
          success: false,
          error: 'Unbekanntes Export-Format',
        };
    }
  }
}