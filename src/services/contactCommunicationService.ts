import { supabase } from '@/integrations/supabase/client';
import { 
  ContactCommunication,
  CreateCommunicationData,
  UpdateCommunicationData,
  CommunicationWithDetails,
  CommunicationStats,
  CommunicationFilters,
  CommunicationType,
  PaginatedResponse,
  CommunicationQuery
} from '@/types/communications';

export class ContactCommunicationService {
  // Create a new communication record
  static async createCommunication(
    data: CreateCommunicationData
  ): Promise<ContactCommunication | null> {
    try {
      // Get current user for security validation
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        console.error('User not authenticated for communication creation');
        return null;
      }

      // Prepare data with user_id
      const communicationData = {
        ...data,
        user_id: user.id,
        updated_at: new Date().toISOString()
      };

      const { data: communication, error } = await supabase
        .from('contact_communications')
        .insert([communicationData])
        .select()
        .single();

      if (error) {
        console.error('Error creating communication:', error);
        return null;
      }

      return communication as ContactCommunication;
    } catch (error) {
      console.error('Error creating communication:', error);
      return null;
    }
  }

  // Get all communications for a specific contact
  static async getCommunicationsByContact(
    contactId: string,
    limit: number = 50
  ): Promise<ContactCommunication[]> {
    try {
      const { data, error } = await supabase
        .from('contact_communications')
        .select('*')
        .eq('contact_id', contactId)
        .order('communication_date', { ascending: false })
        .limit(limit);

      if (error) {
        console.error('Error fetching communications:', error);
        return [];
      }

      return data as ContactCommunication[];
    } catch (error) {
      console.error('Error fetching communications:', error);
      return [];
    }
  }

  // Get communications with detailed contact and project information (legacy method)
  static async getCommunicationsWithDetails(
    filters: CommunicationFilters = {},
    limit: number = 50
  ): Promise<CommunicationWithDetails[]> {
    try {
      let query = supabase
        .from('contact_communications')
        .select(`
          *,
          contact:contacts!inner(id, name, company, email),
          project:project_applications(id, project_name, company_name)
        `)
        .order('communication_date', { ascending: false })
        .limit(limit);

      // Apply filters
      if (filters.contact_id) {
        query = query.eq('contact_id', filters.contact_id);
      }
      if (filters.communication_type) {
        query = query.eq('communication_type', filters.communication_type);
      }
      if (filters.project_id) {
        query = query.eq('project_id', filters.project_id);
      }
      if (filters.is_project_related !== undefined) {
        query = query.eq('is_project_related', filters.is_project_related);
      }
      if (filters.date_from) {
        query = query.gte('communication_date', filters.date_from);
      }
      if (filters.date_to) {
        query = query.lte('communication_date', filters.date_to);
      }
      if (filters.search_text) {
        query = query.or(
          `notes.ilike.%${filters.search_text}%,subject.ilike.%${filters.search_text}%`
        );
      }

      const { data, error } = await query;

      if (error) {
        console.error('Error fetching communications with details:', error);
        return [];
      }

      return data as CommunicationWithDetails[];
    } catch (error) {
      console.error('Error fetching communications with details:', error);
      return [];
    }
  }

  // Enhanced method with pagination support
  static async getCommunicationsWithDetailsPaginated(
    query: CommunicationQuery
  ): Promise<PaginatedResponse<CommunicationWithDetails>> {
    try {
      const { page, limit, ...filters } = query;
      const offset = (page - 1) * limit;

      // Build the base query
      let supabaseQuery = supabase
        .from('contact_communications')
        .select(`
          *,
          contact:contacts!inner(id, name, company, email),
          project:project_applications(id, project_name, company_name)
        `, { count: 'exact' })
        .order('communication_date', { ascending: false })
        .range(offset, offset + limit - 1);

      // Apply filters
      if (filters.contact_id) {
        supabaseQuery = supabaseQuery.eq('contact_id', filters.contact_id);
      }
      if (filters.communication_type) {
        supabaseQuery = supabaseQuery.eq('communication_type', filters.communication_type);
      }
      if (filters.project_id) {
        supabaseQuery = supabaseQuery.eq('project_id', filters.project_id);
      }
      if (filters.is_project_related !== undefined) {
        supabaseQuery = supabaseQuery.eq('is_project_related', filters.is_project_related);
      }
      if (filters.date_from) {
        supabaseQuery = supabaseQuery.gte('communication_date', filters.date_from);
      }
      if (filters.date_to) {
        supabaseQuery = supabaseQuery.lte('communication_date', filters.date_to);
      }
      if (filters.search_text) {
        supabaseQuery = supabaseQuery.or(
          `notes.ilike.%${filters.search_text}%,subject.ilike.%${filters.search_text}%`
        );
      }

      const { data, error, count } = await supabaseQuery;

      if (error) {
        console.error('Error fetching paginated communications with details:', error);
        return {
          data: [],
          pagination: {
            page,
            limit,
            total: 0,
            totalPages: 0,
            hasNextPage: false,
            hasPreviousPage: false
          }
        };
      }

      const total = count || 0;
      const totalPages = Math.ceil(total / limit);

      return {
        data: data as CommunicationWithDetails[],
        pagination: {
          page,
          limit,
          total,
          totalPages,
          hasNextPage: page < totalPages,
          hasPreviousPage: page > 1
        }
      };
    } catch (error) {
      console.error('Error fetching paginated communications with details:', error);
      return {
        data: [],
        pagination: {
          page: query.page,
          limit: query.limit,
          total: 0,
          totalPages: 0,
          hasNextPage: false,
          hasPreviousPage: false
        }
      };
    }
  }

  // Update an existing communication
  static async updateCommunication(
    data: UpdateCommunicationData
  ): Promise<ContactCommunication | null> {
    try {
      // Get current user for security validation
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        console.error('User not authenticated for communication update');
        return null;
      }

      const updateData = {
        ...data,
        updated_at: new Date().toISOString()
      };

      // Remove id from update data
      const { id, ...updateFields } = updateData;

      const { data: communication, error } = await supabase
        .from('contact_communications')
        .update(updateFields)
        .eq('id', id)
        .eq('user_id', user.id) // Ensure user can only update their own communications
        .select()
        .single();

      if (error) {
        console.error('Error updating communication:', error);
        return null;
      }

      return communication as ContactCommunication;
    } catch (error) {
      console.error('Error updating communication:', error);
      return null;
    }
  }

  // Delete a communication
  static async deleteCommunication(communicationId: string): Promise<boolean> {
    try {
      // Get current user for security validation
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        console.error('User not authenticated for communication deletion');
        return false;
      }

      const { error } = await supabase
        .from('contact_communications')
        .delete()
        .eq('id', communicationId)
        .eq('user_id', user.id); // Ensure user can only delete their own communications

      if (error) {
        console.error('Error deleting communication:', error);
        return false;
      }

      return true;
    } catch (error) {
      console.error('Error deleting communication:', error);
      return false;
    }
  }

  // Get communication statistics for a contact
  static async getCommunicationStats(contactId: string): Promise<CommunicationStats> {
    try {
      const { data: communications, error } = await supabase
        .from('contact_communications')
        .select('*')
        .eq('contact_id', contactId)
        .order('communication_date', { ascending: false });

      if (error) {
        console.error('Error fetching communication stats:', error);
        return {
          total_communications: 0,
          communication_by_type: {} as Record<CommunicationType, number>,
          recent_communications: []
        };
      }

      // Calculate statistics
      const stats: CommunicationStats = {
        total_communications: communications.length,
        last_communication_date: communications[0]?.communication_date,
        communication_by_type: {} as Record<CommunicationType, number>,
        recent_communications: communications.slice(0, 5) as ContactCommunication[]
      };

      // Count by type
      communications.forEach(comm => {
        const type = comm.communication_type as CommunicationType;
        stats.communication_by_type[type] = (stats.communication_by_type[type] || 0) + 1;
      });

      return stats;
    } catch (error) {
      console.error('Error calculating communication stats:', error);
      return {
        total_communications: 0,
        communication_by_type: {} as Record<CommunicationType, number>,
        recent_communications: []
      };
    }
  }

  // Get recent communications across all contacts
  static async getRecentCommunications(limit: number = 10): Promise<CommunicationWithDetails[]> {
    try {
      const { data, error } = await supabase
        .from('contact_communications')
        .select(`
          *,
          contact:contacts!inner(id, name, company, email),
          project:project_applications(id, project_name, company_name)
        `)
        .order('communication_date', { ascending: false })
        .limit(limit);

      if (error) {
        console.error('Error fetching recent communications:', error);
        return [];
      }

      return data as CommunicationWithDetails[];
    } catch (error) {
      console.error('Error fetching recent communications:', error);
      return [];
    }
  }

  // Search communications by text
  static async searchCommunications(
    searchText: string,
    limit: number = 20
  ): Promise<CommunicationWithDetails[]> {
    try {
      const { data, error } = await supabase
        .from('contact_communications')
        .select(`
          *,
          contact:contacts!inner(id, name, company, email),
          project:project_applications(id, project_name, company_name)
        `)
        .or(`notes.ilike.%${searchText}%,subject.ilike.%${searchText}%,summarized_notes.ilike.%${searchText}%`)
        .order('communication_date', { ascending: false })
        .limit(limit);

      if (error) {
        console.error('Error searching communications:', error);
        return [];
      }

      return data as CommunicationWithDetails[];
    } catch (error) {
      console.error('Error searching communications:', error);
      return [];
    }
  }

  // Update communication with AI summary
  static async updateCommunicationSummary(
    communicationId: string,
    summary: string
  ): Promise<boolean> {
    try {
      // Get current user for security validation
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        console.error('User not authenticated for summary update');
        return false;
      }

      const { error } = await supabase
        .from('contact_communications')
        .update({
          summarized_notes: summary,
          updated_at: new Date().toISOString()
        })
        .eq('id', communicationId)
        .eq('user_id', user.id);

      if (error) {
        console.error('Error updating communication summary:', error);
        return false;
      }

      return true;
    } catch (error) {
      console.error('Error updating communication summary:', error);
      return false;
    }
  }

  // Get communications for a specific project
  static async getCommunicationsByProject(
    projectId: string,
    limit: number = 50
  ): Promise<CommunicationWithDetails[]> {
    try {
      const { data, error } = await supabase
        .from('contact_communications')
        .select(`
          *,
          contact:contacts!inner(id, name, company, email),
          project:project_applications!inner(id, project_name, company_name)
        `)
        .eq('project_id', projectId)
        .order('communication_date', { ascending: false })
        .limit(limit);

      if (error) {
        console.error('Error fetching project communications:', error);
        return [];
      }

      return data as CommunicationWithDetails[];
    } catch (error) {
      console.error('Error fetching project communications:', error);
      return [];
    }
  }

  // Export communications for a contact (for backup/analysis)
  static async exportCommunications(contactId?: string): Promise<ContactCommunication[]> {
    try {
      let query = supabase
        .from('contact_communications')
        .select('*')
        .order('communication_date', { ascending: false });

      if (contactId) {
        query = query.eq('contact_id', contactId);
      }

      const { data, error } = await query;

      if (error) {
        console.error('Error exporting communications:', error);
        return [];
      }

      return data as ContactCommunication[];
    } catch (error) {
      console.error('Error exporting communications:', error);
      return [];
    }
  }

  // Get communication by ID
  static async getCommunicationById(communicationId: string): Promise<ContactCommunication | null> {
    try {
      const { data, error } = await supabase
        .from('contact_communications')
        .select('*')
        .eq('id', communicationId)
        .single();

      if (error) {
        console.error('Error fetching communication by ID:', error);
        return null;
      }

      return data as ContactCommunication;
    } catch (error) {
      console.error('Error fetching communication by ID:', error);
      return null;
    }
  }

  // Bulk delete communications
  static async bulkDeleteCommunications(communicationIds: string[]): Promise<boolean> {
    try {
      // Get current user for security validation
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        console.error('User not authenticated for bulk deletion');
        return false;
      }

      const { error } = await supabase
        .from('contact_communications')
        .delete()
        .in('id', communicationIds)
        .eq('user_id', user.id);

      if (error) {
        console.error('Error bulk deleting communications:', error);
        return false;
      }

      return true;
    } catch (error) {
      console.error('Error bulk deleting communications:', error);
      return false;
    }
  }
}