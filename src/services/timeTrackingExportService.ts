import { format } from 'date-fns';
import { de } from 'date-fns/locale';
import * as XLSX from 'xlsx';
import { jsPDF } from 'jspdf';
import { autoTable } from 'jspdf-autotable';
import type { TimeEntryWithDuration } from '@/types/projects';
import type { ProjectWithStats } from '@/types/projects';

export interface ExportData {
  entries: TimeEntryWithDuration[];
  projects: ProjectWithStats[];
  period: string;
  totalHours: number;
  billableHours: number;
}

export class TimeTrackingExportService {
  /**
   * Export time tracking data to Excel
   */
  static exportToExcel(data: ExportData): void {
    const { entries, projects, period, totalHours, billableHours } = data;

    // Create workbook
    const wb = XLSX.utils.book_new();

    // Summary sheet
    const summaryData = [
      ['Zeitraum:', period],
      ['Gesamtstunden:', `${totalHours.toFixed(1)}h`],
      ['Abrechenbare Stunden:', `${billableHours.toFixed(1)}h`],
      ['<PERSON><PERSON><PERSON> Einträge:', entries.length.toString()],
      [''],
      ['Generiert am:', format(new Date(), 'dd.MM.yyyy HH:mm', { locale: de })]
    ];

    const summaryWS = XLSX.utils.aoa_to_sheet(summaryData);
    XLSX.utils.book_append_sheet(wb, summaryWS, 'Zusammenfassung');

    // Detailed entries sheet
    const entriesData = [
      ['Datum', 'Start', 'Ende', 'Projekt', 'Client', 'Kategorie', 'Beschreibung', 'Dauer', 'Abrechenbar']
    ];

    entries
      .sort((a, b) => new Date(b.start_time).getTime() - new Date(a.start_time).getTime())
      .forEach(entry => {
        const project = projects.find(p => p.id === entry.project_id);
        entriesData.push([
          format(new Date(entry.start_time), 'dd.MM.yyyy', { locale: de }),
          format(new Date(entry.start_time), 'HH:mm'),
          entry.end_time ? format(new Date(entry.end_time), 'HH:mm') : '',
          project?.title || 'Unknown Project',
          project?.client_name || '',
          entry.category || '',
          entry.description || '',
          entry.formatted_duration || '',
          entry.billable ? 'Ja' : 'Nein'
        ]);
      });

    const entriesWS = XLSX.utils.aoa_to_sheet(entriesData);
    
    // Set column widths
    entriesWS['!cols'] = [
      { width: 12 }, // Datum
      { width: 8 },  // Start
      { width: 8 },  // Ende
      { width: 25 }, // Projekt
      { width: 20 }, // Client
      { width: 15 }, // Kategorie
      { width: 30 }, // Beschreibung
      { width: 10 }, // Dauer
      { width: 12 }  // Abrechenbar
    ];

    XLSX.utils.book_append_sheet(wb, entriesWS, 'Zeiteinträge');

    // Project summary sheet
    const projectStats = entries.reduce((acc, entry) => {
      const project = projects.find(p => p.id === entry.project_id);
      const projectName = project?.title || 'Unknown Project';
      const duration = entry.calculated_duration || 0;
      
      if (!acc[projectName]) {
        acc[projectName] = {
          total: 0,
          billable: 0,
          entries: 0,
          client: project?.client_name || ''
        };
      }
      
      acc[projectName].total += duration;
      acc[projectName].entries += 1;
      if (entry.billable) {
        acc[projectName].billable += duration;
      }
      
      return acc;
    }, {} as Record<string, { total: number; billable: number; entries: number; client: string }>);

    const projectData = [
      ['Projekt', 'Client', 'Gesamtstunden', 'Abrechenbare Stunden', 'Einträge']
    ];

    Object.entries(projectStats)
      .sort(([, a], [, b]) => b.total - a.total)
      .forEach(([projectName, stats]) => {
        projectData.push([
          projectName,
          stats.client,
          `${(stats.total / 60).toFixed(1)}h`,
          `${(stats.billable / 60).toFixed(1)}h`,
          stats.entries.toString()
        ]);
      });

    const projectWS = XLSX.utils.aoa_to_sheet(projectData);
    projectWS['!cols'] = [
      { width: 25 }, // Projekt
      { width: 20 }, // Client
      { width: 15 }, // Gesamtstunden
      { width: 18 }, // Abrechenbare Stunden
      { width: 10 }  // Einträge
    ];

    XLSX.utils.book_append_sheet(wb, projectWS, 'Nach Projekt');

    // Generate filename
    const filename = `Zeiterfassung_${format(new Date(), 'yyyy-MM-dd_HH-mm')}.xlsx`;
    
    // Save file
    XLSX.writeFile(wb, filename);
  }

  /**
   * Export time tracking data to PDF
   */
  static exportToPDF(data: ExportData): void {
    const { entries, projects, period, totalHours, billableHours } = data;

    const doc = new jsPDF();
    
    // Title
    doc.setFontSize(20);
    doc.text('Zeiterfassung - Bericht', 20, 30);
    
    // Summary
    doc.setFontSize(12);
    doc.text(`Zeitraum: ${period}`, 20, 50);
    doc.text(`Gesamtstunden: ${totalHours.toFixed(1)}h`, 20, 60);
    doc.text(`Abrechenbare Stunden: ${billableHours.toFixed(1)}h`, 20, 70);
    doc.text(`Anzahl Einträge: ${entries.length}`, 20, 80);
    doc.text(`Generiert am: ${format(new Date(), 'dd.MM.yyyy HH:mm', { locale: de })}`, 20, 90);

    // Group entries by day for better structure
    const entriesByDay = entries.reduce((acc, entry) => {
      const day = format(new Date(entry.start_time), 'yyyy-MM-dd');
      if (!acc[day]) acc[day] = [];
      acc[day].push(entry);
      return acc;
    }, {} as Record<string, TimeEntryWithDuration[]>);

    let yPosition = 110;

    // Daily breakdown
    Object.entries(entriesByDay)
      .sort(([a], [b]) => b.localeCompare(a))
      .forEach(([day, dayEntries]) => {
        // Check if we need a new page
        if (yPosition > 250) {
          doc.addPage();
          yPosition = 30;
        }

        const dayTotal = dayEntries.reduce((sum, entry) => sum + (entry.calculated_duration || 0), 0);
        const dayBillable = dayEntries
          .filter(entry => entry.billable)
          .reduce((sum, entry) => sum + (entry.calculated_duration || 0), 0);

        // Day header
        doc.setFontSize(14);
        doc.setFont('helvetica', 'bold');
        doc.text(
          `${format(new Date(day), 'EEEE, dd.MM.yyyy', { locale: de })} - ${(dayTotal / 60).toFixed(1)}h (${(dayBillable / 60).toFixed(1)}h abrechenbar)`,
          20,
          yPosition
        );
        yPosition += 15;

        // Day entries table
        const tableData = dayEntries
          .sort((a, b) => new Date(a.start_time).getTime() - new Date(b.start_time).getTime())
          .map(entry => {
            const project = projects.find(p => p.id === entry.project_id);
            return [
              format(new Date(entry.start_time), 'HH:mm'),
              entry.end_time ? format(new Date(entry.end_time), 'HH:mm') : '',
              project?.title || 'Unknown',
              entry.category || '',
              entry.description || '',
              entry.formatted_duration || '',
              entry.billable ? 'Ja' : 'Nein'
            ];
          });

        autoTable(doc, {
          startY: yPosition,
          head: [['Start', 'Ende', 'Projekt', 'Kategorie', 'Beschreibung', 'Dauer', 'Abr.']],
          body: tableData,
          styles: { fontSize: 8 },
          headStyles: { fillColor: [70, 130, 180] },
          columnStyles: {
            0: { cellWidth: 15 }, // Start
            1: { cellWidth: 15 }, // Ende
            2: { cellWidth: 35 }, // Projekt
            3: { cellWidth: 20 }, // Kategorie
            4: { cellWidth: 40 }, // Beschreibung
            5: { cellWidth: 15 }, // Dauer
            6: { cellWidth: 10 }  // Abrechenbar
          },
          margin: { left: 20, right: 20 }
        });

        yPosition = (doc as any).lastAutoTable.finalY + 15;
      });

    // Project summary on new page
    doc.addPage();
    yPosition = 30;

    doc.setFontSize(16);
    doc.setFont('helvetica', 'bold');
    doc.text('Zusammenfassung nach Projekt', 20, yPosition);
    yPosition += 20;

    // Calculate project stats
    const projectStats = entries.reduce((acc, entry) => {
      const project = projects.find(p => p.id === entry.project_id);
      const projectName = project?.title || 'Unknown Project';
      const duration = entry.calculated_duration || 0;
      
      if (!acc[projectName]) {
        acc[projectName] = {
          total: 0,
          billable: 0,
          entries: 0,
          client: project?.client_name || ''
        };
      }
      
      acc[projectName].total += duration;
      acc[projectName].entries += 1;
      if (entry.billable) {
        acc[projectName].billable += duration;
      }
      
      return acc;
    }, {} as Record<string, { total: number; billable: number; entries: number; client: string }>);

    const projectTableData = Object.entries(projectStats)
      .sort(([, a], [, b]) => b.total - a.total)
      .map(([projectName, stats]) => [
        projectName,
        stats.client,
        `${(stats.total / 60).toFixed(1)}h`,
        `${(stats.billable / 60).toFixed(1)}h`,
        stats.entries.toString()
      ]);

    autoTable(doc, {
      startY: yPosition,
      head: [['Projekt', 'Client', 'Gesamt', 'Abrechenbar', 'Einträge']],
      body: projectTableData,
      styles: { fontSize: 10 },
      headStyles: { fillColor: [70, 130, 180] },
      columnStyles: {
        0: { cellWidth: 50 }, // Projekt
        1: { cellWidth: 40 }, // Client
        2: { cellWidth: 25 }, // Gesamt
        3: { cellWidth: 25 }, // Abrechenbar
        4: { cellWidth: 20 }  // Einträge
      },
      margin: { left: 20, right: 20 }
    });

    // Generate filename
    const filename = `Zeiterfassung_${format(new Date(), 'yyyy-MM-dd_HH-mm')}.pdf`;
    
    // Save file
    doc.save(filename);
  }
}