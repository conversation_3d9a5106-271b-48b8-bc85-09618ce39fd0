import { supabase } from '@/integrations/supabase/client';
import { Application, APPLICATION_STATUS_LABELS } from '@/types/applications';
import { ActivityType } from '@/types/shared';

interface ActivityData {
  project_id: string;
  user_id: string;
  activity_type: ActivityType;
  description: string;
  notes?: string;
  notes_date?: string;
}

export class ActivityService {
  /**
   * Get current user ID for activity logging
   */
  private static async getCurrentUserId(): Promise<string | null> {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      return user?.id || null;
    } catch (error) {
      console.error('Failed to get current user:', error);
      return null;
    }
  }

  /**
   * Main activity logging method used by hooks
   */
  static async logActivity(
    projectId: string, 
    activityType: ActivityType, 
    description: string,
    notes?: string,
    notesDate?: string
  ): Promise<void> {
    try {
      const userId = await this.getCurrentUserId();
      if (!userId) {
        console.warn('Cannot log activity: No authenticated user');
        return;
      }

      // Use provided notesDate or default to current timestamp
      const finalNotesDate = notesDate || new Date().toISOString();

      await this.log({
        project_id: projectId,
        user_id: userId,
        activity_type: activityType,
        description,
        notes,
        notes_date: finalNotesDate
      });
    } catch (error) {
      console.error('Activity logging error:', error);
    }
  }

  /**
   * Simple activity logging - internal method
   */
  static async log(data: ActivityData): Promise<void> {
    try {
      const { error } = await supabase
        .from('project_activities')
        .insert([data]);

      if (error) {
        console.error('Failed to log activity:', error);
      }
    } catch (error) {
      console.error('Activity logging error:', error);
    }
  }

  /**
   * Smart change detection - compare old vs new application
   * Enhanced with notes support for status changes
   */
  static async logApplicationChanges(
    oldApplication: Application, 
    newApplication: Application, 
    statusNotes?: { notes?: string; notes_date?: string }
  ): Promise<void> {
    const { id: project_id } = newApplication;

    // Status change (most important) - now with optional notes
    if (oldApplication.status !== newApplication.status) {
      const oldLabel = APPLICATION_STATUS_LABELS[oldApplication.status] || oldApplication.status;
      const newLabel = APPLICATION_STATUS_LABELS[newApplication.status] || newApplication.status;
      
      await this.logActivity(
        project_id,
        'application_status_changed',
        `Status: ${oldLabel} → ${newLabel}`,
        statusNotes?.notes,
        statusNotes?.notes_date
      );
    }

    // Other significant changes
    if (oldApplication.contact_email !== newApplication.contact_email ||
        oldApplication.contact_phone !== newApplication.contact_phone ||
        oldApplication.contact_person !== newApplication.contact_person) {
      await this.logActivity(
        project_id,
        'contact_updated',
        'Kontakte aktualisiert'
      );
    }

    if (oldApplication.notes !== newApplication.notes) {
      await this.logActivity(
        project_id,
        'application_updated',
        'Notizen aktualisiert'
      );
    }

    if (oldApplication.application_date !== newApplication.application_date && newApplication.application_date) {
      await this.logActivity(
        project_id,
        'application_sent',
        `Bewerbung versendet am ${new Date(newApplication.application_date).toLocaleDateString('de-DE')}`
      );
    }
  }

  /**
   * Log application creation
   */
  static async logApplicationCreated(application: Application): Promise<void> {
    await this.logActivity(
      application.id,
      'application_created',
      `Bewerbung "${application.project_name}" erstellt`
    );
  }
}