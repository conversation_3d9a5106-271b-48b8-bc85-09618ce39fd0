import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { AISummaryService, RateLimitError } from '../aiSummaryService'
import { ValidationError, SecurityError } from '@/lib/validation/communicationValidation'
import { supabase } from '@/integrations/supabase/client'
import { aiRateLimiter } from '@/lib/rateLimiting/aiRateLimiter'

// Mock dependencies
vi.mock('@/integrations/supabase/client', () => ({
  supabase: {
    auth: {
      getUser: vi.fn(),
      getSession: vi.fn()
    },
    functions: {
      invoke: vi.fn()
    },
    from: vi.fn()
  }
}))

vi.mock('@/lib/rateLimiting/aiRateLimiter', () => ({
  aiRateLimiter: {
    checkRateLimit: vi.fn(),
    getUserStats: vi.fn()
  }
}))

vi.mock('@/lib/validation/communicationValidation', () => ({
  validateNotes: vi.fn(),
  validateSubject: vi.fn(),
  validateCommunicationType: vi.fn(),
  ValidationError: class ValidationError extends Error {
    constructor(message: string) {
      super(message)
      this.name = 'ValidationError'
    }
  },
  SecurityError: class SecurityError extends Error {
    constructor(message: string) {
      super(message)
      this.name = 'SecurityError'
    }
  }
}))

vi.mock('@/i18n', () => ({
  default: {
    t: vi.fn((key: string) => key)
  }
}))

const mockUser = {
  id: 'user-123',
  email: '<EMAIL>'
}

const mockSession = {
  access_token: 'mock-token-123'
}

const mockCommunication = {
  id: 'comm-123',
  notes: 'This is a test communication with detailed notes about the project discussion.',
  communication_type: 'email',
  subject: 'Project Discussion',
  contact: {
    id: 'contact-123',
    name: 'John Doe',
    company: 'Tech Corp',
    email: '<EMAIL>'
  },
  project: {
    id: 'project-123',
    project_name: 'React App Development',
    company_name: 'Tech Corp'
  }
}

const mockEdgeFunctionResponse = {
  summary: 'AI-generated summary of the communication'
}

describe('AISummaryService', () => {
  beforeEach(async () => {
    vi.clearAllMocks()
    
    // Setup default mocks
    vi.mocked(supabase.auth.getUser).mockResolvedValue({
      data: { user: mockUser },
      error: null
    })
    
    vi.mocked(supabase.auth.getSession).mockResolvedValue({
      data: { session: mockSession },
      error: null
    })
    
    vi.mocked(aiRateLimiter.checkRateLimit).mockReturnValue({
      allowed: true,
      remaining: 10,
      resetTime: Date.now() + 3600000,
      message: ''
    })
    
    // Setup validation mocks
    const { validateNotes, validateSubject, validateCommunicationType } = await import('@/lib/validation/communicationValidation')
    vi.mocked(validateNotes).mockImplementation((notes) => notes)
    vi.mocked(validateSubject).mockImplementation((subject) => subject || '')
    vi.mocked(validateCommunicationType).mockImplementation((type) => type)
  })

  afterEach(() => {
    vi.resetAllMocks()
  })

  describe('generateSummary', () => {
    beforeEach(() => {
      vi.mocked(supabase.functions.invoke).mockResolvedValue({
        data: mockEdgeFunctionResponse,
        error: null
      })
    })

    it('should generate summary with valid input', async () => {
      const input = {
        notes: 'This is a test communication with detailed notes.',
        communicationType: 'email',
        subject: 'Test Subject',
        contactName: 'John Doe',
        companyName: 'Tech Corp'
      }

      const result = await AISummaryService.generateSummary(input)

      expect(result).toBe('AI-generated summary of the communication')
      expect(supabase.functions.invoke).toHaveBeenCalledWith('summarize-communication', {
        body: {
          direct_mode: true,
          summary_type: 'brief',
          notes: input.notes,
          communication_type: input.communicationType,
          subject: input.subject,
          contact_name: input.contactName,
          company_name: input.companyName,
          project_context: undefined,
          context_include_project: false
        },
        headers: {
          Authorization: `Bearer ${mockSession.access_token}`
        }
      })
    })

    it('should handle different summary types', async () => {
      const input = {
        notes: 'Test notes',
        communicationType: 'call',
        summaryType: 'detailed' as const
      }

      await AISummaryService.generateSummary(input)

      expect(supabase.functions.invoke).toHaveBeenCalledWith(
        'summarize-communication',
        expect.objectContaining({
          body: expect.objectContaining({
            summary_type: 'detailed'
          })
        })
      )
    })

    it('should include project context when provided', async () => {
      const input = {
        notes: 'Test notes',
        communicationType: 'meeting',
        projectContext: {
          projectName: 'Test Project',
          companyName: 'Test Company',
          status: 'In Progress'
        },
        includeProjectContext: true
      }

      await AISummaryService.generateSummary(input)

      expect(supabase.functions.invoke).toHaveBeenCalledWith(
        'summarize-communication',
        expect.objectContaining({
          body: expect.objectContaining({
            project_context: input.projectContext,
            context_include_project: true
          })
        })
      )
    })

    it('should throw ValidationError when user is not authenticated', async () => {
      vi.mocked(supabase.auth.getUser).mockResolvedValue({
        data: { user: null },
        error: new Error('Not authenticated')
      })

      const input = {
        notes: 'Test notes',
        communicationType: 'email'
      }

      await expect(AISummaryService.generateSummary(input)).rejects.toThrow(ValidationError)
    })

    it('should throw RateLimitError when rate limit is exceeded', async () => {
      vi.mocked(aiRateLimiter.checkRateLimit).mockReturnValue({
        allowed: false,
        remaining: 0,
        resetTime: Date.now() + 3600000,
        message: 'Rate limit exceeded'
      })

      const input = {
        notes: 'Test notes',
        communicationType: 'email'
      }

      await expect(AISummaryService.generateSummary(input)).rejects.toThrow(RateLimitError)
    })

    it('should throw error when session token is missing', async () => {
      vi.mocked(supabase.auth.getSession).mockResolvedValue({
        data: { session: null },
        error: null
      })

      const input = {
        notes: 'Test notes',
        communicationType: 'email'
      }

      await expect(AISummaryService.generateSummary(input)).rejects.toThrow('Keine gültige Sitzung für AI-Zusammenfassung')
    })

    it('should throw error when edge function returns error', async () => {
      vi.mocked(supabase.functions.invoke).mockResolvedValue({
        data: null,
        error: { message: 'Edge function error' }
      })

      const input = {
        notes: 'Test notes',
        communicationType: 'email'
      }

      await expect(AISummaryService.generateSummary(input)).rejects.toThrow('Edge Function Fehler')
    })

    it('should throw error when no summary is returned', async () => {
      vi.mocked(supabase.functions.invoke).mockResolvedValue({
        data: { summary: null },
        error: null
      })

      const input = {
        notes: 'Test notes',
        communicationType: 'email'
      }

      await expect(AISummaryService.generateSummary(input)).rejects.toThrow('AI-Antwort enthält keine Zusammenfassung')
    })

    it('should validate input parameters', async () => {
      const validationModule = await import('@/lib/validation/communicationValidation')
      vi.mocked(validationModule.validateNotes).mockImplementation(() => {
        throw new ValidationError('Notes are too short')
      })

      const input = {
        notes: 'short',
        communicationType: 'email'
      }

      await expect(AISummaryService.generateSummary(input)).rejects.toThrow(ValidationError)
    })

    it('should handle security validation errors', async () => {
      const validationModule = await import('@/lib/validation/communicationValidation')
      vi.mocked(validationModule.validateNotes).mockImplementation(() => {
        throw new SecurityError('Contains sensitive information')
      })

      const input = {
        notes: 'sensitive data',
        communicationType: 'email'
      }

      await expect(AISummaryService.generateSummary(input)).rejects.toThrow(SecurityError)
    })
  })

  describe('summarizeFromCommunication', () => {
    beforeEach(() => {
      vi.mocked(supabase.from).mockReturnValue({
        select: vi.fn().mockReturnThis(),
        eq: vi.fn().mockReturnThis(),
        single: vi.fn().mockResolvedValue({
          data: mockCommunication,
          error: null
        })
      } as any)

      vi.mocked(supabase.functions.invoke).mockResolvedValue({
        data: mockEdgeFunctionResponse,
        error: null
      })
    })

    it('should fetch communication and generate summary', async () => {
      const options = {
        communicationId: 'comm-123',
        contactId: 'contact-123',
        summaryType: 'brief' as const,
        includeProjectContext: false
      }

      const result = await AISummaryService.summarizeFromCommunication(options)

      expect(result).toBe('AI-generated summary of the communication')
      expect(supabase.from).toHaveBeenCalledWith('contact_communications')
    })

    it('should include project context when requested', async () => {
      const options = {
        communicationId: 'comm-123',
        contactId: 'contact-123',
        includeProjectContext: true
      }

      await AISummaryService.summarizeFromCommunication(options)

      expect(supabase.functions.invoke).toHaveBeenCalledWith(
        'summarize-communication',
        expect.objectContaining({
          body: expect.objectContaining({
            project_context: expect.objectContaining({
              projectName: 'React App Development',
              companyName: 'Tech Corp'
            })
          })
        })
      )
    })

    it('should throw error when communication is not found', async () => {
      vi.mocked(supabase.from).mockReturnValue({
        select: vi.fn().mockReturnThis(),
        eq: vi.fn().mockReturnThis(),
        single: vi.fn().mockResolvedValue({
          data: null,
          error: { message: 'Not found' }
        })
      } as any)

      const options = {
        communicationId: 'nonexistent',
        contactId: 'contact-123'
      }

      await expect(AISummaryService.summarizeFromCommunication(options)).rejects.toThrow('Kommunikation nicht gefunden')
    })
  })

  describe('legacy compatibility methods', () => {
    beforeEach(() => {
      vi.mocked(supabase.from).mockReturnValue({
        select: vi.fn().mockReturnThis(),
        eq: vi.fn().mockReturnThis(),
        single: vi.fn().mockResolvedValue({
          data: mockCommunication,
          error: null
        })
      } as any)

      vi.mocked(supabase.functions.invoke).mockResolvedValue({
        data: mockEdgeFunctionResponse,
        error: null
      })
    })

    it('should generate brief summary', async () => {
      const result = await AISummaryService.generateBriefSummary('comm-123', 'contact-123', false)
      expect(result).toBe('AI-generated summary of the communication')
    })

    it('should generate detailed summary', async () => {
      const result = await AISummaryService.generateDetailedSummary('comm-123', 'contact-123', true)
      expect(result).toBe('AI-generated summary of the communication')
    })

    it('should extract action items', async () => {
      const result = await AISummaryService.extractActionItems('comm-123', 'contact-123', true)
      expect(result).toBe('AI-generated summary of the communication')
    })

    it('should generate follow-up recommendations', async () => {
      const result = await AISummaryService.generateFollowUpRecommendations('comm-123', 'contact-123', true)
      expect(result).toBe('AI-generated summary of the communication')
    })

    it('should handle auto-summarization with threshold', async () => {
      const result = await AISummaryService.autoSummarizeIfNeeded('comm-123', 'contact-123', 300, 200)
      expect(result).toBe('AI-generated summary of the communication')
    })

    it('should skip auto-summarization when below threshold', async () => {
      const result = await AISummaryService.autoSummarizeIfNeeded('comm-123', 'contact-123', 100, 200)
      expect(result).toBeNull()
    })

    it('should return null on error in detailed summary', async () => {
      vi.mocked(supabase.functions.invoke).mockRejectedValue(new Error('API Error'))
      
      const result = await AISummaryService.generateDetailedSummary('comm-123', 'contact-123', true)
      expect(result).toBeNull()
    })
  })

  describe('batchSummarize', () => {
    beforeEach(() => {
      vi.mocked(supabase.from).mockReturnValue({
        select: vi.fn().mockReturnThis(),
        eq: vi.fn().mockReturnThis(),
        single: vi.fn().mockResolvedValue({
          data: mockCommunication,
          error: null
        })
      } as any)

      vi.mocked(supabase.functions.invoke).mockResolvedValue({
        data: mockEdgeFunctionResponse,
        error: null
      })
    })

    it('should process multiple communications', async () => {
      const communications = [
        { communicationId: 'comm-1', contactId: 'contact-1', notesLength: 300 },
        { communicationId: 'comm-2', contactId: 'contact-2', notesLength: 250 }
      ]

      const results = await AISummaryService.batchSummarize(communications, 'brief', 200)

      expect(results).toHaveLength(2)
      expect(results[0].summary).toBe('AI-generated summary of the communication')
      expect(results[1].summary).toBe('AI-generated summary of the communication')
    })

    it('should skip communications below threshold', async () => {
      const communications = [
        { communicationId: 'comm-1', contactId: 'contact-1', notesLength: 100 }
      ]

      const results = await AISummaryService.batchSummarize(communications, 'brief', 200)

      expect(results).toHaveLength(1)
      expect(results[0].summary).toBeNull()
      expect(results[0].error).toBe('Notes too short for summarization')
    })

    it('should handle individual errors gracefully', async () => {
      vi.mocked(supabase.functions.invoke)
        .mockResolvedValueOnce({ data: mockEdgeFunctionResponse, error: null })
        .mockRejectedValueOnce(new Error('API Error'))

      const communications = [
        { communicationId: 'comm-1', contactId: 'contact-1', notesLength: 300 },
        { communicationId: 'comm-2', contactId: 'contact-2', notesLength: 300 }
      ]

      const results = await AISummaryService.batchSummarize(communications, 'brief', 200)

      expect(results).toHaveLength(2)
      expect(results[0].summary).toBe('AI-generated summary of the communication')
      expect(results[1].summary).toBeNull()
      expect(results[1].error).toContain('API Error')
    })
  })

  describe('validation methods', () => {
    it('should validate communication for AI processing', () => {
      const result = AISummaryService.validateCommunicationForAI(
        'This is a long enough note for AI processing',
        'email'
      )
      expect(result.isValid).toBe(true)
    })

    it('should reject notes that are too short', () => {
      const result = AISummaryService.validateCommunicationForAI('short', 'email')
      expect(result.isValid).toBe(false)
      expect(result.reason).toBe('Notes too short (minimum 30 characters)')
    })

    it('should reject notes that are too long', () => {
      const longNotes = 'a'.repeat(10001)
      const result = AISummaryService.validateCommunicationForAI(longNotes, 'email')
      expect(result.isValid).toBe(false)
      expect(result.reason).toBe('Notes too long (maximum 10000 characters)')
    })

    it('should detect sensitive information patterns', () => {
      const notesWithCreditCard = 'My credit card number is 1234-5678-9012-3456'
      const result = AISummaryService.validateCommunicationForAI(notesWithCreditCard, 'email')
      expect(result.isValid).toBe(false)
      expect(result.reason).toBe('Notes may contain sensitive information')
    })

    it('should validate notes for AI using validation library', () => {
      const result = AISummaryService.validateNotesForAI('Valid notes for processing')
      expect(result.isValid).toBe(true)
    })

    it('should handle validation errors', async () => {
      const validationModule = await import('@/lib/validation/communicationValidation')
      vi.mocked(validationModule.validateNotes).mockImplementation(() => {
        throw new ValidationError('Validation failed')
      })

      const result = AISummaryService.validateNotesForAI('invalid notes')
      expect(result.isValid).toBe(false)
      expect(result.reason).toBe('Validation failed')
    })

    it('should handle security errors', async () => {
      const validationModule = await import('@/lib/validation/communicationValidation')
      vi.mocked(validationModule.validateNotes).mockImplementation(() => {
        throw new SecurityError('Security check failed')
      })

      const result = AISummaryService.validateNotesForAI('suspicious content')
      expect(result.isValid).toBe(false)
      expect(result.reason).toBe('Eingabe enthält nicht erlaubte Inhalte')
    })
  })

  describe('rate limiting', () => {
    it('should get rate limit status for authenticated user', async () => {
      vi.mocked(aiRateLimiter.getUserStats).mockReturnValue({
        used: 5,
        limit: 10,
        remaining: 5
      })

      const status = await AISummaryService.getRateLimitStatus()

      expect(status.allowed).toBe(true)
      expect(status.remaining).toBe(10)
      expect(status.stats).toEqual({
        used: 5,
        limit: 10,
        remaining: 5
      })
    })

    it('should handle unauthenticated user', async () => {
      vi.mocked(supabase.auth.getUser).mockResolvedValue({
        data: { user: null },
        error: new Error('Not authenticated')
      })

      const status = await AISummaryService.getRateLimitStatus()

      expect(status.allowed).toBe(false)
      expect(status.remaining).toBe(0)
    })
  })

  describe('AI processing statistics', () => {
    it('should calculate AI processing statistics', async () => {
      const mockCommunications = [
        { notes: 'Test notes 1', summarized_notes: 'Summary 1' },
        { notes: 'Test notes 2', summarized_notes: null },
        { notes: 'Test notes 3', summarized_notes: 'Summary 3' }
      ]

      // Create a complete query chain mock that mimics Supabase behavior
      const mockQueryBuilder = {
        eq: vi.fn().mockReturnThis(),
        gte: vi.fn().mockReturnThis(),
        lte: vi.fn().mockReturnThis(),
        then: vi.fn((callback) => callback({ data: mockCommunications, error: null })),
        catch: vi.fn()
      }

      vi.mocked(supabase.from).mockReturnValue({
        select: vi.fn().mockReturnValue(mockQueryBuilder)
      } as any)

      const stats = await AISummaryService.getAIProcessingStats()

      expect(stats.total_communications).toBe(3)
      expect(stats.summarized_communications).toBe(2)
      expect(stats.summarization_rate).toBe(66.66666666666666)
    })

    it('should handle database errors gracefully', async () => {
      // Create a complete query chain mock with error
      const mockQueryBuilder = {
        eq: vi.fn().mockReturnThis(),
        gte: vi.fn().mockReturnThis(),
        lte: vi.fn().mockReturnThis(),
        then: vi.fn((callback) => callback({ data: null, error: { message: 'Database error' } })),
        catch: vi.fn()
      }

      vi.mocked(supabase.from).mockReturnValue({
        select: vi.fn().mockReturnValue(mockQueryBuilder)
      } as any)

      const stats = await AISummaryService.getAIProcessingStats()

      expect(stats.total_communications).toBe(0)
      expect(stats.summarized_communications).toBe(0)
      expect(stats.summarization_rate).toBe(0)
    })
  })
})