import { supabase } from '@/integrations/supabase/client';

export interface PasswordChangeRequest {
  currentPassword: string;
  newPassword: string;
  confirmPassword: string;
}

export interface PasswordChangeResult {
  success: boolean;
  error?: string;
}

export class PasswordService {
  /**
   * Validates password change request on client side
   */
  private static validatePasswordChange(request: PasswordChangeRequest): string | null {
    if (request.newPassword !== request.confirmPassword) {
      return 'Die Passwörter stimmen nicht überein.';
    }

    if (request.newPassword.length < 6) {
      return 'Das Passwort muss mindestens 6 Zeichen lang sein.';
    }

    if (request.currentPassword === request.newPassword) {
      return 'Das neue Passwort muss sich vom aktuellen Passwort unterscheiden.';
    }

    return null;
  }

  /**
   * Changes the password for the authenticated user
   * Follows Supabase best practices for secure password updates
   */
  static async changePassword(request: PasswordChangeRequest): Promise<PasswordChangeResult> {
    try {
      // Client-side validation
      const validationError = this.validatePasswordChange(request);
      if (validationError) {
        return { success: false, error: validationError };
      }

      // Check if user is authenticated
      const { data: { session } } = await supabase.auth.getSession();
      if (!session?.user) {
        return { success: false, error: 'Keine aktive Session gefunden' };
      }

      // For security: First verify current password by attempting sign-in
      // This ensures the user knows their current password before changing it
      if (session.user.email) {
        const { error: verifyError } = await supabase.auth.signInWithPassword({
          email: session.user.email,
          password: request.currentPassword
        });

        if (verifyError) {
          return { success: false, error: 'Das aktuelle Passwort ist nicht korrekt' };
        }
      }

      // Update password - Supabase handles this securely for authenticated users
      // According to Supabase docs, updateUser with password is the correct approach
      const { error: updateError } = await supabase.auth.updateUser({
        password: request.newPassword
      });

      if (updateError) {
        return { success: false, error: updateError.message };
      }

      return { success: true };

    } catch (error) {
      console.error('Password change error:', error);
      const errorMessage = error instanceof Error ? error.message : 'Ein unbekannter Fehler ist aufgetreten.';
      return { success: false, error: errorMessage };
    }
  }
}