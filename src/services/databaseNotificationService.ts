import { supabase } from '@/integrations/supabase/client';
import type { 
  Notification, 
  NotificationType, 
  NotificationPriority,
  NotificationFilters 
} from '@/types/notifications';

interface CreateNotificationData {
  type: NotificationType;
  priority: NotificationPriority;
  title: string;
  message: string;
  metadata?: Record<string, unknown>;
  action_url?: string;
  action_label?: string;
  expires_at?: string;
}

/**
 * Database service with cached user authentication and batch operations
 */
export class DatabaseNotificationService {
  private static userIdCache: string | null = null;
  private static userIdExpiry = 0;
  private static readonly CACHE_DURATION = 5 * 60 * 1000; // 5 minutes

  /**
   * Get cached user ID or fetch if expired
   */
  private static async getUserId(): Promise<string | null> {
    const now = Date.now();
    
    // Return cached user ID if still valid
    if (this.userIdCache && now < this.userIdExpiry) {
      return this.userIdCache;
    }

    try {
      const { data: user } = await supabase.auth.getUser();
      if (user.user) {
        this.userIdCache = user.user.id;
        this.userIdExpiry = now + this.CACHE_DURATION;
        return this.userIdCache;
      }
      
      // Clear cache if no user
      this.userIdCache = null;
      this.userIdExpiry = 0;
      return null;
    } catch (error) {
      console.error('Failed to get user ID:', error);
      // Clear cache on error
      this.userIdCache = null;
      this.userIdExpiry = 0;
      return null;
    }
  }

  /**
   * Clear user cache (call on logout)
   */
  static clearUserCache() {
    this.userIdCache = null;
    this.userIdExpiry = 0;
  }

  /**
   * Get all notifications with unread count in single query
   */
  static async getNotificationsWithCount(): Promise<{ notifications: Notification[]; unreadCount: number }> {
    try {
      const userId = await this.getUserId();
      if (!userId) return { notifications: [], unreadCount: 0 };

      const expiryCondition = new Date().toISOString();
      
      // Get all notifications in single query
      const { data: notifications, error } = await supabase
        .from('notifications')
        .select('*')
        .eq('user_id', userId)
        .or(`expires_at.is.null,expires_at.gt.${expiryCondition}`)
        .order('created_at', { ascending: false });

      if (error) {
        console.error('Error fetching notifications:', error);
        return { notifications: [], unreadCount: 0 };
      }

      const notificationList = notifications || [];
      const unreadCount = notificationList.filter(n => n.status === 'unread').length;

      return { notifications: notificationList, unreadCount };
    } catch (error) {
      console.error('Failed to fetch notifications with count:', error);
      return { notifications: [], unreadCount: 0 };
    }
  }

  /**
   * Get filtered notifications
   */
  static async getFilteredNotifications(filters: NotificationFilters): Promise<Notification[]> {
    try {
      const userId = await this.getUserId();
      if (!userId) return [];

      let query = supabase
        .from('notifications')
        .select('*')
        .eq('user_id', userId)
        .or(`expires_at.is.null,expires_at.gt.${new Date().toISOString()}`);

      // Apply filters
      if (filters.types?.length) {
        query = query.in('type', filters.types);
      }

      if (filters.priorities?.length) {
        query = query.in('priority', filters.priorities);
      }

      if (filters.status) {
        query = query.eq('status', filters.status);
      }

      if (filters.unread_only) {
        query = query.eq('status', 'unread');
      }

      if (filters.date_from) {
        query = query.gte('created_at', filters.date_from);
      }

      if (filters.date_to) {
        query = query.lte('created_at', filters.date_to);
      }

      const { data: notifications, error } = await query
        .order('created_at', { ascending: false });

      if (error) {
        console.error('Error fetching filtered notifications:', error);
        return [];
      }

      return notifications || [];
    } catch (error) {
      console.error('Failed to fetch filtered notifications:', error);
      return [];
    }
  }

  /**
   * Batch mark multiple notifications as read
   */
  static async markMultipleAsRead(notificationIds: string[]): Promise<boolean> {
    try {
      const userId = await this.getUserId();
      if (!userId) return false;

      const { error } = await supabase
        .from('notifications')
        .update({
          status: 'read',
          read_at: new Date().toISOString()
        })
        .eq('user_id', userId)
        .in('id', notificationIds);

      if (error) {
        console.error('Error batch marking notifications as read:', error);
        return false;
      }

      return true;
    } catch (error) {
      console.error('Failed to batch mark notifications as read:', error);
      return false;
    }
  }

  /**
   * Mark all notifications as read (optimized)
   */
  static async markAllAsRead(): Promise<boolean> {
    try {
      const userId = await this.getUserId();
      if (!userId) return false;

      const { error } = await supabase
        .from('notifications')
        .update({
          status: 'read',
          read_at: new Date().toISOString()
        })
        .eq('user_id', userId)
        .eq('status', 'unread');

      if (error) {
        console.error('Error marking all notifications as read:', error);
        return false;
      }

      return true;
    } catch (error) {
      console.error('Failed to mark all notifications as read:', error);
      return false;
    }
  }

  /**
   * Single notification operations (maintaining compatibility)
   */
  static async markAsRead(notificationId: string): Promise<boolean> {
    try {
      const userId = await this.getUserId();
      if (!userId) return false;

      const { error } = await supabase
        .from('notifications')
        .update({
          status: 'read',
          read_at: new Date().toISOString()
        })
        .eq('id', notificationId)
        .eq('user_id', userId);

      if (error) {
        console.error('Error marking notification as read:', error);
        return false;
      }

      return true;
    } catch (error) {
      console.error('Failed to mark notification as read:', error);
      return false;
    }
  }

  static async markAsUnread(notificationId: string): Promise<boolean> {
    try {
      const userId = await this.getUserId();
      if (!userId) return false;

      const { error } = await supabase
        .from('notifications')
        .update({
          status: 'unread',
          read_at: null
        })
        .eq('id', notificationId)
        .eq('user_id', userId);

      if (error) {
        console.error('Error marking notification as unread:', error);
        return false;
      }

      return true;
    } catch (error) {
      console.error('Failed to mark notification as unread:', error);
      return false;
    }
  }

  static async createNotification(data: CreateNotificationData): Promise<string | null> {
    try {
      const userId = await this.getUserId();
      if (!userId) {
        console.error('No authenticated user');
        return null;
      }

      const { data: notification, error } = await supabase
        .from('notifications')
        .insert({
          user_id: userId,
          type: data.type,
          priority: data.priority,
          title: data.title,
          message: data.message,
          metadata: data.metadata || {},
          action_url: data.action_url,
          action_label: data.action_label,
          expires_at: data.expires_at
        })
        .select('id')
        .single();

      if (error) {
        console.error('Error creating notification:', error);
        return null;
      }

      return notification?.id || null;
    } catch (error) {
      console.error('Failed to create notification:', error);
      return null;
    }
  }

  static async clearAll(): Promise<boolean> {
    try {
      const userId = await this.getUserId();
      if (!userId) return false;

      const { error } = await supabase
        .from('notifications')
        .delete()
        .eq('user_id', userId);

      if (error) {
        console.error('Error clearing all notifications:', error);
        return false;
      }

      return true;
    } catch (error) {
      console.error('Failed to clear all notifications:', error);
      return false;
    }
  }

  static async removeNotification(notificationId: string): Promise<boolean> {
    try {
      const userId = await this.getUserId();
      if (!userId) return false;

      const { error } = await supabase
        .from('notifications')
        .delete()
        .eq('id', notificationId)
        .eq('user_id', userId);

      if (error) {
        console.error('Error removing notification:', error);
        return false;
      }

      return true;
    } catch (error) {
      console.error('Failed to remove notification:', error);
      return false;
    }
  }
}