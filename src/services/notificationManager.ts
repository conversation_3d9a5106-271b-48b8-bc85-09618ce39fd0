import { NotificationService } from './notificationService';

/**
 * Notification Manager - Simplified for Edge Function based notifications
 * Now only manages real-time subscriptions, not app-side checking
 */
export class NotificationManager {
  private static isInitialized = false;

  /**
   * Initialize notification system only for authenticated users
   * This should be called when user is authenticated
   */
  static initialize(isAuthenticated = false): void {
    // Only initialize for authenticated users and avoid double initialization
    if (!isAuthenticated || this.isInitialized) {
      return;
    }

    if (process.env.NODE_ENV === 'development') {
      console.log('🔔 Initializing notification system for authenticated user');
    }
    
    // Initialize the core notification service (real-time subscriptions)
    NotificationService.initialize();
    this.isInitialized = true;
    
    if (process.env.NODE_ENV === 'development') {
      console.log('✅ Notification system initialized');
    }
  }

  /**
   * Cleanup all notification services
   */
  static cleanup(): void {
    NotificationService.cleanup();
    this.isInitialized = false;
  }

  /**
   * Restart the notification system
   */
  static restart(): void {
    this.cleanup();
    this.initialize();
  }

  /**
   * Trigger immediate notification check via Edge Function
   */
  static async triggerImmediateCheck(): Promise<void> {
    if (process.env.NODE_ENV === 'development') {
      console.log('🔔 Triggering immediate notification check');
    }
    try {
      await NotificationService.triggerCheck();
      if (process.env.NODE_ENV === 'development') {
        console.log('✅ Immediate check completed');
      }
    } catch (error) {
      console.error('❌ Error triggering immediate check:', error);
    }
  }
}