import { supabase } from '@/integrations/supabase/client';
import { DatabaseNotificationService } from './databaseNotificationService';
import type { 
  Notification, 
  NotificationType, 
  NotificationPriority, 
  NotificationFilters,
  NotificationGroup
} from '@/types/notifications';

export class NotificationService {
  private static listeners = new WeakSet<(notifications: Notification[]) => void>();
  private static listenerCallbacks: ((notifications: Notification[]) => void)[] = [];
  private static realtimeChannel: any = null;
  private static isInitialized = false;
  private static cleanupTimeout: NodeJS.Timeout | null = null;

  // Initialize real-time subscription
  static initialize(): void {
    if (typeof window === 'undefined' || this.isInitialized) return;
    
    try {
      this.setupRealtimeSubscription();
      this.isInitialized = true;
    } catch (error) {
      console.error('Failed to initialize notification service:', error);
    }
  }

  // Setup Supabase real-time subscription with proper error handling
  private static setupRealtimeSubscription(): void {
    // Clean up existing channel first
    if (this.realtimeChannel) {
      this.realtimeChannel.unsubscribe();
    }

    this.realtimeChannel = supabase
      .channel('notifications', {
        config: {
          presence: {
            key: 'notifications-service'
          }
        }
      })
      .on('postgres_changes', 
        { event: '*', schema: 'public', table: 'notifications' },
        (payload) => {
          if (process.env.NODE_ENV === 'development') {
            console.log('📡 Notification real-time update:', payload.eventType);
          }
          this.handleRealtimeUpdate(payload);
        }
      )
      .subscribe((status) => {
        if (process.env.NODE_ENV === 'development') {
          console.log('📡 Notification channel status:', status);
        }
        
        if (status === 'CHANNEL_ERROR') {
          console.warn('📡 Notification channel error, attempting reconnect in 5s');
          setTimeout(() => {
            if (!this.isInitialized) return;
            this.setupRealtimeSubscription();
          }, 5000);
        }
      });
  }

  // Handle real-time updates
  private static async handleRealtimeUpdate(payload: any): Promise<void> {
    try {
      // Refresh notifications from database
      const { notifications } = await DatabaseNotificationService.getNotificationsWithCount();
      
      // Notify listeners with proper error handling and cleanup
      this.listenerCallbacks = this.listenerCallbacks.filter(listener => {
        try {
          if (this.listeners.has(listener)) {
            listener(notifications);
            return true; // Keep listener
          }
          return false; // Remove listener
        } catch (error) {
          console.error('Error in notification listener:', error);
          return false; // Remove failing listener
        }
      });
      
      // Show browser notification for new notifications
      if (payload.eventType === 'INSERT') {
        this.showBrowserNotification(payload.new);
      }
    } catch (error) {
      console.error('Error handling real-time update:', error);
    }
  }

  // Create a new notification
  static async createNotification(
    type: NotificationType,
    title: string,
    message: string,
    priority: NotificationPriority = 'normal',
    metadata?: Record<string, unknown>,
    actionUrl?: string,
    actionLabel?: string,
    expiresAt?: string
  ): Promise<string | null> {
    try {
      const id = await DatabaseNotificationService.createNotification({
        type,
        title,
        message,
        priority,
        metadata,
        action_url: actionUrl,
        action_label: actionLabel,
        expires_at: expiresAt
      });
      
      return id;
    } catch (error) {
      console.error('Error creating notification:', error);
      return null;
    }
  }

  // Legacy method - now handled by Edge Function cron job
  // Kept for backward compatibility but should not be used for new code
  static async createFollowUpDueNotification(
    followUpId: string,
    applicationId: string,
    applicationName: string,
    companyName: string,
    templateName: string,
    scheduledDate: string
  ): Promise<string | null> {
    console.warn('createFollowUpDueNotification is deprecated - now handled by Edge Function');
    return null; // Don't create duplicate notifications
  }

  // Legacy method - now handled by Edge Function cron job  
  static async createFollowUpOverdueNotification(
    followUpId: string,
    applicationId: string,
    applicationName: string,
    companyName: string,
    templateName: string,
    scheduledDate: string,
    daysOverdue: number
  ): Promise<string | null> {
    console.warn('createFollowUpOverdueNotification is deprecated - now handled by Edge Function');
    return null; // Don't create duplicate notifications
  }

  // Legacy method - now handled by Edge Function cron job
  static async createApplicationReminderNotification(
    applicationId: string,
    applicationName: string,
    companyName: string,
    daysSinceApplication: number,
    status: string
  ): Promise<string | null> {
    console.warn('createApplicationReminderNotification is deprecated - now handled by Edge Function');
    return null; // Don't create duplicate notifications
  }

  // Legacy method - now handled by Edge Function cron job
  static async createInterviewReminderNotification(
    applicationId: string,
    applicationName: string,
    companyName: string,
    interviewDate: string,
    hoursUntilInterview: number
  ): Promise<string | null> {
    console.warn('createInterviewReminderNotification is deprecated - now handled by Edge Function');
    return null; // Don't create duplicate notifications
  }

  // Create calendar event notification
  static async createCalendarEventNotification(
    eventId: string,
    eventTitle: string,
    eventDate: string,
    eventTime: string | undefined,
    minutesUntilEvent: number
  ): Promise<string | null> {
    const timeText = minutesUntilEvent < 60
      ? `in ${minutesUntilEvent} Minuten`
      : `in ${Math.round(minutesUntilEvent / 60)} Stunde${Math.round(minutesUntilEvent / 60) > 1 ? 'n' : ''}`;

    return await this.createNotification(
      'calendar_event',
      'Termin-Erinnerung',
      `"${eventTitle}" beginnt ${timeText}`,
      minutesUntilEvent <= 15 ? 'urgent' : minutesUntilEvent <= 60 ? 'high' : 'normal',
      {
        event_id: eventId,
        event_title: eventTitle,
        event_date: eventDate,
        event_time: eventTime,
        minutes_until_event: minutesUntilEvent
      },
      '/calendar',
      'Kalender öffnen'
    );
  }

  // Get all notifications with unread count
  static async getNotificationsWithCount(): Promise<{ notifications: Notification[]; unreadCount: number }> {
    try {
      return await DatabaseNotificationService.getNotificationsWithCount();
    } catch (error) {
      console.error('Error fetching notifications:', error);
      return { notifications: [], unreadCount: 0 };
    }
  }

  // Get all notifications (legacy compatibility)
  static async getNotifications(): Promise<Notification[]> {
    try {
      const { notifications } = await DatabaseNotificationService.getNotificationsWithCount();
      return notifications;
    } catch (error) {
      console.error('Error fetching notifications:', error);
      return [];
    }
  }

  // Get notifications with filters
  static async getFilteredNotifications(filters: NotificationFilters): Promise<Notification[]> {
    try {
      return await DatabaseNotificationService.getFilteredNotifications(filters);
    } catch (error) {
      console.error('Error fetching filtered notifications:', error);
      return [];
    }
  }

  // Get grouped notifications
  static async getGroupedNotifications(): Promise<NotificationGroup[]> {
    try {
      const notifications = await this.getNotifications();
      const groups = new Map<NotificationType, NotificationGroup>();

      notifications.forEach(notification => {
        if (!groups.has(notification.type)) {
          groups.set(notification.type, {
            type: notification.type,
            count: 0,
            latest_notification: notification,
            notifications: []
          });
        }

        const group = groups.get(notification.type)!;
        group.count++;
        group.notifications.push(notification);
        
        // Update latest if this notification is newer
        if (new Date(notification.created_at) > new Date(group.latest_notification.created_at)) {
          group.latest_notification = notification;
        }
      });

      return Array.from(groups.values())
        .sort((a, b) => new Date(b.latest_notification.created_at).getTime() - new Date(a.latest_notification.created_at).getTime());
    } catch (error) {
      console.error('Error getting grouped notifications:', error);
      return [];
    }
  }

  // Get unread count (optimized to use cached data)
  static async getUnreadCount(): Promise<number> {
    try {
      const { unreadCount } = await DatabaseNotificationService.getNotificationsWithCount();
      return unreadCount;
    } catch (error) {
      console.error('Error getting unread count:', error);
      return 0;
    }
  }

  // Mark notification as read
  static async markAsRead(notificationId: string): Promise<void> {
    try {
      await DatabaseNotificationService.markAsRead(notificationId);
    } catch (error) {
      console.error('Error marking notification as read:', error);
    }
  }

  // Mark all notifications as read
  static async markAllAsRead(): Promise<void> {
    try {
      await DatabaseNotificationService.markAllAsRead();
    } catch (error) {
      console.error('Error marking all notifications as read:', error);
    }
  }

  // Mark notification as unread
  static async markAsUnread(notificationId: string): Promise<void> {
    try {
      await DatabaseNotificationService.markAsUnread(notificationId);
    } catch (error) {
      console.error('Error marking notification as unread:', error);
    }
  }

  // Remove notification
  static async removeNotification(notificationId: string): Promise<void> {
    try {
      await DatabaseNotificationService.removeNotification(notificationId);
    } catch (error) {
      console.error('Error removing notification:', error);
    }
  }

  // Clear all notifications
  static async clearAll(): Promise<void> {
    try {
      await DatabaseNotificationService.clearAll();
    } catch (error) {
      console.error('Error clearing all notifications:', error);
    }
  }

  // Subscribe to notifications changes with proper cleanup
  static subscribe(listener: (notifications: Notification[]) => void): () => void {
    // Add to both WeakSet for checking and array for iteration
    this.listeners.add(listener);
    this.listenerCallbacks.push(listener);
    
    // Initial load for new subscriber
    this.getNotifications().then(notifications => {
      try {
        if (this.listeners.has(listener)) {
          listener(notifications);
        }
      } catch (error) {
        console.error('Error in initial notification listener call:', error);
        // Remove failing listener
        this.listeners.delete(listener);
        const index = this.listenerCallbacks.indexOf(listener);
        if (index !== -1) {
          this.listenerCallbacks.splice(index, 1);
        }
      }
    });
    
    return () => {
      // Remove from both WeakSet and array
      this.listeners.delete(listener);
      const index = this.listenerCallbacks.indexOf(listener);
      if (index !== -1) {
        this.listenerCallbacks.splice(index, 1);
      }
    };
  }

  // Private helper for browser notifications
  private static async showBrowserNotification(notification: Notification): Promise<void> {
    if (typeof window === 'undefined') return;


    // Request permission if not granted
    if (Notification.permission === 'default') {
      const permission = await Notification.requestPermission();
    }

    // Show browser notification if permission granted
    if (Notification.permission === 'granted') {
      const browserNotification = new Notification(notification.title, {
        body: notification.message,
        icon: '/favicon.ico',
        badge: '/favicon.ico',
        tag: notification.id,
        requireInteraction: notification.priority === 'urgent'
      });

      // Auto-close after 5 seconds (except urgent notifications)
      if (notification.priority !== 'urgent') {
        setTimeout(() => browserNotification.close(), 5000);
      }

      // Handle click to navigate to action URL
      if (notification.action_url) {
        browserNotification.onclick = () => {
          window.focus();
          window.location.href = notification.action_url!;
          browserNotification.close();
        };
      }
    }
  }

  // Comprehensive cleanup method for memory leak prevention
  static cleanup(): void {
    // Clear cleanup timeout
    if (this.cleanupTimeout) {
      clearTimeout(this.cleanupTimeout);
      this.cleanupTimeout = null;
    }

    // Properly unsubscribe from Supabase channel
    if (this.realtimeChannel) {
      try {
        this.realtimeChannel.unsubscribe();
        supabase.removeChannel(this.realtimeChannel);
      } catch (error) {
        console.error('Error during channel cleanup:', error);
      }
      this.realtimeChannel = null;
    }
    
    // Clear all listeners
    this.listeners = new WeakSet();
    this.listenerCallbacks = [];
    this.isInitialized = false;
    
    // Cleanup completed silently
  }

  // Restart the notification system
  static restart(): void {
    this.cleanup();
    this.initialize();
  }

  // Trigger immediate check (calls Edge Function)
  static async triggerCheck(): Promise<void> {
    try {
      console.log('🔔 Triggering immediate notification check');
      await supabase.functions.invoke('check-notifications');
    } catch (error) {
      console.error('Error triggering notification check:', error);
    }
  }
}