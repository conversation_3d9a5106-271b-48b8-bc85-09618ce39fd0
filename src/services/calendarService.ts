import { supabase } from '@/integrations/supabase/client';
import { format, addDays } from 'date-fns';
import type { ApplicationStatus } from '@/types/applications';
import type { CalendarEventType, CreateCalendarEventData } from '@/types/calendar';
import { EVENT_TYPE_COLORS } from '@/types/calendar';

interface StatusChangeEventData {
  projectId: string;
  projectName: string;
  companyName: string;
  newStatus: ApplicationStatus;
  statusNotes?: { notes?: string; notes_date?: string };
  referenceType?: 'project' | 'application';
}

// Mapping of project statuses to calendar event types
const STATUS_TO_EVENT_TYPE: Partial<Record<ApplicationStatus, CalendarEventType>> = {
  'interview_scheduled': 'interview',
  'application_sent': 'follow_up',
  'offer_received': 'meeting'
};

// Status-specific event titles
const STATUS_EVENT_TITLES: Partial<Record<ApplicationStatus, string>> = {
  'interview_scheduled': 'Interview',
  'application_sent': 'Nachfassen',
  'offer_received': 'Vertragsverhandlung'
};

// Status-specific event descriptions
const STATUS_EVENT_DESCRIPTIONS: Partial<Record<ApplicationStatus, (projectName: string, companyName: string) => string>> = {
  'interview_scheduled': (projectName, companyName) => 
    `Interview für das Projekt "${projectName}" bei ${companyName}`,
  'application_sent': (projectName, companyName) => 
    `Nachfassen für die Bewerbung auf "${projectName}" bei ${companyName}`,
  'offer_received': (projectName, companyName) => 
    `Vertragsverhandlung für "${projectName}" bei ${companyName}`
};

/**
 * Creates a specific interview calendar event with date and time
 */
export const createInterviewEvent = async (
  applicationId: string,
  projectName: string,
  companyName: string,
  interviewDate: string,
  interviewTime?: string,
  notes?: string
): Promise<void> => {
  try {
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) {
      console.error('User not authenticated for interview event creation');
      return;
    }

    const eventData: CreateCalendarEventData = {
      reference_id: applicationId,
      reference_type: 'application',
      title: `Interview: ${projectName}`,
      description: `Interview für das Projekt "${projectName}" bei ${companyName}${notes ? `\n\nNotizen: ${notes}` : ''}`,
      start_date: interviewDate,
      start_time: interviewTime || '10:00',
      all_day: !interviewTime,
      event_type: 'interview',
      color: EVENT_TYPE_COLORS.interview,
      created_automatically: true,
      source_status: 'interview_scheduled',
      reminder_enabled: true,
      reminder_minutes_before: 60, // 1 hour reminder
    };

    console.log('Creating interview calendar event:', eventData);

    const { data: createdEvent, error } = await supabase
      .from('calendar_events')
      .insert([{
        ...eventData,
        user_id: user.id,
      }])
      .select()
      .single();

    if (error) {
      console.error('Error creating interview calendar event:', error);
      throw error;
    }

    console.log('Interview calendar event created successfully:', createdEvent);
  } catch (error) {
    console.error('Failed to create interview calendar event:', error);
    // Don't throw the error to avoid breaking the status update flow
  }
};

/**
 * Creates a calendar event automatically when a project status changes
 */
export const createEventForStatusChange = async (data: StatusChangeEventData): Promise<void> => {
  const { projectId, projectName, companyName, newStatus, statusNotes, referenceType = 'application' } = data;
  
  // Check if this status change should create a calendar event
  const eventType = STATUS_TO_EVENT_TYPE[newStatus];
  if (!eventType) {
    return; // No event needed for this status
  }

  try {
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) {
      console.error('User not authenticated for calendar event creation');
      return;
    }

    // Determine event date
    let eventDate: string;
    if (statusNotes?.notes_date) {
      // Use the date from status notes if provided
      eventDate = statusNotes.notes_date;
    } else {
      // Default to tomorrow for interviews, 1 week from now for follow-ups
      const defaultDays = newStatus === 'interview_scheduled' ? 1 : 7;
      eventDate = format(addDays(new Date(), defaultDays), 'yyyy-MM-dd');
    }

    // Create event data
    const eventData: CreateCalendarEventData = {
      project_id: referenceType === 'project' ? projectId : null,
      title: `${STATUS_EVENT_TITLES[newStatus] || 'Termin'}: ${projectName}`,
      description: STATUS_EVENT_DESCRIPTIONS[newStatus]?.(projectName, companyName) || 
                  `Automatisch erstellt für Status-Änderung zu "${newStatus}"`,
      start_date: eventDate,
      start_time: newStatus === 'interview_scheduled' ? '10:00' : undefined, // Default interview time
      all_day: newStatus !== 'interview_scheduled',
      event_type: eventType,
      color: EVENT_TYPE_COLORS[eventType],
      created_automatically: true,
      source_status: newStatus,
      reference_type: referenceType,
      reference_id: projectId,
    };

    // Add notes from status change if available
    if (statusNotes?.notes) {
      eventData.description = `${eventData.description}\n\nNotizen: ${statusNotes.notes}`;
    }

    console.log('Creating automatic calendar event:', eventData);

    const { data: createdEvent, error } = await supabase
      .from('calendar_events')
      .insert([{
        ...eventData,
        user_id: user.id,
      }])
      .select()
      .single();

    if (error) {
      console.error('Error creating automatic calendar event:', error);
      throw error;
    }

    console.log('Automatic calendar event created successfully:', createdEvent);
  } catch (error) {
    console.error('Failed to create automatic calendar event:', error);
    // Don't throw the error to avoid breaking the status update flow
  }
};

/**
 * Creates calendar events for project start and end dates
 */
export const createProjectDateEvents = async (
  projectId: string,
  projectName: string,
  companyName: string,
  startDate?: string,
  endDate?: string,
  referenceType: 'project' | 'application' = 'project'
): Promise<void> => {
  try {
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) {
      console.error('User not authenticated for calendar event creation');
      return;
    }

    const eventsToCreate: CreateCalendarEventData[] = [];

    // Create start date event
    if (startDate) {
      eventsToCreate.push({
        project_id: referenceType === 'project' ? projectId : null,
        title: `Projektstart: ${projectName}`,
        description: `Beginn des Projekts "${projectName}" bei ${companyName}`,
        start_date: startDate,
        all_day: true,
        event_type: 'project_start',
        color: EVENT_TYPE_COLORS.project_start,
        created_automatically: true,
        source_status: 'project_start',
        reference_type: referenceType,
        reference_id: projectId,
      });
    }

    // Create end date event
    if (endDate) {
      eventsToCreate.push({
        project_id: referenceType === 'project' ? projectId : null,
        title: `Projektende: ${projectName}`,
        description: `Ende des Projekts "${projectName}" bei ${companyName}`,
        start_date: endDate,
        all_day: true,
        event_type: 'project_end',
        color: EVENT_TYPE_COLORS.project_end,
        created_automatically: true,
        source_status: 'project_end',
        reference_type: referenceType,
        reference_id: projectId,
      });
    }

    if (eventsToCreate.length === 0) {
      return;
    }

    console.log('Creating automatic project date events:', eventsToCreate);

    const { data: createdEvents, error } = await supabase
      .from('calendar_events')
      .insert(eventsToCreate.map(event => ({
        ...event,
        user_id: user.id,
      })))
      .select();

    if (error) {
      console.error('Error creating automatic project date events:', error);
      throw error;
    }

    console.log('Automatic project date events created successfully:', createdEvents);
  } catch (error) {
    console.error('Failed to create automatic project date events:', error);
    // Don't throw the error to avoid breaking the project creation flow
  }
};

/**
 * Deletes all automatic calendar events for a project or application
 */
export const deleteProjectEvents = async (projectId: string, referenceType?: 'project' | 'application'): Promise<void> => {
  try {
    let query = supabase
      .from('calendar_events')
      .delete()
      .eq('created_automatically', true);

    if (referenceType) {
      // Delete by reference_type and reference_id (new system)
      query = query
        .eq('reference_type', referenceType)
        .eq('reference_id', projectId);
    } else {
      // Delete by both old and new system for backward compatibility
      query = query
        .or(`project_id.eq.${projectId},and(reference_id.eq.${projectId},reference_type.in.("project","application"))`);
    }

    const { error } = await query;

    if (error) {
      console.error('Error deleting automatic project calendar events:', error);
      throw error;
    }

    console.log('Automatic project calendar events deleted successfully');
  } catch (error) {
    console.error('Failed to delete automatic project calendar events:', error);
    // Don't throw the error to avoid breaking the project deletion flow
  }
};