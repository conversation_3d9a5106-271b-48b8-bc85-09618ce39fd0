import { supabase } from '@/integrations/supabase/client';
import { aiRateLimiter } from '@/lib/rateLimiting/aiRateLimiter';
import { 
  validateNotes, 
  validateSubject, 
  validateCommunicationType,
  ValidationError,
  SecurityError 
} from '@/lib/validation/communicationValidation';
import i18n from '@/i18n';
import {
  CommunicationSummaryRequest,
  CommunicationSummaryResponse,
  ContactCommunication
} from '@/types/communications';

// Rate limiting error class
export class RateLimitError extends Error {
  constructor(message: string, public resetTime: number, public remaining: number) {
    super(message);
    this.name = 'RateLimitError';
  }
}

// Core input interface for AI summarization
interface SummaryInput {
  notes: string;
  communicationType: string;
  subject?: string;
  contactName?: string;
  companyName?: string;
  projectContext?: {
    projectName: string;
    companyName: string;
    status: string;
  };
  summaryType?: 'brief' | 'detailed' | 'action_items' | 'follow_up';
  includeProjectContext?: boolean;
}

// Options for database-based summarization
interface DatabaseSummaryOptions {
  communicationId: string;
  contactId: string;
  summaryType?: 'brief' | 'detailed' | 'action_items' | 'follow_up';
  includeProjectContext?: boolean;
}

export class AISummaryService {
  /**
   * Core method: Generate AI summary from direct input
   * This is the main method that all other methods use internally
   */
  static async generateSummary(input: SummaryInput): Promise<string | null> {
    try {
      // Get current user for authentication
      const { data: { user }, error: authError } = await supabase.auth.getUser();
      if (authError || !user) {
        throw new ValidationError(i18n.t('errors:ai.user_not_authenticated'));
      }

      // Validate and sanitize input
      const validatedNotes = validateNotes(input.notes);
      const validatedCommunicationType = validateCommunicationType(input.communicationType);
      const validatedSubject = validateSubject(input.subject);

      // Validate contact and company names if provided
      if (input.contactName && typeof input.contactName !== 'string') {
        throw new ValidationError('Contact name must be a string');
      }
      if (input.companyName && typeof input.companyName !== 'string') {
        throw new ValidationError('Company name must be a string');
      }

      // Check rate limits before proceeding
      const rateLimitResult = aiRateLimiter.checkRateLimit(user.id, 'basic');
      if (!rateLimitResult.allowed) {
        throw new RateLimitError(
          rateLimitResult.message || 'Rate limit exceeded',
          rateLimitResult.resetTime,
          rateLimitResult.remaining
        );
      }

      // Prepare data for edge function
      const edgeFunctionPayload = {
        direct_mode: true,
        summary_type: input.summaryType || 'brief',
        notes: validatedNotes,
        communication_type: validatedCommunicationType,
        subject: validatedSubject || 'Kein Betreff',
        contact_name: input.contactName?.trim() || 'Unbekannt',
        company_name: input.companyName?.trim() || 'Unbekannt',
        project_context: input.projectContext,
        context_include_project: input.includeProjectContext || false
      };

      console.log('🤖 Starting AI summary generation:', edgeFunctionPayload);

      // Get current session token
      const { data: { session } } = await supabase.auth.getSession();
      if (!session?.access_token) {
        throw new Error('Keine gültige Sitzung für AI-Zusammenfassung');
      }

      // Call the summarize-communication edge function
      const { data, error } = await supabase.functions.invoke('summarize-communication', {
        body: edgeFunctionPayload,
        headers: {
          Authorization: `Bearer ${session.access_token}`,
        },
      });

      if (error) {
        console.error('❌ Edge function error:', error);
        throw new Error(`Edge Function Fehler: ${error.message || JSON.stringify(error)}`);
      }

      if (!data) {
        console.error('❌ Edge function returned no data');
        throw new Error('Edge Function gab keine Daten zurück');
      }

      console.log('✅ Edge function response:', data);
      
      // Validate response structure
      if (!data.summary) {
        console.error('❌ Edge function response missing summary field');
        throw new Error('AI-Antwort enthält keine Zusammenfassung');
      }

      return data.summary;

    } catch (error) {
      console.error('❌ Error generating AI summary:', error);
      
      // Handle specific error types
      if (error instanceof ValidationError || error instanceof SecurityError || error instanceof RateLimitError) {
        throw error;
      }
      
      if (error instanceof Error) {
        throw new ValidationError(`AI-Zusammenfassung fehlgeschlagen: ${error.message}`);
      }
      
      throw new ValidationError('Unbekannter Fehler bei der AI-Zusammenfassung');
    }
  }

  /**
   * Helper method: Generate summary from existing communication record
   */
  static async summarizeFromCommunication(options: DatabaseSummaryOptions): Promise<string | null> {
    try {
      console.log('📝 Generating summary from database communication:', options.communicationId);

      // Get current user for security validation
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        console.error('❌ No valid session for AI summarization');
        throw new Error('Keine gültige Sitzung für AI-Zusammenfassung');
      }

      // Fetch communication data
      const { data: communication, error: commError } = await supabase
        .from('contact_communications')
        .select(`
          *,
          contact:contacts!inner(id, name, company, email),
          project:project_applications(id, project_name, company_name)
        `)
        .eq('id', options.communicationId)
        .eq('user_id', user.id)
        .single();

      if (commError || !communication) {
        console.error('❌ Error fetching communication:', commError);
        throw new Error('Kommunikation nicht gefunden');
      }

      // Prepare project context if needed
      let projectContext;
      if (options.includeProjectContext && communication.project) {
        projectContext = {
          projectName: communication.project.project_name,
          companyName: communication.project.company_name,
          status: 'In Bearbeitung' // You might want to fetch actual status
        };
      }

      // Use the core generateSummary method
      return await this.generateSummary({
        notes: communication.notes,
        communicationType: communication.communication_type,
        subject: communication.subject,
        contactName: communication.contact?.name,
        companyName: communication.contact?.company,
        projectContext,
        summaryType: options.summaryType || 'brief',
        includeProjectContext: options.includeProjectContext
      });

    } catch (error) {
      console.error('Error generating summary from communication:', error);
      throw error;
    }
  }

  /**
   * Legacy compatibility methods - these maintain the existing API
   */

  // Generate AI summary (legacy CommunicationAIService compatibility)
  static async generateLegacySummary(
    request: CommunicationSummaryRequest
  ): Promise<CommunicationSummaryResponse | null> {
    try {
      const summary = await this.summarizeFromCommunication({
        communicationId: request.communication_id,
        contactId: request.contact_id,
        summaryType: request.summary_type,
        includeProjectContext: request.context_include_project
      });

      if (!summary) {
        return null;
      }

      return { summary } as CommunicationSummaryResponse;
    } catch (error) {
      console.error('Error in legacy summary generation:', error);
      throw error;
    }
  }

  // Generate brief summary (auto-saved to database)
  static async generateBriefSummary(
    communicationId: string,
    contactId: string,
    includeProjectContext: boolean = false
  ): Promise<string | null> {
    console.log('📝 Generating brief summary for communication:', communicationId);
    
    const summary = await this.summarizeFromCommunication({
      communicationId,
      contactId,
      summaryType: 'brief',
      includeProjectContext
    });

    return summary;
  }

  // Generate detailed summary
  static async generateDetailedSummary(
    communicationId: string,
    contactId: string,
    includeProjectContext: boolean = true
  ): Promise<string | null> {
    try {
      return await this.summarizeFromCommunication({
        communicationId,
        contactId,
        summaryType: 'detailed',
        includeProjectContext
      });
    } catch (error) {
      console.error('Error generating detailed summary:', error);
      return null;
    }
  }

  // Extract action items from communication
  static async extractActionItems(
    communicationId: string,
    contactId: string,
    includeProjectContext: boolean = true
  ): Promise<string | null> {
    try {
      return await this.summarizeFromCommunication({
        communicationId,
        contactId,
        summaryType: 'action_items',
        includeProjectContext
      });
    } catch (error) {
      console.error('Error extracting action items:', error);
      return null;
    }
  }

  // Generate follow-up recommendations
  static async generateFollowUpRecommendations(
    communicationId: string,
    contactId: string,
    includeProjectContext: boolean = true
  ): Promise<string | null> {
    try {
      return await this.summarizeFromCommunication({
        communicationId,
        contactId,
        summaryType: 'follow_up',
        includeProjectContext
      });
    } catch (error) {
      console.error('Error generating follow-up recommendations:', error);
      return null;
    }
  }

  // Auto-summarize communication if notes are long enough
  static async autoSummarizeIfNeeded(
    communicationId: string,
    contactId: string,
    notesLength: number,
    threshold: number = 200
  ): Promise<string | null> {
    try {
      // Only auto-summarize if notes are longer than threshold
      if (notesLength < threshold) {
        return null;
      }

      return await this.generateBriefSummary(communicationId, contactId, false);
    } catch (error) {
      console.error('Error in auto-summarization:', error);
      return null;
    }
  }

  // Batch process multiple communications for summarization
  static async batchSummarize(
    communications: Array<{
      communicationId: string;
      contactId: string;
      notesLength: number;
    }>,
    summaryType: 'brief' | 'detailed' | 'action_items' | 'follow_up' = 'brief',
    threshold: number = 200
  ): Promise<Array<{ communicationId: string; summary: string | null; error?: string }>> {
    const results: Array<{ communicationId: string; summary: string | null; error?: string }> = [];

    // Process communications in batches to avoid overwhelming the API
    const batchSize = 3;
    for (let i = 0; i < communications.length; i += batchSize) {
      const batch = communications.slice(i, i + batchSize);
      
      const batchPromises = batch.map(async (comm) => {
        try {
          // Skip if notes are too short
          if (comm.notesLength < threshold) {
            return {
              communicationId: comm.communicationId,
              summary: null,
              error: 'Notes too short for summarization'
            };
          }

          const summary = await this.summarizeFromCommunication({
            communicationId: comm.communicationId,
            contactId: comm.contactId,
            summaryType,
            includeProjectContext: false // Skip project context for batch processing
          });

          return {
            communicationId: comm.communicationId,
            summary: summary || null,
            error: summary ? undefined : 'Failed to generate summary'
          };
        } catch (error) {
          return {
            communicationId: comm.communicationId,
            summary: null,
            error: error instanceof Error ? error.message : 'Unknown error'
          };
        }
      });

      const batchResults = await Promise.all(batchPromises);
      results.push(...batchResults);

      // Add delay between batches to be nice to the API
      if (i + batchSize < communications.length) {
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
    }

    return results;
  }

  /**
   * Validation and utility methods
   */

  // Validate if a communication is suitable for AI processing
  static validateCommunicationForAI(
    notes: string,
    communicationType: string
  ): { isValid: boolean; reason?: string } {
    // Check minimum length
    if (notes.length < 30) {
      return { isValid: false, reason: 'Notes too short (minimum 30 characters)' };
    }

    // Check maximum length (Gemini has token limits)
    if (notes.length > 10000) {
      return { isValid: false, reason: 'Notes too long (maximum 10000 characters)' };
    }

    // Check for sensitive information patterns (basic check)
    const sensitivePatterns = [
      /\b\d{4}[-\s]?\d{4}[-\s]?\d{4}[-\s]?\d{4}\b/, // Credit card numbers
      /\b\d{3}-\d{2}-\d{4}\b/, // SSN pattern
      /password|pwd|passwort/i, // Password mentions
    ];

    for (const pattern of sensitivePatterns) {
      if (pattern.test(notes)) {
        return { isValid: false, reason: 'Notes may contain sensitive information' };
      }
    }

    return { isValid: true };
  }

  // Enhanced validation using validation library
  static validateNotesForAI(notes: string): { isValid: boolean; reason?: string } {
    try {
      validateNotes(notes);
      return { isValid: true };
    } catch (error) {
      if (error instanceof ValidationError) {
        return { isValid: false, reason: error.message };
      } else if (error instanceof SecurityError) {
        return { isValid: false, reason: 'Eingabe enthält nicht erlaubte Inhalte' };
      }
      return { isValid: false, reason: 'Unbekannter Validierungsfehler' };
    }
  }

  // Get rate limit status for current user
  static async getRateLimitStatus(): Promise<{ allowed: boolean; remaining: number; resetTime: number; stats?: unknown }> {
    try {
      const { data: { user }, error: authError } = await supabase.auth.getUser();
      if (authError || !user) {
        throw new ValidationError('Benutzer nicht authentifiziert');
      }

      const rateLimitResult = aiRateLimiter.checkRateLimit(user.id, 'basic');
      const stats = aiRateLimiter.getUserStats(user.id, 'basic');

      return {
        allowed: rateLimitResult.allowed,
        remaining: rateLimitResult.remaining,
        resetTime: rateLimitResult.resetTime,
        stats
      };
    } catch (error) {
      console.error('Error checking rate limit status:', error);
      return {
        allowed: false,
        remaining: 0,
        resetTime: Date.now() + 60000 // 1 minute default
      };
    }
  }

  // Get AI processing statistics
  static async getAIProcessingStats(
    contactId?: string,
    dateFrom?: string,
    dateTo?: string
  ): Promise<{
    total_communications: number;
    summarized_communications: number;
    summarization_rate: number;
    avg_notes_length: number;
  }> {
    try {
      let query = supabase
        .from('contact_communications')
        .select('notes, summarized_notes');

      if (contactId) {
        query = query.eq('contact_id', contactId);
      }

      if (dateFrom) {
        query = query.gte('communication_date', dateFrom);
      }

      if (dateTo) {
        query = query.lte('communication_date', dateTo);
      }

      const { data: communications, error } = await query;

      if (error) {
        console.error('Error fetching AI stats:', error);
        return {
          total_communications: 0,
          summarized_communications: 0,
          summarization_rate: 0,
          avg_notes_length: 0
        };
      }

      const total = communications.length;
      const summarized = communications.filter(c => c.summarized_notes).length;
      const avgLength = total > 0 ? 
        communications.reduce((sum, c) => sum + (c.notes?.length || 0), 0) / total : 0;

      return {
        total_communications: total,
        summarized_communications: summarized,
        summarization_rate: total > 0 ? (summarized / total) * 100 : 0,
        avg_notes_length: Math.round(avgLength)
      };
    } catch (error) {
      console.error('Error calculating AI processing stats:', error);
      return {
        total_communications: 0,
        summarized_communications: 0,
        summarization_rate: 0,
        avg_notes_length: 0
      };
    }
  }
}