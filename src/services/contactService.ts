import { supabase } from '@/integrations/supabase/client';
import { Contact, CreateContactData, UpdateContactData } from '@/types/applications';

export class ContactService {
  // Update contact statistics after project/application changes
  static async updateContactStatistics(contactId: string): Promise<boolean> {
    try {
      // Get current user for security validation
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        console.error('User not authenticated for contact statistics update');
        return false;
      }

      // Verify contact ownership before proceeding
      const { data: contact, error: contactError } = await supabase
        .from('contacts')
        .select('id')
        .eq('id', contactId)
        .eq('user_id', user.id)
        .single();

      if (contactError || !contact) {
        console.error('Contact not found or access denied for statistics update');
        return false;
      }

      // Fetch applications for this contact
      const { data: applications, error: applicationsError } = await supabase
        .from('project_applications')
        .select('*')
        .eq('contact_id', contactId);

      if (applicationsError) throw applicationsError;

      // Fetch active projects for this contact
      const { data: activeProjects, error: projectsError } = await supabase
        .from('projects')
        .select('*')
        .eq('contact_id', contactId);

      if (projectsError) throw projectsError;

      // Calculate statistics
      const totalApplications = applications?.length || 0;
      const totalActiveProjects = activeProjects?.length || 0;
      const totalProjects = totalApplications + totalActiveProjects;
      
      const successfulProjects = applications?.filter(p => 
        p.status === 'offer_received' || p.status === 'project_completed'
      ).length || 0;

      // Update the contact record
      const { error: updateError } = await supabase
        .from('contacts')
        .update({
          total_projects: totalProjects,
          successful_projects: successfulProjects,
          updated_at: new Date().toISOString()
        })
        .eq('id', contactId);

      if (updateError) throw updateError;

      return true;
    } catch (error) {
      console.error('Error updating contact statistics:', error);
      return false;
    }
  }
  // Create or find existing contact
  static async createOrFindContact(
    contactData: CreateContactData
  ): Promise<Contact | null> {
    try {
      // First, try to find existing contact by email and company
      if (contactData.email) {
        const { data: existingContact, error: findError } = await supabase
          .from('contacts')
          .select('*')
          .eq('email', contactData.email)
          .eq('company', contactData.company || '')
          .maybeSingle();

        if (findError && findError.code !== 'PGRST116') {
          throw findError;
        }

        if (existingContact) {
          // Update existing contact with new information
          const updateData: UpdateContactData = {
            id: existingContact.id,
            name: contactData.name || existingContact.name,
            phone: contactData.phone || existingContact.phone,
            notes: contactData.notes 
              ? `${existingContact.notes || ''}\n${contactData.notes}`.trim()
              : existingContact.notes
          };

          const { data: updatedContact, error: updateError } = await supabase
            .from('contacts')
            .update({
              ...updateData,
              updated_at: new Date().toISOString()
            })
            .eq('id', existingContact.id)
            .select()
            .single();

          if (updateError) throw updateError;
          
          // Update statistics for the updated contact
          await this.updateContactStatistics(existingContact.id);
          
          return updatedContact as Contact;
        }
      }

      // Create new contact if not found
      const { data: newContact, error: createError } = await supabase
        .from('contacts')
        .insert([contactData])
        .select()
        .single();

      if (createError) throw createError;
      
      // Update statistics for the new contact (will be 0 initially but ensures consistency)
      await this.updateContactStatistics(newContact.id);
      
      return newContact as Contact;
    } catch (error) {
      console.error('Error creating or finding contact:', error);
      return null;
    }
  }

  // Auto-suggest contacts based on partial input
  static async suggestContacts(
    query: string,
    limit: number = 5
  ): Promise<Contact[]> {
    try {
      if (!query.trim()) return [];

      const { data, error } = await supabase
        .from('contacts')
        .select('*')
        .or(`name.ilike.%${query}%,email.ilike.%${query}%,company.ilike.%${query}%`)
        .order('total_projects', { ascending: false })
        .limit(limit);

      if (error) throw error;
      return data || [];
    } catch (error) {
      console.error('Error suggesting contacts:', error);
      return [];
    }
  }

  // Merge duplicate contacts
  static async mergeDuplicateContacts(
    keepContactId: string,
    duplicateContactIds: string[]
  ): Promise<boolean> {
    try {
      // Start a transaction-like operation
      const { data: keepContact, error: keepError } = await supabase
        .from('contacts')
        .select('*')
        .eq('id', keepContactId)
        .single();

      if (keepError) throw keepError;

      const { data: duplicateContacts, error: duplicateError } = await supabase
        .from('contacts')
        .select('*')
        .in('id', duplicateContactIds);

      if (duplicateError) throw duplicateError;

      // Merge data from duplicate contacts
      const mergedData = duplicateContacts.reduce((acc, contact) => {
        return {
          name: acc.name || contact.name,
          phone: acc.phone || contact.phone,
          notes: [acc.notes, contact.notes].filter(Boolean).join('\n'),
          company: acc.company || contact.company
        };
      }, keepContact);

      // Update the contact we're keeping
      const { error: updateError } = await supabase
        .from('contacts')
        .update({
          ...mergedData,
          updated_at: new Date().toISOString()
        })
        .eq('id', keepContactId);

      if (updateError) throw updateError;

      // Update all applications and projects to point to the kept contact
      const { error: applicationUpdateError } = await supabase
        .from('project_applications')
        .update({ contact_id: keepContactId })
        .in('contact_id', duplicateContactIds);

      if (applicationUpdateError) throw applicationUpdateError;

      const { error: projectUpdateError } = await supabase
        .from('projects')
        .update({ contact_id: keepContactId })
        .in('contact_id', duplicateContactIds);

      if (projectUpdateError) throw projectUpdateError;

      // Delete duplicate contacts
      const { error: deleteError } = await supabase
        .from('contacts')
        .delete()
        .in('id', duplicateContactIds);

      if (deleteError) throw deleteError;

      return true;
    } catch (error) {
      console.error('Error merging duplicate contacts:', error);
      return false;
    }
  }

  // Find potential duplicate contacts
  static async findPotentialDuplicates(): Promise<{
    email_duplicates: Contact[][];
    name_company_duplicates: Contact[][];
  }> {
    try {
      // Find duplicates by email
      const { data: emailDuplicates, error: emailError } = await supabase
        .from('contacts')
        .select('*')
        .not('email', 'is', null)
        .order('email');

      if (emailError) throw emailError;

      // Group by email
      const emailGroups: Record<string, Contact[]> = {};
      emailDuplicates?.forEach(contact => {
        if (contact.email) {
          if (!emailGroups[contact.email]) {
            emailGroups[contact.email] = [];
          }
          emailGroups[contact.email].push(contact);
        }
      });

      const emailDuplicateGroups = Object.values(emailGroups).filter(group => group.length > 1);

      // Find duplicates by name + company combination
      const { data: nameCompanyContacts, error: nameError } = await supabase
        .from('contacts')
        .select('*')
        .not('name', 'is', null)
        .not('company', 'is', null);

      if (nameError) throw nameError;

      const nameCompanyGroups: Record<string, Contact[]> = {};
      nameCompanyContacts?.forEach(contact => {
        if (contact.name && contact.company) {
          const key = `${contact.name.toLowerCase()}|${contact.company.toLowerCase()}`;
          if (!nameCompanyGroups[key]) {
            nameCompanyGroups[key] = [];
          }
          nameCompanyGroups[key].push(contact);
        }
      });

      const nameCompanyDuplicateGroups = Object.values(nameCompanyGroups).filter(group => group.length > 1);

      return {
        email_duplicates: emailDuplicateGroups,
        name_company_duplicates: nameCompanyDuplicateGroups
      };
    } catch (error) {
      console.error('Error finding potential duplicates:', error);
      return {
        email_duplicates: [],
        name_company_duplicates: []
      };
    }
  }

  // Export contacts for backup or analysis
  static async exportContacts(): Promise<Contact[]> {
    try {
      const { data, error } = await supabase
        .from('contacts')
        .select('*')
        .order('created_at', { ascending: false });

      if (error) throw error;
      return data || [];
    } catch (error) {
      console.error('Error exporting contacts:', error);
      return [];
    }
  }

  // Import contacts from external source
  static async importContacts(contacts: CreateContactData[]): Promise<{
    success: number;
    errors: number;
    duplicates: number;
  }> {
    let success = 0;
    let errors = 0;
    let duplicates = 0;

    for (const contactData of contacts) {
      try {
        const result = await this.createOrFindContact(contactData);
        if (result) {
          // Check if this was a new contact or existing one
          const isExisting = await supabase
            .from('contacts')
            .select('id')
            .eq('id', result.id)
            .eq('created_at', result.updated_at)
            .single();
          
          if (isExisting.data) {
            success++;
          } else {
            duplicates++;
          }
        } else {
          errors++;
        }
      } catch (error) {
        console.error('Error importing contact:', error);
        errors++;
      }
    }

    return { success, errors, duplicates };
  }
}