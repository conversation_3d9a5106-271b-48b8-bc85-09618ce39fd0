// SEO utilities for Lanzr application

export interface SEOPageData {
  title: string;
  description: string;
  keywords: string[];
  url: string;
  image?: string;
  lastModified?: string;
  priority?: number;
  changeFreq?: 'always' | 'hourly' | 'daily' | 'weekly' | 'monthly' | 'yearly' | 'never';
}

// Main page SEO data
export const seoData: Record<string, SEOPageData> = {
  '/': {
    title: 'Lanzr - Professionelles Freelance Projektmanagement | AI-gestützte Bewerbungserstellung',
    description: 'Revolutioniere dein Freelance Business mit Lanzr! ✨ AI-gestützte Bewerbungserstellung, Projekttracking, Zeiterfassung & Analytics. Kostenlos starten und mehr Aufträge gewinnen!',
    keywords: ['freelance projektmanagement', 'bewerbung generator ai', 'freelancer tool', 'projekt tracking', 'zeiterfassung'],
    url: 'https://lanzr.de/',
    priority: 1.0,
    changeFreq: 'weekly'
  },
  '/applications': {
    title: 'Bewerbungsmanagement - AI-gestützte Bewerbungserstellung | Lanzr',
    description: 'Erstelle professionelle Freelance-Bewerbungen mit AI-Unterstützung. Automatische Textgenerierung basierend auf Projektbeschreibungen und deinem CV.',
    keywords: ['bewerbung generator', 'ai bewerbung', 'freelance bewerbung', 'bewerbungsmanagement', 'automatische bewerbung'],
    url: 'https://lanzr.de/applications',
    priority: 0.9,
    changeFreq: 'daily'
  },
  '/projects': {
    title: 'Aktive Projekte verwalten - Freelance Projektmanagement | Lanzr',
    description: 'Verwalte deine aktiven Freelance-Projekte mit Zeiterfassung, Meilenstein-Tracking und detailliertem Reporting. Optimiere deine Projektabwicklung.',
    keywords: ['projektmanagement', 'aktive projekte', 'zeiterfassung', 'projekt tracking', 'freelance projekte'],
    url: 'https://lanzr.de/projects',
    priority: 0.9,
    changeFreq: 'daily'
  },
  '/statistics': {
    title: 'Freelance Analytics & Statistiken - Erfolg messbar machen | Lanzr',
    description: 'Detaillierte Statistiken und Analytics für dein Freelance Business. Bewerbungsquoten, Projekterfolg und Umsatzanalysen auf einen Blick.',
    keywords: ['freelance statistiken', 'analytics', 'bewerbungsquote', 'projekterfolg', 'umsatz analyse'],
    url: 'https://lanzr.de/statistics',
    priority: 0.8,
    changeFreq: 'weekly'
  },
  '/contacts': {
    title: 'Kontaktmanagement für Freelancer - CRM & Lead-Verwaltung | Lanzr',
    description: 'Professionelle Kontaktverwaltung für Freelancer. Verwalte Leads, Kunden und Ansprechpartner zentral mit Analytics und Follow-up-Funktionen.',
    keywords: ['kontaktmanagement', 'crm freelancer', 'lead verwaltung', 'kundenverwaltung', 'ansprechpartner'],
    url: 'https://lanzr.de/contacts',
    priority: 0.7,
    changeFreq: 'weekly'
  },
  '/calendar': {
    title: 'Kalender & Terminplanung - Freelance Zeit optimal nutzen | Lanzr',
    description: 'Integrierte Kalender-Funktion für Freelancer. Plane Meetings, Deadlines und Projekttermine mit automatischen Erinnerungen.',
    keywords: ['freelance kalender', 'terminplanung', 'meetings', 'deadlines', 'projekttermine'],
    url: 'https://lanzr.de/calendar',
    priority: 0.6,
    changeFreq: 'daily'
  },
  '/settings': {
    title: 'Einstellungen - Profile & Präferenzen verwalten | Lanzr',
    description: 'Verwalte dein Lanzr-Profil, lade dein CV hoch und konfiguriere Einstellungen für optimale AI-Bewerbungserstellung.',
    keywords: ['profile settings', 'cv upload', 'freelancer profile', 'einstellungen', 'präferenzen'],
    url: 'https://lanzr.de/settings',
    priority: 0.4,
    changeFreq: 'monthly'
  }
};

// Generate structured data for pages
export const generatePageStructuredData = (pageKey: string) => {
  const pageData = seoData[pageKey];
  if (!pageData) return null;

  return {
    "@context": "https://schema.org",
    "@type": "WebPage",
    "name": pageData.title,
    "description": pageData.description,
    "url": pageData.url,
    "inLanguage": "de-DE",
    "isPartOf": {
      "@type": "WebSite",
      "name": "Lanzr",
      "url": "https://lanzr.de"
    },
    "author": {
      "@type": "Organization",
      "name": "Lanzr"
    },
    "dateModified": pageData.lastModified || new Date().toISOString(),
    "keywords": pageData.keywords.join(', ')
  };
};

// Generate sitemap.xml content
export const generateSitemap = () => {
  const baseUrl = 'https://lanzr.de';
  const currentDate = new Date().toISOString().split('T')[0];
  
  const urlEntries = Object.entries(seoData)
    .map(([path, data]) => `
  <url>
    <loc>${data.url}</loc>
    <lastmod>${data.lastModified || currentDate}</lastmod>
    <changefreq>${data.changeFreq || 'weekly'}</changefreq>
    <priority>${data.priority || 0.5}</priority>
  </url>`)
    .join('');

  return `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
${urlEntries}
</urlset>`;
};

// Update page title and meta description dynamically
export const updatePageSEO = (pageKey: string) => {
  const pageData = seoData[pageKey];
  if (!pageData) return;

  // Update document title
  document.title = pageData.title;

  // Update meta description
  const descriptionMeta = document.querySelector('meta[name="description"]');
  if (descriptionMeta) {
    descriptionMeta.setAttribute('content', pageData.description);
  }

  // Update Open Graph tags
  const ogTitle = document.querySelector('meta[property="og:title"]');
  if (ogTitle) {
    ogTitle.setAttribute('content', pageData.title);
  }

  const ogDescription = document.querySelector('meta[property="og:description"]');
  if (ogDescription) {
    ogDescription.setAttribute('content', pageData.description);
  }

  const ogUrl = document.querySelector('meta[property="og:url"]');
  if (ogUrl) {
    ogUrl.setAttribute('content', pageData.url);
  }

  // Update Twitter Card tags
  const twitterTitle = document.querySelector('meta[property="twitter:title"]');
  if (twitterTitle) {
    twitterTitle.setAttribute('content', pageData.title);
  }

  const twitterDescription = document.querySelector('meta[property="twitter:description"]');
  if (twitterDescription) {
    twitterDescription.setAttribute('content', pageData.description);
  }

  const twitterUrl = document.querySelector('meta[property="twitter:url"]');
  if (twitterUrl) {
    twitterUrl.setAttribute('content', pageData.url);
  }

  // Update canonical URL
  const canonicalLink = document.querySelector('link[rel="canonical"]');
  if (canonicalLink) {
    canonicalLink.setAttribute('href', pageData.url);
  }
};

// Generate robots.txt content for production
export const generateRobotsTxt = (isProduction: boolean = false) => {
  if (!isProduction) {
    return `# Robots.txt für Lanzr - Development Version
# Aktuell: Keine Indexierung (Development Phase)

User-agent: *
Disallow: /`;
  }

  return `User-agent: *
Allow: /

Disallow: /auth
Disallow: /settings
Disallow: /api/
Disallow: /private/
Disallow: /_*
Disallow: /admin/

Sitemap: https://lanzr.de/sitemap.xml

Crawl-delay: 1`;
};