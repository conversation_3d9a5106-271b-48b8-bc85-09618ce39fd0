import type { 
  FollowUpTemplate, 
  ScheduledFollowUp, 
  FollowUpHistory,
  FollowUpAnalytics
} from '@/types/followup'
import type { 
  ContactCommunication, 
  CreateCommunicationData,
  CommunicationType 
} from '@/types/communications'

export const mockUser = {
  id: 'test-user-id',
  email: '<EMAIL>',
  created_at: '2024-01-01T00:00:00Z',
}

export const mockTemplate: FollowUpTemplate = {
  id: 'template-1',
  user_id: 'test-user-id',
  name: 'Test Template',
  subject: 'Follow-up: {project_name}',
  body: '<PERSON><PERSON> {contact_person}, bezüglich {project_name} bei {company_name}. Nach {trigger_days} Tagen möchte ich nachfragen...',
  trigger_days: 7,
  status_trigger: 'application_sent',
  is_active: true,
  created_at: '2024-01-01T00:00:00Z',
  updated_at: '2024-01-01T00:00:00Z',
}

export const mockScheduledFollowUp: ScheduledFollowUp = {
  id: 'schedule-1',
  user_id: 'test-user-id',
  application_id: 'app-1',
  template_id: 'template-1',
  scheduled_date: '2024-08-16T12:00:00Z',
  status: 'scheduled',
  response_received: false,
  created_at: '2024-08-09T12:00:00Z',
  template: mockTemplate,
  application: {
    id: 'app-1',
    project_name: 'React Developer',
    contact_person: 'Max Mustermann',
    company_name: 'Tech GmbH',
    status: 'application_sent',
  },
}

export const mockFollowUpHistory: FollowUpHistory = {
  id: 'history-1',
  user_id: 'test-user-id',
  application_id: 'app-1',
  template_id: 'template-1',
  sent_at: '2024-08-09T12:00:00Z',
  subject: 'Follow-up: React Developer',
  body: 'Hallo Max Mustermann, bezüglich React Developer bei Tech GmbH...',
  response_received: false,
  created_at: '2024-08-09T12:00:00Z',
  template: mockTemplate,
  application: {
    id: 'app-1',
    project_name: 'React Developer',
    contact_person: 'Max Mustermann',
    company_name: 'Tech GmbH',
  },
}

export const mockAnalytics: FollowUpAnalytics = {
  total_sent: 10,
  response_rate: 0.3,
  avg_response_time_days: 5.2,
  success_by_template: [
    {
      template_id: 'template-1',
      template_name: 'Test Template',
      sent_count: 6,
      response_count: 2,
      response_rate: 0.333,
    },
    {
      template_id: 'template-2',
      template_name: 'Interview Follow-up',
      sent_count: 4,
      response_count: 1,
      response_rate: 0.25,
    },
  ],
  success_by_timing: [
    {
      trigger_days: 3,
      sent_count: 2,
      response_count: 1,
      response_rate: 0.5,
    },
    {
      trigger_days: 7,
      sent_count: 6,
      response_count: 2,
      response_rate: 0.333,
    },
    {
      trigger_days: 14,
      sent_count: 2,
      response_count: 0,
      response_rate: 0,
    },
  ],
}

export const mockTemplateVariables = {
  project_name: 'React Developer',
  contact_person: 'Max Mustermann',
  company_name: 'Tech GmbH',
  user_name: 'John Doe',
  trigger_days: 7,
  application_date: '2024-08-01',
  interview_date: '2024-08-05',
}

// Communication Mocks
export const mockContact = {
  id: 'contact-123',
  user_id: 'test-user-id',
  name: 'Max Mustermann',
  email: '<EMAIL>',
  company: 'Tech GmbH',
  phone: '+49 **********',
  created_at: '2024-01-01T00:00:00Z',
  updated_at: '2024-01-01T00:00:00Z'
}

export const mockProject = {
  id: 'project-123',
  project_name: 'React Developer Position',
  company_name: 'Tech GmbH',
  contact_person: 'Max Mustermann',
  status: 'interview_completed',
  created_at: '2024-08-01T00:00:00Z'
}

export const mockCommunication: ContactCommunication = {
  id: 'comm-123',
  user_id: 'test-user-id',
  contact_id: 'contact-123',
  communication_type: 'call',
  subject: 'Follow-up call regarding React Developer position',
  notes: 'Productive call discussing project timeline and technical requirements. Candidate showed strong React and TypeScript skills.',
  communication_date: '2024-08-22T14:30:00.000Z',
  duration_minutes: 45,
  project_id: 'project-123',
  is_project_related: true,
  ai_summary: null,
  summarized_notes: null,
  created_at: '2024-08-22T14:30:00.000Z',
  updated_at: '2024-08-22T14:30:00.000Z',
  contact: mockContact,
  project: mockProject
}

export const mockCreateCommunicationData: CreateCommunicationData = {
  contact_id: 'contact-123',
  communication_type: 'call',
  subject: 'Follow-up call regarding React Developer position',
  notes: 'Productive call discussing project timeline and technical requirements.',
  communication_date: '2024-08-22T14:30:00.000Z',
  duration_minutes: 45,
  project_id: 'project-123',
  is_project_related: true
}

export const mockCommunicationsList: ContactCommunication[] = [
  mockCommunication,
  {
    ...mockCommunication,
    id: 'comm-124',
    communication_type: 'email',
    subject: 'Technical questions via email',
    notes: 'Sent detailed technical questions about the React project. Awaiting response.',
    communication_date: '2024-08-20T10:15:00.000Z',
    duration_minutes: null,
    is_project_related: true
  },
  {
    ...mockCommunication,
    id: 'comm-125',
    communication_type: 'message',
    subject: null,
    notes: 'Quick LinkedIn message to confirm interview time.',
    communication_date: '2024-08-19T16:45:00.000Z',
    duration_minutes: null,
    is_project_related: false,
    project_id: null
  }
]

// Helper function to create mock Supabase response
export const createMockSupabaseResponse = <T>(data: T, error: any = null) => ({
  data,
  error,
  count: null,
  status: error ? 400 : 200,
  statusText: error ? 'Bad Request' : 'OK'
})
export const createMockQueryClient = () => ({
  invalidateQueries: vi.fn(),
  setQueryData: vi.fn(),
  getQueryData: vi.fn(),
})