import '@testing-library/jest-dom'
import { beforeAll, vi } from 'vitest'

// Mock Supabase
vi.mock('@/integrations/supabase/client', () => {
  const createMockQueryBuilder = () => ({
    select: vi.fn().mockReturnThis(),
    insert: vi.fn().mockReturnThis(),
    update: vi.fn().mockReturnThis(),
    delete: vi.fn().mockReturnThis(),
    eq: vi.fn().mockReturnThis(),
    order: vi.fn().mockReturnThis(),
    single: vi.fn().mockResolvedValue({ data: null, error: null }),
    maybeSingle: vi.fn().mockResolvedValue({ data: null, error: null }),
  })

  const mockSupabase = {
    auth: {
      getUser: vi.fn(),
      onAuthStateChange: vi.fn(() => ({ data: { subscription: { unsubscribe: vi.fn() } } })),
    },
    from: vi.fn(() => createMockQueryBuilder()),
    storage: {
      from: vi.fn(() => ({
        upload: vi.fn(),
        download: vi.fn(),
        remove: vi.fn(),
        getPublicUrl: vi.fn(),
      })),
    },
  }
  
  return { supabase: mockSupabase }
})

// Mock error handling
vi.mock('@/lib/errorHandling', () => ({
  withFollowUpErrorHandling: vi.fn((fn) => fn()),
  createAppError: vi.fn(),
  logError: vi.fn(),
  tryCatch: vi.fn(),
}))

// Mock toast notifications
vi.mock('@/lib/toast', () => ({
  toast: {
    success: vi.fn(),
    error: vi.fn(),
    info: vi.fn(),
  },
}))

// Mock date-fns locale
vi.mock('date-fns/locale/de', () => ({
  de: {},
}))

// Mock navigation
vi.mock('react-router-dom', () => ({
  useNavigate: () => vi.fn(),
  useLocation: () => ({ pathname: '/' }),
  useParams: () => ({}),
  Link: ({ children, to }: { children: any; to: string }) => `<a href="${to}">${children}</a>`,
}))

// Mock window.open for mailto links
Object.defineProperty(window, 'open', {
  writable: true,
  value: vi.fn(),
})

// Setup fake timers for date consistency
beforeAll(() => {
  vi.useFakeTimers()
  vi.setSystemTime(new Date('2024-08-09T12:00:00Z'))
})