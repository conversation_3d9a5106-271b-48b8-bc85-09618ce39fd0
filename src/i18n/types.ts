// TypeScript definitions for i18n
export interface TranslationResources {
  common: typeof import('./resources/de/common.json');
  forms: typeof import('./resources/de/forms.json');
  pages: typeof import('./resources/de/pages.json');
  status: typeof import('./resources/de/status.json');
  errors: typeof import('./resources/de/errors.json');
  calendar: typeof import('./resources/de/calendar.json');
  export: typeof import('./resources/de/export.json');
  timer: typeof import('./resources/de/timer.json');
  followup: typeof import('./resources/de/followup.json');
  tables: typeof import('./resources/de/tables.json');
  settings: typeof import('./resources/de/settings.json');
  contacts: typeof import('./resources/de/contacts.json');
  applications: typeof import('./resources/de/applications.json');
}

// Extend react-i18next module to include our resource types
declare module 'react-i18next' {
  interface CustomTypeOptions {
    defaultNS: 'common';
    resources: TranslationResources;
  }
}

// Available languages
export const SUPPORTED_LANGUAGES = ['de', 'en'] as const;
export type SupportedLanguage = (typeof SUPPORTED_LANGUAGES)[number];

// Language display names
export const LANGUAGE_NAMES: Record<SupportedLanguage, string> = {
  de: 'Deutsch',
  en: 'English',
};