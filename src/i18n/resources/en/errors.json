{"validation": {"required": "This field is required", "email_invalid": "Invalid email address", "min_length": "At least {{count}} characters", "max_length": "Maximum {{count}} characters", "date_invalid": "Invalid date", "number_invalid": "Invalid number", "phone_invalid": "Invalid phone number"}, "api": {"network_error": "Network error", "unauthorized": "Unauthorized", "forbidden": "Access denied", "not_found": "Not found", "server_error": "Server error", "invalid_code": "Invalid code", "timeout": "Timeout"}, "auth": {"invalid_credentials": "Invalid credentials", "password_too_weak": "Password too weak", "email_already_exists": "Email already registered", "login_failed": "<PERSON><PERSON> failed"}, "project": {"save_failed": "Project could not be saved", "delete_failed": "Project could not be deleted", "load_failed": "Project could not be loaded"}, "communication": {"notes_required": "Notes are required", "notes_too_short": "Notes must be at least {{count}} characters long", "notes_too_long": "Notes cannot be longer than {{count}} characters", "subject_too_long": "Subject cannot be longer than {{count}} characters", "duration_invalid": "Duration must be a positive number", "duration_too_large": "Duration cannot exceed {{count}} minutes (24 hours)", "communication_type_required": "Communication type is required", "communication_type_invalid": "Invalid communication type. Must be one of: {{types}}", "date_required": "{{field}} is required", "date_invalid_format": "Invalid {{field}} format", "date_too_far_future": "{{field}} cannot be more than 1 year in the future", "date_too_far_past": "{{field}} cannot be more than 10 years in the past", "search_text_required": "Search text is required", "search_text_too_short": "Search text must be at least {{count}} characters long", "search_text_too_long": "Search text cannot be longer than {{count}} characters", "uuid_required": "{{field}} is required", "uuid_invalid_format": "Invalid {{field}} format", "invalid_type": "{{field}} must be of type {{type}}", "limit_too_large": "Limit cannot exceed {{count}}", "ids_empty_array": "At least one communication ID is required", "ids_too_many": "Cannot process more than {{count}} communications at once", "invalid_id_at_index": "Invalid ID at index {{index}}: {{message}}"}, "security": {"sql_injection": "Potential SQL injection detected in {{field}}", "xss_attack": "Potential XSS attack detected in {{field}}", "command_injection": "Potential command injection detected in {{field}}", "path_traversal": "Potential path traversal detected in {{field}}", "excessive_repetition": "Excessive character repetition detected in {{field}}", "content_not_allowed": "Input contains not allowed content"}, "rate_limit": {"exceeded": "Rate limit exceeded", "burst_exceeded": "Burst limit exceeded. Please wait before making another request.", "tier_exceeded": "{{tier}} tier limit exceeded ({{max}} requests per minute).", "hourly_exceeded": "Hourly security limit exceeded ({{max}} requests per hour).", "daily_exceeded": "Daily security limit exceeded ({{max}} requests per day).", "try_again_at": "Try again at {{time}}. Remaining requests: {{remaining}}"}, "ai": {"summary_failed": "AI summary failed", "user_not_authenticated": "User not authenticated", "processing_error": "Error in AI processing", "unknown_error": "Unknown error in AI summary", "validation_error": "Unknown validation error"}, "general": {"unexpected_error": "An unexpected error occurred", "try_again": "Please try again", "contact_support": "Contact support"}}