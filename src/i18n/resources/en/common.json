{"navigation": {"dashboard": "Dashboard", "applications": "Applications", "projects": "Projects", "calendar": "Calendar", "contacts": "Contacts", "settings": "Settings", "time_tracking": "Time Tracking", "statistics": "Statistics", "reports": "Reports", "followups": "Follow-ups", "acquisition": "Acquisition", "back_to_contacts": "Back to Contacts"}, "actions": {"save": "Save", "cancel": "Cancel", "delete": "Delete", "edit": "Edit", "create": "Create", "update": "Update", "back": "Back", "next": "Next", "loading": "Loading...", "saving": "Saving...", "search": "Search", "filter": "Filter", "export": "Export", "import": "Import", "close": "Close", "submit": "Submit", "reset": "Reset", "confirm": "Confirm"}, "general": {"yes": "Yes", "no": "No", "or": "or", "and": "and", "optional": "(optional)", "required": "*", "all": "All", "none": "None", "welcome": "Welcome", "logout": "Logout", "login": "<PERSON><PERSON>", "loading": "Loading...", "saving": "Saving...", "change_status": "Change Status"}, "common": {"or": "or"}, "messages": {"login_success": "Login successful", "login_failed": "<PERSON><PERSON> failed", "registration_success": "Registration successful", "registration_failed": "Registration failed", "welcome_back": "Welcome back!", "welcome_lanzr": "Welcome to Lanzr! You can now create projects.", "project_created": "Project created", "project_created_desc": "New project was created successfully."}, "footer": {"copyright": "© 2025 Lanzr. Professional project management for freelancers."}, "note": "Note", "loading_states": {"dashboard": "Loading Dashboard...", "data": "Loading Data...", "saving": "Saving..."}, "error_states": {"data_loading": "Error loading data", "unknown_error": "Unknown error"}, "project_labels": {"unknown": "Unknown", "application": "Application", "project": "Project"}, "activities": {"application_created": "Application Created", "application_updated": "Application Updated", "application_deleted": "Application Deleted", "application_sent": "Application Sent", "application_status_changed": "Application Status Changed", "application_converted_to_project": "Converted to Project", "ai_analysis_generated": "AI Analysis Generated", "ai_application_generated": "Application Text Generated", "followup_scheduled": "Follow-up Scheduled", "followup_sent": "Follow-up <PERSON><PERSON>", "followup_marked_as_sent": "Follow-up Marked as <PERSON><PERSON>", "followup_deleted": "Follow-up Deleted", "contact_created": "Contact Created", "contact_updated": "Contact Updated", "contact_deleted": "Contact Deleted", "status_changed": "Status Changed", "created": "Created", "updated": "Updated", "deleted": "Deleted", "all_activities": "All Activities", "search_activities": "Search activities...", "filter_type": "Filter Type", "all_types": "All Types", "activities_count": "{{count}} of {{total}} activities", "loading_activities": "Loading Activities...", "no_activities_found": "No activities found", "no_activities_yet": "No activities yet", "notes": "Notes"}, "project_card": {"contact_and_company": "Contact & Company", "project_details": "Project Details", "budget": "Budget", "budget_not_specified": "Not specified", "all_skills": "All Skills: {{skills}}", "followups": "Follow-ups", "no_followups_planned": "No follow-ups planned", "followups_help_text": "Follow-ups help you stay on track", "followups_loading": "Loading follow-ups...", "followups_error": "Could not load follow-ups", "followup_template": "Follow-up Template", "edit": "Edit", "mark_as_sent": "<PERSON> as <PERSON><PERSON>", "delete_confirmation": "Really delete follow-up?", "delete": "Delete", "more_followups": "+{{count}} more follow-ups", "followup_sent": "Follow-up sent", "not_specified": "Not specified", "invalid_date": "Invalid date", "submitted": "Submitted", "work_location": "Work Location", "start_date": "Start", "end_date": "End", "technologies": "Technologies", "project_description": "Project Description", "loading": "Loading...", "schedule": "Schedule", "open_email": "Open E-mail"}, "status_change": {"title": "Status Change Notes", "status_change": "Status Change", "notes_optional": "Notes (optional)", "ai_formatting": "AI Format", "formatting": "Formatting...", "notes_placeholder": "You can record details about this status change here...\n\nExamples:\n• Email content from inquiries\n• Interview feedback\n• Next steps\n• Important dates or deadlines\n\nTip: Enter unstructured notes - AI can format them!", "characters": "{{count}} characters", "ai_formatted_version": "AI-Formatted Version", "use_formatted": "Use Formatted Version", "close_preview": "Close Preview", "interview_appointment": "Interview Appointment", "date_required": "Date *", "time_required": "Time *", "calendar_info": "This information will be used for automatic calendar entry.", "use_custom_date": "Use custom date (e.g. when email was received)", "note_date_time": "Date/Time of Note", "change_without_notes": "Change Status Without Notes", "saving": "Saving...", "change_with_notes": "Change Status With Notes", "notes_formatted": "Notes formatted", "check_preview": "Check preview and apply!", "formatting_failed": "Formatting failed", "try_again": "Try again."}, "timeline": {"project_timeline": "Project Timeline", "error_loading": "Error loading timeline", "error_description": "Activities could not be loaded.", "activities": "Activities", "status_changes": "Status Changes", "project_updates": "Project Updates", "followups": "Follow-ups", "status_duration": "Status Duration", "status_duration_description": "Time in each status (in days)", "day": "day", "days": "days", "activity_timeline": "Activity Timeline", "activity_timeline_description": "Chronological record of all project activities", "no_activities": "No activities yet", "show_notes": "Show notes", "notes_date": "Note date: {{date}}"}, "search": {"global_search": "Global Search", "search_placeholder": "Search all projects, applications and contacts...", "search_description": "Search through all your projects, applications, contacts and calendar entries", "search_field_label": "Search field", "search_help": "Use arrow keys to navigate and Enter to open. Escape to close.", "searching": "Searching...", "search_failed": "Search failed", "search_running": "Search running", "min_characters": "Enter at least {{count}} characters", "min_characters_notice": "Enter at least 2 characters to search", "no_results": "No results found", "no_results_for": "No results found for \"{{query}}\".", "results_found": "{{count}} result{{plural}} found", "search_suggestions": "Try these search terms:", "search_tips": "Tips: Try shorter terms or different spellings", "error_occurred": "⚠️ Search error", "no_results_icon": "🔍 No results", "shortcuts": {"navigate": "Navigate", "select": "Select", "close": "Close"}}, "notifications": {"title": "Notifications", "mark_all_read": "<PERSON> all read", "mark_all_as_read": "Mark all as read", "clear_all": "Clear all", "no_notifications": "No notifications", "up_to_date": "You're up to date!", "all_read": "All read", "no_unread": "No unread notifications", "no_groups": "No groups", "no_notifications_to_group": "No notifications to group", "show_all": "Show all notifications", "more_notifications": "more notifications", "today": "Today", "yesterday": "Yesterday", "latest": "Latest", "all_notifications": "All Notifications", "all_done": "All done!", "all_caught_up": "You're all caught up. New notifications will appear here when they arrive.", "mark_as_read": "<PERSON> as read", "mark_as_unread": "<PERSON> as unread", "new": "new", "urgent": "<PERSON><PERSON>", "tabs": {"all": "All", "unread": "Unread", "groups": "Grouped"}, "types": {"follow_up_due": "Follow-up Due", "follow_up_overdue": "Follow-up Overdue", "application_reminder": "Application Follow-up", "interview_reminder": "Interview Reminder", "calendar_event": "Calendar Reminder", "system_update": "System Update", "general": "General"}, "stats": {"total": "Total", "unread": "Unread", "urgent": "<PERSON><PERSON>", "recent": "Today", "messages": "Messages", "not_read": "Not read", "important": "Important", "from_today": "From today"}}, "pwa": {"install": {"title": "Install App", "description": "For faster access", "install_button": "Install", "later_button": "Later"}}}