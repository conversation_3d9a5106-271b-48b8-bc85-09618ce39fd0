{"title": "Calendar", "new": "New", "new_event": "New Event", "edit_event": "Edit Event", "delete_event": "Delete Event", "event_title": "Title", "event_description": "Description", "event_location": "Location", "event_date": "Date", "event_time": "Time", "all_day": "All Day", "reminder": "Reminder", "navigation": {"today": "Today", "today_events": "Today's Events", "events_on": "Events on", "no_events_today": "No events for today", "more_events": "more"}, "view_toggle": {"list": "List", "grid": "Grid"}, "weekdays": {"monday": "Monday", "tuesday": "Tuesday", "wednesday": "Wednesday", "thursday": "Thursday", "friday": "Friday", "saturday": "Saturday", "sunday": "Sunday", "short": {"monday": "Mon", "tuesday": "<PERSON><PERSON>", "wednesday": "Wed", "thursday": "<PERSON>hu", "friday": "<PERSON><PERSON>", "saturday": "Sat", "sunday": "Sun"}}, "dialog": {"create_title": "Create New Event", "edit_title": "Edit Event", "details_title": "Event Details", "delete_title": "Delete Event", "delete_description": "Are you sure you want to delete the event \"{{title}}\"? This action cannot be undone."}, "form": {"title_required": "Title *", "description": "Description", "link_type": "Link", "no_link": "No Link", "link_application": "Link to Application", "link_project": "Link to Project", "select_application": "Select Application", "select_project": "Select Project", "select_project_optional": "Select project (optional)", "date_time": "Date & Time", "start_date": "Start Date", "start_time": "Start Time", "end_date": "End Date", "end_time": "End Time", "event_type": "Type", "color": "Color", "location": "Location", "reminder_before": "Reminder before event"}, "placeholders": {"event_title": "Event title...", "event_description": "Additional details about the event...", "event_location": "Event location...", "select_project": "Select project (optional)"}, "event_types": {"interview": "Interview", "meeting": "Meeting", "deadline": "Deadline", "followup": "Follow-up", "follow_up": "Follow-up", "call": "Call", "presentation": "Presentation", "manual": "Manual", "other": "Other", "project_start": "Project Start", "project_end": "Project End", "application_due": "Application Due"}, "link_types": {"application": "Application", "project": "Project", "contact": "Contact"}, "actions": {"create": "Create Event", "creating": "Creating...", "save": "Save Changes", "saving": "Saving...", "cancel": "Cancel", "close": "Close", "delete": "Delete", "deleting": "Deleting...", "edit": "Edit", "link_to_project": "Go to Project", "select_link_type": "Select Link Type", "select_application": "Select Application", "select_project": "Select Project"}, "reminders": {"none": "No Reminder", "5_min": "5 minutes before", "15_min": "15 minutes before", "30_min": "30 minutes before", "1_hour": "1 hour before", "1_day": "1 day before"}, "status": {"completed": "Completed", "pending": "Pending", "all_day": "All Day", "automatically_created": "Automatically created from project status"}}