{"new_project": {"title": "Add New Project", "description": "Create a new freelance project", "back": "Back", "status_setup": {"title": "Set Initial Project Status", "placeholder_project": "New Project", "placeholder_company": "To be defined"}, "toasts": {"created_title": "Project Created", "created_message": "New project was successfully added.", "error_title": "Error", "error_message": "Project could not be created.", "status_set_title": "Status Set", "status_set_message": "Initial project status has been set."}, "validation": {"required_fields_title": "Error", "required_fields_message": "Project name and company name are required.", "contact_created_title": "Contact Created", "contact_created_message": "New contact \"{{name}}\" was created."}}, "components": {"basic_info": {"title": "Project Basic Data", "project_name_label": "Project Name *", "company_name_label": "Company Name *", "source_label": "Source", "source_placeholder": "e.g. freelancermap, gulp, hays", "listing_url_label": "Link to Job Posting"}, "contact_selector": {"title": "Contact Information", "contact_label": "Contact", "select_existing": "Select Existing Contact", "select_contact_dialog": "Select Contact", "search_contacts": "Search Contact", "loading_contacts": "Loading contacts...", "no_matching_contacts": "No matching contacts found", "no_contacts": "No contacts available", "unnamed_contact": "Unnamed Contact", "remove_contact": "Remove Contact", "selected_contact": "Selected Contact:", "contact_person_label": "Contact Person", "contact_person_placeholder": "Contact person name", "contact_company_label": "Contact Company", "contact_company_placeholder": "Contact's company", "contact_selected_toast": "Contact Selected", "contact_selected_message": "Contact \"{{name}}\" was selected."}, "details": {"title": "Project Details", "project_start_label": "Project Start", "project_end_label": "Project End", "budget_label": "Hourly Rate", "budget_placeholder": "e.g. $85/h or $80-90/h", "project_description_label": "Project Description", "required_skills_label": "Required Skills", "skill_placeholder": "Add skill..."}, "work_location": {"title": "Work Model", "work_location_type_label": "Work Location Type", "work_location_placeholder": "Select work model", "remote": "Remote", "onsite": "On-site", "hybrid": "Hybrid", "flexible": "Flexible", "remote_percentage_label": "Remote Percentage (%)", "remote_percentage_placeholder": "e.g. 80", "work_location_notes_label": "Work Location Notes", "work_location_notes_placeholder": "Additional information about the work model..."}, "application": {"title": "Application", "generate_ai_application": "Generate AI Application", "generate_ai_application_short": "AI Application", "generating": "Generating", "application_date_label": "Application Date", "status_label": "Status", "application_text_label": "Application Text", "application_text_placeholder": "Your application for this project...", "notes_label": "Notes", "notes_placeholder": "Additional notes about this project...", "error_no_description": "Please add a project description.", "application_generated_toast": "Application Generated", "application_generated_message": "A professional application was created.", "no_application_received": "No application received"}, "ai_analysis": {"title": "AI Project Analysis", "project_text_label": "Insert Project Posting", "project_text_placeholder": "Paste the complete project posting here. The AI will automatically extract all important information...", "analyze_button": "Analyze Project", "analyzing": "Analyzing", "error_no_text": "Please insert a project posting.", "similar_contact_toast": "Similar Contact Found", "similar_contact_message": "A similar contact \"{{name}}\" already exists.", "contact_data_recognized_toast": "Contact Data Recognized", "contact_data_recognized_message": "Contact data was automatically filled. The contact will be created when saving.", "analysis_completed_toast": "Analysis Completed", "analysis_completed_message": "Project data was automatically filled."}}, "form": {"buttons": {"save_project": "Save Project", "cancel": "Cancel"}}}