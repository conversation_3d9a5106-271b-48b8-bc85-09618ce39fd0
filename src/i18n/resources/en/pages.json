{"dashboard": {"title": "Dashboard", "subtitle": "Project overview and key metrics", "empty_state": "No projects available yet", "worked_today": "Worked today", "recent_applications": "Recent applications", "recent_projects": "Recent projects", "recent_activities": "Recent activities", "no_applications": "No applications available yet", "no_projects": "No projects available yet", "no_activities": "No activities available yet", "show_all": "Show all", "show_all_short": "All", "kpi_cards": {"running_applications": "Running Applications", "active_projects": "Active Projects", "worked_today": "Worked Today", "followups_count": "Follow-ups Count", "actively_running": "Actively Running", "in_processing": "In Progress", "hours_today": "Hours Today", "scheduled": "Scheduled"}, "quick_actions": {"title": "Quick Actions", "new_application": "New Application", "new_project": "New Project", "start_timer": "Start Timer", "calendar": "Calendar"}, "stats": {"total_projects": "Total Projects", "all_applications": "All Applications", "success_rate": "Success Rate", "positive_responses": "Positive Responses", "in_progress": "In Progress", "in_processing": "In Processing", "offers": "Offers", "successful_projects": "Successful Projects"}}, "applications": {"title": "Applications", "subtitle": "Manage your project applications", "create_new": "New Application", "create_new_desc": "Create a new project application", "edit_title": "Edit Application", "edit_desc": "Edit an existing application", "details_title": "Application Details", "details_desc": "Detailed view of an application", "loading": "Loading Applications...", "new_project_button": "New Project", "all_status": "All Status", "no_projects_found": "No projects found", "no_projects_yet": "No projects yet", "try_different_search": "Try different search terms or filters.", "create_first_project": "Create your first project to get started.", "create_new_project": "Create New Project", "project_details": "Project Details", "contact": "Contact", "budget": "Budget", "description": "Description", "required_skills": "Required Skills"}, "projects": {"title": "Active Projects", "subtitle": "Manage your active projects", "create_new": "New Project", "create_new_desc": "Create a new active project", "create_new_title": "Create New Project", "create_new_subtitle": "Create a new active project for your project management", "edit_title": "Edit Project", "edit_active": "Edit active project", "edit_desc": "Edit an active project details", "labels": {"start_date": "Start Date", "planned_end_date": "Planned End Date"}, "details_title": "Project Details", "details_desc": "Detailed view of an active project", "timer_title": "Time Tracking", "timer_subtitle": "Manage your working time and start timers for your projects", "reports_title": "Reports", "reports_subtitle": "Detailed reports and analyses of your projects", "import_section": {"title": "Projects ready to import", "description": "These projects have 'Offer received' status and can be imported as active projects.", "no_client_name": "No client name", "importing": "Importing...", "import": "Import"}, "empty_state": {"title": "No active projects", "description": "Import a project from your applications or create a new active project.", "new_project": "New Project"}, "card": {"view_details": "View Details", "edit": "Edit", "delete": "Delete", "type": "Type:", "hourly_rate": "Hourly Rate:", "start": "Start:", "planned_end": "Planned End:", "total_hours": "Total Hours:", "time_tracking": "Time Tracking", "timer_running": "Timer running", "hours_total": "{{hours}}h total", "current_session": "Current Session: {{time}}"}, "stats": {"today": "Today", "this_week": "This Week", "active_projects": "Active Projects", "timer_status": "Timer Status", "stopped": "Stopped"}, "delete_dialog": {"title": "Delete Project", "description": "Are you sure you want to delete this project? This action cannot be undone and all associated time entries and notes will also be deleted.", "cancel": "Cancel", "deleting": "Deleting...", "delete": "Delete"}}, "calendar": {"title": "Calendar", "subtitle": "Events and important dates"}, "contacts": {"title": "Contacts", "subtitle": "Manage your business contacts", "create_new": "New Contact", "create_new_desc": "Create a new contact", "edit_title": "Edit Contact", "edit_desc": "Edit an existing contact", "details_title": "Contact Details", "details_desc": "Detailed view of a contact"}, "settings": {"title": "Settings", "subtitle": "Profile and application settings"}, "auth": {"login_title": "<PERSON><PERSON>", "register_title": "Registration", "access_code_title": "Enter Access Code"}, "loading": {"project": "Loading project", "applications": "Loading Applications...", "general": "Loading...", "saving": "Saving..."}, "not_found": {"project_title": "Project not found", "project_description": "The requested project does not exist", "project_message": "The project could not be found.", "contact_title": "Contact not found", "contact_description": "The requested contact does not exist", "contact_message": "The contact to edit does not exist or has been deleted."}, "contact": {"create_title": "New contact", "edit_title": "Edit contact", "create_description": "Add a new contact to your database", "edit_description": "Edit information for {{name}}", "edit_description_default": "Edit information for this contact", "loading_contact": "Loading contact data...", "note": "Note: At least a name, email address, or company is required."}, "statistics": {"title": "Statistics & Analytics", "subtitle": "Detailed analysis of your application activities", "total_projects": "Total: {{count}} Projects", "total_projects_count": "Total Projects", "application_rate": "Application Rate", "success_rate": "Success Rate", "interview_rate": "Interview Rate", "last_30_days": "Last 30 Days", "new_projects": "New Projects", "applications": "Applications", "activity_rate": "Activity Rate", "daily_average": "Daily Avg", "conversion": "Conversion", "offers": "Offers", "running": "Running", "rejections": "Rejections", "rejection_rate": "Rate", "status_distribution": "Status Distribution", "status_breakdown_desc": "Detailed breakdown of all application statuses", "performance_metrics": "Performance Metrics", "pipeline_analysis": "Pipeline Analysis", "rejection_rate_label": "Rejection Rate", "open_applications": "Open Applications", "completed": "Completed"}, "application_details": {"loading_application": "Loading application...", "application_not_found": "Application not found", "application_not_found_description": "The requested application does not exist or has been deleted.", "back_to_overview": "Back to Overview", "edit": "Edit", "delete": "Delete", "company": "Company", "application": "Application", "start": "Start", "end": "End", "budget": "Budget", "work_location": "Work Location", "remote": "Remote", "onsite": "On-site", "hybrid": "Hybrid ({{percentage}}% Remote)", "flexible": "Flexible", "application_information": "Application Information", "project_description": "Project Description", "application_text": "Application Text", "skills_match_analysis": "Skills & Match Analysis", "required_skills": "Required Skills", "skill_match_analysis": "Skill Match Analysis", "notes": "Notes", "contact_data": "Contact Data", "job_posting": "Job Posting", "application_portal": "Application Portal", "source": "Source: {{source}}", "actions": "Actions", "status_change": "Change Status", "open_calendar": "Open Calendar", "next_possible_status": "Next possible status", "no_further_status_changes": "No further status changes possible", "not_specified": "Not specified"}, "followups": {"title": "Follow-ups", "subtitle": "All scheduled follow-ups and analytics in chronological order"}, "notifications": {"title": "Notifications", "subtitle": "Manage all your notifications and reminders"}, "reports": {"export_menu": {"export": "Export", "as_excel": "Export as Excel", "as_pdf": "Export as PDF"}, "filter": {"title": "Filter", "period": "Period", "project": "Project", "date_range": "Date Range", "from": "From", "to": "To", "today": "Today", "this_week": "This Week", "this_month": "This Month", "custom": "Custom", "all_projects": "All Projects"}, "stats": {"total_hours": "Total Hours", "billable": "Billable", "sessions": "Sessions", "projects": "Projects"}, "tabs": {"overview": "Overview", "overview_short": "Info", "details": "Details", "details_short": "Detail", "by_project": "By Project", "by_project_short": "Project", "by_category": "By Category", "by_category_short": "Category"}, "overview": {"daily_breakdown": "Daily Breakdown", "recent_sessions": "Recent Sessions", "no_data_period": "No data for the selected period", "no_sessions_period": "No sessions in the selected period"}, "details": {"title": "Detailed Time Entries", "description": "All time entries for the selected period with complete details", "no_entries": "No time entries found for the selected period", "total_label": "Total", "billable_label": "Billable"}, "by_project": {"title": "Time by Project", "no_data": "No project data available"}, "by_category": {"title": "Time by Category", "no_data": "No category data available"}, "messages": {"excel_success": "Excel export successful", "excel_success_desc": "The file has been downloaded.", "excel_error": "Excel export error", "excel_error_desc": "Please try again.", "pdf_success": "PDF export successful", "pdf_success_desc": "The file has been downloaded.", "pdf_error": "PDF export error", "pdf_error_desc": "Please try again."}}}