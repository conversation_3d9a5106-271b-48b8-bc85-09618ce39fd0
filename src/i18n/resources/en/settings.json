{"tabs": {"profile": "Profile", "cv": "CV", "security": "Security", "followup": "Follow-up", "data": "Data"}, "profile": {"title": "Profile Information", "description": "This data will be used in automatically generated applications.", "completion": "Profile Completion", "completion_badges": {"profile": "Profile", "availability": "Availability", "cv": "CV"}, "personal_data": "Personal Data", "financial_info": "Financial Information", "availability_info": "Availability", "labels": {"full_name": "Full Name", "professional_email": "Professional Email", "phone": "Phone Number", "website": "Website/Portfolio", "address": "Address", "hourly_rate": "Hourly Rate (EUR, net)", "hourly_rate_suffix": "/hour", "hourly_rate_note": "Used in applications and calculations", "available_from": "Available from", "available_until": "Available until", "hours_per_week": "Hours per Week", "hours_per_week_suffix": "hours/week", "hours_per_week_note": "How many hours can you dedicate to new projects per week?", "availability_notes": "Availability Notes"}, "placeholders": {"full_name": "<PERSON>", "professional_email": "<EMAIL>", "phone": "+49 123 456789", "website": "https://www.example.com", "address": "Sample Street 123\n12345 Sample City\nGermany", "hourly_rate": "85", "availability_notes": "Additional information about your availability, preferred working hours, vacation plans, etc."}, "buttons": {"save": "Save Settings", "saving": "Saving..."}}, "cv": {"title": "CV/Resume", "description": "Upload your CV for personalized AI applications.", "upload_success": "CV successfully uploaded", "security_badges": {"secure": "Securely stored", "ai_ready": "AI-ready"}, "no_cv_title": "No CV uploaded", "no_cv_message": "Upload your CV to generate personalized AI applications.", "replace_cv": "Replace CV", "upload_cv": "Upload CV", "uploading": "Uploading...", "upload_progress": "Please wait while your CV is being uploaded...", "click_to_replace": "Click or drag a file here to replace your CV", "click_to_upload": "Click or drag a PDF file here", "drag_drop_hint": "Only PDF files are accepted", "view": "View", "delete": "Delete", "delete_success_title": "CV successfully deleted", "delete_success_message": "Your CV has been permanently removed.", "delete_error_title": "CV deletion error", "delete_error_message": "An unknown error occurred.", "requirements": {"pdf_only": "Only PDF files are accepted", "auto_use": "Automatically used in application generation", "secure": "Securely encrypted and only accessible to you"}, "errors": {"invalid_file_type": "Invalid file type", "pdf_required": "Please select a PDF file.", "load_error": "Error loading", "load_failed": "The CV file could not be loaded.", "open_error": "Error opening", "open_failed": "The CV file could not be opened.", "upload_success_title": "CV successfully uploaded", "upload_success_message": "Your CV is now available for applications.", "upload_error_title": "CV upload error", "upload_error_message": "An unknown error occurred."}, "auth_error": "Not authenticated"}, "security": {"title": "Security Settings", "description": "Manage your password and other security settings.", "change_password": "Change Password", "labels": {"current_password": "Current Password", "new_password": "New Password", "confirm_password": "Confirm Password"}, "placeholders": {"current_password": "Enter your current password", "new_password": "Enter your new password", "confirm_password": "Confirm your new password"}, "notes": {"current_password": "For security, we need your current password.", "new_password": "The password must be at least 6 characters long."}, "buttons": {"change_password": "Change Password", "changing": "Changing password..."}, "success": {"title": "Password changed", "message": "Your password has been successfully changed."}, "errors": {"title": "Error changing password", "message": "An unknown error occurred."}}, "data": {"title": "Data Management", "description": "Export and import your projects and settings in various formats.", "export": {"title": "Export Data", "pdf": {"title": "PDF Export", "description": "Formatted report with all project details and statistics.", "button": "Create PDF"}, "excel": {"title": "Excel Export", "description": "Structured table for further editing and analysis.", "button": "Create Excel"}, "json": {"title": "JSON Export", "description": "Complete data for backup and import into other tools.", "button": "Create JSON"}}, "import": {"title": "Import Data", "warning_title": "Important Notice", "warning_message": "Importing will overwrite existing data. Create a backup first!", "button": "Select JSON File", "importing": "Importing...", "note": "Select a previously exported JSON file to import your data.", "errors": {"invalid_file_type": "Invalid file type", "json_required": "Please select a JSON file.", "file_too_large": "File too large", "size_limit": "Import files may be at most 50MB in size. Your file is {{size}}MB."}}}, "profile_picture": {"errors": {"invalid_file_type": "Invalid file type", "format_required": "Please select a JPG, PNG or WebP file.", "file_too_large": "File too large", "size_limit": "The profile picture may be at most 5MB in size.", "upload_success_title": "Profile picture successfully uploaded", "upload_success_message": "Your new profile picture will now be displayed in the application.", "upload_error_title": "Upload error", "upload_error_message": "An unknown error occurred."}, "auth_error": "Not authenticated"}, "hero": {"your_name": "Your Name"}}