{"new_project": {"title": "Neues Projekt hinzufügen", "description": "<PERSON><PERSON><PERSON><PERSON> ein neues Freelance-Projekt", "back": "Zurück", "status_setup": {"title": "Initialen Projekt-Status festlegen", "placeholder_project": "Neues Projekt", "placeholder_company": "<PERSON><PERSON> def<PERSON>n"}, "toasts": {"created_title": "Projekt erstellt", "created_message": "Neues Projekt wurde erfolgreich hinzugefügt.", "error_title": "<PERSON><PERSON>", "error_message": "Projekt konnte nicht erstellt werden.", "status_set_title": "Status gesetzt", "status_set_message": "Initialer Projekt-Status wurde festgelegt."}, "validation": {"required_fields_title": "<PERSON><PERSON>", "required_fields_message": "Projektname und Firmenname sind erforderlich.", "contact_created_title": "Kontakt erstellt", "contact_created_message": "Neuer Kontakt \"{{name}}\" wurde erstellt."}}, "components": {"basic_info": {"title": "Projekt-Grunddaten", "project_name_label": "Projektname *", "company_name_label": "Firmenname *", "source_label": "<PERSON><PERSON>", "source_placeholder": "z.B. freelancermap, gulp, hays", "listing_url_label": "Link zur Ausschreibung"}, "contact_selector": {"title": "Kontaktinformationen", "contact_label": "Kontakt", "select_existing": "Vorhandenen Kontakt auswählen", "select_contact_dialog": "Kontakt auswählen", "search_contacts": "Kontakt suchen", "loading_contacts": "Lade Kontakte...", "no_matching_contacts": "<PERSON><PERSON> passenden Kontakte gefunden", "no_contacts": "<PERSON><PERSON> vorhanden", "unnamed_contact": "Unbenannter Kontakt", "remove_contact": "Kontakt entfernen", "selected_contact": "Ausgewählter Kontakt:", "contact_person_label": "Kontaktperson", "contact_person_placeholder": "Name der Kontaktperson", "contact_company_label": "Kontakt-Firma", "contact_company_placeholder": "Firma des Kontakts", "contact_selected_toast": "Kontakt ausgewählt", "contact_selected_message": "Kontakt \"{{name}}\" wurde ausgewählt."}, "details": {"title": "Projekt-Details", "project_start_label": "Projektstart", "project_end_label": "Projektende", "budget_label": "Stundensatz", "budget_placeholder": "z.B. 85€/h oder 80-90€/h", "project_description_label": "Projektbeschreibung", "required_skills_label": "Benötigte Skills", "skill_placeholder": "Skill hinzuf<PERSON>gen..."}, "work_location": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "work_location_type_label": "Arbeitsort-Typ", "work_location_placeholder": "Arbeitsmodell auswählen", "remote": "Remote", "onsite": "<PERSON><PERSON>", "hybrid": "Hybrid", "flexible": "Flexibel", "remote_percentage_label": "Remote-Anteil (%)", "remote_percentage_placeholder": "z.B. 80", "work_location_notes_label": "Arbeitsort-Notizen", "work_location_notes_placeholder": "Zusätzliche Informationen zum Arbeitsmodell..."}, "application": {"title": "Bewerbung", "generate_ai_application": "KI-Bewerbung generieren", "generate_ai_application_short": "KI-Bewerbung", "generating": "Generiere", "application_date_label": "Bewerbungsdatum", "status_label": "Status", "application_text_label": "Bewerbungstext", "application_text_placeholder": "Deine Bewerbung für dieses Projekt...", "notes_label": "Notizen", "notes_placeholder": "Zusätzliche Notizen zu diesem Projekt...", "error_no_description": "Bitte füge eine Projektbeschreibung hinzu.", "application_generated_toast": "Bewerbung generiert", "application_generated_message": "Eine professionelle Bewerbung wurde erstellt.", "no_application_received": "<PERSON>ine Bewerbung erhalten"}, "ai_analysis": {"title": "KI-Projektanalyse", "project_text_label": "Projektausschreibung einfügen", "project_text_placeholder": "Füge hier die komplette Projektausschreibung ein. Die KI wird automatisch alle wichtigen Informationen extrahieren...", "analyze_button": "Projekt analysieren", "analyzing": "<PERSON><PERSON><PERSON><PERSON>", "error_no_text": "Bitte füge eine Projektausschreibung ein.", "similar_contact_toast": "Ähnlicher Kontakt gefunden", "similar_contact_message": "Ein ähnlicher Kontakt \"{{name}}\" existiert bereits.", "contact_data_recognized_toast": "Kontaktdaten erkannt", "contact_data_recognized_message": "Kontaktdaten wurden automatisch ausgefüllt. Beim Speichern wird der Kontakt erstellt.", "analysis_completed_toast": "Analy<PERSON> ab<PERSON><PERSON>", "analysis_completed_message": "Projektdaten wurden automatisch ausgefüllt."}}, "form": {"buttons": {"save_project": "Projekt speichern", "cancel": "Abbrechen"}}}