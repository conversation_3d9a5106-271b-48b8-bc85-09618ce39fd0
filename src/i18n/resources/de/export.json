{"title": "Export", "as_pdf": "Als PDF exportieren", "as_excel": "Als Excel exportieren", "json_backup": "JSON Backup", "export_all": "Alle Daten exportieren", "export_filtered": "Gefilterte Daten exportieren", "export_success": "Export erfolgreich", "export_failed": "Export fehlgeschlagen", "import": {"title": "Import", "select_file": "<PERSON>i ausw<PERSON>hlen", "drag_drop": "<PERSON><PERSON> hierher ziehen oder klicken zum Auswählen", "supported_formats": "Unterstützte Formate: JSON", "max_size": "Maximale Dateigröße: 50MB", "duplicates_found": "Duplikate gefunden", "import_success": "Import er<PERSON><PERSON>g<PERSON>ich", "import_failed": "Import fehlgeschlagen", "processing": "Verarbeite...", "validation_failed": "Validierung fehlgeschlagen"}, "headers": {"project_title": "Projekttitel", "client_name": "Client Name", "status": "Status", "priority": "Priorität", "project_type": "Projekttyp", "start_date": "Startdatum", "end_date": "Enddatum", "hourly_rate": "Stundensatz", "estimated_hours": "Geschätzte Stunden", "description": "Beschreibung", "created_at": "Erstellt am", "updated_at": "Aktualisiert am"}, "progress": {"preparing": "Vorbereitung...", "processing": "Verarbeitung...", "finalizing": "Abschluss...", "completed": "Abgeschlossen"}}