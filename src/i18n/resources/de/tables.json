{"headers": {"project": "Projekt", "client": "Client", "status": "Status", "priority": "Priorität", "date": "Datum", "actions": "Aktionen", "title": "Titel", "type": "<PERSON><PERSON>", "created": "<PERSON><PERSON><PERSON><PERSON>", "updated": "<PERSON>ktual<PERSON><PERSON>", "deadline": "Deadline", "progress": "Fort<PERSON><PERSON>t", "name": "Name", "email": "E-Mail", "phone": "Telefon", "company": "Unternehmen", "contact": "Kontakt", "time": "Zeit", "description": "Beschreibung", "activity": "Aktivität", "user": "<PERSON><PERSON><PERSON>"}, "states": {"loading": "Lade...", "empty": "<PERSON><PERSON> ve<PERSON>ü<PERSON>", "no_results": "<PERSON><PERSON> gefunden", "error": "Fehler beim Laden der Daten"}, "pagination": {"previous": "<PERSON><PERSON><PERSON><PERSON>", "next": "Nächste", "page": "Seite", "of": "von", "items_per_page": "Einträge pro Seite", "showing": "<PERSON><PERSON><PERSON>", "to": "bis", "entries": "Einträge"}, "sorting": {"sort_by": "Sortieren nach", "ascending": "Aufsteigend", "descending": "Absteigend", "unsorted": "<PERSON><PERSON><PERSON><PERSON>"}, "filters": {"filter": "Filter", "clear_filters": "<PERSON><PERSON>", "apply_filters": "<PERSON><PERSON> anwenden", "search": "Suchen...", "date_range": "Datumsbereich", "from": "<PERSON>", "to": "Bis"}}