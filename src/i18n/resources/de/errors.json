{"validation": {"required": "<PERSON><PERSON> ist erford<PERSON>lich", "email_invalid": "Ungültige E-Mail-Adresse", "min_length": "Mindestens {{count}} <PERSON><PERSON><PERSON>", "max_length": "Maximal {{count}} <PERSON><PERSON><PERSON>", "date_invalid": "Ungültiges Datum", "number_invalid": "Ungültige Zahl", "phone_invalid": "Ungültige Telefonnummer", "passwords_no_match": "Passwörter stimmen nicht überein", "passwords_no_match_desc": "Bitte überprüfen Sie Ihre Passwort-Eingabe.", "password_too_short": "Passwort zu kurz", "password_min_length": "Das Passwort muss mindestens 6 <PERSON><PERSON>chen lang sein.", "contact_min_required": "<PERSON>te geben Si<PERSON> mindestens einen Namen, eine E-Mail oder ein Unternehmen an.", "title_client_required": "Titel und Client Name sind erforderlich."}, "api": {"network_error": "Netzwerkfehler", "unauthorized": "Nicht autorisiert", "forbidden": "<PERSON><PERSON><PERSON> verweigert", "not_found": "Nicht gefunden", "server_error": "<PERSON><PERSON><PERSON>", "invalid_code": "Ungültiger Code", "timeout": "Zeitüberschreitung"}, "auth": {"invalid_credentials": "Ungültige Anmeldedaten", "password_too_weak": "Passwort zu schwach", "email_already_exists": "E-Mail bereits registriert", "login_failed": "Anmeldung fehlgeschlagen"}, "project": {"save_failed": "Projekt konnte nicht gespeichert werden", "create_failed_desc": "Projekt konnte nicht erstellt werden.", "delete_failed": "Projekt konnte nicht gelöscht werden", "load_failed": "Projekt konnte nicht geladen werden"}, "communication": {"notes_required": "Notizen sind erforderlich", "notes_too_short": "Notizen müssen mindestens {{count}} <PERSON><PERSON><PERSON> lang sein", "notes_too_long": "Notizen können nicht länger als {{count}} <PERSON><PERSON><PERSON> sein", "subject_too_long": "<PERSON><PERSON><PERSON> kann nicht länger als {{count}} <PERSON><PERSON><PERSON> sein", "duration_invalid": "Dauer muss eine positive <PERSON><PERSON> sein", "duration_too_large": "Dauer kann nicht länger als {{count}} Minuten (24 Stunden) sein", "communication_type_required": "Kommunikationsart ist erforderlich", "communication_type_invalid": "Ungültige Kommunikationsart. Muss eine von: {{types}} sein", "date_required": "{{field}} ist erforderlich", "date_invalid_format": "Ungültiges {{field}} Format", "date_too_far_future": "{{field}} kann nicht mehr als 1 Jahr in der Zukunft liegen", "date_too_far_past": "{{field}} kann nicht mehr als 10 Jahre in der Vergangenheit liegen", "search_text_required": "Suchtext ist erforderlich", "search_text_too_short": "Suchtext muss mindestens {{count}} <PERSON><PERSON><PERSON> lang sein", "search_text_too_long": "Suchtext kann nicht länger als {{count}} <PERSON><PERSON><PERSON> sein", "uuid_required": "{{field}} ist erforderlich", "uuid_invalid_format": "Ungültiges {{field}} Format", "invalid_type": "{{field}} muss vom Typ {{type}} sein", "limit_too_large": "Limit kann nicht größer als {{count}} sein", "ids_empty_array": "Mindestens eine Kommunikations-ID ist erforderlich", "ids_too_many": "Kann nicht mehr als {{count}} Kommunikationen gleichzeitig verarbeiten", "invalid_id_at_index": "Ungültige ID bei Index {{index}}: {{message}}"}, "security": {"sql_injection": "Potentielle SQL-Injection in {{field}} erkannt", "xss_attack": "Potentielle XSS-Attacke in {{field}} erkannt", "command_injection": "Potentielle Command-Injection in {{field}} erkannt", "path_traversal": "Potentielle Path-Traversal in {{field}} erkannt", "excessive_repetition": "Übermäßige Zeichenwiederholung in {{field}} erkannt", "content_not_allowed": "Eingabe enthält nicht erlaubte Inhalte"}, "rate_limit": {"exceeded": "Rate Limit erreicht", "burst_exceeded": "Burst-Limit überschritten. Bitte warten Sie vor der nächsten Anfrage.", "tier_exceeded": "{{tier}} Tier-Limit überschritten ({{max}} Anfragen pro Minute).", "hourly_exceeded": "Stündliches Sicherheitslimit überschritten ({{max}} Anfragen pro Stunde).", "daily_exceeded": "Tägliches Sicherheitslimit überschritten ({{max}} Anfragen pro Tag).", "try_again_at": "Versuchen Sie es wieder um {{time}}. Verbleibende Anfragen: {{remaining}}"}, "ai": {"summary_failed": "AI-Zusammenfassung fehlgeschlagen", "user_not_authenticated": "Benutzer nicht authentifiziert", "processing_error": "Fehler bei der AI-Verarbeitung", "unknown_error": "Unbekannter Fehler bei der AI-Zusammenfassung", "validation_error": "Unbekannter Validierungsfehler"}, "general": {"unexpected_error": "Ein unerwarteter Fehler ist aufgetreten", "try_again": "Bitte versuchen Sie es erneut", "contact_support": "Support kontaktieren"}}