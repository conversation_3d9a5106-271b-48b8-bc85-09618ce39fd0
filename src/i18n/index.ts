import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';
import LanguageDetector from 'i18next-browser-languagedetector';

// Import German translations
import commonDe from './resources/de/common.json';
import formsDe from './resources/de/forms.json';
import pagesDe from './resources/de/pages.json';
import statusDe from './resources/de/status.json';
import errorsDe from './resources/de/errors.json';
import calendarDe from './resources/de/calendar.json';
import exportDe from './resources/de/export.json';
import timerDe from './resources/de/timer.json';
import followupDe from './resources/de/followup.json';
import tablesDe from './resources/de/tables.json';
import settingsDe from './resources/de/settings.json';
import contactsDe from './resources/de/contacts.json';
import applicationsDe from './resources/de/applications.json';

// Import English translations
import commonEn from './resources/en/common.json';
import formsEn from './resources/en/forms.json';
import pagesEn from './resources/en/pages.json';
import statusEn from './resources/en/status.json';
import errorsEn from './resources/en/errors.json';
import calendarEn from './resources/en/calendar.json';
import exportEn from './resources/en/export.json';
import timerEn from './resources/en/timer.json';
import followupEn from './resources/en/followup.json';
import tablesEn from './resources/en/tables.json';
import settingsEn from './resources/en/settings.json';
import contactsEn from './resources/en/contacts.json';
import applicationsEn from './resources/en/applications.json';

// Configure i18n
i18n
  .use(LanguageDetector)
  .use(initReactI18next)
  .init({
    // Fallback language
    fallbackLng: 'de',
    
    // Debug mode (disable in production)
    debug: import.meta.env.DEV,

    // Default namespace
    defaultNS: 'common',
    
    // Namespaces to load
    ns: ['common', 'forms', 'pages', 'status', 'errors', 'calendar', 'export', 'timer', 'followup', 'tables', 'settings', 'contacts', 'applications'],

    // Language resources
    resources: {
      de: {
        common: commonDe,
        forms: formsDe,
        pages: pagesDe,
        status: statusDe,
        errors: errorsDe,
        calendar: calendarDe,
        export: exportDe,
        timer: timerDe,
        followup: followupDe,
        tables: tablesDe,
        settings: settingsDe,
        contacts: contactsDe,
        applications: applicationsDe,
      },
      en: {
        common: commonEn,
        forms: formsEn,
        pages: pagesEn,
        status: statusEn,
        errors: errorsEn,
        calendar: calendarEn,
        export: exportEn,
        timer: timerEn,
        followup: followupEn,
        tables: tablesEn,
        settings: settingsEn,
        contacts: contactsEn,
        applications: applicationsEn,
      },
    },

    // Interpolation settings
    interpolation: {
      escapeValue: false, // React already handles XSS protection
    },

    // Language detection settings
    detection: {
      // Storage key for language preference
      lookupLocalStorage: 'freelance-tracker-language',
      
      // Detection order
      order: ['localStorage', 'navigator', 'htmlTag'],
      
      // Cache the detected language
      caches: ['localStorage'],
    },

    // React settings
    react: {
      // Use Suspense for loading translations
      useSuspense: true,
    },
  });

export default i18n;