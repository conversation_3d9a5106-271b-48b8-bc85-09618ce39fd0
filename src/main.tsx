import { StrictMode } from "react";
import { createRoot } from "react-dom/client";
import { Analytics } from "@vercel/analytics/react";
import App from "./App";
import "./index.css";
import { ThemeProvider } from "./components/theme-provider";
import { TranslationProvider } from "./components/i18n/TranslationProvider";
import "./i18n";

createRoot(document.getElementById("root")!).render(
  <StrictMode>
    <TranslationProvider>
      <ThemeProvider defaultTheme="dark" storageKey="freelance-tracker-theme">
        <App />
        <Analytics />
      </ThemeProvider>
    </TranslationProvider>
  </StrictMode>
);
