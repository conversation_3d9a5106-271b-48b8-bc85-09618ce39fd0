import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { toast } from '@/lib/toast';
import { FollowUpService } from '@/services/followUpService';
import { ActivityService } from '@/services/activityService';
import { withFollowUpErrorHandling } from '@/lib/errorHandling';
import { NotificationCleanupService } from '@/services/notificationCleanupService';
import type { 
  ScheduledFollowUp, 
  CreateScheduledFollowUp, 
  UpdateScheduledFollowUp,
  FollowUpNotification
} from '@/types/followup';

const QUERY_KEY = ['followUpSchedule'];
const NOTIFICATIONS_QUERY_KEY = ['followUpNotifications'];

export const useFollowUpSchedule = () => {
  const queryClient = useQueryClient();

  // Helper function to log manual follow-up activity
  const logManualFollowUpActivity = async (data: ScheduledFollowUp): Promise<void> => {
    if (!data?.application || !data.application_id) {
      return;
    }

    try {
      const templateName = data.template?.name || 'Manueller Follow-up';
      const projectName = data.application.project_name || 'Projekt';
      const companyName = data.application.company_name || 'Unbekannte Firma';
      const contactName = data.application.contact?.name || 'Sehr geehrte Damen und Herren';
      const scheduledDate = new Date(data.scheduled_date).toLocaleDateString('de-DE');
      
      const manualContent = `**Template:** ${templateName}
**Geplant für:** ${scheduledDate}
**Kontaktperson:** ${contactName}
**Manuell erstellt:** Ja
**Status:** Geplant`;

      await withFollowUpErrorHandling(
        () => ActivityService.logActivity(
          data.application_id,
          'followup_scheduled',
          `Follow-up "${templateName}" für ${projectName} bei ${companyName} geplant`,
          manualContent
        ),
        'createScheduledFollowUp.logActivity'
      );
    } catch (activityError) {
      console.error('Failed to log follow-up creation activity:', activityError);
      // Activity logging is non-critical - continue with follow-up creation
      // TODO: Add monitoring/alerting for activity logging failures
    }
  };

  // Helper function to invalidate related queries
  const invalidateFollowUpQueries = (): void => {
    queryClient.invalidateQueries({ queryKey: QUERY_KEY });
    queryClient.invalidateQueries({ queryKey: NOTIFICATIONS_QUERY_KEY });
    queryClient.invalidateQueries({ queryKey: ['project-activities'] });
  };

  // Fetch all scheduled follow-ups for the current user
  const {
    data: scheduledFollowUps = [],
    isLoading,
    error
  } = useQuery({
    queryKey: QUERY_KEY,
    queryFn: async (): Promise<ScheduledFollowUp[]> => {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error('User not authenticated');

      return await withFollowUpErrorHandling(
        () => FollowUpService.getScheduledFollowUps(user.id),
        'useFollowUpSchedule.getScheduledFollowUps'
      );
    }
  });

  // Fetch due follow-ups (notifications)
  const {
    data: dueFollowUps = [],
    isLoading: isDueLoading
  } = useQuery({
    queryKey: NOTIFICATIONS_QUERY_KEY,
    queryFn: async (): Promise<FollowUpNotification[]> => {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error('User not authenticated');

      return await withFollowUpErrorHandling(
        () => FollowUpService.getDueFollowUps(user.id),
        'useFollowUpSchedule.getDueFollowUps'
      );
    },
    refetchInterval: 5 * 60 * 1000, // Refresh every 5 minutes
  });

  // Create scheduled follow-up
  const createScheduledFollowUp = useMutation({
    mutationFn: async (scheduleData: CreateScheduledFollowUp): Promise<ScheduledFollowUp> => {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error('User not authenticated');

      const { data, error } = await supabase
        .from('follow_up_schedule')
        .insert({
          ...scheduleData,
          user_id: user.id,
          status: 'scheduled'
        })
        .select(`
          *,
          template:follow_up_templates(*),
          application:project_applications(
            id, 
            project_name, 
            company_name, 
            status,
            contact:contacts(name, email, phone)
          )
        `)
        .single();

      if (error) throw error;

      // Log activity for manually created follow-up
      await logManualFollowUpActivity(data as ScheduledFollowUp);

      return data as ScheduledFollowUp;
    },
    onSuccess: (_, variables) => {
      invalidateFollowUpQueries();
      
      // Cancel auto-suggestion notifications since user planned a follow-up manually
      NotificationCleanupService.cancelFollowUpSuggestions(variables.application_id);
      
      toast.success('Follow-up geplant', 'Follow-up wurde erfolgreich geplant.');
    },
    onError: (error) => {
      console.error('Error creating scheduled follow-up:', error);
      toast.error('Fehler', 'Follow-up konnte nicht geplant werden.');
    }
  });

  // Create follow-up from template
  const createFromTemplate = useMutation({
    mutationFn: async ({ 
      applicationId, 
      templateId, 
      customScheduleDate 
    }: { 
      applicationId: string; 
      templateId: string; 
      customScheduleDate?: string; 
    }): Promise<ScheduledFollowUp> => {
      return await FollowUpService.createFollowUpFromTemplate(
        applicationId, 
        templateId, 
        customScheduleDate
      );
    },
    onSuccess: () => {
      invalidateFollowUpQueries();
      toast.success('Follow-up erstellt', 'Follow-up wurde aus Template erstellt.');
    },
    onError: (error) => {
      console.error('Error creating follow-up from template:', error);
      toast.error('Fehler', 'Follow-up konnte nicht erstellt werden.');
    }
  });

  // Update scheduled follow-up
  const updateScheduledFollowUp = useMutation({
    mutationFn: async (updateData: UpdateScheduledFollowUp): Promise<ScheduledFollowUp> => {
      const { id, ...updates } = updateData;
      
      // Get current follow-up data before updating to check if scheduled_date changed
      const { data: currentFollowUp, error: currentError } = await supabase
        .from('follow_up_schedule')
        .select(`
          *,
          template:follow_up_templates(*),
          application:project_applications(id, project_name, contact_person, company_name, status, application_date, interview_date)
        `)
        .eq('id', id)
        .single();

      if (currentError) throw currentError;

      const { data, error } = await supabase
        .from('follow_up_schedule')
        .update(updates)
        .eq('id', id)
        .select(`
          *,
          template:follow_up_templates(*),
          application:project_applications(id, project_name, company_name, status)
        `)
        .single();

      if (error) throw error;

      // If scheduled_date changed, update the corresponding calendar event
      if (updates.scheduled_date && currentFollowUp && currentFollowUp.scheduled_date !== updates.scheduled_date) {
        try {
          await FollowUpService.updateFollowUpCalendarEvent(
            currentFollowUp.application_id,
            currentFollowUp.scheduled_date,
            updates.scheduled_date,
            currentFollowUp.template,
            currentFollowUp.application
          );
        } catch (calendarError) {
          console.error('Failed to update follow-up calendar event:', calendarError);
          // Don't throw error to avoid breaking follow-up update
        }
      }

      return data as ScheduledFollowUp;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: QUERY_KEY });
      queryClient.invalidateQueries({ queryKey: NOTIFICATIONS_QUERY_KEY });
      toast.success('Follow-up aktualisiert', 'Follow-up wurde erfolgreich aktualisiert.');
    },
    onError: (error) => {
      console.error('Error updating scheduled follow-up:', error);
      toast.error('Fehler', 'Follow-up konnte nicht aktualisiert werden.');
    }
  });

  // Mark follow-up as sent
  const markAsSent = useMutation({
    mutationFn: async ({ 
      followUpId, 
      actualSubject, 
      actualBody 
    }: { 
      followUpId: string; 
      actualSubject: string; 
      actualBody: string; 
    }): Promise<void> => {
      await FollowUpService.markFollowUpAsSent(followUpId, actualSubject, actualBody);
    },
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: QUERY_KEY });
      queryClient.invalidateQueries({ queryKey: NOTIFICATIONS_QUERY_KEY });
      queryClient.invalidateQueries({ queryKey: ['followUpHistory'] }); // Also refresh history
      queryClient.invalidateQueries({ queryKey: ['project-activities'] }); // Refresh activity log
      
      // Cancel all notifications for this specific follow-up
      NotificationCleanupService.cancelFollowUpNotifications(variables.followUpId);
      
      toast.success('Follow-up gesendet', 'Follow-up wurde als gesendet markiert.');
    },
    onError: (error) => {
      console.error('Error marking follow-up as sent:', error);
      toast.error('Fehler', 'Follow-up konnte nicht als gesendet markiert werden.');
    }
  });

  // Delete scheduled follow-up
  const deleteScheduledFollowUp = useMutation({
    mutationFn: async (followUpId: string): Promise<void> => {
      // Use the new service method that includes activity logging
      await FollowUpService.deleteFollowUpWithLogging(followUpId);
    },
    onSuccess: (_, followUpId) => {
      queryClient.invalidateQueries({ queryKey: QUERY_KEY });
      queryClient.invalidateQueries({ queryKey: NOTIFICATIONS_QUERY_KEY });
      queryClient.invalidateQueries({ queryKey: ['project-activities'] }); // Refresh activity log
      
      // Cancel all notifications for this specific follow-up
      NotificationCleanupService.cancelFollowUpNotifications(followUpId);
      
      toast.success('Follow-up gelöscht', 'Follow-up wurde erfolgreich gelöscht.');
    },
    onError: (error) => {
      console.error('Error deleting scheduled follow-up:', error);
      toast.error('Fehler', 'Follow-up konnte nicht gelöscht werden.');
    }
  });

  // Dismiss follow-up (mark as dismissed)
  const dismissFollowUp = useMutation({
    mutationFn: async (followUpId: string): Promise<void> => {
      const { error } = await supabase
        .from('follow_up_schedule')
        .update({ status: 'dismissed' })
        .eq('id', followUpId);

      if (error) throw error;
    },
    onSuccess: (_, followUpId) => {
      queryClient.invalidateQueries({ queryKey: QUERY_KEY });
      queryClient.invalidateQueries({ queryKey: NOTIFICATIONS_QUERY_KEY });
      
      // Cancel all notifications for this specific follow-up
      NotificationCleanupService.cancelFollowUpNotifications(followUpId);
      
      toast.success('Follow-up abgelehnt', 'Follow-up wurde als nicht relevant markiert.');
    },
    onError: (error) => {
      console.error('Error dismissing follow-up:', error);
      toast.error('Fehler', 'Follow-up konnte nicht abgelehnt werden.');
    }
  });

  // Helper functions
  const getScheduledByApplication = (applicationId: string) => 
    scheduledFollowUps.filter(f => f.application_id === applicationId && f.status === 'scheduled');

  const getDueToday = () => {
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const tomorrow = new Date(today);
    tomorrow.setDate(tomorrow.getDate() + 1);

    return scheduledFollowUps.filter(f => {
      const scheduledDate = new Date(f.scheduled_date);
      return f.status === 'scheduled' && 
             scheduledDate >= today && 
             scheduledDate < tomorrow;
    });
  };

  const getOverdue = () => {
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    return scheduledFollowUps.filter(f => {
      const scheduledDate = new Date(f.scheduled_date);
      return f.status === 'scheduled' && scheduledDate < today;
    });
  };

  return {
    // Data
    scheduledFollowUps,
    dueFollowUps,
    dueToday: getDueToday(),
    overdue: getOverdue(),
    
    // Loading states
    isLoading,
    isDueLoading,
    isCreating: createScheduledFollowUp.isPending,
    isCreatingFromTemplate: createFromTemplate.isPending,
    isUpdating: updateScheduledFollowUp.isPending,
    isMarking: markAsSent.isPending,
    isDeleting: deleteScheduledFollowUp.isPending,
    isDismissing: dismissFollowUp.isPending,
    
    // Actions
    createScheduledFollowUp: createScheduledFollowUp.mutate,
    createFromTemplate: createFromTemplate.mutate,
    createFromTemplateAsync: createFromTemplate.mutateAsync,
    updateScheduledFollowUp: updateScheduledFollowUp.mutate,
    markAsSent: markAsSent.mutate,
    deleteScheduledFollowUp: deleteScheduledFollowUp.mutate,
    dismissFollowUp: dismissFollowUp.mutate,
    
    // Helpers
    getScheduledByApplication,
    
    // Error
    error
  };
};