import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { ProjectActivity } from '@/types/applications';

export const useProjectActivities = (projectId?: string) => {
  return useQuery({
    queryKey: projectId ? ['project-activities', projectId] : ['project-activities', 'all'],
    queryFn: async (): Promise<ProjectActivity[]> => {
      let query = supabase
        .from('project_activities')
        .select('*');
      
      if (projectId) {
        query = query.eq('project_id', projectId);
      }
      
      const { data, error } = await query.order('created_at', { ascending: false });

      if (error) {
        throw new Error(`Failed to fetch project activities: ${error.message}`);
      }

      return data || [];
    },
    staleTime: 30000, // 30 seconds
    refetchOnWindowFocus: true,
  });
};

// Simple status duration calculation - basic implementation
export const calculateStatusDurations = (activities: ProjectActivity[]) => {
  const statusChanges = activities
    .filter(activity => 
      activity.activity_type === 'status_changed' || 
      activity.activity_type === 'application_status_changed' || 
      activity.activity_type === 'project_status_changed'
    )
    .sort((a, b) => new Date(a.created_at).getTime() - new Date(b.created_at).getTime());

  // Simple approach: just count days between consecutive status changes
  const durations: Record<string, number> = {};
  
  for (let i = 0; i < statusChanges.length - 1; i++) {
    const current = statusChanges[i];
    const next = statusChanges[i + 1];
    
    const startTime = new Date(current.created_at).getTime();
    const endTime = new Date(next.created_at).getTime();
    const durationDays = Math.max(1, Math.round((endTime - startTime) / (1000 * 60 * 60 * 24)));
    
    // Extract status from description (e.g., "Status: Noch nicht beworben → Bewerbung gesendet")
    const match = current.description.match(/Status: (.+) →/);
    if (match) {
      const status = match[1];
      durations[status] = (durations[status] || 0) + durationDays;
    }
  }

  return durations;
};

// Utility function to get activity summary
export const getActivitySummary = (activities: ProjectActivity[]) => {
  const summary = {
    totalActivities: activities.length,
    statusChanges: activities.filter(a => 
      a.activity_type === 'status_changed' || 
      a.activity_type === 'application_status_changed' || 
      a.activity_type === 'project_status_changed'
    ).length,
    projectUpdates: activities.filter(a => 
      a.activity_type === 'project_updated' || 
      a.activity_type === 'application_updated'
    ).length,
    contactUpdates: activities.filter(a => a.activity_type === 'contact_updated').length,
    lastActivity: activities[0]?.created_at || null
  };

  return summary;
};