import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { toast } from '@/lib/toast';
import { ActivityService } from '@/services/activityService';
import type {
  Project,
  ProjectInsert,
  ProjectUpdate,
  ProjectWithStats,
  ProjectsResponse,
  ProjectFilters,
  ImportableProject,
  PROJECT_STATUS_LABELS
} from '@/types/projects';

export const useProjects = (filters?: ProjectFilters) => {
  const queryClient = useQueryClient();

  // Fetch projects with statistics
  const {
    data: projects = [],
    isLoading,
    error,
    refetch
  } = useQuery({
    queryKey: ['projects', filters],
    queryFn: async (): Promise<ProjectWithStats[]> => {
      // Get current user session with retry logic
      let session = null;
      let retries = 3;

      while (retries > 0 && !session) {
        const { data } = await supabase.auth.getSession();
        session = data.session;

        if (!session) {
          retries--;
          if (retries > 0) {
            await new Promise(resolve => setTimeout(resolve, 100));
          }
        }
      }

      if (!session?.user) {
        return [];
      }

      // First get projects with their time entries
      let query = supabase
        .from('projects')
        .select(`
          *,
          time_entries(
            id,
            duration_minutes,
            is_running,
            billable,
            start_time
          )
        `);

      // Apply filters
      if (filters?.status && filters.status.length > 0) {
        query = query.in('status', filters.status);
      }

      if (filters?.priority && filters.priority.length > 0) {
        query = query.in('priority', filters.priority);
      }

      if (filters?.project_type && filters.project_type.length > 0) {
        query = query.in('project_type', filters.project_type);
      }

      if (filters?.client_name) {
        query = query.ilike('client_name', `%${filters.client_name}%`);
      }

      if (filters?.date_range) {
        if (filters.date_range.start) {
          query = query.gte('start_date', filters.date_range.start);
        }
        if (filters.date_range.end) {
          query = query.lte('planned_end_date', filters.date_range.end);
        }
      }

      const { data, error } = await query
        .eq('user_id', session.user.id)
        .order('created_at', { ascending: false });

      console.log('Projects query result:', { data, error, dataLength: data?.length });

      if (error) {
        console.error('Error fetching projects:', error);
        throw error;
      }

      // Calculate statistics for each project
      const projectsWithStats: ProjectWithStats[] = (data || []).map(project => {
        const timeEntries = project.time_entries || [];
        
        const totalHours = timeEntries.reduce((sum: number, entry: any) => {
          return sum + (entry.duration_minutes || 0);
        }, 0) / 60;

        const billableHours = timeEntries
          .filter((entry: any) => entry.billable)
          .reduce((sum: number, entry: any) => {
            return sum + (entry.duration_minutes || 0);
          }, 0) / 60;

        const runningTimer = timeEntries.find((entry: any) => entry.is_running);
        const lastEntry = timeEntries[0]; // Most recent entry

        return {
          ...project,
          total_hours: totalHours,
          total_billable_hours: billableHours,
          total_entries: timeEntries.length,
          last_activity: lastEntry?.start_time,
          is_timer_running: !!runningTimer,
          current_timer_id: runningTimer?.id,
          progress_percentage: project.estimated_hours 
            ? Math.min(100, (totalHours / project.estimated_hours) * 100)
            : undefined
        };
      });

      return projectsWithStats;
    },
    staleTime: 2 * 60 * 1000, // 2 minutes
    refetchInterval: 30 * 1000, // Refetch every 30 seconds for timer updates
  });

  // Create new project
  const createProject = useMutation({
    mutationFn: async (data: ProjectInsert): Promise<Project> => {
      const { data: { session } } = await supabase.auth.getSession();
      
      if (!session?.user) {
        throw new Error('No authenticated user');
      }

      const { data: project, error } = await supabase
        .from('projects') // Updated table name
        .insert([{
          ...data,
          user_id: session.user.id
        }])
        .select()
        .single();

      if (error) {
        console.error('Error creating project:', error);
        throw error;
      }

      return project;
    },
    onSuccess: async (newProject) => {
      queryClient.invalidateQueries({ queryKey: ['projects'] });
      toast.success('Projekt erstellt', `"${newProject.title}" wurde erfolgreich erstellt.`);
      
      // Log project creation activity
      await ActivityService.logActivity(
        newProject.id,
        'project_created',
        `Projekt "${newProject.title}" erstellt`
      );
    },
    onError: (error: Error) => {
      console.error('Failed to create project:', error);
      toast.error('Fehler beim Erstellen', 'Das Projekt konnte nicht erstellt werden.');
    }
  });

  // Update project
  const updateProject = useMutation({
    mutationFn: async ({ id, updates }: { id: string; updates: ProjectUpdate }): Promise<Project> => {
      // Get current project data to compare changes
      const { data: currentProject } = await supabase
        .from('projects')
        .select('*')
        .eq('id', id)
        .single();

      const { data: project, error } = await supabase
        .from('projects') // Updated table name
        .update(updates)
        .eq('id', id)
        .select()
        .single();

      if (error) {
        console.error('Error updating project:', error);
        throw error;
      }

      // Log specific changes
      if (currentProject && project) {
        // Status change
        if (currentProject.status !== project.status) {
          await ActivityService.logActivity(
            project.id,
            'project_status_changed',
            `Status geändert von "${PROJECT_STATUS_LABELS[currentProject.status]}" zu "${PROJECT_STATUS_LABELS[project.status]}"`
          );
        }
        
        // General project update for other changes
        const hasOtherChanges = Object.keys(updates).some(key => 
          key !== 'status' && currentProject[key as keyof Project] !== project[key as keyof Project]
        );
        
        if (hasOtherChanges) {
          await ActivityService.logActivity(
            project.id,
            'project_updated',
            `Projekt "${project.title}" aktualisiert`
          );
        }
      }

      return project;
    },
    onSuccess: (updatedProject) => {
      queryClient.invalidateQueries({ queryKey: ['projects'] });
      queryClient.invalidateQueries({ queryKey: ['project', updatedProject.id] });
      toast.success('Projekt aktualisiert', `"${updatedProject.title}" wurde erfolgreich aktualisiert.`);
    },
    onError: (error: Error) => {
      console.error('Failed to update project:', error);
      toast.error('Fehler beim Aktualisieren', 'Das Projekt konnte nicht aktualisiert werden.');
    }
  });

  // Delete project
  const deleteProject = useMutation({
    mutationFn: async (id: string): Promise<{ id: string; title: string }> => {
      // Get project title before deleting for activity log
      const { data: project } = await supabase
        .from('projects')
        .select('id, title')
        .eq('id', id)
        .single();

      const { error } = await supabase
        .from('projects') // Updated table name
        .delete()
        .eq('id', id);

      if (error) {
        console.error('Error deleting project:', error);
        throw error;
      }

      return { id, title: project?.title || 'Unbekanntes Projekt' };
    },
    onSuccess: async (deletedProject) => {
      queryClient.invalidateQueries({ queryKey: ['projects'] });
      toast.success('Projekt gelöscht', 'Das Projekt wurde erfolgreich gelöscht.');
      
      // Log project deletion activity
      await ActivityService.logActivity(
        deletedProject.id,
        'project_deleted',
        `Projekt "${deletedProject.title}" gelöscht`
      );
    },
    onError: (error: Error) => {
      console.error('Failed to delete project:', error);
      toast.error('Fehler beim Löschen', 'Das Projekt konnte nicht gelöscht werden.');
    }
  });

  // Import projects from applications
  const importProjects = useMutation({
    mutationFn: async (projectsToImport: ImportableProject[]): Promise<Project[]> => {
      const { data: { session } } = await supabase.auth.getSession();
      
      if (!session?.user) {
        throw new Error('No authenticated user');
      }

      const projectsData = projectsToImport.map(importProject => ({
        title: importProject.title,
        client_name: importProject.client_name,
        description: importProject.description,
        hourly_rate: importProject.hourly_rate,
        estimated_hours: importProject.estimated_hours,
        source_application_id: importProject.source_application_id, // Updated field name
        status: 'starting' as const,
        priority: 'medium' as const,
        project_type: 'development' as const,
        user_id: session.user.id
      }));

      const { data: projects, error } = await supabase
        .from('projects') // Updated table name
        .insert(projectsData)
        .select();

      if (error) {
        console.error('Error importing projects:', error);
        throw error;
      }

      return projects || [];
    },
    onSuccess: async (importedProjects) => {
      queryClient.invalidateQueries({ queryKey: ['projects'] });
      toast.success(
        'Projekte importiert', 
        `${importedProjects.length} Projekt(e) wurden erfolgreich importiert.`
      );
      
      // Log import activity for each project
      for (const project of importedProjects) {
        await ActivityService.logActivity(
          project.id,
          'data_imported',
          `Projekt "${project.title}" aus Bewerbung importiert`
        );
      }
      
      // Also log bulk operation
      if (importedProjects.length > 1) {
        await ActivityService.logActivity(
          importedProjects[0].id, // Use first project ID as reference
          'bulk_operation_completed',
          `${importedProjects.length} Projekte bulk-importiert`
        );
      }
    },
    onError: (error: Error) => {
      console.error('Failed to import projects:', error);
      toast.error('Fehler beim Importieren', 'Die Projekte konnten nicht importiert werden.');
    }
  });

  return {
    data: projects, // Add data field for consistency with other hooks
    projects,
    isLoading,
    error,
    refetch,
    createProject: createProject.mutate,
    updateProject: updateProject.mutate,
    deleteProject: deleteProject.mutate,
    importProjects: importProjects.mutate,
    isCreating: createProject.isPending,
    isUpdating: updateProject.isPending,
    isDeleting: deleteProject.isPending,
    isImporting: importProjects.isPending
  };
};

// Single project hook
export const useProject = (id: string) => {
  return useQuery({
    queryKey: ['project', id],
    queryFn: async (): Promise<Project | null> => {
      if (!id) return null;

      const { data: { session } } = await supabase.auth.getSession();
      
      if (!session?.user) {
        throw new Error('No authenticated user');
      }

      const { data, error } = await supabase
        .from('projects') // Updated table name
        .select('*')
        .eq('id', id)
        .eq('user_id', session.user.id)
        .single();

      if (error) {
        if (error.code === 'PGRST116') {
          return null; // Project not found
        }
        throw error;
      }

      return data;
    },
    enabled: !!id,
    staleTime: 5 * 60 * 1000
  });
};

// Project statistics
export const useProjectStatistics = () => {
  return useQuery({
    queryKey: ['project-statistics'],
    queryFn: async () => {
      const { data: { session } } = await supabase.auth.getSession();
      
      if (!session?.user) {
        throw new Error('No authenticated user');
      }

      // Get basic project counts
      const { data: projects, error: projectsError } = await supabase
        .from('projects') // Updated table name
        .select('status, created_at')
        .eq('user_id', session.user.id);

      if (projectsError) throw projectsError;

      // Get time entries for the last month
      const oneMonthAgo = new Date();
      oneMonthAgo.setMonth(oneMonthAgo.getMonth() - 1);

      const { data: timeEntries, error: timeError } = await supabase
        .from('time_entries')
        .select('duration_minutes, start_time, category, billable')
        .gte('start_time', oneMonthAgo.toISOString())
        .eq('user_id', session.user.id);

      if (timeError) throw timeError;

      const totalProjects = projects?.length || 0;
      const activeProjects = projects?.filter(p => p.status === 'in_progress').length || 0;
      const completedProjects = projects?.filter(p => p.status === 'completed').length || 0;

      // Calculate time statistics
      const now = new Date();
      const weekStart = new Date(now.getFullYear(), now.getMonth(), now.getDate() - now.getDay());
      const monthStart = new Date(now.getFullYear(), now.getMonth(), 1);

      const thisWeekEntries = timeEntries?.filter(entry => 
        new Date(entry.start_time) >= weekStart
      ) || [];

      const thisMonthEntries = timeEntries?.filter(entry => 
        new Date(entry.start_time) >= monthStart
      ) || [];

      const totalHoursThisWeek = thisWeekEntries.reduce((sum, entry) => 
        sum + (entry.duration_minutes || 0), 0
      ) / 60;

      const totalHoursThisMonth = thisMonthEntries.reduce((sum, entry) => 
        sum + (entry.duration_minutes || 0), 0
      ) / 60;

      return {
        total_projects: totalProjects,
        active_projects: activeProjects,
        completed_projects: completedProjects,
        total_hours_this_week: totalHoursThisWeek,
        total_hours_this_month: totalHoursThisMonth,
        average_hours_per_day: totalHoursThisMonth / 30,
        most_productive_day: 'Monday', // Placeholder
        favorite_category: 'development' // Placeholder
      };
    },
    staleTime: 10 * 60 * 1000 // 10 minutes
  });
};

