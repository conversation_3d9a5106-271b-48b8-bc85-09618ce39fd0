import { useTranslation as useI18nTranslation } from 'react-i18next';
import type { TranslationResources } from '@/i18n/types';

/**
 * Custom hook for type-safe translations
 * Extends react-i18next's useTranslation with TypeScript support
 */
export const useTranslation = <T extends keyof TranslationResources = 'common'>(
  namespace?: T
) => {
  const result = useI18nTranslation(namespace);
  
  return result;
};

// Re-export for convenience
export { useTranslation as useT };

// Hook for changing language
export const useLanguage = () => {
  const { i18n } = useI18nTranslation();
  
  return {
    currentLanguage: i18n.language,
    changeLanguage: (language: string) => i18n.changeLanguage(language),
    languages: i18n.languages,
  };
};