import { useMemo } from 'react';
import { useApplications } from './useApplications';
import { useProjects } from './useProjects';
import { ApplicationWithContact } from '@/types/applications';
import { ProjectWithDetails } from '@/types/projects';

interface DashboardStats {
  applications: {
    total: number;
    pending: number;
    sent: number;
    interviews: number;
    offers: number;
    thisWeek: number;
    successRate: number;
  };
  projects: {
    total: number;
    active: number;
    completed: number;
    planning: number;
    onHold: number;
    thisMonth: number;
  };
  financial: {
    totalRevenue: number;
    activeProjectsValue: number;
    averageProjectValue: number;
    monthlyTarget: number;
  };
  productivity: {
    totalHoursLogged: number;
    thisWeekHours: number;
    averageHoursPerDay: number;
    activeTimerProject: string | null;
  };
}

export const useDashboardStats = () => {
  const { data: applications = [], isLoading: applicationsLoading } = useApplications();
  const { data: projects = [], isLoading: projectsLoading } = useProjects();

  const stats = useMemo((): DashboardStats => {
    const now = new Date();
    const oneWeekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
    const oneMonthAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);

    // Application Statistics
    const applicationStats = {
      total: applications.length,
      pending: applications.filter(app => app.status === 'not_applied').length,
      sent: applications.filter(app => ['application_sent', 'inquiry_received'].includes(app.status)).length,
      interviews: applications.filter(app => ['interview_scheduled', 'interview_completed'].includes(app.status)).length,
      offers: applications.filter(app => app.status === 'offer_received').length,
      thisWeek: applications.filter(app => {
        const createdAt = new Date(app.created_at || 0);
        return createdAt >= oneWeekAgo;
      }).length,
      successRate: applications.length > 0 
        ? Math.round((applications.filter(app => ['offer_received', 'project_completed'].includes(app.status)).length / applications.length) * 100)
        : 0
    };

    // Project Statistics
    const projectStats = {
      total: projects.length,
      active: projects.filter(proj => proj.status === 'in_progress').length,
      completed: projects.filter(proj => proj.status === 'completed').length,
      planning: projects.filter(proj => proj.status === 'planning').length,
      onHold: projects.filter(proj => proj.status === 'on_hold').length,
      thisMonth: projects.filter(proj => {
        const createdAt = new Date(proj.created_at || 0);
        return createdAt >= oneMonthAgo;
      }).length
    };

    // Financial Statistics (placeholder - would need actual budget/revenue data)
    const completedProjects = projects.filter(proj => proj.status === 'completed');
    const activeProjects = projects.filter(proj => ['in_progress', 'planning'].includes(proj.status));
    
    const financialStats = {
      totalRevenue: completedProjects.reduce((sum, proj) => {
        // Placeholder: would extract actual revenue from project data
        const estimatedValue = proj.hourly_rate && proj.estimated_hours 
          ? proj.hourly_rate * proj.estimated_hours 
          : 0;
        return sum + estimatedValue;
      }, 0),
      activeProjectsValue: activeProjects.reduce((sum, proj) => {
        const estimatedValue = proj.hourly_rate && proj.estimated_hours 
          ? proj.hourly_rate * proj.estimated_hours 
          : 0;
        return sum + estimatedValue;
      }, 0),
      averageProjectValue: completedProjects.length > 0 
        ? completedProjects.reduce((sum, proj) => {
            const estimatedValue = proj.hourly_rate && proj.estimated_hours 
              ? proj.hourly_rate * proj.estimated_hours 
              : 0;
            return sum + estimatedValue;
          }, 0) / completedProjects.length
        : 0,
      monthlyTarget: 10000 // Placeholder - would come from user settings
    };

    // Productivity Statistics (placeholder - would need time tracking data)
    const productivityStats = {
      totalHoursLogged: 0, // Would come from time entries
      thisWeekHours: 0, // Would come from time entries this week
      averageHoursPerDay: 0, // Would be calculated from time entries
      activeTimerProject: null // Would come from active timer state
    };

    return {
      applications: applicationStats,
      projects: projectStats,
      financial: financialStats,
      productivity: productivityStats
    };
  }, [applications, projects]);

  const getTrends = useMemo(() => {
    const now = new Date();
    const oneWeekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
    const twoWeeksAgo = new Date(now.getTime() - 14 * 24 * 60 * 60 * 1000);

    // Calculate weekly trends
    const thisWeekApplications = applications.filter(app => {
      const createdAt = new Date(app.created_at || 0);
      return createdAt >= oneWeekAgo;
    }).length;

    const lastWeekApplications = applications.filter(app => {
      const createdAt = new Date(app.created_at || 0);
      return createdAt >= twoWeeksAgo && createdAt < oneWeekAgo;
    }).length;

    const applicationTrend = lastWeekApplications > 0 
      ? Math.round(((thisWeekApplications - lastWeekApplications) / lastWeekApplications) * 100)
      : thisWeekApplications > 0 ? 100 : 0;

    const thisWeekProjects = projects.filter(proj => {
      const createdAt = new Date(proj.created_at || 0);
      return createdAt >= oneWeekAgo;
    }).length;

    const lastWeekProjects = projects.filter(proj => {
      const createdAt = new Date(proj.created_at || 0);
      return createdAt >= twoWeeksAgo && createdAt < oneWeekAgo;
    }).length;

    const projectTrend = lastWeekProjects > 0 
      ? Math.round(((thisWeekProjects - lastWeekProjects) / lastWeekProjects) * 100)
      : thisWeekProjects > 0 ? 100 : 0;

    return {
      applications: {
        value: applicationTrend,
        type: applicationTrend > 0 ? 'up' : applicationTrend < 0 ? 'down' : 'neutral',
        label: 'vs. letzte Woche'
      },
      projects: {
        value: projectTrend,
        type: projectTrend > 0 ? 'up' : projectTrend < 0 ? 'down' : 'neutral',
        label: 'vs. letzte Woche'
      }
    };
  }, [applications, projects]);

  return {
    stats,
    trends: getTrends,
    isLoading: applicationsLoading || projectsLoading,
    error: null // Would handle errors from the hooks
  };
};