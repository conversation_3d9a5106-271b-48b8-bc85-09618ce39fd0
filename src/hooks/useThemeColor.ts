import { useEffect } from 'react';
import { useTheme } from '@/components/theme-provider';

export function useThemeColor() {
  const { theme } = useTheme();

  useEffect(() => {
    // Determine the actual theme being used
    let currentTheme = theme;
    if (theme === 'system') {
      currentTheme = window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
    }
    
    // Define colors for each theme
    const colors = {
      light: '#ffffff',
      dark: '#09090b'
    };

    const themeColor = colors[currentTheme as keyof typeof colors] || colors.dark;

    // Update theme-color meta tag
    const themeColorMeta = document.getElementById('theme-color') as HTMLMetaElement;
    if (themeColorMeta) {
      themeColorMeta.content = themeColor;
    }

    // Update msapplication-navbutton-color for Windows Mobile
    const navButtonColorMeta = document.getElementById('msapplication-navbutton-color') as HTMLMetaElement;
    if (navButtonColorMeta) {
      navButtonColorMeta.content = themeColor;
    }

    // Update apple-mobile-web-app-status-bar-style for iOS
    const statusBarStyleMeta = document.querySelector('meta[name="apple-mobile-web-app-status-bar-style"]') as HTMLMetaElement;
    if (statusBarStyleMeta) {
      statusBarStyleMeta.content = currentTheme === 'dark' ? 'black-translucent' : 'default';
    }
  }, [theme]);
}