import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { toast } from '@/lib/toast';
import { withFollowUpErrorHandling } from '@/lib/errorHandling';
import { FollowUpService } from '@/services/followUpService';
import type { 
  FollowUpHistory, 
  CreateFollowUpHistory,
  FollowUpAnalytics
} from '@/types/followup';

const QUERY_KEY = ['followUpHistory'];
const ANALYTICS_QUERY_KEY = ['followUpAnalytics'];

export const useFollowUpHistory = () => {
  const queryClient = useQueryClient();

  // Fetch all follow-up history for the current user
  const {
    data: history = [],
    isLoading,
    error
  } = useQuery({
    queryKey: QUERY_KEY,
    queryFn: async (): Promise<FollowUpHistory[]> => {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error('User not authenticated');

      return await withFollowUpErrorHandling(
        async () => {
          const { data, error } = await supabase
            .from('follow_up_history')
            .select('*')
            .eq('user_id', user.id)
            .order('sent_at', { ascending: false });

          if (error) throw error;
          return data as FollowUpHistory[];
        },
        'useFollowUpHistory.queryFn'
      );
    }
  });

  // Fetch analytics data
  const {
    data: analytics,
    isLoading: isAnalyticsLoading
  } = useQuery({
    queryKey: ANALYTICS_QUERY_KEY,
    queryFn: async (): Promise<FollowUpAnalytics> => {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error('User not authenticated');

      const result = await withFollowUpErrorHandling(
        async () => {
          const analytics = await FollowUpService.getFollowUpAnalytics(user.id);
          return [analytics];
        },
        'useFollowUpHistory.analytics'
      );

      return result[0] || {
        total_sent: 0,
        response_rate: 0,
        avg_response_time_days: 0,
        success_by_template: [],
        success_by_timing: []
      };
    },
    staleTime: 10 * 60 * 1000, // Cache for 10 minutes
  });

  // Create follow-up history entry (manual entry)
  const createHistoryEntry = useMutation({
    mutationFn: async (historyData: CreateFollowUpHistory): Promise<FollowUpHistory> => {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error('User not authenticated');

      const { data, error } = await supabase
        .from('follow_up_history')
        .insert({
          ...historyData,
          user_id: user.id,
          sent_at: new Date().toISOString()
        })
        .select(`
          *,
          template:follow_up_templates(name, trigger_days),
          application:project_applications(id, project_name, contact_person, company_name)
        `)
        .single();

      if (error) throw error;
      return data as FollowUpHistory;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: QUERY_KEY });
      queryClient.invalidateQueries({ queryKey: ANALYTICS_QUERY_KEY });
      toast.success('History-Eintrag erstellt', 'Follow-up wurde der Historie hinzugefügt.');
    },
    onError: (error) => {
      console.error('Error creating history entry:', error);
      toast.error('Fehler', 'History-Eintrag konnte nicht erstellt werden.');
    }
  });

  // Mark follow-up as responded
  const markAsResponded = useMutation({
    mutationFn: async ({ 
      historyId, 
      responseDate 
    }: { 
      historyId: string; 
      responseDate?: string; 
    }): Promise<FollowUpHistory> => {
      const { data, error } = await supabase
        .from('follow_up_history')
        .update({
          response_received: true,
          response_date: responseDate || new Date().toISOString()
        })
        .eq('id', historyId)
        .select(`
          *,
          template:follow_up_templates(name, trigger_days),
          application:project_applications(id, project_name, contact_person, company_name)
        `)
        .single();

      if (error) throw error;
      return data as FollowUpHistory;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: QUERY_KEY });
      queryClient.invalidateQueries({ queryKey: ANALYTICS_QUERY_KEY });
      toast.success('Antwort vermerkt', 'Follow-up wurde als beantwortet markiert.');
    },
    onError: (error) => {
      console.error('Error marking as responded:', error);
      toast.error('Fehler', 'Antwort konnte nicht vermerkt werden.');
    }
  });

  // Remove response marking
  const unmarkResponse = useMutation({
    mutationFn: async (historyId: string): Promise<FollowUpHistory> => {
      const { data, error } = await supabase
        .from('follow_up_history')
        .update({
          response_received: false,
          response_date: null
        })
        .eq('id', historyId)
        .select(`
          *,
          template:follow_up_templates(name, trigger_days),
          application:project_applications(id, project_name, contact_person, company_name)
        `)
        .single();

      if (error) throw error;
      return data as FollowUpHistory;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: QUERY_KEY });
      queryClient.invalidateQueries({ queryKey: ANALYTICS_QUERY_KEY });
      toast.success('Antwort entfernt', 'Antwort-Status wurde zurückgesetzt.');
    },
    onError: (error) => {
      console.error('Error unmarking response:', error);
      toast.error('Fehler', 'Antwort-Status konnte nicht zurückgesetzt werden.');
    }
  });

  // Delete history entry
  const deleteHistoryEntry = useMutation({
    mutationFn: async (historyId: string): Promise<void> => {
      const { error } = await supabase
        .from('follow_up_history')
        .delete()
        .eq('id', historyId);

      if (error) throw error;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: QUERY_KEY });
      queryClient.invalidateQueries({ queryKey: ANALYTICS_QUERY_KEY });
      toast.success('History-Eintrag gelöscht', 'Eintrag wurde aus der Historie entfernt.');
    },
    onError: (error) => {
      console.error('Error deleting history entry:', error);
      toast.error('Fehler', 'History-Eintrag konnte nicht gelöscht werden.');
    }
  });

  // Helper functions
  const getHistoryByApplication = (applicationId: string) => 
    history.filter(h => h.application_id === applicationId);

  const getHistoryByTemplate = (templateId: string) =>
    history.filter(h => h.template_id === templateId);

  const getRecentHistory = (days: number = 30) => {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - days);
    
    return history.filter(h => new Date(h.sent_at) >= cutoffDate);
  };

  const getResponseStats = () => {
    const totalSent = history.length;
    const totalResponses = history.filter(h => h.response_received).length;
    const responseRate = totalSent > 0 ? (totalResponses / totalSent) * 100 : 0;
    
    return {
      totalSent,
      totalResponses,
      responseRate: Math.round(responseRate * 100) / 100
    };
  };

  const getTopPerformingTemplates = (limit: number = 5) => {
    if (!analytics) return [];
    
    return analytics.success_by_template
      .sort((a, b) => b.response_rate - a.response_rate)
      .slice(0, limit);
  };

  return {
    // Data
    history,
    analytics,
    responseStats: getResponseStats(),
    topPerformingTemplates: getTopPerformingTemplates(),
    
    // Loading states
    isLoading,
    isAnalyticsLoading,
    isCreating: createHistoryEntry.isPending,
    isMarking: markAsResponded.isPending,
    isUnmarking: unmarkResponse.isPending,
    isDeleting: deleteHistoryEntry.isPending,
    
    // Actions
    createHistoryEntry: createHistoryEntry.mutate,
    createHistoryEntryAsync: createHistoryEntry.mutateAsync,
    markAsResponded: markAsResponded.mutate,
    unmarkResponse: unmarkResponse.mutate,
    deleteHistoryEntry: deleteHistoryEntry.mutate,
    
    // Helpers
    getHistoryByApplication,
    getHistoryByTemplate,
    getRecentHistory,
    
    // Error
    error
  };
};