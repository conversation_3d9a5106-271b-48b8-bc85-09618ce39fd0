import { useState, useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { ApplicationWithContact, Application, CreateApplicationData, UpdateApplicationData, APPLICATION_STATUS_LABELS } from '@/types/applications';
import { toast } from '@/lib/toast';
import { ActivityService } from '@/services/activityService';
import { createEventForStatusChange, createInterviewEvent, createProjectDateEvents, deleteProjectEvents } from '@/services/calendarService';
import { FollowUpService } from '@/services/followUpService';
import { NotificationCleanupService } from '@/services/notificationCleanupService';
import { ContactService } from '@/services/contactService';

export const useApplications = () => {
  const [data, setData] = useState<ApplicationWithContact[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isCreating, setIsCreating] = useState(false);
  const [isUpdating, setIsUpdating] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);

  // Fetch applications with contacts
  const fetchApplications = async () => {
    try {
      setIsLoading(true);
      
      const { data: applications, error } = await supabase
        .from('project_applications')
        .select('*')
        .order('created_at', { ascending: false });

      if (error) throw error;

      if (!applications || applications.length === 0) {
        setData([]);
        return;
      }

      // Get unique contact IDs
      const contactIds = [...new Set(
        applications
          .map(app => app.contact_id)
          .filter(Boolean)
      )] as string[];

      let contacts = [];
      if (contactIds.length > 0) {
        const { data: contactData, error: contactError } = await supabase
          .from('contacts')
          .select('*')
          .in('id', contactIds);

        if (contactError) throw contactError;
        contacts = contactData || [];
      }

      // Combine applications with their contacts
      const applicationsWithContacts: ApplicationWithContact[] = applications.map(app => ({
        ...app,
        contact: contacts.find(contact => contact.id === app.contact_id) || null
      }));

      setData(applicationsWithContacts);
    } catch (error) {
      console.error('Error fetching applications:', error);
      toast.error('Fehler beim Laden', 'Bewerbungen konnten nicht geladen werden.');
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchApplications();
  }, []);

  // Create new application
  const createApplication = async (applicationData: CreateApplicationData): Promise<Application | null> => {
    try {
      setIsCreating(true);
      
      // Get current user from Supabase (still needed for user_id)
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        throw new Error('Benutzer nicht authentifiziert');
      }

      const { data, error } = await supabase
        .from('project_applications')
        .insert([{
          ...applicationData,
          user_id: user.id,
          status: applicationData.status || 'not_applied'
        }])
        .select()
        .single();

      if (error) throw error;

      const newApplication = data as Application;
      
      // Update local state
      const newApplicationWithContact: ApplicationWithContact = {
        ...newApplication,
        contact: null
      };
      setData(prev => [newApplicationWithContact, ...prev]);

      // Create calendar events for the application
      if (newApplication.project_start_date || newApplication.project_end_date) {
        await createProjectDateEvents(
          newApplication.id,
          newApplication.project_name,
          newApplication.company_name,
          newApplication.project_start_date,
          newApplication.project_end_date,
          'application'
        );
      }

      // Log activity
      await ActivityService.logActivity(
        newApplication.id,
        'application_created',
        `Bewerbung "${newApplication.project_name}" wurde erstellt`
      );

      // Update contact statistics if contact is linked
      if (newApplication.contact_id) {
        await ContactService.updateContactStatistics(newApplication.contact_id);
      }
      
      toast.success('Bewerbung erstellt', `"${newApplication.project_name}" wurde erfolgreich erstellt.`);

      return newApplication;
    } catch (error) {
      console.error('Error creating application:', error);
      toast.error('Fehler beim Erstellen', 'Die Bewerbung konnte nicht erstellt werden.');
      return null;
    } finally {
      setIsCreating(false);
    }
  };

  // Update existing application
  const updateApplication = async ({ 
    id, 
    updates, 
    statusNotes 
  }: { 
    id: string; 
    updates: Partial<UpdateApplicationData>; 
    statusNotes?: { notes?: string; notes_date?: string } 
  }): Promise<boolean> => {
    try {
      setIsUpdating(true);
      
      
      // Get current application to compare status
      const { data: currentApp, error: fetchError } = await supabase
        .from('project_applications')
        .select('*')
        .eq('id', id)
        .single();
        
      if (fetchError) {
        console.warn('Could not fetch current application for comparison:', fetchError);
      }

      // Handle interview date/time when status changes to interview_scheduled
      const processedUpdates = { ...updates };
      if (updates.status === 'interview_scheduled' && statusNotes?.notes_date) {
        try {
          // Parse interview date/time from statusNotes.notes_date format (YYYY-MM-DDTHH:mm or YYYY-MM-DD)
          const dateTimeStr = statusNotes.notes_date;
          if (dateTimeStr.includes('T')) {
            const [date, time] = dateTimeStr.split('T');
            processedUpdates.interview_date = date;
            processedUpdates.interview_time = time;
          } else {
            processedUpdates.interview_date = dateTimeStr;
            processedUpdates.interview_time = null;
          }
        } catch (error) {
          console.error('Error parsing interview date/time:', error);
        }
      }

      // Handle notes separation:
      // - If statusNotes is provided, don't save ANY notes to project_applications (they go to project_activities)
      // - If no statusNotes, preserve notes from general application editing
      // - Always remove notes_date (doesn't exist in project_applications table)
      let dbUpdates;
      if (statusNotes) {
        // Status update: exclude notes from database updates (they go to activity log)
        const { notes: _, notes_date: __, ...cleanUpdates } = processedUpdates;
        dbUpdates = cleanUpdates;
      } else {
        // General application update: preserve notes but remove notes_date
        const { notes_date: __, ...cleanUpdates } = processedUpdates;
        dbUpdates = cleanUpdates;
      }

      const { data: application, error } = await supabase
        .from('project_applications')
        .update({
          ...dbUpdates,
          updated_at: new Date().toISOString()
        })
        .eq('id', id)
        .select()
        .single();

      if (error) throw error;

      // Update local state with actual DB response data (not just dbUpdates)
      // This ensures UI reflects the actual DB state, including deleted/null fields
      setData(prev => 
        prev.map(app => 
          app.id === id 
            ? { ...app, ...application }
            : app
        )
      );

      // If status changed, create calendar event, log activity, and auto-schedule follow-ups
      if (currentApp && application && updates.status && currentApp.status && currentApp.status !== updates.status) {
        // Clean up notifications based on status changes
        if (updates.status === 'rejected' || updates.status === 'project_completed') {
          // Cancel all notifications when application is completed/rejected
          await NotificationCleanupService.cancelApplicationNotifications(application.id);
        } else if (updates.status === 'interview_scheduled') {
          // Cancel follow-up suggestion notifications when interview is scheduled
          await NotificationCleanupService.cancelFollowUpSuggestions(application.id);
        } else if (updates.status === 'interview_completed') {
          // Cancel interview reminders when interview is completed
          await NotificationCleanupService.cancelInterviewNotifications(application.id);
        }

        // Special handling for interview_scheduled status
        if (updates.status === 'interview_scheduled' && application.interview_date) {
          try {
            await createInterviewEvent(
              application.id,
              application.project_name,
              application.company_name,
              application.interview_date,
              application.interview_time || undefined,
              statusNotes?.notes
            );
          } catch (error) {
            console.error('Error creating interview calendar event:', error);
          }
        } else {
          // Regular status change event creation
          await createEventForStatusChange({
            projectId: application.id,
            projectName: application.project_name,
            companyName: application.company_name,
            newStatus: updates.status,
            statusNotes: statusNotes,
            referenceType: 'application'
          });
        }

        await ActivityService.logActivity(
          application.id,
          'application_status_changed',
          `Status geändert von "${APPLICATION_STATUS_LABELS[currentApp.status] || currentApp.status}" zu "${APPLICATION_STATUS_LABELS[updates.status] || updates.status}"`,
          statusNotes?.notes,
          statusNotes?.notes_date
        );

        // Auto-schedule follow-ups for the new status
        try {
          const { data: { user } } = await supabase.auth.getUser();
          if (user) {
            const scheduledFollowUps = await FollowUpService.autoScheduleFollowUps(
              application.id,
              updates.status,
              user.id
            );
            
            if (scheduledFollowUps.length > 0) {
              toast.success(
                'Follow-ups geplant',
                `${scheduledFollowUps.length} Follow-up(s) wurden automatisch geplant.`
              );
            }
          }
        } catch (error) {
          console.error('Error auto-scheduling follow-ups:', error);
          // Don't show error toast as this is a nice-to-have feature
        }
      } else if (application && (updates.project_start_date || updates.project_end_date)) {
        // Update calendar events if dates changed
        await deleteProjectEvents(application.id);
        if (application.project_start_date || application.project_end_date) {
          await createProjectDateEvents(
            application.id,
            application.project_name,
            application.company_name,
            application.project_start_date,
            application.project_end_date,
            'application'
          );
        }

        await ActivityService.logActivity(
          application.id,
          'application_updated',
          `Bewerbung "${application.project_name}" wurde aktualisiert`
        );
      } else {
        await ActivityService.logActivity(
          application.id,
          'application_updated',
          `Bewerbung "${application.project_name}" wurde aktualisiert`
        );
      }

      // Update contact statistics if contact is linked (for current or old contact)
      const contactIdsToUpdate = new Set<string>();
      if (application.contact_id) {
        contactIdsToUpdate.add(application.contact_id);
      }
      if (currentApp?.contact_id && currentApp.contact_id !== application.contact_id) {
        contactIdsToUpdate.add(currentApp.contact_id);
      }
      
      // Update statistics for all affected contacts
      for (const contactId of contactIdsToUpdate) {
        try {
          await ContactService.updateContactStatistics(contactId);
        } catch (error) {
          console.error(`Failed to update statistics for contact ${contactId}:`, error);
          // Continue with other contacts rather than failing completely
        }
      }

      toast.success('Bewerbung aktualisiert', `"${application.project_name}" wurde erfolgreich aktualisiert.`);

      return true;
    } catch (error) {
      console.error('Error updating application:', error);
      toast.error('Fehler beim Aktualisieren', 'Die Bewerbung konnte nicht aktualisiert werden.');
      return false;
    } finally {
      setIsUpdating(false);
    }
  };

  // Delete application
  const deleteApplication = async (applicationId: string): Promise<boolean> => {
    try {
      setIsDeleting(true);
      
      // Get application details before deletion
      const { data: application } = await supabase
        .from('project_applications')
        .select('project_name, contact_id')
        .eq('id', applicationId)
        .single();

      const { error } = await supabase
        .from('project_applications')
        .delete()
        .eq('id', applicationId);

      if (error) throw error;

      // Update local state
      setData(prev => prev.filter(app => app.id !== applicationId));

      // Delete associated calendar events
      await deleteProjectEvents(applicationId);

      // Cancel all notifications for the deleted application
      await NotificationCleanupService.cancelApplicationNotifications(applicationId);

      // Update contact statistics if contact was linked
      if (application?.contact_id) {
        await ContactService.updateContactStatistics(application.contact_id);
      }

      toast.success('Bewerbung gelöscht', application?.project_name 
        ? `"${application.project_name}" wurde erfolgreich gelöscht.`
        : 'Die Bewerbung wurde erfolgreich gelöscht.');

      return true;
    } catch (error) {
      console.error('Error deleting application:', error);
      toast.error('Fehler beim Löschen', 'Die Bewerbung konnte nicht gelöscht werden.');
      return false;
    } finally {
      setIsDeleting(false);
    }
  };

  // Duplicate application
  const duplicateApplication = async (applicationId: string): Promise<Application | null> => {
    try {
      setIsCreating(true);
      
      const { data: originalApp, error: fetchError } = await supabase
        .from('project_applications')
        .select('*')
        .eq('id', applicationId)
        .single();

      if (fetchError || !originalApp) {
        throw new Error('Bewerbung nicht gefunden');
      }

      // Remove fields that shouldn't be duplicated
      const { id: _, user_id, created_at, updated_at, ...duplicateData } = originalApp;

      // Get current user from Supabase (still needed for user_id)
      const { data: { user } } = await supabase.auth.getUser();      
      if (!user) {
        throw new Error('Benutzer nicht authentifiziert');
      }

      const { data: newApp, error } = await supabase
        .from('project_applications')
        .insert([{
          ...duplicateData,
          user_id: user.id,
          project_name: `${duplicateData.project_name} (Kopie)`,
          status: 'not_applied' // Reset status for duplicate
        }])
        .select()
        .single();

      if (error) throw error;

      const newApplication = newApp as Application;
      
      // Update local state
      const newApplicationWithContact: ApplicationWithContact = {
        ...newApplication,
        contact: null
      };
      setData(prev => [newApplicationWithContact, ...prev]);

      // Log activity for the new application
      await ActivityService.logActivity(
        newApplication.id,
        'application_created',
        `Bewerbung "${newApplication.project_name}" wurde als Kopie erstellt`
      );

      toast.success('Bewerbung dupliziert', `"${newApplication.project_name}" wurde erfolgreich erstellt.`);

      return newApplication;
    } catch (error) {
      console.error('Error duplicating application:', error);
      toast.error('Fehler beim Duplizieren', 'Die Bewerbung konnte nicht dupliziert werden.');
      return null;
    } finally {
      setIsCreating(false);
    }
  };

  return {
    data,
    isLoading,
    isCreating,
    isUpdating,
    isDeleting,
    fetchApplications,
    createApplication,
    updateApplication,
    deleteApplication,
    duplicateApplication
  };
};

export const useApplication = (id: string) => {
  const [application, setApplication] = useState<ApplicationWithContact | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  const fetchApplication = async () => {
    try {
      setIsLoading(true);
      
      if (!id) {
        setApplication(null);
        return;
      }

      // Get current user from Supabase (still needed for user_id check)
      const { data: { user } } = await supabase.auth.getUser();      
      if (!user) {
        throw new Error('Benutzer nicht authentifiziert');
      }

      const { data: applicationData, error } = await supabase
        .from('project_applications')
        .select('*')
        .eq('id', id)
        .eq('user_id', user.id)
        .single();

      if (error) {
        if (error.code === 'PGRST116') {
          setApplication(null);
          return;
        }
        throw error;
      }

      // Get contact if contact_id exists
      let contact = null;
      if (applicationData.contact_id) {
        const { data: contactData, error: contactError } = await supabase
          .from('contacts')
          .select('*')
          .eq('id', applicationData.contact_id)
          .single();

        if (!contactError && contactData) {
          contact = contactData;
        }
      }

      const applicationWithContact: ApplicationWithContact = {
        ...applicationData,
        contact
      };

      setApplication(applicationWithContact);
    } catch (error) {
      console.error('Error fetching application:', error);
      setApplication(null);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchApplication();
  }, [id]);

  return {
    data: application,
    isLoading,
    refetch: fetchApplication
  };
};

