import { useState } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { toast } from '@/lib/toast';

interface MatchScoreResult {
  matchScore: number;
  matchReasoning: string;
  cached: boolean;
}

export const useMatchScore = () => {
  const [isCalculating, setIsCalculating] = useState(false);

  const calculateMatchScore = async (
    applicationId: string,
    forceRecalculate: boolean = false
  ): Promise<MatchScoreResult | null> => {
    setIsCalculating(true);
    
    try {
      const { data, error } = await supabase.functions.invoke('calculate-match-score', {
        body: {
          applicationId,
          forceRecalculate
        }
      });

      if (error) {
        throw new Error(error.message || 'Fehler beim Berechnen des Match-Scores');
      }

      if (!data) {
        throw new Error('Keine Daten vom Server erhalten');
      }

      // Save to database via normal client (respects RLS)
      const { error: saveError } = await supabase
        .from('project_applications')
        .update({
          match_score: data.matchScore,
          match_reasoning: data.matchReasoning,
          match_calculated_at: new Date().toISOString()
        })
        .eq('id', applicationId);

      if (saveError) {
        console.error('Error saving match score:', saveError);
        toast.error('Fehler', 'Score berechnet, aber Speichern fehlgeschlagen');
        return null;
      }

      // Show appropriate toast message
      if (data.cached) {
        toast.info('Match Score', 'Gespeicherter Score geladen');
      } else {
        toast.success('Match Score berechnet', `${data.matchScore}% Übereinstimmung ermittelt`);
      }

      return {
        matchScore: data.matchScore,
        matchReasoning: data.matchReasoning,
        cached: data.cached
      };

    } catch (error: any) {
      console.error('Match score calculation error:', error);
      toast.error('Fehler', error.message || 'Match-Score konnte nicht berechnet werden');
      return null;
    } finally {
      setIsCalculating(false);
    }
  };

  const invalidateAllMatchScores = async (userId: string): Promise<boolean> => {
    try {
      const { error } = await supabase
        .from('project_applications')
        .update({ 
          match_calculated_at: null,
          match_score: null,
          match_reasoning: null 
        })
        .eq('user_id', userId);

      if (error) {
        throw new Error(error.message);
      }

      toast.success('Match Scores zurückgesetzt', 'Alle Scores werden bei der nächsten Berechnung aktualisiert');
      return true;

    } catch (error: any) {
      console.error('Error invalidating match scores:', error);
      toast.error('Fehler', 'Match-Scores konnten nicht zurückgesetzt werden');
      return false;
    }
  };

  return {
    calculateMatchScore,
    invalidateAllMatchScores,
    isCalculating
  };
};