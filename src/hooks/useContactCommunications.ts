import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { toast } from 'sonner';
import { useTranslation } from 'react-i18next';
import { ContactCommunicationService } from '@/services/contactCommunicationService';
import {
  ContactCommunication,
  CreateCommunicationData,
  UpdateCommunicationData,
  CommunicationWithDetails,
  CommunicationStats,
  CommunicationFilters,
  PaginatedResponse,
  CommunicationQuery
} from '@/types/communications';
import {
  validateCreateCommunicationData,
  validateUpdateCommunicationData,
  validateCommunicationFilters,
  validateCommunicationIds,
  validateUUID,
  validateLimit,
  validateSearchText,
  ValidationError,
  SecurityError
} from '@/lib/validation/communicationValidation';

// Query keys for consistent caching
const QUERY_KEYS = {
  COMMUNICATIONS: 'contact-communications',
  COMMUNICATION_STATS: 'communication-stats',
  RECENT_COMMUNICATIONS: 'recent-communications',
  COMMUNICATIONS_BY_CONTACT: 'communications-by-contact',
  COMMUNICATIONS_BY_PROJECT: 'communications-by-project',
  COMMUNICATION_BY_ID: 'communication-by-id'
} as const;

// Hook for getting communications by contact
export function useContactCommunications(contactId: string, limit: number = 50) {
  return useQuery({
    queryKey: [QUERY_KEYS.COMMUNICATIONS_BY_CONTACT, contactId, limit],
    queryFn: async () => {
      // Validate input parameters
      const validatedContactId = validateUUID(contactId, 'contactId');
      const validatedLimit = validateLimit(limit);
      
      return ContactCommunicationService.getCommunicationsByContact(validatedContactId, validatedLimit);
    },
    enabled: !!contactId,
    staleTime: 1000 * 60 * 5, // 5 minutes
    refetchOnWindowFocus: false, // Prevent excessive refetches
    refetchOnMount: false, // Only refetch if stale
  });
}

// Hook for getting communications with detailed information (legacy)
export function useCommunicationsWithDetails(
  filters: CommunicationFilters = {},
  limit: number = 50
) {
  return useQuery({
    queryKey: [QUERY_KEYS.COMMUNICATIONS, filters, limit],
    queryFn: async () => {
      // Validate input parameters
      const validatedFilters = validateCommunicationFilters(filters);
      const validatedLimit = validateLimit(limit);
      
      return ContactCommunicationService.getCommunicationsWithDetails(validatedFilters, validatedLimit);
    },
    staleTime: 1000 * 60 * 5, // 5 minutes
  });
}

// Hook for getting paginated communications with detailed information
export function useCommunicationsWithDetailsPaginated(
  query: CommunicationQuery
) {
  return useQuery({
    queryKey: [QUERY_KEYS.COMMUNICATIONS, 'paginated', query],
    queryFn: async () => {
      // Validate input parameters
      const validatedFilters = validateCommunicationFilters(query);
      const validatedLimit = validateLimit(query.limit);
      
      if (query.page < 1) {
        throw new ValidationError('Page must be 1 or greater', 'page');
      }

      const validatedQuery: CommunicationQuery = {
        ...validatedFilters,
        page: query.page,
        limit: validatedLimit
      };
      
      return ContactCommunicationService.getCommunicationsWithDetailsPaginated(validatedQuery);
    },
    staleTime: 1000 * 60 * 5, // 5 minutes
    keepPreviousData: true, // Keep previous page data while loading new page
  });
}

// Hook for getting communication statistics
export function useCommunicationStats(contactId: string) {
  return useQuery({
    queryKey: [QUERY_KEYS.COMMUNICATION_STATS, contactId],
    queryFn: () => ContactCommunicationService.getCommunicationStats(contactId),
    enabled: !!contactId,
    staleTime: 1000 * 60 * 10, // 10 minutes
    refetchOnWindowFocus: false, // Prevent excessive refetches
    refetchOnMount: false, // Only refetch if stale
  });
}

// Hook for getting recent communications
export function useRecentCommunications(limit: number = 10) {
  return useQuery({
    queryKey: [QUERY_KEYS.RECENT_COMMUNICATIONS, limit],
    queryFn: () => ContactCommunicationService.getRecentCommunications(limit),
    staleTime: 1000 * 60 * 2, // 2 minutes
  });
}

// Hook for getting communications by project
export function useProjectCommunications(projectId: string, limit: number = 50) {
  return useQuery({
    queryKey: [QUERY_KEYS.COMMUNICATIONS_BY_PROJECT, projectId, limit],
    queryFn: () => ContactCommunicationService.getCommunicationsByProject(projectId, limit),
    enabled: !!projectId,
    staleTime: 1000 * 60 * 5, // 5 minutes
  });
}

// Hook for getting a single communication by ID
export function useCommunication(communicationId: string) {
  return useQuery({
    queryKey: [QUERY_KEYS.COMMUNICATION_BY_ID, communicationId],
    queryFn: () => ContactCommunicationService.getCommunicationById(communicationId),
    enabled: !!communicationId,
    staleTime: 1000 * 60 * 10, // 10 minutes
  });
}

// Hook for creating a new communication
export function useCreateCommunication() {
  const queryClient = useQueryClient();
  const { t } = useTranslation('contacts');

  return useMutation({
    mutationFn: async (data: CreateCommunicationData) => {
      try {
        // Validate and sanitize input data
        const validatedData = validateCreateCommunicationData(data);
        return await ContactCommunicationService.createCommunication(validatedData);
      } catch (error) {
        if (error instanceof ValidationError || error instanceof SecurityError) {
          // Log security issues for monitoring
          if (error instanceof SecurityError) {
            console.warn('Security validation failed:', {
              error: error.message,
              severity: error.severity,
              timestamp: new Date().toISOString()
            });
          }
          throw error;
        }
        throw new ValidationError('Invalid communication data provided');
      }
    },
    onSuccess: (result, variables) => {
      if (result) {
        toast.success(t('communications.created_success'));
        
        // Optimized cache invalidation - update cache directly first, then selective invalidation
        // Update the cache optimistically for better UX
        const newCommunication = result;
        
        // Update communications by contact cache
        queryClient.setQueryData(
          [QUERY_KEYS.COMMUNICATIONS_BY_CONTACT, variables.contact_id],
          (oldData: ContactCommunication[] | undefined) => {
            if (!oldData) return [newCommunication];
            return [newCommunication, ...oldData];
          }
        );
        
        // Update paginated cache if it exists
        queryClient.setQueriesData(
          { queryKey: [QUERY_KEYS.COMMUNICATIONS, 'paginated'] },
          (oldData: any) => {
            if (!oldData?.data) return oldData;
            return {
              ...oldData,
              data: [newCommunication, ...oldData.data.slice(0, oldData.pagination?.limit - 1 || 24)],
              pagination: {
                ...oldData.pagination,
                total: (oldData.pagination?.total || 0) + 1
              }
            };
          }
        );
        
        // Selective invalidation only for stats and recent communications
        queryClient.invalidateQueries({ 
          queryKey: [QUERY_KEYS.COMMUNICATION_STATS, variables.contact_id] 
        });
        queryClient.invalidateQueries({ 
          queryKey: [QUERY_KEYS.RECENT_COMMUNICATIONS] 
        });
        
        if (variables.project_id) {
          queryClient.invalidateQueries({ 
            queryKey: [QUERY_KEYS.COMMUNICATIONS_BY_PROJECT, variables.project_id] 
          });
        }
      } else {
        toast.error(t('communications.created_error'));
      }
    },
    onError: (error) => {
      console.error('Error creating communication:', error);
      
      // Handle different error types with appropriate user messages
      if (error instanceof ValidationError) {
        toast.error(`Validierungsfehler: ${error.message}`);
      } else if (error instanceof SecurityError) {
        toast.error('Sicherheitsfehler: Eingabe enthält nicht erlaubte Inhalte');
      } else {
        toast.error(t('communications.created_error'));
      }
    },
  });
}

// Hook for updating a communication
export function useUpdateCommunication() {
  const queryClient = useQueryClient();
  const { t } = useTranslation('contacts');

  return useMutation({
    mutationFn: async (data: UpdateCommunicationData) => {
      try {
        // Validate and sanitize input data
        const validatedData = validateUpdateCommunicationData(data);
        return await ContactCommunicationService.updateCommunication(validatedData);
      } catch (error) {
        if (error instanceof ValidationError || error instanceof SecurityError) {
          // Log security issues for monitoring
          if (error instanceof SecurityError) {
            console.warn('Security validation failed:', {
              error: error.message,
              severity: error.severity,
              timestamp: new Date().toISOString()
            });
          }
          throw error;
        }
        throw new ValidationError('Invalid communication update data provided');
      }
    },
    onSuccess: (result, variables) => {
      if (result) {
        toast.success(t('communications.updated_success'));
        
        // Optimized cache updates - directly update cache instead of invalidating
        const updatedCommunication = result;
        
        // Update the specific communication by ID
        queryClient.setQueryData(
          [QUERY_KEYS.COMMUNICATION_BY_ID, variables.id],
          updatedCommunication
        );
        
        // Update communications by contact cache
        if (result.contact_id) {
          queryClient.setQueryData(
            [QUERY_KEYS.COMMUNICATIONS_BY_CONTACT, result.contact_id],
            (oldData: ContactCommunication[] | undefined) => {
              if (!oldData) return [updatedCommunication];
              return oldData.map(comm => 
                comm.id === variables.id ? updatedCommunication : comm
              );
            }
          );
        }
        
        // Update paginated cache if it exists
        queryClient.setQueriesData(
          { queryKey: [QUERY_KEYS.COMMUNICATIONS, 'paginated'] },
          (oldData: any) => {
            if (!oldData?.data) return oldData;
            return {
              ...oldData,
              data: oldData.data.map((comm: any) => 
                comm.id === variables.id ? updatedCommunication : comm
              )
            };
          }
        );
        
        // Only invalidate stats and recent communications (cheaper to refetch these)
        if (result.contact_id) {
          queryClient.invalidateQueries({ 
            queryKey: [QUERY_KEYS.COMMUNICATION_STATS, result.contact_id] 
          });
        }
        queryClient.invalidateQueries({ 
          queryKey: [QUERY_KEYS.RECENT_COMMUNICATIONS] 
        });
        
        if (result.project_id) {
          queryClient.invalidateQueries({ 
            queryKey: [QUERY_KEYS.COMMUNICATIONS_BY_PROJECT, result.project_id] 
          });
        }
      } else {
        toast.error(t('communications.updated_error'));
      }
    },
    onError: (error) => {
      console.error('Error updating communication:', error);
      
      // Handle different error types with appropriate user messages
      if (error instanceof ValidationError) {
        toast.error(`Validierungsfehler: ${error.message}`);
      } else if (error instanceof SecurityError) {
        toast.error('Sicherheitsfehler: Eingabe enthält nicht erlaubte Inhalte');
      } else {
        toast.error(t('communications.updated_error'));
      }
    },
  });
}

// Hook for deleting a communication
export function useDeleteCommunication() {
  const queryClient = useQueryClient();
  const { t } = useTranslation('contacts');

  return useMutation({
    mutationFn: async (communicationId: string) => {
      try {
        // Validate communication ID
        const validatedId = validateUUID(communicationId, 'communicationId');
        return await ContactCommunicationService.deleteCommunication(validatedId);
      } catch (error) {
        if (error instanceof ValidationError) {
          throw error;
        }
        throw new ValidationError('Invalid communication ID provided');
      }
    },
    onSuccess: (success, communicationId) => {
      if (success) {
        toast.success(t('communications.deleted_success'));
        
        // Optimized cache updates - remove from specific caches instead of broad invalidation
        // Remove the specific communication from cache
        queryClient.removeQueries({ 
          queryKey: [QUERY_KEYS.COMMUNICATION_BY_ID, communicationId] 
        });
        
        // Update all contact communication caches by removing the deleted item
        queryClient.setQueriesData(
          { queryKey: [QUERY_KEYS.COMMUNICATIONS_BY_CONTACT] },
          (oldData: ContactCommunication[] | undefined) => {
            if (!oldData) return oldData;
            return oldData.filter(comm => comm.id !== communicationId);
          }
        );
        
        // Update all paginated caches by removing the deleted item
        queryClient.setQueriesData(
          { queryKey: [QUERY_KEYS.COMMUNICATIONS, 'paginated'] },
          (oldData: any) => {
            if (!oldData?.data) return oldData;
            return {
              ...oldData,
              data: oldData.data.filter((comm: any) => comm.id !== communicationId),
              pagination: {
                ...oldData.pagination,
                total: Math.max(0, (oldData.pagination?.total || 1) - 1)
              }
            };
          }
        );
        
        // Only invalidate smaller, aggregate queries
        queryClient.invalidateQueries({ 
          queryKey: [QUERY_KEYS.RECENT_COMMUNICATIONS] 
        });
        queryClient.invalidateQueries({ 
          queryKey: [QUERY_KEYS.COMMUNICATION_STATS] 
        });
        queryClient.invalidateQueries({ 
          queryKey: [QUERY_KEYS.COMMUNICATIONS_BY_PROJECT] 
        });
      } else {
        toast.error(t('communications.delete_error'));
      }
    },
    onError: (error) => {
      console.error('Error deleting communication:', error);
      
      // Handle different error types with appropriate user messages
      if (error instanceof ValidationError) {
        toast.error(`Validierungsfehler: ${error.message}`);
      } else {
        toast.error(t('communications.delete_error'));
      }
    },
  });
}

// Hook for updating communication summary (AI-generated)
export function useUpdateCommunicationSummary() {
  const queryClient = useQueryClient();
  const { t } = useTranslation('contacts');

  return useMutation({
    mutationFn: ({ communicationId, summary }: { communicationId: string; summary: string }) =>
      ContactCommunicationService.updateCommunicationSummary(communicationId, summary),
    onSuccess: (success, variables) => {
      if (success) {
        toast.success(t('communications.summary_updated_success'));
        
        // Invalidate the specific communication
        queryClient.invalidateQueries({ 
          queryKey: [QUERY_KEYS.COMMUNICATION_BY_ID, variables.communicationId] 
        });
        
        // Invalidate communication lists
        queryClient.invalidateQueries({ 
          queryKey: [QUERY_KEYS.COMMUNICATIONS] 
        });
        queryClient.invalidateQueries({ 
          queryKey: [QUERY_KEYS.COMMUNICATIONS_BY_CONTACT] 
        });
        queryClient.invalidateQueries({ 
          queryKey: [QUERY_KEYS.COMMUNICATIONS_BY_PROJECT] 
        });
      } else {
        toast.error(t('communications.summary_updated_error'));
      }
    },
    onError: (error) => {
      console.error('Error updating communication summary:', error);
      toast.error(t('communications.summary_updated_error'));
    },
  });
}

// Hook for searching communications
export function useSearchCommunications(searchText: string, limit: number = 20) {
  return useQuery({
    queryKey: [QUERY_KEYS.COMMUNICATIONS, 'search', searchText, limit],
    queryFn: async () => {
      // Validate input parameters
      const validatedSearchText = validateSearchText(searchText);
      const validatedLimit = validateLimit(limit);
      
      return ContactCommunicationService.searchCommunications(validatedSearchText, validatedLimit);
    },
    enabled: !!searchText && searchText.length >= 2, // Only search if text is at least 2 characters
    staleTime: 1000 * 60 * 5, // 5 minutes
  });
}

// Hook for bulk deleting communications
export function useBulkDeleteCommunications() {
  const queryClient = useQueryClient();
  const { t } = useTranslation('contacts');

  return useMutation({
    mutationFn: async (communicationIds: string[]) => {
      try {
        // Validate communication IDs
        const validatedIds = validateCommunicationIds(communicationIds);
        return await ContactCommunicationService.bulkDeleteCommunications(validatedIds);
      } catch (error) {
        if (error instanceof ValidationError) {
          throw error;
        }
        throw new ValidationError('Invalid communication IDs provided');
      }
    },
    onSuccess: (success, communicationIds) => {
      if (success) {
        toast.success(t('communications.bulk_deleted_success', { count: communicationIds.length }));
        
        // Invalidate all communication-related queries
        queryClient.invalidateQueries({ 
          queryKey: [QUERY_KEYS.COMMUNICATIONS] 
        });
        queryClient.invalidateQueries({ 
          queryKey: [QUERY_KEYS.RECENT_COMMUNICATIONS] 
        });
        queryClient.invalidateQueries({ 
          queryKey: [QUERY_KEYS.COMMUNICATIONS_BY_CONTACT] 
        });
        queryClient.invalidateQueries({ 
          queryKey: [QUERY_KEYS.COMMUNICATIONS_BY_PROJECT] 
        });
        queryClient.invalidateQueries({ 
          queryKey: [QUERY_KEYS.COMMUNICATION_STATS] 
        });
        
        // Remove specific communications from cache
        communicationIds.forEach(id => {
          queryClient.removeQueries({ 
            queryKey: [QUERY_KEYS.COMMUNICATION_BY_ID, id] 
          });
        });
      } else {
        toast.error(t('communications.bulk_deleted_error'));
      }
    },
    onError: (error) => {
      console.error('Error bulk deleting communications:', error);
      
      // Handle different error types with appropriate user messages
      if (error instanceof ValidationError) {
        toast.error(`Validierungsfehler: ${error.message}`);
      } else {
        toast.error(t('communications.bulk_deleted_error'));
      }
    },
  });
}

// Hook for exporting communications
export function useExportCommunications() {
  const { t } = useTranslation('contacts');

  return useMutation({
    mutationFn: (contactId?: string) =>
      ContactCommunicationService.exportCommunications(contactId),
    onSuccess: (data) => {
      toast.success(t('communications.exported_success', { count: data.length }));
    },
    onError: (error) => {
      console.error('Error exporting communications:', error);
      toast.error(t('communications.exported_error'));
    },
  });
}