import { describe, it, expect, vi, beforeEach } from 'vitest'
import { renderHook, waitFor, act } from '@testing-library/react'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { useFollowUpSchedule } from '../useFollowUpSchedule'
import { supabase } from '@/integrations/supabase/client'
import { mockUser, mockScheduledFollowUp } from '@/test/mocks'
import { toast } from '@/lib/toast'

// Create wrapper component for React Query
const createWrapper = () => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: { retry: false },
      mutations: { retry: false },
    },
  })
  
  return ({ children }: { children: React.ReactNode }) => (
    <QueryClientProvider client={queryClient}>
      {children}
    </QueryClientProvider>
  )
}

describe('useFollowUpSchedule', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    
    // Mock authenticated user
    vi.mocked(supabase.auth.getUser).mockResolvedValue({
      data: { user: mockUser },
      error: null,
    })
  })

  describe('scheduled follow-ups data', () => {
    it('should have scheduled follow-ups data structure', () => {
      const mockScheduled = [mockScheduledFollowUp]
      
      expect(mockScheduled[0]).toHaveProperty('id')
      expect(mockScheduled[0]).toHaveProperty('application_id')
      expect(mockScheduled[0]).toHaveProperty('template_id')
      expect(mockScheduled[0]).toHaveProperty('scheduled_date')
      expect(mockScheduled[0]).toHaveProperty('status')
      expect(mockScheduled[0].status).toBe('scheduled')
    })
  })

  describe('markAsSent mutation', () => {
    it('should create history entry when marking as sent', async () => {
      // Mock successful history creation
      const mockHistoryData = {
        followUpId: 'schedule-1',
        actualSubject: 'Follow-up: React Developer',
        actualBody: 'Hello Max...'
      }

      // Mock the history insert
      const mockInsert = vi.fn().mockReturnThis()
      const mockSelect = vi.fn().mockReturnThis()
      const mockSingle = vi.fn().mockResolvedValue({
        data: { id: 'history-1', ...mockHistoryData },
        error: null
      })

      vi.mocked(supabase.from).mockImplementation((table: string) => {
        if (table === 'follow_up_history') {
          return {
            insert: mockInsert,
            select: mockSelect,
            single: mockSingle,
          } as any
        }
        
        // For other tables, return basic mock
        return {
          select: vi.fn().mockReturnThis(),
          eq: vi.fn().mockReturnThis(),
          update: vi.fn().mockReturnThis(),
          single: vi.fn().mockResolvedValue({ data: null, error: null }),
        } as any
      })

      const { result } = renderHook(() => useFollowUpSchedule(), {
        wrapper: createWrapper(),
      })

      // The hook should be available
      expect(result.current).toBeDefined()
      expect(result.current.markAsSent).toBeDefined()

      // Test the mutation data structure
      expect(mockHistoryData.followUpId).toBe('schedule-1')
      expect(mockHistoryData.actualSubject).toContain('Follow-up')
      expect(mockHistoryData.actualBody).toBeTruthy()
    })
  })

  describe('dismissFollowUp mutation', () => {
    it('should update follow-up status to dismissed', async () => {
      const followUpId = 'schedule-1'

      // Mock successful dismissal
      const mockUpdate = vi.fn().mockReturnThis()
      const mockEq = vi.fn().mockResolvedValue({ data: null, error: null })
      
      vi.mocked(supabase.from).mockReturnValue({
        update: mockUpdate,
        eq: mockEq,
      } as any)

      const { result } = renderHook(() => useFollowUpSchedule(), {
        wrapper: createWrapper(),
      })

      expect(result.current.dismissFollowUp).toBeDefined()
      
      // Verify the expected data structure for dismissal
      expect(followUpId).toBe('schedule-1')
    })
  })

  describe('deleteScheduledFollowUp mutation', () => {
    it('should delete follow-up from schedule', async () => {
      const followUpId = 'schedule-1'

      // Mock successful deletion
      const mockDelete = vi.fn().mockReturnThis()
      const mockEq = vi.fn().mockResolvedValue({ data: null, error: null })
      
      vi.mocked(supabase.from).mockReturnValue({
        delete: mockDelete,
        eq: mockEq,
      } as any)

      const { result } = renderHook(() => useFollowUpSchedule(), {
        wrapper: createWrapper(),
      })

      expect(result.current.deleteScheduledFollowUp).toBeDefined()
      
      // Verify expected deletion behavior
      expect(followUpId).toBe('schedule-1')
    })
  })

  describe('error handling', () => {
    it('should handle authentication errors', async () => {
      vi.mocked(supabase.auth.getUser).mockResolvedValue({
        data: { user: null },
        error: null,
      })

      const { result } = renderHook(() => useFollowUpSchedule(), {
        wrapper: createWrapper(),
      })

      // Hook should still be defined even with auth issues
      expect(result.current).toBeDefined()
    })

    it('should handle database errors gracefully', async () => {
      const error = new Error('Database error')
      
      vi.mocked(supabase.from).mockReturnValue({
        update: vi.fn().mockReturnThis(),
        eq: vi.fn().mockResolvedValue({ data: null, error }),
      } as any)

      const { result } = renderHook(() => useFollowUpSchedule(), {
        wrapper: createWrapper(),
      })

      expect(result.current).toBeDefined()
      expect(result.current.error).toBeDefined()
    })
  })

  describe('loading states', () => {
    it('should manage loading states correctly', async () => {
      const { result } = renderHook(() => useFollowUpSchedule(), {
        wrapper: createWrapper(),
      })

      // Initially should have loading state management
      expect(result.current).toBeDefined()
      expect(result.current.isDeleting).toBeDefined()
    })
  })

  describe('validation', () => {
    it('should validate mark as sent data structure', () => {
      const validMarkAsSentData = {
        followUpId: 'schedule-1',
        actualSubject: 'Follow-up: React Developer',
        actualBody: 'Hello Max Mustermann, I wanted to follow up...'
      }

      expect(validMarkAsSentData.followUpId).toBeTruthy()
      expect(validMarkAsSentData.actualSubject).toBeTruthy()
      expect(validMarkAsSentData.actualBody).toBeTruthy()
      expect(validMarkAsSentData.actualSubject).toContain('Follow-up')
    })

    it('should validate scheduled follow-up data structure', () => {
      const validScheduleData = {
        application_id: 'app-1',
        template_id: 'template-1',
        scheduled_date: '2024-08-16T12:00:00Z',
        status: 'scheduled' as const,
        response_received: false
      }

      expect(validScheduleData.application_id).toBeTruthy()
      expect(validScheduleData.template_id).toBeTruthy()
      expect(validScheduleData.scheduled_date).toMatch(/\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}/)
      expect(['scheduled', 'sent', 'dismissed', 'cancelled']).toContain(validScheduleData.status)
      expect(typeof validScheduleData.response_received).toBe('boolean')
    })
  })
})