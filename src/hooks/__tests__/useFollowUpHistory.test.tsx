import { describe, it, expect, vi, beforeEach } from 'vitest'
import { renderHook, waitFor } from '@testing-library/react'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { useFollowUpHistory } from '../useFollowUpHistory'
import { supabase } from '@/integrations/supabase/client'
import { mockUser, mockFollowUpHistory, mockAnalytics } from '@/test/mocks'
import { toast } from '@/lib/toast'

// Create wrapper component for React Query
const createWrapper = () => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: { retry: false },
      mutations: { retry: false },
    },
  })
  
  return ({ children }: { children: React.ReactNode }) => (
    <QueryClientProvider client={queryClient}>
      {children}
    </QueryClientProvider>
  )
}

describe('useFollowUpHistory', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    
    // Mock authenticated user
    vi.mocked(supabase.auth.getUser).mockResolvedValue({
      data: { user: mockUser },
      error: null,
    })
  })

  describe('history data structure', () => {
    it('should have correct follow-up history data structure', () => {
      const history = mockFollowUpHistory
      
      expect(history).toHaveProperty('id')
      expect(history).toHaveProperty('user_id')
      expect(history).toHaveProperty('application_id')
      expect(history).toHaveProperty('template_id')
      expect(history).toHaveProperty('sent_at')
      expect(history).toHaveProperty('subject')
      expect(history).toHaveProperty('body')
      expect(history).toHaveProperty('response_received')
      expect(history).toHaveProperty('created_at')
      
      expect(typeof history.response_received).toBe('boolean')
      expect(history.sent_at).toMatch(/\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}/)
    })

    it('should have populated relations', () => {
      const history = mockFollowUpHistory
      
      if (history.template) {
        expect(history.template).toHaveProperty('id')
        expect(history.template).toHaveProperty('name')
      }
      
      if (history.application) {
        expect(history.application).toHaveProperty('id')
        expect(history.application).toHaveProperty('project_name')
        expect(history.application).toHaveProperty('company_name')
      }
    })
  })

  describe('analytics data structure', () => {
    it('should have correct analytics structure', () => {
      const analytics = mockAnalytics
      
      expect(analytics).toHaveProperty('total_sent')
      expect(analytics).toHaveProperty('response_rate')
      expect(analytics).toHaveProperty('avg_response_time_days')
      expect(analytics).toHaveProperty('success_by_template')
      expect(analytics).toHaveProperty('success_by_timing')
      
      expect(typeof analytics.total_sent).toBe('number')
      expect(typeof analytics.response_rate).toBe('number')
      expect(typeof analytics.avg_response_time_days).toBe('number')
      expect(Array.isArray(analytics.success_by_template)).toBe(true)
      expect(Array.isArray(analytics.success_by_timing)).toBe(true)
    })

    it('should calculate response rates correctly', () => {
      const analytics = mockAnalytics
      
      expect(analytics.response_rate).toBeGreaterThanOrEqual(0)
      expect(analytics.response_rate).toBeLessThanOrEqual(1)
      
      // Check template success rates
      analytics.success_by_template.forEach(template => {
        expect(template).toHaveProperty('template_id')
        expect(template).toHaveProperty('template_name')
        expect(template).toHaveProperty('sent_count')
        expect(template).toHaveProperty('response_count')
        expect(template).toHaveProperty('response_rate')
        
        expect(template.response_rate).toBeGreaterThanOrEqual(0)
        expect(template.response_rate).toBeLessThanOrEqual(1)
        expect(template.response_count).toBeLessThanOrEqual(template.sent_count)
      })
    })

    it('should have timing analysis data', () => {
      const analytics = mockAnalytics
      
      analytics.success_by_timing.forEach(timing => {
        expect(timing).toHaveProperty('trigger_days')
        expect(timing).toHaveProperty('sent_count')
        expect(timing).toHaveProperty('response_count')
        expect(timing).toHaveProperty('response_rate')
        
        expect(typeof timing.trigger_days).toBe('number')
        expect(timing.trigger_days).toBeGreaterThan(0)
        expect(timing.response_count).toBeLessThanOrEqual(timing.sent_count)
      })
    })
  })

  describe('markAsResponded mutation', () => {
    it('should have correct data structure for marking response', () => {
      const responseData = {
        historyId: 'history-1',
        responseDate: '2024-08-10T12:00:00Z'
      }

      // Mock successful response marking
      const mockUpdate = vi.fn().mockReturnThis()
      const mockEq = vi.fn().mockReturnThis()
      const mockSelect = vi.fn().mockReturnThis()
      const mockSingle = vi.fn().mockResolvedValue({
        data: { id: responseData.historyId, response_received: true },
        error: null
      })

      vi.mocked(supabase.from).mockReturnValue({
        update: mockUpdate,
        eq: mockEq,
        select: mockSelect,
        single: mockSingle,
      } as any)

      const { result } = renderHook(() => useFollowUpHistory(), {
        wrapper: createWrapper(),
      })

      expect(result.current.markAsResponded).toBeDefined()
      expect(responseData.historyId).toBeTruthy()
      expect(responseData.responseDate).toMatch(/\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}/)
    })
  })

  describe('unmarkResponse mutation', () => {
    it('should handle unmarking response', () => {
      const historyId = 'history-1'

      // Mock successful unmarking
      const mockUpdate = vi.fn().mockReturnThis()
      const mockEq = vi.fn().mockResolvedValue({
        data: { id: historyId, response_received: false },
        error: null
      })

      vi.mocked(supabase.from).mockReturnValue({
        update: mockUpdate,
        eq: mockEq,
      } as any)

      const { result } = renderHook(() => useFollowUpHistory(), {
        wrapper: createWrapper(),
      })

      expect(result.current.unmarkResponse).toBeDefined()
      expect(historyId).toBeTruthy()
    })
  })

  describe('createHistoryEntry mutation', () => {
    it('should validate history entry data structure', () => {
      const historyData = {
        application_id: 'app-1',
        template_id: 'template-1',
        subject: 'Follow-up: React Developer',
        body: 'Hello Max Mustermann...',
        response_received: false
      }

      expect(historyData.application_id).toBeTruthy()
      expect(historyData.template_id).toBeTruthy()
      expect(historyData.subject).toBeTruthy()
      expect(historyData.body).toBeTruthy()
      expect(typeof historyData.response_received).toBe('boolean')
      expect(historyData.subject).toContain('Follow-up')
    })
  })

  describe('error handling', () => {
    it('should handle authentication errors', () => {
      vi.mocked(supabase.auth.getUser).mockResolvedValue({
        data: { user: null },
        error: null,
      })

      const { result } = renderHook(() => useFollowUpHistory(), {
        wrapper: createWrapper(),
      })

      expect(result.current).toBeDefined()
    })

    it('should handle database errors', () => {
      const error = new Error('Database error')
      
      vi.mocked(supabase.from).mockReturnValue({
        select: vi.fn().mockReturnThis(),
        eq: vi.fn().mockReturnThis(),
        order: vi.fn().mockResolvedValue({ data: null, error }),
      } as any)

      const { result } = renderHook(() => useFollowUpHistory(), {
        wrapper: createWrapper(),
      })

      expect(result.current).toBeDefined()
    })
  })

  describe('loading states', () => {
    it('should manage loading states', () => {
      const { result } = renderHook(() => useFollowUpHistory(), {
        wrapper: createWrapper(),
      })

      expect(result.current).toBeDefined()
      expect(result.current.isLoading).toBeDefined()
      expect(result.current.isAnalyticsLoading).toBeDefined()
    })
  })

  describe('validation helpers', () => {
    it('should validate response time calculations', () => {
      const sentDate = '2024-08-09T12:00:00Z'
      const responseDate = '2024-08-14T12:00:00Z'
      
      const sentTime = new Date(sentDate).getTime()
      const responseTime = new Date(responseDate).getTime()
      const daysDiff = Math.floor((responseTime - sentTime) / (1000 * 60 * 60 * 24))
      
      expect(daysDiff).toBe(5)
      expect(daysDiff).toBeGreaterThan(0)
    })

    it('should validate analytics calculations', () => {
      // Test response rate calculation
      const totalSent = 10
      const totalResponses = 3
      const responseRate = totalSent > 0 ? totalResponses / totalSent : 0
      
      expect(responseRate).toBe(0.3)
      expect(responseRate).toBeGreaterThanOrEqual(0)
      expect(responseRate).toBeLessThanOrEqual(1)
      
      // Test zero division handling
      const zeroSentRate = 0 > 0 ? 1 / 0 : 0
      expect(zeroSentRate).toBe(0)
    })
  })
})