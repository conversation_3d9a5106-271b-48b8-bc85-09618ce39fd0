import { useState, useEffect } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { toast } from '@/lib/toast';
import { withFollowUpErrorHandling } from '@/lib/errorHandling';
import type { 
  FollowUpTemplate, 
  CreateFollowUpTemplate, 
  UpdateFollowUpTemplate 
} from '@/types/followup';

const QUERY_KEY = ['followUpTemplates'];

export const useFollowUpTemplates = () => {
  const queryClient = useQueryClient();

  // Fetch all templates for the current user
  const {
    data: templates = [],
    isLoading,
    error
  } = useQuery({
    queryKey: QUERY_KEY,
    queryFn: async (): Promise<FollowUpTemplate[]> => {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error('User not authenticated');

      return await withFollowUpErrorHandling(
        async () => {
          const { data, error } = await supabase
            .from('follow_up_templates')
            .select('*')
            .eq('user_id', user.id)
            .order('created_at', { ascending: false });
          
          if (error) throw error;
          return data || [];
        },
        'useFollowUpTemplates.queryFn'
      );
    }
  });

  // Create new template
  const createTemplate = useMutation({
    mutationFn: async (templateData: CreateFollowUpTemplate): Promise<FollowUpTemplate> => {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error('User not authenticated');

      const { data, error } = await supabase
        .from('follow_up_templates')
        .insert({
          ...templateData,
          user_id: user.id
        })
        .select()
        .single();

      if (error) throw error;
      return data;
    },
    onSuccess: (newTemplate) => {
      queryClient.invalidateQueries({ queryKey: QUERY_KEY });
      toast.success('Template erstellt', `Template "${newTemplate.name}" wurde erfolgreich erstellt.`);
    },
    onError: (error) => {
      console.error('Error creating template:', error);
      toast.error('Fehler', 'Template konnte nicht erstellt werden.');
    }
  });

  // Update existing template
  const updateTemplate = useMutation({
    mutationFn: async (templateData: UpdateFollowUpTemplate): Promise<FollowUpTemplate> => {
      const { id, ...updateData } = templateData;
      
      const { data, error } = await supabase
        .from('follow_up_templates')
        .update({
          ...updateData,
          updated_at: new Date().toISOString()
        })
        .eq('id', id)
        .select()
        .single();

      if (error) throw error;
      return data;
    },
    onSuccess: (updatedTemplate) => {
      queryClient.invalidateQueries({ queryKey: QUERY_KEY });
      toast.success('Template aktualisiert', `Template "${updatedTemplate.name}" wurde erfolgreich aktualisiert.`);
    },
    onError: (error) => {
      console.error('Error updating template:', error);
      toast.error('Fehler', 'Template konnte nicht aktualisiert werden.');
    }
  });

  // Delete template
  const deleteTemplate = useMutation({
    mutationFn: async (templateId: string): Promise<void> => {
      const { error } = await supabase
        .from('follow_up_templates')
        .delete()
        .eq('id', templateId);

      if (error) throw error;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: QUERY_KEY });
      toast.success('Template gelöscht', 'Template wurde erfolgreich gelöscht.');
    },
    onError: (error) => {
      console.error('Error deleting template:', error);
      toast.error('Fehler', 'Template konnte nicht gelöscht werden.');
    }
  });

  // Toggle template active status
  const toggleTemplateActive = useMutation({
    mutationFn: async ({ templateId, isActive }: { templateId: string; isActive: boolean }): Promise<FollowUpTemplate> => {
      const { data, error } = await supabase
        .from('follow_up_templates')
        .update({ 
          is_active: isActive,
          updated_at: new Date().toISOString()
        })
        .eq('id', templateId)
        .select()
        .single();

      if (error) throw error;
      return data;
    },
    onSuccess: (updatedTemplate) => {
      queryClient.invalidateQueries({ queryKey: QUERY_KEY });
      toast.success(
        updatedTemplate.is_active ? 'Template aktiviert' : 'Template deaktiviert',
        `Template "${updatedTemplate.name}" wurde ${updatedTemplate.is_active ? 'aktiviert' : 'deaktiviert'}.`
      );
    },
    onError: (error) => {
      console.error('Error toggling template:', error);
      toast.error('Fehler', 'Template-Status konnte nicht geändert werden.');
    }
  });

  // Create default templates for new users
  const createDefaultTemplates = async () => {
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) return;

    const defaultTemplates: CreateFollowUpTemplate[] = [
      {
        name: "Nach Bewerbung nachfragen",
        subject: "Nachfrage zu meiner Bewerbung - {project_name}",
        body: `Liebe/r {contact_person},

vor {trigger_days} Tagen habe ich mich über {company_name} auf die Position {project_name} beworben und wollte höflich nachfragen, wie der aktuelle Stand des Auswahlverfahrens ist.

Falls Sie weitere Informationen von mir benötigen oder Rückfragen haben, stehe ich gerne zur Verfügung.

Vielen Dank für Ihr Feedback und die Vermittlung.

Beste Grüße
{user_name}`,
        trigger_days: 7,
        status_trigger: 'application_sent',
        is_active: true
      },
      {
        name: "Nach Interview nachfassen",
        subject: "Nachfrage zum Auswahlverfahren - {project_name}",
        body: `Liebe/r {contact_person},

vielen Dank für die Vermittlung des Interviews am {interview_date} für die Position {project_name}. Das Gespräch war sehr interessant und aufschlussreich.

Wie besprochen, wollte ich mich nach dem aktuellen Stand des Auswahlverfahrens erkundigen und freue mich auf eine Rückmeldung Ihrerseits.

Beste Grüße
{user_name}`,
        trigger_days: 3,
        status_trigger: 'interview_completed',
        is_active: true
      },
      {
        name: "Gentle Reminder",
        subject: "Kurze Rückfrage - {project_name}",
        body: `Liebe/r {contact_person},

ich hoffe, Sie hatten eine schöne Zeit und möchte kurz nach dem Status meiner Bewerbung für die Position {project_name} fragen, die Sie freundlicherweise für mich vermitteln.

Falls vom Endkunden noch keine Rückmeldung vorliegt oder Sie weitere Zeit benötigen, ist das selbstverständlich kein Problem.

Herzliche Grüße
{user_name}`,
        trigger_days: 14,
        status_trigger: 'application_sent',
        is_active: false
      },
      {
        name: "Nach Rückfrage",
        subject: "Rückmeldung zu {project_name}",
        body: `Liebe/r {contact_person},

vielen Dank für Ihre Rückfrage bezüglich der Position {project_name}. Gerne beantworte ich weitere Fragen oder stelle zusätzliche Unterlagen zur Verfügung.

Bitte lassen Sie mich wissen, welche Informationen Sie für die weitere Vermittlung benötigen.

Beste Grüße
{user_name}`,
        trigger_days: 2,
        status_trigger: 'inquiry_received',
        is_active: true
      },
      {
        name: "Verfügbarkeit bestätigen",
        subject: "Verfügbarkeit für {project_name}",
        body: `Liebe/r {contact_person},

gerne bestätige ich meine Verfügbarkeit für die Position {project_name}. 

Sollten sich die Projektdetails oder der Starttermin geändert haben, lassen Sie mich gerne wissen. Ich freue mich auf weitere Informationen von Ihrer Seite.

Beste Grüße
{user_name}`,
        trigger_days: 7,
        status_trigger: 'inquiry_received',
        is_active: false
      }
    ];

    for (const template of defaultTemplates) {
      try {
        await createTemplate.mutateAsync(template);
      } catch (error) {
        console.error('Failed to create default template:', error);
      }
    }
  };

  // Helper functions
  const getActiveTemplates = () => templates.filter(t => t.is_active);
  const getTemplatesByStatus = (statusTrigger: string) => 
    templates.filter(t => t.status_trigger === statusTrigger && t.is_active);

  return {
    // Data
    templates,
    activeTemplates: getActiveTemplates(),
    
    // Loading states
    isLoading,
    isCreating: createTemplate.isPending,
    isUpdating: updateTemplate.isPending,
    isDeleting: deleteTemplate.isPending,
    isToggling: toggleTemplateActive.isPending,
    
    // Actions
    createTemplate: createTemplate.mutate,
    createTemplateAsync: createTemplate.mutateAsync,
    updateTemplate: updateTemplate.mutate,
    updateTemplateAsync: updateTemplate.mutateAsync,
    deleteTemplate: deleteTemplate.mutate,
    deleteTemplateAsync: deleteTemplate.mutateAsync,
    toggleTemplateActive: toggleTemplateActive.mutate,
    createDefaultTemplates,
    
    // Helpers
    getTemplatesByStatus,
    
    // Error
    error
  };
};