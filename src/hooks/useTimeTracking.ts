import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { toast } from '@/lib/toast';
import { useState, useEffect, useCallback } from 'react';
import type {
  TimeEntry,
  TimeEntryInsert,
  TimeEntryUpdate,
  TimeEntryWithDuration,
  TimeEntriesResponse,
  TimeEntryFilters,
  TimerState,
  TimerAction,
  TimeEntryCategory
} from '@/types/projects';

// Format duration in minutes to human readable string
const formatDuration = (minutes: number): string => {
  const hours = Math.floor(minutes / 60);
  const mins = minutes % 60;
  
  if (hours === 0) {
    return `${mins}m`;
  } else if (mins === 0) {
    return `${hours}h`;
  } else {
    return `${hours}h ${mins}m`;
  }
};

// Format duration with seconds for timer display
const formatDurationWithSeconds = (totalSeconds: number): string => {
  const hours = Math.floor(totalSeconds / 3600);
  const minutes = Math.floor((totalSeconds % 3600) / 60);
  const seconds = totalSeconds % 60;
  
  if (hours === 0) {
    return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
  } else {
    return `${hours}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
  }
};

// Calculate duration between two timestamps in minutes
const calculateDuration = (startTime: string, endTime?: string): number => {
  const start = new Date(startTime);
  const end = endTime ? new Date(endTime) : new Date();
  return Math.floor((end.getTime() - start.getTime()) / (1000 * 60));
};

// Calculate duration between two timestamps in seconds (for real-time timer)
const calculateDurationInSeconds = (startTime: string, endTime?: string): number => {
  const start = new Date(startTime);
  const end = endTime ? new Date(endTime) : new Date();
  return Math.floor((end.getTime() - start.getTime()) / 1000);
};

export const useTimeTracking = (projectId?: string, filters?: TimeEntryFilters) => {
  const queryClient = useQueryClient();

  // Fetch time entries
  const {
    data: entries = [],
    isLoading,
    error,
    refetch
  } = useQuery({
    queryKey: ['time-entries', projectId, filters],
    queryFn: async (): Promise<TimeEntryWithDuration[]> => {
      // Get current user session (same pattern as other hooks)
      const { data: { session } } = await supabase.auth.getSession();
      if (!session?.user) return [];

      let query = supabase
        .from('time_entries')
        .select(`
          *,
          projects!inner(
            id,
            title,
            client_name
          )
        `)
        .eq('user_id', session.user.id);

      // Apply project filter
      if (projectId) {
        query = query.eq('project_id', projectId);
      }

      // Apply other filters
      if (filters?.category && filters.category.length > 0) {
        query = query.in('category', filters.category);
      }
      if (filters?.billable !== undefined) {
        query = query.eq('billable', filters.billable);
      }
      if (filters?.date_range) {
        query = query
          .gte('start_time', filters.date_range.start)
          .lte('start_time', filters.date_range.end);
      }

      const { data, error } = await query.order('start_time', { ascending: false });

      if (error) throw error;

      // Add calculated duration and formatting
      const entriesWithDuration: TimeEntryWithDuration[] = data.map(entry => {
        const calculatedDuration = entry.duration_minutes || 
          calculateDuration(entry.start_time, entry.end_time);
        
        return {
          ...entry,
          calculated_duration: calculatedDuration,
          formatted_duration: formatDuration(calculatedDuration)
        };
      });

      return entriesWithDuration;
    },
    enabled: true, // Auth check is done in queryFn
    staleTime: 1000 * 5, // 5 seconds for timer consistency
    refetchInterval: 1000 * 5, // 5 seconds for real-time stats
  });

  // Get running timer
  const {
    data: runningTimer,
    isLoading: isLoadingTimer
  } = useQuery({
    queryKey: ['running-timer'],
    queryFn: async (): Promise<TimeEntry | null> => {
      const { data: { session } } = await supabase.auth.getSession();
      if (!session?.user) return null;

      const { data, error } = await supabase
        .from('time_entries')
        .select(`
          *,
          projects!inner(
            id,
            title,
            client_name
          )
        `)
        .eq('user_id', session.user.id)
        .eq('is_running', true)
        .maybeSingle();

      if (error) throw error;
      return data;
    },
    enabled: true, // Auth check is done in queryFn
    staleTime: 1000 * 5, // 5 seconds
    refetchInterval: 1000 * 5, // 5 seconds for live timer updates
  });

  // Create time entry mutation
  const createEntryMutation = useMutation({
    mutationFn: async (entryData: TimeEntryInsert): Promise<TimeEntry> => {
      const { data: { session } } = await supabase.auth.getSession();
      if (!session?.user) throw new Error('User not authenticated');

      // If this is a new running timer, stop any existing running timer first
      if (entryData.is_running) {
        const { error: stopError } = await supabase
          .from('time_entries')
          .update({ 
            is_running: false,
            end_time: new Date().toISOString()
          })
          .eq('user_id', session.user.id)
          .eq('is_running', true);

        if (stopError) {
          console.warn('Could not stop existing timer:', stopError);
        }
      }

      const { data, error } = await supabase
        .from('time_entries')
        .insert({
          ...entryData,
          user_id: session.user.id
        })
        .select()
        .single();

      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['time-entries'] });
      queryClient.invalidateQueries({ queryKey: ['running-timer'] });
      queryClient.invalidateQueries({ queryKey: ['projects'] });
    },
    onError: (error) => {
      console.error('Error creating time entry:', error);
      toast.error('Fehler beim Erstellen des Zeiteintrags');
    }
  });

  // Update time entry mutation
  const updateEntryMutation = useMutation({
    mutationFn: async ({ 
      id, 
      updates 
    }: { 
      id: string; 
      updates: TimeEntryUpdate 
    }): Promise<TimeEntry> => {
      const { data: { session } } = await supabase.auth.getSession();
      if (!session?.user) throw new Error('User not authenticated');

      // Calculate duration if both start_time and end_time are provided
      if (updates.start_time && updates.end_time) {
        updates.duration_minutes = calculateDuration(updates.start_time, updates.end_time);
      }

      const { data, error } = await supabase
        .from('time_entries')
        .update(updates)
        .eq('id', id)
        .eq('user_id', session.user.id)
        .select()
        .single();

      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['time-entries'] });
      queryClient.invalidateQueries({ queryKey: ['running-timer'] });
      queryClient.invalidateQueries({ queryKey: ['projects'] });
    },
    onError: (error) => {
      console.error('Error updating time entry:', error);
      toast.error('Fehler beim Aktualisieren des Zeiteintrags');
    }
  });

  // Delete time entry mutation
  const deleteEntryMutation = useMutation({
    mutationFn: async (id: string): Promise<void> => {
      const { data: { session } } = await supabase.auth.getSession();
      if (!session?.user) throw new Error('User not authenticated');

      const { error } = await supabase
        .from('time_entries')
        .delete()
        .eq('id', id)
        .eq('user_id', session.user.id);

      if (error) throw error;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['time-entries'] });
      queryClient.invalidateQueries({ queryKey: ['running-timer'] });
      queryClient.invalidateQueries({ queryKey: ['projects'] });
      toast.success('Zeiteintrag wurde gelöscht');
    },
    onError: (error) => {
      console.error('Error deleting time entry:', error);
      toast.error('Fehler beim Löschen des Zeiteintrags');
    }
  });

  // Timer control functions
  const startTimer = useCallback(async (
    projectId: string,
    category: TimeEntryCategory = 'development',
    description?: string
  ) => {
    const entryData: TimeEntryInsert = {
      project_id: projectId,
      start_time: new Date().toISOString(),
      category,
      description,
      is_running: true,
      billable: true
    };

    createEntryMutation.mutate(entryData);
    toast.success('Timer gestartet');
  }, [createEntryMutation]);

  const stopTimer = useCallback(async (entryId?: string) => {
    const timerToStop = entryId || runningTimer?.id;
    
    if (!timerToStop) {
      toast.error('Kein laufender Timer gefunden');
      return;
    }

    const endTime = new Date().toISOString();
    const startTime = runningTimer?.start_time;
    
    // Calculate duration and ensure it's at least 1 minute to satisfy DB constraint
    let durationMinutes = startTime ? calculateDuration(startTime, endTime) : 1;
    if (durationMinutes <= 0) {
      durationMinutes = 1; // Minimum 1 minute to satisfy check constraint
    }
    
    const updates: TimeEntryUpdate = {
      is_running: false,
      end_time: endTime,
      duration_minutes: durationMinutes
    };

    updateEntryMutation.mutate({ id: timerToStop, updates });
    toast.success('Timer gestoppt');
  }, [runningTimer, updateEntryMutation]);

  const addManualEntry = useCallback(async (
    projectId: string,
    startTime: string,
    endTime: string,
    category: TimeEntryCategory = 'development',
    description?: string,
    billable: boolean = true
  ) => {
    const entryData: TimeEntryInsert = {
      project_id: projectId,
      start_time: startTime,
      end_time: endTime,
      duration_minutes: calculateDuration(startTime, endTime),
      category,
      description,
      is_running: false,
      billable
    };

    createEntryMutation.mutate(entryData);
    toast.success('Zeiteintrag hinzugefügt');
  }, [createEntryMutation]);

  // Get today's entries (including running timer)
  const todayEntries = entries.filter(entry => {
    const entryDate = new Date(entry.start_time).toDateString();
    const today = new Date().toDateString();
    return entryDate === today;
  });

  // Calculate today's total hours (including real-time running timer)
  const todayMinutes = todayEntries.reduce((total, entry) => {
    if (entry.is_running && runningTimer?.id === entry.id) {
      // For the currently running timer, calculate real-time duration
      return total + calculateDuration(entry.start_time);
    } else {
      // For completed entries, use stored duration
      return total + (entry.calculated_duration || 0);
    }
  }, 0);
  
  const todayHours = todayMinutes / 60;

  // Calculate this week's total hours (including running timer)
  const weekStart = new Date();
  weekStart.setDate(weekStart.getDate() - weekStart.getDay());
  weekStart.setHours(0, 0, 0, 0);
  
  const thisWeekMinutes = entries
    .filter(entry => new Date(entry.start_time) >= weekStart)
    .reduce((total, entry) => {
      if (entry.is_running && runningTimer?.id === entry.id) {
        // For the currently running timer, calculate real-time duration
        return total + calculateDuration(entry.start_time);
      } else {
        // For completed entries, use stored duration
        return total + (entry.calculated_duration || 0);
      }
    }, 0);
  
  const thisWeekHours = thisWeekMinutes / 60;

  return {
    // Data
    entries,
    runningTimer,
    isLoading: isLoading || isLoadingTimer,
    error,

    // Statistics
    todayHours: Math.round(todayHours * 100) / 100,
    thisWeekHours: Math.round(thisWeekHours * 100) / 100,
    totalEntries: entries.length,
    todayEntries, // Export today's entries for consistency

    // Timer controls
    startTimer,
    stopTimer,
    addManualEntry,

    // CRUD operations
    updateEntry: updateEntryMutation.mutate,
    deleteEntry: deleteEntryMutation.mutate,
    refetch,

    // Loading states
    isCreating: createEntryMutation.isPending,
    isUpdating: updateEntryMutation.isPending,
    isDeleting: deleteEntryMutation.isPending,

    // Utilities
    formatDuration,
    formatDurationWithSeconds,
    calculateDuration,
    calculateDurationInSeconds,
  };
};

// Hook for real-time timer state (with local state management)
export const useTimerState = () => {
  const [elapsedTimeInSeconds, setElapsedTimeInSeconds] = useState(0);
  const { runningTimer, todayEntries, entries } = useTimeTracking();

  useEffect(() => {
    if (!runningTimer?.is_running) {
      setElapsedTimeInSeconds(0);
      return;
    }

    const updateElapsed = () => {
      const elapsed = calculateDurationInSeconds(runningTimer.start_time);
      setElapsedTimeInSeconds(elapsed);
    };

    // Update immediately
    updateElapsed();

    // Update every second for real-time display
    const interval = setInterval(updateElapsed, 1000);

    return () => clearInterval(interval);
  }, [runningTimer]);

  const elapsedTimeInMinutes = Math.floor(elapsedTimeInSeconds / 60);

  // Calculate real-time today and week hours that include the current timer
  const realTimeTodayMinutes = todayEntries.reduce((total, entry) => {
    if (entry.is_running && runningTimer?.id === entry.id) {
      // Use exact real-time elapsed time in minutes (not rounded)
      return total + (elapsedTimeInSeconds / 60);
    } else {
      // Use stored duration for completed entries
      return total + (entry.calculated_duration || 0);
    }
  }, 0);

  const weekStart = new Date();
  weekStart.setDate(weekStart.getDate() - weekStart.getDay());
  weekStart.setHours(0, 0, 0, 0);
  
  const realTimeWeekMinutes = entries
    .filter(entry => new Date(entry.start_time) >= weekStart)
    .reduce((total, entry) => {
      if (entry.is_running && runningTimer?.id === entry.id) {
        // Use exact real-time elapsed time in minutes (not rounded)
        return total + (elapsedTimeInSeconds / 60);
      } else {
        // Use stored duration for completed entries
        return total + (entry.calculated_duration || 0);
      }
    }, 0);

  return {
    isRunning: !!runningTimer?.is_running,
    currentProject: runningTimer ? {
      id: (runningTimer as any).projects?.id,
      title: (runningTimer as any).projects?.title
    } : null,
    elapsedTime: elapsedTimeInMinutes, // minutes for compatibility
    elapsedTimeInSeconds, // seconds for timer display
    formattedElapsedTime: formatDuration(elapsedTimeInMinutes), // minutes format
    formattedElapsedTimeWithSeconds: formatDurationWithSeconds(elapsedTimeInSeconds), // with seconds
    timerId: runningTimer?.id,
    
    // Real-time statistics
    realTimeTodayHours: Math.round((realTimeTodayMinutes / 60) * 100) / 100,
    realTimeWeekHours: Math.round((realTimeWeekMinutes / 60) * 100) / 100,
  };
};