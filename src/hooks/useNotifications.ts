import { useState, useEffect, useMemo, useCallback } from 'react';
import { NotificationService } from '@/services/notificationService';
import type { 
  Notification, 
  NotificationFilters, 
  NotificationGroup,
  NotificationType 
} from '@/types/notifications';

// Singleton cache to prevent duplicate queries across components
class NotificationCache {
  private static instance: NotificationCache;
  private notifications: Notification[] = [];
  private loading = true;
  private lastFetch = 0;
  private subscribers = new Set<(data: { notifications: Notification[]; loading: boolean }) => void>();
  private cleanup: (() => void) | null = null;

  static getInstance() {
    if (!NotificationCache.instance) {
      NotificationCache.instance = new NotificationCache();
    }
    return NotificationCache.instance;
  }

  private constructor() {
    this.initialize();
  }

  private initialize() {
    // Set up single real-time subscription for all components
    this.cleanup = NotificationService.subscribe((notifications) => {
      this.notifications = notifications;
      this.loading = false;
      this.lastFetch = Date.now();
      this.notifySubscribers();
    });
  }

  subscribe(callback: (data: { notifications: Notification[]; loading: boolean }) => void) {
    this.subscribers.add(callback);
    
    // Immediately notify with current state
    callback({ notifications: this.notifications, loading: this.loading });

    return () => {
      this.subscribers.delete(callback);
      
      // Clean up if no subscribers left
      if (this.subscribers.size === 0 && this.cleanup) {
        this.cleanup();
        this.cleanup = null;
        NotificationCache.instance = null as any;
      }
    };
  }

  private notifySubscribers() {
    const data = { notifications: this.notifications, loading: this.loading };
    this.subscribers.forEach(callback => {
      try {
        callback(data);
      } catch (error) {
        console.error('Error in notification cache subscriber:', error);
      }
    });
  }

  getNotifications() {
    return { notifications: this.notifications, loading: this.loading };
  }

  // Update notifications directly and notify all subscribers
  updateNotifications(updater: (notifications: Notification[]) => Notification[]) {
    this.notifications = updater(this.notifications);
    this.notifySubscribers();
  }

  // Force refresh if data is stale (older than 30 seconds)
  async refreshIfStale() {
    if (Date.now() - this.lastFetch > 30000) {
      try {
        this.loading = true;
        this.notifySubscribers();
        const { notifications } = await NotificationService.getNotificationsWithCount();
        this.notifications = notifications;
        this.loading = false;
        this.lastFetch = Date.now();
        this.notifySubscribers();
      } catch (error) {
        this.loading = false;
        this.notifySubscribers();
        console.error('Error refreshing notifications:', error);
      }
    }
  }
}

/**
 * Hook for notifications with caching and single real-time subscription
 */
export const useNotifications = (filters?: NotificationFilters) => {
  const [state, setState] = useState<{ notifications: Notification[]; loading: boolean }>({
    notifications: [],
    loading: true
  });

  const cache = NotificationCache.getInstance();

  // Filter notifications client-side for better performance
  const filteredNotifications = useMemo(() => {
    if (!filters) return state.notifications;
    
    return state.notifications.filter(notification => {
      // Apply filters client-side
      if (filters.types?.length && !filters.types.includes(notification.type)) {
        return false;
      }
      
      if (filters.priorities?.length && !filters.priorities.includes(notification.priority)) {
        return false;
      }
      
      if (filters.status && notification.status !== filters.status) {
        return false;
      }
      
      if (filters.unread_only && notification.status !== 'unread') {
        return false;
      }
      
      if (filters.date_from && new Date(notification.created_at) < new Date(filters.date_from)) {
        return false;
      }
      
      if (filters.date_to && new Date(notification.created_at) > new Date(filters.date_to)) {
        return false;
      }
      
      return true;
    });
  }, [state.notifications, filters]);

  // Calculate unread count from cached data
  const unreadCount = useMemo(() => {
    return state.notifications.filter(n => n.status === 'unread').length;
  }, [state.notifications]);

  // Generate groups from cached data
  const groups = useMemo(() => {
    const groupMap = new Map<NotificationType, NotificationGroup>();
    
    state.notifications.forEach(notification => {
      if (!groupMap.has(notification.type)) {
        groupMap.set(notification.type, {
          type: notification.type,
          count: 0,
          latest_notification: notification,
          notifications: []
        });
      }
      
      const group = groupMap.get(notification.type)!;
      group.count++;
      group.notifications.push(notification);
      
      if (new Date(notification.created_at) > new Date(group.latest_notification.created_at)) {
        group.latest_notification = notification;
      }
    });
    
    return Array.from(groupMap.values())
      .sort((a, b) => new Date(b.latest_notification.created_at).getTime() - new Date(a.latest_notification.created_at).getTime());
  }, [state.notifications]);

  useEffect(() => {
    // Subscribe to cache updates
    const unsubscribe = cache.subscribe(setState);
    
    // Refresh if stale
    cache.refreshIfStale();
    
    return unsubscribe;
  }, [cache]);

  // Optimized action methods that don't trigger full refetch
  const markAsRead = useCallback(async (notificationId: string) => {
    try {
      await NotificationService.markAsRead(notificationId);
      // Optimistically update cache directly
      cache.updateNotifications(prev => prev.map(n => 
        n.id === notificationId 
          ? { ...n, status: 'read' as const, read_at: new Date().toISOString() }
          : n
      ));
    } catch (error) {
      console.error('Error marking notification as read:', error);
    }
  }, [cache]);

  const markAsUnread = useCallback(async (notificationId: string) => {
    try {
      await NotificationService.markAsUnread(notificationId);
      // Optimistically update cache directly
      cache.updateNotifications(prev => prev.map(n => 
        n.id === notificationId 
          ? { ...n, status: 'unread' as const, read_at: null }
          : n
      ));
    } catch (error) {
      console.error('Error marking notification as unread:', error);
    }
  }, [cache]);

  const markAllAsRead = useCallback(async () => {
    try {
      await NotificationService.markAllAsRead();
      // Optimistically update cache directly
      cache.updateNotifications(prev => prev.map(n => 
        n.status === 'unread' 
          ? { ...n, status: 'read' as const, read_at: new Date().toISOString() }
          : n
      ));
    } catch (error) {
      console.error('Error marking all notifications as read:', error);
    }
  }, [cache]);

  const clearAll = useCallback(async () => {
    try {
      await NotificationService.clearAll();
      // Optimistically update cache directly
      cache.updateNotifications(() => []);
    } catch (error) {
      console.error('Error clearing all notifications:', error);
    }
  }, [cache]);

  return {
    notifications: filteredNotifications,
    allNotifications: state.notifications,
    loading: state.loading,
    unreadCount,
    groups,
    markAsRead,
    markAsUnread,
    markAllAsRead,
    clearAll
  };
};

/**
 * Simple hook for just unread count - uses cached data
 */
export const useUnreadNotifications = () => {
  const { unreadCount, loading } = useNotifications();
  return unreadCount;
};

/**
 * Hook for notification groups
 */
export const useNotificationGroups = () => {
  const { groups, loading } = useNotifications();
  return { groups, loading };
};