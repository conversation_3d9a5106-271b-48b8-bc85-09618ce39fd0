import { useLocation } from 'react-router-dom';
import { useTranslation } from '@/hooks/useTranslation';

export interface PageConfig {
  title: string;
  description: string;
}

// Route to translation key mapping
const ROUTE_TRANSLATION_MAP: Record<string, { title: string; description: string }> = {
  '/': { title: 'dashboard.title', description: 'dashboard.subtitle' },
  '/applications': { title: 'applications.title', description: 'applications.subtitle' },
  '/projects': { title: 'projects.title', description: 'projects.subtitle' },
  '/contacts': { title: 'contacts.title', description: 'contacts.subtitle' },
  '/statistics': { title: 'statistics.title', description: 'statistics.subtitle' },
  '/follow-ups': { title: 'followups.title', description: 'followups.subtitle' },
  '/settings': { title: 'settings.title', description: 'settings.subtitle' },
  '/calendar': { title: 'calendar.title', description: 'calendar.subtitle' },
  '/projects/timer': { title: 'projects.timer_title', description: 'projects.timer_subtitle' },
  '/projects/reports': { title: 'projects.reports_title', description: 'projects.reports_subtitle' },
  '/notifications': { title: 'notifications.title', description: 'notifications.subtitle' },
  '/projects/new': { title: 'projects.create_new', description: 'projects.create_new_desc' },
  '/projects/edit/:id': { title: 'projects.edit_title', description: 'projects.edit_desc' },
  '/projects/:id': { title: 'projects.details_title', description: 'projects.details_desc' },
  '/applications/new': { title: 'applications.create_new', description: 'applications.create_new_desc' },
  '/applications/edit/:id': { title: 'applications.edit_title', description: 'applications.edit_desc' },
  '/applications/:id': { title: 'applications.details_title', description: 'applications.details_desc' },
  '/contacts/new': { title: 'contacts.create_new', description: 'contacts.create_new_desc' },
  '/contacts/edit/:id': { title: 'contacts.edit_title', description: 'contacts.edit_desc' },
  '/contacts/:id': { title: 'contacts.details_title', description: 'contacts.details_desc' }
};

/**
 * Get translation keys for a specific route
 */
const getRouteTranslationKeys = (pathname: string): { title: string; description: string } => {
  // Handle exact matches first
  if (ROUTE_TRANSLATION_MAP[pathname]) {
    return ROUTE_TRANSLATION_MAP[pathname];
  }
  
  // Handle dynamic routes with pattern matching
  if (pathname.startsWith('/projects/')) {
    if (pathname === '/projects/new') return ROUTE_TRANSLATION_MAP['/projects/new'];
    if (pathname === '/projects/timer') return ROUTE_TRANSLATION_MAP['/projects/timer'];
    if (pathname === '/projects/reports') return ROUTE_TRANSLATION_MAP['/projects/reports'];
    if (pathname.match(/^\/projects\/\d+$/)) return ROUTE_TRANSLATION_MAP['/projects/:id'];
    if (pathname.match(/^\/projects\/edit\/\d+$/)) return ROUTE_TRANSLATION_MAP['/projects/edit/:id'];
    return ROUTE_TRANSLATION_MAP['/projects'];
  }
  
  if (pathname.startsWith('/applications/')) {
    if (pathname === '/applications/new') return ROUTE_TRANSLATION_MAP['/applications/new'];
    if (pathname.match(/^\/applications\/\d+$/)) return ROUTE_TRANSLATION_MAP['/applications/:id'];
    if (pathname.match(/^\/applications\/edit\/\d+$/)) return ROUTE_TRANSLATION_MAP['/applications/edit/:id'];
    return ROUTE_TRANSLATION_MAP['/applications'];
  }
  
  if (pathname.startsWith('/contacts/')) {
    if (pathname === '/contacts/new') return ROUTE_TRANSLATION_MAP['/contacts/new'];
    if (pathname.match(/^\/contacts\/\d+$/)) return ROUTE_TRANSLATION_MAP['/contacts/:id'];
    if (pathname.match(/^\/contacts\/\d+\/edit$/)) return ROUTE_TRANSLATION_MAP['/contacts/edit/:id'];
    return ROUTE_TRANSLATION_MAP['/contacts'];
  }
  
  // Default fallback
  return ROUTE_TRANSLATION_MAP['/'];
};

/**
 * Custom hook to get consistent page configuration with translations
 * @param pathname - The current pathname (optional, uses current location if not provided)
 * @returns PageConfig object with translated title and description
 */
export const usePageConfig = (pathname?: string): PageConfig => {
  const location = useLocation();
  const targetPath = pathname || location.pathname;
  const { t } = useTranslation('pages');
  
  const translationKeys = getRouteTranslationKeys(targetPath);
  
  return {
    title: t(translationKeys.title),
    description: t(translationKeys.description)
  };
};

/**
 * Convenience hook to get page configuration for current route with translations
 * @returns PageConfig object with translated title and description for current route
 */
export const useCurrentPageConfig = (): PageConfig => {
  const location = useLocation();
  return usePageConfig(location.pathname);
};