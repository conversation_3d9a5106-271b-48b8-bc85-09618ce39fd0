import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { CreateUserSettingsData } from '@/types/settings';
import { toast } from '@/lib/toast';

export const useUserSettings = () => {
  const queryClient = useQueryClient();

  const getUserAndSettings = async () => {
    // Get current user from Supabase (still needed for user_id)
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) return null;
    
    const { data, error } = await supabase
      .from('user_settings')
      .select('*')
      .eq('user_id', user.id)
      .maybeSingle();
    
    if (error) throw error;
    return data;
  };

  const {
    data: settings,
    isLoading,
    error,
    refetch
  } = useQuery({
    queryKey: ['user-settings'],
    queryFn: getUserAndSettings
  });

  const createOrUpdateSettings = useMutation({
    mutationFn: async (settingsData: CreateUserSettingsData) => {
      // Get current user from Supabase (still needed for user_id)
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error('User not authenticated');

      // Try to update first, if no rows affected, insert
      const { data: existingSettings } = await supabase
        .from('user_settings')
        .select('id')
        .eq('user_id', user.id)
        .maybeSingle();

      if (existingSettings) {
        const { data, error } = await supabase
          .from('user_settings')
          .update(settingsData)
          .eq('user_id', user.id)
          .select()
          .single();

        if (error) throw error;
        return data;
      } else {
        const { data, error } = await supabase
          .from('user_settings')
          .insert([{ ...settingsData, user_id: user.id }])
          .select()
          .single();

        if (error) throw error;
        return data;
      }
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['user-settings'] });
      toast.success('Einstellungen gespeichert', 'Ihre Einstellungen wurden erfolgreich aktualisiert.');
    },
    onError: (error) => {
      toast.error('Fehler', 'Einstellungen konnten nicht gespeichert werden.');
      console.error('Error saving settings:', error);
    }
  });

  return {
    settings,
    isLoading,
    error,
    saveSettings: createOrUpdateSettings.mutateAsync,
    isSaving: createOrUpdateSettings.isPending,
    refetchSettings: refetch
  };
};