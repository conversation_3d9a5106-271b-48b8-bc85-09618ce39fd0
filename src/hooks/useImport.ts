import { useState } from 'react';
import { useQueryClient } from '@tanstack/react-query';
import { ImportService, ImportProgress, ImportResult, ImportOptions } from '@/services/importService';
import { ExportData } from '@/types/export';

export const useImport = () => {
  const [isImporting, setIsImporting] = useState(false);
  const [progress, setProgress] = useState<ImportProgress | null>(null);
  const [result, setResult] = useState<ImportResult | null>(null);
  const [showModal, setShowModal] = useState(false);
  const queryClient = useQueryClient();

  const importFromFile = async (file: File, options: ImportOptions = {}): Promise<ImportResult> => {
    try {
      setIsImporting(true);
      setProgress(null);
      setResult(null);
      setShowModal(true);

      // Read and validate file
      const importData = await ImportService.importFromFile(file);
      
      // Import data with progress callback
      const importResult = await ImportService.importData(importData, (progressData) => {
        setProgress(progressData);
      }, options);

      // Refresh all related queries
      await Promise.all([
        queryClient.invalidateQueries({ queryKey: ['freelance-projects'] }),
        queryClient.invalidateQueries({ queryKey: ['user-settings'] }),
        queryClient.invalidateQueries({ queryKey: ['project-activities'] }),
        queryClient.invalidateQueries({ queryKey: ['calendar-events'] })
      ]);

      setResult(importResult);
      return importResult;

    } catch (error) {
      console.error('Import error:', error);
      const errorResult = {
        success: false,
        successCount: 0,
        errorCount: 1,
        errors: [error.message || 'Unbekannter Fehler']
      };
      setResult(errorResult);
      return errorResult;
    } finally {
      setIsImporting(false);
      setProgress(null);
    }
  };

  const importFromData = async (importData: ExportData, options: ImportOptions = {}): Promise<ImportResult> => {
    try {
      setIsImporting(true);
      setProgress(null);
      setResult(null);
      setShowModal(true);

      const importResult = await ImportService.importData(importData, (progressData) => {
        setProgress(progressData);
      }, options);

      // Refresh all related queries
      await Promise.all([
        queryClient.invalidateQueries({ queryKey: ['freelance-projects'] }),
        queryClient.invalidateQueries({ queryKey: ['user-settings'] }),
        queryClient.invalidateQueries({ queryKey: ['project-activities'] }),
        queryClient.invalidateQueries({ queryKey: ['calendar-events'] })
      ]);

      setResult(importResult);
      return importResult;

    } catch (error) {
      console.error('Import error:', error);
      const errorResult = {
        success: false,
        successCount: 0,
        errorCount: 1,
        errors: [error.message || 'Unbekannter Fehler']
      };
      setResult(errorResult);
      return errorResult;
    } finally {
      setIsImporting(false);
      setProgress(null);
    }
  };

  const closeModal = () => {
    setShowModal(false);
    setResult(null);
    setProgress(null);
  };

  return {
    importFromFile,
    importFromData,
    isImporting,
    progress,
    result,
    showModal,
    closeModal
  };
};