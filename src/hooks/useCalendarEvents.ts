import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { toast } from '@/lib/toast';
import type { 
  CalendarEvent, 
  CreateCalendarEventData, 
  UpdateCalendarEventData,
  CalendarEventWithProject 
} from '@/types/calendar';

// Query key factory
const calendarKeys = {
  all: ['calendar-events'] as const,
  lists: () => [...calendarKeys.all, 'list'] as const,
  list: (filters: Record<string, any>) => [...calendarKeys.lists(), { filters }] as const,
  details: () => [...calendarKeys.all, 'detail'] as const,
  detail: (id: string) => [...calendarKeys.details(), id] as const,
};

export const useCalendarEvents = () => {
  const queryClient = useQueryClient();

  // Fetch all calendar events for the current user
  const {
    data: events = [],
    isLoading,
    error
  } = useQuery({
    queryKey: calendarKeys.lists(),
    queryFn: async (): Promise<CalendarEventWithProject[]> => {
      // Get current user from Supabase (still needed for user_id)
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error('User not authenticated');

      // Fetch calendar events with support for both project types
      const { data: calendarEvents, error } = await supabase
        .from('calendar_events')
        .select('*')
        .eq('user_id', user.id)
        .order('start_date', { ascending: true });

      if (error) {
        console.error('Error fetching calendar events:', error);
        throw error;
      }

      if (!calendarEvents || calendarEvents.length === 0) {
        return [];
      }

      // Separate events by reference type for efficient querying
      const projectEvents = calendarEvents.filter(event => 
        event.reference_type === 'project' || 
        (event.project_id && !event.reference_type)
      );
      const applicationEvents = calendarEvents.filter(event => 
        event.reference_type === 'application'
      );
      const standaloneEvents = calendarEvents.filter(event => 
        !event.reference_type && !event.project_id && !event.reference_id
      );
      
      // Log for debugging
      console.log('Calendar events breakdown:', {
        total: calendarEvents.length,
        projectEvents: projectEvents.length,
        applicationEvents: applicationEvents.length,
        standaloneEvents: standaloneEvents.length,
        nullReferences: calendarEvents.filter(e => !e.reference_type && !e.reference_id && !e.project_id).length
      });

      // Fetch project data for project events
      let projectsData: any[] = [];
      if (projectEvents.length > 0) {
        const projectIds = projectEvents
          .map(event => event.reference_id || event.project_id)
          .filter(Boolean);
        
        if (projectIds.length > 0) {
          const { data: projects } = await supabase
            .from('projects')
            .select('id, title, client_name, status')
            .in('id', projectIds);
          projectsData = projects || [];
        }
      }

      // Fetch application data for application events
      let applicationsData: any[] = [];
      if (applicationEvents.length > 0) {
        const applicationIds = applicationEvents
          .map(event => event.reference_id)
          .filter(Boolean);
        
        if (applicationIds.length > 0) {
          const { data: applications } = await supabase
            .from('project_applications')
            .select('id, project_name, company_name, status')
            .in('id', applicationIds);
          applicationsData = applications || [];
        }
      }

      // Combine events with their project/application data
      const eventsWithProjects: CalendarEventWithProject[] = calendarEvents.map(event => {
        let projectData = null;

        if (event.reference_type === 'project' || (event.project_id && !event.reference_type)) {
          const refId = event.reference_id || event.project_id;
          projectData = projectsData.find(p => p.id === refId);
        } else if (event.reference_type === 'application') {
          const appData = applicationsData.find(a => a.id === event.reference_id);
          if (appData) {
            projectData = {
              ...appData,
              title: appData.project_name,
              client_name: appData.company_name
            };
          }
        }
        // For standalone events (no references), projectData remains null

        return {
          ...event,
          project: projectData || null
        };
      });

      console.log(`Returning ${eventsWithProjects.length} calendar events (${eventsWithProjects.filter(e => e.project).length} with project data)`);
      return eventsWithProjects;
    },
    staleTime: 1000 * 60 * 5, // 5 minutes
  });

  // Fetch events for a specific date range
  const fetchEventsForDateRange = (startDate: string, endDate: string) => {
    return useQuery({
      queryKey: calendarKeys.list({ startDate, endDate }),
      queryFn: async (): Promise<CalendarEventWithProject[]> => {
        // Get current user from Supabase (still needed for user_id)
        const { data: { user } } = await supabase.auth.getUser();
        if (!user) throw new Error('User not authenticated');

        // Fetch calendar events in date range
        const { data: calendarEvents, error } = await supabase
          .from('calendar_events')
          .select('*')
          .eq('user_id', user.id)
          .gte('start_date', startDate)
          .lte('start_date', endDate)
          .order('start_date', { ascending: true });

        if (error) {
          console.error('Error fetching calendar events for date range:', error);
          throw error;
        }

        if (!calendarEvents || calendarEvents.length === 0) {
          return [];
        }

        // Use the same logic as main query for consistency
        const projectEvents = calendarEvents.filter(event => 
          event.reference_type === 'project' || 
          (event.project_id && !event.reference_type)
        );
        const applicationEvents = calendarEvents.filter(event => 
          event.reference_type === 'application'
        );

        // Fetch project data
        let projectsData: any[] = [];
        if (projectEvents.length > 0) {
          const projectIds = projectEvents
            .map(event => event.reference_id || event.project_id)
            .filter(Boolean);
          
          if (projectIds.length > 0) {
            const { data: projects } = await supabase
              .from('projects')
              .select('id, title, client_name, status')
              .in('id', projectIds);
            projectsData = projects || [];
          }
        }

        // Fetch application data
        let applicationsData: any[] = [];
        if (applicationEvents.length > 0) {
          const applicationIds = applicationEvents
            .map(event => event.reference_id)
            .filter(Boolean);
          
          if (applicationIds.length > 0) {
            const { data: applications } = await supabase
              .from('project_applications')
              .select('id, project_name, company_name, status')
              .in('id', applicationIds);
            applicationsData = applications || [];
          }
        }

        // Combine events with their data
        const eventsWithProjects: CalendarEventWithProject[] = calendarEvents.map(event => {
          let projectData = null;

          if (event.reference_type === 'project' || (event.project_id && !event.reference_type)) {
            const refId = event.reference_id || event.project_id;
            projectData = projectsData.find(p => p.id === refId);
          } else if (event.reference_type === 'application') {
            const appData = applicationsData.find(a => a.id === event.reference_id);
            if (appData) {
              projectData = {
                ...appData,
                title: appData.project_name,
                client_name: appData.company_name
              };
            }
          }

          return {
            ...event,
            project: projectData || null
          };
        });

        return eventsWithProjects;
      },
      staleTime: 1000 * 60 * 5, // 5 minutes
    });
  };

  // Create calendar event mutation
  const createEventMutation = useMutation({
    mutationFn: async (eventData: CreateCalendarEventData): Promise<CalendarEvent> => {
      // Get current user from Supabase (still needed for user_id)
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error('User not authenticated');

      console.log('Creating calendar event with data:', eventData);

      const { data, error } = await supabase
        .from('calendar_events')
        .insert([{
          ...eventData,
          user_id: user.id,
        }])
        .select()
        .single();

      if (error) {
        console.error('Supabase error details:', error);
        throw error;
      }

      return data;
    },
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: calendarKeys.all });
      toast.success('Termin erstellt', 'Der Kalendereintrag wurde erfolgreich hinzugefügt.');
    },
    onError: (error: any) => {
      console.error('Error creating calendar event:', error);
      toast.error('Fehler beim Erstellen', error.message || 'Der Kalendereintrag konnte nicht erstellt werden.');
    },
  });

  // Update calendar event mutation
  const updateEventMutation = useMutation({
    mutationFn: async (eventData: UpdateCalendarEventData): Promise<CalendarEvent> => {
      const { id, ...updateData } = eventData;

      console.log('Updating calendar event:', id, 'with data:', updateData);

      const { data, error } = await supabase
        .from('calendar_events')
        .update(updateData)
        .eq('id', id)
        .select()
        .single();

      if (error) {
        console.error('Supabase error details:', error);
        throw error;
      }

      return data;
    },
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: calendarKeys.all });
      toast.success('Termin aktualisiert', 'Der Kalendereintrag wurde erfolgreich geändert.');
    },
    onError: (error: any) => {
      console.error('Error updating calendar event:', error);
      toast.error('Fehler beim Aktualisieren', error.message || 'Der Kalendereintrag konnte nicht aktualisiert werden.');
    },
  });

  // Delete calendar event mutation  
  const deleteEventMutation = useMutation({
    mutationFn: async (eventId: string): Promise<void> => {
      console.log('Deleting calendar event:', eventId);

      const { error } = await supabase
        .from('calendar_events')
        .delete()
        .eq('id', eventId);

      if (error) {
        console.error('Supabase error details:', error);
        throw error;
      }
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: calendarKeys.all });
      toast.success('Termin gelöscht', 'Der Kalendereintrag wurde erfolgreich entfernt.');
    },
    onError: (error: any) => {
      console.error('Error deleting calendar event:', error);
      toast.error('Fehler beim Löschen', error.message || 'Der Kalendereintrag konnte nicht gelöscht werden.');
    },
  });

  // Toggle event completion
  const toggleEventCompletionMutation = useMutation({
    mutationFn: async ({ eventId, completed }: { eventId: string; completed: boolean }): Promise<CalendarEvent> => {
      const { data, error } = await supabase
        .from('calendar_events')
        .update({ completed })
        .eq('id', eventId)
        .select()
        .single();

      if (error) {
        console.error('Supabase error details:', error);
        throw error;
      }

      return data;
    },
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: calendarKeys.all });
      toast.success(data.completed ? 'Termin abgeschlossen' : 'Termin reaktiviert', data.completed 
        ? 'Der Kalendereintrag wurde als abgeschlossen markiert.'
        : 'Der Kalendereintrag wurde wieder aktiviert.');
    },
    onError: (error: any) => {
      console.error('Error toggling event completion:', error);
      toast.error('Fehler', error.message || 'Der Status konnte nicht geändert werden.');
    },
  });

  return {
    // Data
    events,
    isLoading,
    error,
    
    // Mutations
    createEvent: createEventMutation.mutateAsync,
    updateEvent: updateEventMutation.mutateAsync,
    deleteEvent: deleteEventMutation.mutateAsync,
    toggleEventCompletion: toggleEventCompletionMutation.mutateAsync,
    
    // Loading states
    isCreating: createEventMutation.isPending,
    isUpdating: updateEventMutation.isPending,
    isDeleting: deleteEventMutation.isPending,
    isToggling: toggleEventCompletionMutation.isPending,
    
    // Utilities
    fetchEventsForDateRange,
  };
};