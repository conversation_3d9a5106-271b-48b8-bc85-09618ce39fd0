import { useMutation, useQuery } from '@tanstack/react-query';
import { toast } from 'sonner';
import { useTranslation } from 'react-i18next';
import { AISummaryService } from '@/services/aiSummaryService';
import { CommunicationSummaryRequest } from '@/types/communications';

// Hook for generating AI summary
export function useCommunicationSummary() {
  const { t } = useTranslation('contacts');

  return useMutation({
    mutationFn: (request: CommunicationSummaryRequest) =>
      AISummaryService.generateLegacySummary(request),
    onSuccess: (result) => {
      if (result) {
        toast.success(t('communications.ai.success', { type: t('communications.ai.brief_summary') }));
      } else {
        toast.error(t('communications.ai.error', { type: t('communications.ai.brief_summary') }));
      }
    },
    onError: (error) => {
      console.error('Error generating summary:', error);
      toast.error(t('communications.ai.processing_error'));
    },
  });
}

// Hook for generating brief summary (auto-saved)
export function useBriefSummary() {
  return useMutation({
    mutationFn: ({ 
      communicationId, 
      contactId, 
      includeProjectContext = false 
    }: {
      communicationId: string;
      contactId: string;
      includeProjectContext?: boolean;
    }) =>
      AISummaryService.generateBriefSummary(communicationId, contactId, includeProjectContext),
    // Remove onSuccess/onError toasts - handled manually by components
  });
}

// Hook for generating detailed summary
export function useDetailedSummary() {
  return useMutation({
    mutationFn: ({ 
      communicationId, 
      contactId, 
      includeProjectContext = true 
    }: {
      communicationId: string;
      contactId: string;
      includeProjectContext?: boolean;
    }) =>
      AISummaryService.generateDetailedSummary(communicationId, contactId, includeProjectContext),
    // Remove onSuccess/onError toasts - handled manually by components
  });
}

// Hook for extracting action items
export function useActionItems() {
  return useMutation({
    mutationFn: ({ 
      communicationId, 
      contactId, 
      includeProjectContext = true 
    }: {
      communicationId: string;
      contactId: string;
      includeProjectContext?: boolean;
    }) =>
      AISummaryService.extractActionItems(communicationId, contactId, includeProjectContext),
    // Remove onSuccess/onError toasts - handled manually by components
  });
}

// Hook for generating follow-up recommendations
export function useFollowUpRecommendations() {
  return useMutation({
    mutationFn: ({ 
      communicationId, 
      contactId, 
      includeProjectContext = true 
    }: {
      communicationId: string;
      contactId: string;
      includeProjectContext?: boolean;
    }) =>
      AISummaryService.generateFollowUpRecommendations(communicationId, contactId, includeProjectContext),
    // Remove onSuccess/onError toasts - handled manually by components
  });
}

// Hook for auto-summarization
export function useAutoSummarize() {
  const { t } = useTranslation('contacts');

  return useMutation({
    mutationFn: ({ 
      communicationId, 
      contactId, 
      notesLength, 
      threshold = 200 
    }: {
      communicationId: string;
      contactId: string;
      notesLength: number;
      threshold?: number;
    }) =>
      AISummaryService.autoSummarizeIfNeeded(communicationId, contactId, notesLength, threshold),
    onSuccess: (result, variables) => {
      if (result) {
        toast.success(t('communications.ai.success', { type: t('communications.ai.brief_summary') }));
      } else if (variables.notesLength < variables.threshold!) {
        // Don't show error for short notes, it's expected
        console.log('Notes too short for auto-summarization');
      } else {
        toast.error(t('communications.ai.error', { type: t('communications.ai.brief_summary') }));
      }
    },
    onError: (error) => {
      console.error('Error in auto-summarization:', error);
      toast.error(t('communications.ai.processing_error'));
    },
  });
}

// Hook for batch summarization
export function useBatchSummarize() {
  const { t } = useTranslation('contacts');

  return useMutation({
    mutationFn: ({ 
      communications, 
      summaryType = 'brief', 
      threshold = 200 
    }: {
      communications: Array<{
        communicationId: string;
        contactId: string;
        notesLength: number;
      }>;
      summaryType?: CommunicationSummaryRequest['summary_type'];
      threshold?: number;
    }) =>
      AISummaryService.batchSummarize(communications, summaryType, threshold),
    onSuccess: (results) => {
      const successful = results.filter(r => r.summary !== null).length;
      const failed = results.filter(r => r.error).length;
      const skipped = results.filter(r => r.summary === null && r.error?.includes('too short')).length;

      if (successful > 0) {
        // Create a localized batch summary message
        let message = `${successful} ${t('communications.ai.success', { type: t('communications.ai.brief_summary') })}`;
        if (skipped > 0) message += `, ${skipped} übersprungen`;
        if (failed > 0) message += `, ${failed} Fehler`;
        toast.success(message);
      } else if (failed > 0) {
        toast.error(`${failed} ${t('communications.ai.error', { type: t('communications.ai.brief_summary') })}`);
      } else {
        toast.info('Keine Kommunikationen waren lang genug für eine Zusammenfassung');
      }
    },
    onError: (error) => {
      console.error('Error in batch summarization:', error);
      toast.error(t('communications.ai.processing_error'));
    },
  });
}

// Hook for getting AI processing statistics
export function useAIProcessingStats(
  contactId?: string,
  dateFrom?: string,
  dateTo?: string
) {
  return useQuery({
    queryKey: ['ai-processing-stats', contactId, dateFrom, dateTo],
    queryFn: () => AISummaryService.getAIProcessingStats(contactId, dateFrom, dateTo),
    staleTime: 1000 * 60 * 10, // 10 minutes
    refetchOnWindowFocus: false,
  });
}

// Hook for validating communication for AI processing
export function useValidateCommunicationForAI() {
  const { t } = useTranslation('contacts');

  return useMutation({
    mutationFn: ({ notes, communicationType }: { notes: string; communicationType: string }) =>
      Promise.resolve(AISummaryService.validateCommunicationForAI(notes, communicationType)),
    onSuccess: (result) => {
      if (!result.isValid && result.reason) {
        toast.warning(`${t('communications.ai.processing_error')}: ${result.reason}`);
      }
    },
  });
}