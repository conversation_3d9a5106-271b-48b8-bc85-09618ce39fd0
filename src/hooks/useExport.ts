import { useState } from 'react';
import { useMutation, useQuery } from '@tanstack/react-query';
import { toast } from '@/lib/toast';
import { ExportService } from '@/services/exportService';
import { Application, ApplicationActivity } from '@/types/applications';
import { UserSettings } from '@/types/settings';
import { CalendarEvent } from '@/types/calendar';
import { supabase } from '@/integrations/supabase/client';
import { 
  ExportFormat, 
  ExportOptions, 
  ExportResult, 
  ExportProgress,
  PDFExportConfig,
  ExcelExportConfig
} from '@/types/export';

interface UseExportProps {
  projects: Application[];
  settings?: UserSettings | null;
}

interface ExportMutationParams {
  format: ExportFormat;
  options?: Partial<ExportOptions>;
  pdfConfig?: Partial<PDFExportConfig>;
  excelConfig?: Partial<ExcelExportConfig>;
}

export const useExport = ({ projects, settings }: UseExportProps) => {
  const [progress, setProgress] = useState<ExportProgress | null>(null);

  // Fetch all related data for complete export
  const { data: activities = [] } = useQuery({
    queryKey: ['project-activities-export', projects.map(p => p.id)],
    queryFn: async (): Promise<ApplicationActivity[]> => {
      if (projects.length === 0) return [];
      
      // Limit to prevent memory issues
      if (projects.length > 1000) {
        console.warn('Large project export - activities may be limited');
      }
      
      const { data, error } = await supabase
        .from('application_activities')
        .select('*')
        .in('project_id', projects.map(p => p.id))
        .order('created_at', { ascending: false })
        .limit(10000); // Prevent memory issues
      
      if (error) throw error;
      return data || [];
    },
    enabled: projects.length > 0,
    staleTime: 1000 * 60 * 5, // 5 minutes - prevent unnecessary refetches
  });

  const { data: calendarEvents = [] } = useQuery({
    queryKey: ['calendar-events-export', projects.map(p => p.id)],
    queryFn: async (): Promise<CalendarEvent[]> => {
      if (projects.length === 0) return [];
      
      const { data, error } = await supabase
        .from('calendar_events')
        .select('*')
        .in('project_id', projects.map(p => p.id))
        .order('start_date', { ascending: false })
        .limit(5000); // Prevent memory issues
      
      if (error) throw error;
      return data || [];
    },
    enabled: projects.length > 0,
    staleTime: 1000 * 60 * 5, // 5 minutes - prevent unnecessary refetches
  });

  // Helper function to validate export options
  const validateExportOptions = (options: Partial<ExportOptions>): string | null => {
    if (options.dateRange?.startDate && options.dateRange?.endDate) {
      const startDate = new Date(options.dateRange.startDate);
      const endDate = new Date(options.dateRange.endDate);
      if (startDate > endDate) {
        return 'Das Startdatum muss vor dem Enddatum liegen.';
      }
    }
    return null;
  };

  // Helper function to get export format description
  const getFormatDescription = (format: ExportFormat): string => {
    switch (format) {
      case 'pdf':
        return 'PDF-Dokument';
      case 'excel':
        return 'Excel-Tabelle';
      case 'json':
        return 'JSON-Datei';
      default:
        return 'Datei';
    }
  };

  const exportMutation = useMutation({
    mutationFn: async ({ format, options = {}, pdfConfig, excelConfig }: ExportMutationParams): Promise<ExportResult> => {
      // Validate input
      if (!projects || projects.length === 0) {
        throw new Error('Keine Projekte zum Exportieren vorhanden.');
      }

      // Validate export options
      const validationError = validateExportOptions(options);
      if (validationError) {
        throw new Error(validationError);
      }

      // Set initial progress
      setProgress({
        stage: 'preparing',
        progress: 0,
        message: 'Export wird vorbereitet...'
      });

      try {
        // Filter projects based on options
        let filteredProjects = projects;
        
        // Apply date range filter if specified
        if (options.dateRange?.startDate || options.dateRange?.endDate) {
          filteredProjects = projects.filter(project => {
            const projectDate = new Date(project.created_at);
            const startDate = options.dateRange?.startDate ? new Date(options.dateRange.startDate) : null;
            const endDate = options.dateRange?.endDate ? new Date(options.dateRange.endDate) : null;
            
            if (startDate && projectDate < startDate) return false;
            if (endDate && projectDate > endDate) return false;
            return true;
          });
        }

        // Apply status filter if specified
        if (options.statusFilter && options.statusFilter.length > 0) {
          filteredProjects = filteredProjects.filter(project => 
            options.statusFilter!.includes(project.status)
          );
        }

        // Warn if exporting a large number of projects
        if (filteredProjects.length > 1000) {
          toast.info('Großer Export', `Sie exportieren ${filteredProjects.length} Projekte. Dies kann einige Zeit dauern.`);
        }

        setProgress({
          stage: 'processing',
          progress: 25,
          message: `${filteredProjects.length} Projekte werden verarbeitet...`
        });

        // Generate export based on format
        let result: ExportResult;
        
        setProgress({
          stage: 'generating',
          progress: 50,
          message: `${format.toUpperCase()}-Datei wird erstellt...`
        });

        switch (format) {
          case 'pdf':
            result = await ExportService.generatePDF(
              filteredProjects,
              options.includeSettings ? settings : undefined,
              options,
              pdfConfig
            );
            break;
          case 'excel':
            result = await ExportService.generateExcel(
              filteredProjects,
              options.includeSettings ? settings : undefined,
              options,
              excelConfig
            );
            break;
          case 'json':
            result = await generateJSONExport(filteredProjects, settings, activities, calendarEvents, options);
            break;
          default:
            throw new Error(`Unsupported export format: ${format}`);
        }

        if (result.success) {
          setProgress({
            stage: 'complete',
            progress: 100,
            message: 'Export erfolgreich abgeschlossen!'
          });
        } else {
          setProgress({
            stage: 'error',
            progress: 0,
            message: result.error || 'Export fehlgeschlagen'
          });
        }

        return result;
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Unbekannter Fehler beim Export';
        setProgress({
          stage: 'error',
          progress: 0,
          message: errorMessage
        });
        throw error;
      }
    },
    onSuccess: (result, variables) => {
      if (result.success) {
        const formatDesc = getFormatDescription(variables.format);
        toast.success('Export erfolgreich', `${formatDesc} "${result.fileName}" wurde heruntergeladen.`);
      } else {
        toast.error('Export fehlgeschlagen', result.error || 'Ein unbekannter Fehler ist aufgetreten.');
      }
      // Clear progress after a short delay
      setTimeout(() => setProgress(null), 2000);
    },
    onError: (error) => {
      const errorMessage = error instanceof Error ? error.message : 'Unbekannter Fehler beim Export';
      toast.error('Export fehlgeschlagen', errorMessage);
      setProgress({
        stage: 'error',
        progress: 0,
        message: errorMessage
      });
      // Clear progress after a short delay
      setTimeout(() => setProgress(null), 3000);
    },
  });

  // Helper function for JSON export (existing functionality)
  const generateJSONExport = async (
    projects: Application[],
    settings?: UserSettings | null,
    allActivities: ApplicationActivity[] = [],
    allCalendarEvents: CalendarEvent[] = [],
    options?: Partial<ExportOptions>
  ): Promise<ExportResult> => {
    try {
      // Filter activities and calendar events for exported projects
      const projectIds = new Set(projects.map(p => p.id));
      const filteredActivities = allActivities.filter(activity => 
        projectIds.has(activity.project_id)
      );
      const filteredCalendarEvents = allCalendarEvents.filter(event => 
        event.project_id && projectIds.has(event.project_id)
      );

      const exportData = {
        projects,
        settings: options?.includeSettings ? settings : null,
        activities: filteredActivities,
        calendarEvents: filteredCalendarEvents,
        exportDate: new Date().toISOString(),
        version: '2.0', // Updated version to indicate new format
        totalProjects: projects.length,
      };

      const dataStr = JSON.stringify(exportData, null, 2);
      const dataBlob = new Blob([dataStr], { type: 'application/json' });
      const url = URL.createObjectURL(dataBlob);
      
      const timestamp = new Date().toISOString().split('T')[0];
      const fileName = `freelance-tracker-export-${timestamp}.json`;
      
      const link = document.createElement('a');
      link.href = url;
      link.download = fileName;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);

      return {
        success: true,
        fileName,
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Fehler beim JSON-Export',
      };
    }
  };

  // Convenience methods for different export formats
  const exportToPDF = (options?: Partial<ExportOptions>, config?: Partial<PDFExportConfig>) => {
    return exportMutation.mutateAsync({ 
      format: 'pdf', 
      options, 
      pdfConfig: config 
    });
  };

  const exportToExcel = (options?: Partial<ExportOptions>, config?: Partial<ExcelExportConfig>) => {
    return exportMutation.mutateAsync({ 
      format: 'excel', 
      options, 
      excelConfig: config 
    });
  };

  const exportToJSON = (options?: Partial<ExportOptions>) => {
    return exportMutation.mutateAsync({ 
      format: 'json', 
      options 
    });
  };

  return {
    // Export functions
    exportToPDF,
    exportToExcel,
    exportToJSON,
    
    // State
    isExporting: exportMutation.isPending,
    progress,
    
    // Generic export function
    exportData: exportMutation.mutateAsync,
    
    // Reset progress manually if needed
    clearProgress: () => setProgress(null),
  };
};
