import { useState, useEffect, useCallback } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { Contact, CreateContactData, UpdateContactData, ContactAnalytics } from '@/types/applications';
import { toast } from '@/lib/toast';
import { ContactService } from '@/services/contactService';

export const useContacts = () => {
  const [contacts, setContacts] = useState<Contact[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isCreating, setIsCreating] = useState(false);
  const [isUpdating, setIsUpdating] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);

  // Fetch all contacts
  const fetchContacts = async () => {
    try {
      setIsLoading(true);
      
      const { data, error } = await supabase
        .from('contacts')
        .select('*')
        .order('created_at', { ascending: false });

      if (error) throw error;
      setContacts(data || []);
    } catch (error) {
      console.error('Error fetching contacts:', error);
      toast.error('<PERSON><PERSON> beim <PERSON> der Kontakte', 'Die Kontakte konnten nicht geladen werden.');
    } finally {
      setIsLoading(false);
    }
  };

  // Create new contact
  const createContact = async (contactData: CreateContactData): Promise<Contact | null> => {
    try {
      setIsCreating(true);
      
      // Get current user from Supabase (still needed for user_id)
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        throw new Error('Benutzer nicht authentifiziert');
      }

      const { data, error } = await supabase
        .from('contacts')
        .insert([{
          ...contactData,
          user_id: user.id
        }])
        .select()
        .single();

      if (error) throw error;

      const newContact = data as Contact;
      setContacts(prev => [newContact, ...prev]);
      
      toast.success('Kontakt erstellt', `Kontakt "${newContact.name || newContact.email}" wurde erfolgreich erstellt.`);

      return newContact;
    } catch (error) {
      console.error('Error creating contact:', error);
      toast.error('Fehler beim Erstellen', 'Der Kontakt konnte nicht erstellt werden.');
      return null;
    } finally {
      setIsCreating(false);
    }
  };

  // Update existing contact
  const updateContact = async (updateData: UpdateContactData): Promise<boolean> => {
    try {
      setIsUpdating(true);
      const { id, ...updates } = updateData;
      
      const { error } = await supabase
        .from('contacts')
        .update({
          ...updates,
          updated_at: new Date().toISOString()
        })
        .eq('id', id);

      if (error) throw error;

      // Update local state
      setContacts(prev => 
        prev.map(contact => 
          contact.id === id 
            ? { ...contact, ...updates, updated_at: new Date().toISOString() }
            : contact
        )
      );

      toast.success('Kontakt aktualisiert', 'Die Kontaktdaten wurden erfolgreich gespeichert.');

      return true;
    } catch (error) {
      console.error('Error updating contact:', error);
      toast.error('Fehler beim Aktualisieren', 'Der Kontakt konnte nicht aktualisiert werden.');
      return false;
    } finally {
      setIsUpdating(false);
    }
  };

  // Delete contact
  const deleteContact = async (contactId: string): Promise<boolean> => {
    try {
      setIsDeleting(true);
      
      // Check if contact has associated applications
      const { data: applications, error: applicationsError } = await supabase
        .from('project_applications')
        .select('id')
        .eq('contact_id', contactId);

      if (applicationsError) throw applicationsError;

      // Check if contact has associated active projects
      const { data: projects, error: projectsError } = await supabase
        .from('projects')
        .select('id')
        .eq('contact_id', contactId);

      if (projectsError) throw projectsError;

      const totalLinkedItems = (applications?.length || 0) + (projects?.length || 0);

      if (totalLinkedItems > 0) {
        // Set contact_id to null for associated applications and projects
        if (applications && applications.length > 0) {
          const { error: updateApplicationsError } = await supabase
            .from('project_applications')
            .update({ contact_id: null })
            .eq('contact_id', contactId);

          if (updateApplicationsError) throw updateApplicationsError;
        }

        if (projects && projects.length > 0) {
          const { error: updateProjectsError } = await supabase
            .from('projects')
            .update({ contact_id: null })
            .eq('contact_id', contactId);

          if (updateProjectsError) throw updateProjectsError;
        }
      }

      // Delete the contact
      const { error } = await supabase
        .from('contacts')
        .delete()
        .eq('id', contactId);

      if (error) throw error;

      // Update local state
      setContacts(prev => prev.filter(contact => contact.id !== contactId));

      toast.success('Kontakt gelöscht', totalLinkedItems > 0 
        ? `Kontakt wurde gelöscht. ${totalLinkedItems} verknüpfte Einträge wurden vom Kontakt getrennt.`
        : 'Kontakt wurde erfolgreich gelöscht.');

      return true;
    } catch (error) {
      console.error('Error deleting contact:', error);
      toast.error('Fehler beim Löschen', 'Der Kontakt konnte nicht gelöscht werden.');
      return false;
    } finally {
      setIsDeleting(false);
    }
  };

  // Get contact by ID
  const getContactById = (contactId: string): Contact | undefined => {
    return contacts.find(contact => contact.id === contactId);
  };

  // Get contacts by company
  const getContactsByCompany = (company: string): Contact[] => {
    return contacts.filter(contact => 
      contact.company?.toLowerCase().includes(company.toLowerCase())
    );
  };

  // Search contacts
  const searchContacts = (searchTerm: string): Contact[] => {
    if (!searchTerm.trim()) return contacts;
    
    const term = searchTerm.toLowerCase();
    return contacts.filter(contact =>
      contact.name?.toLowerCase().includes(term) ||
      contact.email?.toLowerCase().includes(term) ||
      contact.company?.toLowerCase().includes(term) ||
      contact.phone?.includes(term)
    );
  };

  // Get contact analytics - memoized to prevent infinite re-renders
  const getContactAnalytics = useCallback(async (contactId: string): Promise<ContactAnalytics | null> => {
    try {
      const contact = getContactById(contactId);
      if (!contact) return null;

      // Fetch applications for this contact
      const { data: applications, error: applicationsError } = await supabase
        .from('project_applications')
        .select('*')
        .eq('contact_id', contactId)
        .order('created_at', { ascending: false });

      if (applicationsError) throw applicationsError;

      // Fetch active projects for this contact
      const { data: activeProjects, error: projectsError } = await supabase
        .from('projects')
        .select('*')
        .eq('contact_id', contactId)
        .order('created_at', { ascending: false });

      if (projectsError) throw projectsError;

      // Combine both types of projects for analytics
      const allProjects = [...(applications || []), ...(activeProjects || [])];
      
      // Calculate analytics based on applications (main source)
      const totalApplications = applications?.length || 0;
      const successfulApplications = applications?.filter(p => 
        p.status === 'offer_received' || p.status === 'project_completed'
      ).length || 0;
      
      const successRate = totalApplications > 0 ? (successfulApplications / totalApplications) * 100 : 0;
      
      // Calculate average project value from applications (if budget_range contains numbers)
      const applicationsWithBudget = applications?.filter(p => p.budget_range) || [];
      const avgProjectValue = applicationsWithBudget.length > 0 
        ? applicationsWithBudget.reduce((sum, p) => {
            // Try to extract number from budget_range (rough estimation)
            const match = p.budget_range?.match(/(\d+)/);
            return sum + (match ? parseInt(match[1]) : 0);
          }, 0) / applicationsWithBudget.length
        : undefined;

      // Get the most recent activity from both types
      const mostRecentApplication = applications?.[0];
      const mostRecentProject = activeProjects?.[0];
      
      let lastContactDate = mostRecentApplication?.updated_at;
      let mostRecentProjectName = mostRecentApplication?.project_name;
      
      if (mostRecentProject && (!lastContactDate || mostRecentProject.updated_at > lastContactDate)) {
        lastContactDate = mostRecentProject.updated_at;
        mostRecentProjectName = mostRecentProject.title;
      }

      const analytics: ContactAnalytics = {
        ...contact,
        stats: {
          total_projects: totalApplications + (activeProjects?.length || 0),
          successful_projects: successfulApplications,
          success_rate: Math.round(successRate * 100) / 100,
          avg_project_value: avgProjectValue,
          last_contact_date: lastContactDate,
          most_recent_project: mostRecentProjectName
        },
        recent_applications: applications?.slice(0, 5) || []
      };

      return analytics;
    } catch (error) {
      console.error('Error fetching contact analytics:', error);
      return null;
    }
  }, [contacts]); // Only re-create when contacts change

  // Update contact statistics
  const updateContactStatistics = async (contactId: string): Promise<boolean> => {
    try {
      // Use the ContactService to update statistics
      const success = await ContactService.updateContactStatistics(contactId);

      if (success) {
        // Fetch the updated contact from database to sync local state
        const { data: updatedContact, error } = await supabase
          .from('contacts')
          .select('*')
          .eq('id', contactId)
          .single();

        if (!error && updatedContact) {
          // Update local state
          setContacts(prev => 
            prev.map(contact => 
              contact.id === contactId ? updatedContact : contact
            )
          );
        }
      }

      return success;
    } catch (error) {
      console.error('Error updating contact statistics:', error);
      return false;
    }
  };

  // Recalculate all contact statistics
  const recalculateAllContactStatistics = async (): Promise<boolean> => {
    try {
      setIsLoading(true);
      
      for (const contact of contacts) {
        await updateContactStatistics(contact.id);
      }
      
      return true;
    } catch (error) {
      console.error('Error recalculating contact statistics:', error);
      return false;
    } finally {
      setIsLoading(false);
    }
  };

  // Initialize
  useEffect(() => {
    fetchContacts();
  }, []);

  return {
    contacts,
    isLoading,
    isCreating,
    isUpdating,
    isDeleting,
    fetchContacts,
    createContact,
    updateContact,
    deleteContact,
    getContactById,
    getContactsByCompany,
    searchContacts,
    getContactAnalytics,
    updateContactStatistics,
    recalculateAllContactStatistics
  };
};