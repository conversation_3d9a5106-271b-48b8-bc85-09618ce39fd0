// Central configuration for page titles and descriptions
// This ensures consistency across all components that display page metadata
// NOTE: This file is now deprecated - use usePageConfig hook with translations instead

export interface PageConfig {
  title: string;
  description: string;
}

export interface PageConfigs {
  [key: string]: PageConfig;
}

// DEPRECATED: These are fallback configurations with hardcoded strings
// Use usePageConfig hook with i18n translations instead
export const PAGE_CONFIGS: PageConfigs = {
  '/': {
    title: 'Dashboard',
    description: 'Project overview and key metrics'
  },
  '/applications': {
    title: 'Applications',
    description: 'Manage your project applications'
  },
  '/projects': {
    title: 'Active Projects',
    description: 'Manage your active projects'
  },
  '/contacts': {
    title: 'Contacts',
    description: 'Manage your business contacts'
  },
  '/statistics': {
    title: 'Statistics & Analytics',
    description: 'Detailed analysis of your application activities'
  },
  '/follow-ups': {
    title: 'Follow-ups',
    description: 'All scheduled follow-ups and analytics in chronological order'
  },
  '/settings': {
    title: 'Settings',
    description: 'Profile and application settings'
  },
  '/calendar': {
    title: 'Calendar',
    description: 'Events and important dates'
  },
  '/projects/timer': {
    title: 'Time Tracking',
    description: 'Manage your working time and start timers for your projects'
  },
  '/projects/reports': {
    title: 'Reports',
    description: 'Detailed reports and analyses of your projects'
  },
  '/notifications': {
    title: 'Notifications',
    description: 'Manage all your notifications and reminders'
  },
  '/projects/new': {
    title: 'New Project',
    description: 'Create a new active project'
  },
  '/projects/edit/:id': {
    title: 'Edit Project',
    description: 'Edit an active project details'
  },
  '/projects/:id': {
    title: 'Project Details',
    description: 'Detailed view of an active project'
  },
  '/applications/new': {
    title: 'New Application',
    description: 'Create a new project application'
  },
  '/applications/edit/:id': {
    title: 'Edit Application',
    description: 'Edit an existing application'
  },
  '/applications/:id': {
    title: 'Application Details',
    description: 'Detailed view of an application'
  },
  '/contacts/new': {
    title: 'New Contact',
    description: 'Create a new contact'
  },
  '/contacts/edit/:id': {
    title: 'Edit Contact',
    description: 'Edit an existing contact'
  },
  '/contacts/:id': {
    title: 'Contact Details',
    description: 'Detailed view of a contact'
  }
};

// DEPRECATED: Helper function to get page config by pathname
export const getPageConfig = (pathname: string): PageConfig => {
  return PAGE_CONFIGS[pathname] || PAGE_CONFIGS['/'];
};

// DEPRECATED: Helper function for route matching with dynamic segments
export const getPageConfigByRoute = (pathname: string): PageConfig => {
  // Handle exact matches first
  if (PAGE_CONFIGS[pathname]) {
    return PAGE_CONFIGS[pathname];
  }
  
  // Handle dynamic routes with pattern matching
  // Projects routes
  if (pathname.startsWith('/projects/')) {
    if (pathname === '/projects/new') return PAGE_CONFIGS['/projects/new'];
    if (pathname === '/projects/timer') return PAGE_CONFIGS['/projects/timer'];
    if (pathname === '/projects/reports') return PAGE_CONFIGS['/projects/reports'];
    if (pathname.match(/^\/projects\/\d+$/)) return PAGE_CONFIGS['/projects/:id'];
    if (pathname.match(/^\/projects\/edit\/\d+$/)) return PAGE_CONFIGS['/projects/edit/:id'];
    return PAGE_CONFIGS['/projects'];
  }
  
  // Applications routes
  if (pathname.startsWith('/applications/')) {
    if (pathname === '/applications/new') return PAGE_CONFIGS['/applications/new'];
    if (pathname.match(/^\/applications\/\d+$/)) return PAGE_CONFIGS['/applications/:id'];
    if (pathname.match(/^\/applications\/edit\/\d+$/)) return PAGE_CONFIGS['/applications/edit/:id'];
    return PAGE_CONFIGS['/applications'];
  }
  
  // Contacts routes
  if (pathname.startsWith('/contacts/')) {
    if (pathname === '/contacts/new') return PAGE_CONFIGS['/contacts/new'];
    if (pathname.match(/^\/contacts\/\d+$/)) return PAGE_CONFIGS['/contacts/:id'];
    if (pathname.match(/^\/contacts\/\d+\/edit$/)) return PAGE_CONFIGS['/contacts/edit/:id'];
    return PAGE_CONFIGS['/contacts'];
  }
  
  // Default fallback
  return PAGE_CONFIGS['/'];
};