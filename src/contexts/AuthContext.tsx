import { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import { User, Session } from '@supabase/supabase-js';
import { supabase } from '@/integrations/supabase/client';

interface AuthContextType {
  user: User | null;
  session: Session | null;
  isLoading: boolean;
  isAuthenticated: boolean;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider = ({ children }: AuthProviderProps) => {
  const [user, setUser] = useState<User | null>(null);
  const [session, setSession] = useState<Session | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // Get initial session
    const initializeAuth = async () => {
      try {
        const { data: { session: initialSession } } = await supabase.auth.getSession();
        setSession(initialSession);
        setUser(initialSession?.user ?? null);
      } catch (error) {
        console.error('Error getting initial session:', error);
      } finally {
        setIsLoading(false);
      }
    };

    initializeAuth();

    // Listen for auth changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        // Only log important auth events in development
        if (process.env.NODE_ENV === 'development' && event !== 'INITIAL_SESSION') {
          console.log('Auth state changed:', event);
        }
        
        // Handle new Google sign-ins
        if (event === 'SIGNED_IN' && session?.user) {
          const user = session.user;
          
          // Check if this is a new user (first time signing in)
          const isNewUser = user.created_at === user.updated_at || 
                           !user.email_confirmed_at;
          
          // If it's a new Google user, create user settings
          if (isNewUser && user.user_metadata && user.app_metadata?.provider === 'google') {
            try {
              const { error: settingsError } = await supabase
                .from('user_settings')
                .insert([{
                  user_id: user.id,
                  full_name: user.user_metadata.full_name || 
                           user.user_metadata.name || 
                           user.email?.split('@')[0] || 'Google User'
                }]);
              
              if (settingsError) {
                console.warn('Failed to create user settings for Google user:', settingsError);
              }
            } catch (error) {
              console.warn('Error creating user settings for Google user:', error);
            }
          }
        }
        
        setSession(session);
        setUser(session?.user ?? null);
        setIsLoading(false);
      }
    );

    return () => {
      subscription.unsubscribe();
    };
  }, []);

  const value: AuthContextType = {
    user,
    session,
    isLoading,
    isAuthenticated: !!user,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};