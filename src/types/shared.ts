// Shared activity types that can be used across different entities
export type SharedActivityType = 
  // Kontakt-Management (Übergreifend)
  | 'contact_created'
  | 'contact_updated'
  | 'contact_deleted'
  // Daten-Management (Übergreifend)
  | 'data_imported'
  | 'data_exported'
  | 'bulk_operation_completed'
  // Termine/Kalender (Übergreifend)
  | 'calendar_event_created'
  | 'calendar_event_updated'
  | 'calendar_event_deleted'
  // Follow-up Management (Übergreifend)
  | 'followup_scheduled'
  | 'followup_sent'
  | 'followup_marked_as_sent'
  | 'followup_deleted'
  // System (Übergreifend)
  | 'file_uploaded'
  | 'settings_updated'
  // Legacy activity types (for backward compatibility with old data)
  | 'status_changed'
  | 'created'
  | 'updated'
  | 'deleted';

export const SHARED_ACTIVITY_COLORS: Record<SharedActivityType, string> = {
  // Kontakt-Management (Übergreifend)
  contact_created: 'bg-purple-500/10 text-purple-600 border-purple-500/20',
  contact_updated: 'bg-purple-500/10 text-purple-600 border-purple-500/20',
  contact_deleted: 'bg-red-500/10 text-red-600 border-red-500/20',
  // Daten-Management (Übergreifend)
  data_imported: 'bg-indigo-500/10 text-indigo-600 border-indigo-500/20',
  data_exported: 'bg-teal-500/10 text-teal-600 border-teal-500/20',
  bulk_operation_completed: 'bg-slate-500/10 text-slate-600 border-slate-500/20',
  // Termine/Kalender (Übergreifend)
  calendar_event_created: 'bg-pink-500/10 text-pink-600 border-pink-500/20',
  calendar_event_updated: 'bg-pink-500/10 text-pink-600 border-pink-500/20',
  calendar_event_deleted: 'bg-red-500/10 text-red-600 border-red-500/20',
  // Follow-up Management (Übergreifend)
  followup_scheduled: 'bg-amber-500/10 text-amber-600 border-amber-500/20',
  followup_sent: 'bg-emerald-500/10 text-emerald-600 border-emerald-500/20',
  followup_marked_as_sent: 'bg-lime-500/10 text-lime-600 border-lime-500/20',
  followup_deleted: 'bg-red-500/10 text-red-600 border-red-500/20',
  // System (Übergreifend)
  file_uploaded: 'bg-sky-500/10 text-sky-600 border-sky-500/20',
  settings_updated: 'bg-gray-500/10 text-gray-600 border-gray-500/20',
  // Legacy activity types (for backward compatibility with old data)
  status_changed: 'bg-primary/10 text-primary border-primary/20',
  created: 'bg-blue-500/10 text-blue-600 border-blue-500/20',
  updated: 'bg-orange-500/10 text-orange-600 border-orange-500/20',
  deleted: 'bg-red-500/10 text-red-600 border-red-500/20'
};

export const SHARED_ACTIVITY_LABELS: Record<SharedActivityType, string> = {
  // Kontakt-Management (Übergreifend)
  contact_created: 'Kontakt erstellt',
  contact_updated: 'Kontakt aktualisiert',
  contact_deleted: 'Kontakt gelöscht',
  // Daten-Management (Übergreifend)
  data_imported: 'Daten importiert',
  data_exported: 'Daten exportiert',
  bulk_operation_completed: 'Bulk-Operation abgeschlossen',
  // Termine/Kalender (Übergreifend)
  calendar_event_created: 'Termin erstellt',
  calendar_event_updated: 'Termin aktualisiert',
  calendar_event_deleted: 'Termin gelöscht',
  // Follow-up Management (Übergreifend)
  followup_scheduled: 'Follow-up geplant',
  followup_sent: 'Follow-up gesendet',
  followup_marked_as_sent: 'Follow-up als gesendet markiert',
  followup_deleted: 'Follow-up gelöscht',
  // System (Übergreifend)
  file_uploaded: 'Datei hochgeladen',
  settings_updated: 'Einstellungen aktualisiert',
  // Legacy activity types (for backward compatibility with old data)
  status_changed: 'Status geändert',
  created: 'Erstellt',
  updated: 'Aktualisiert',
  deleted: 'Gelöscht'
};

// Combined types and exports for the shared dashboard
import { ApplicationActivityType, APPLICATION_ACTIVITY_COLORS, APPLICATION_ACTIVITY_LABELS } from './applications';
import { ProjectActivityType, PROJECT_ACTIVITY_COLORS, PROJECT_ACTIVITY_LABELS } from './projects';

// Combined activity type for the ActivityService and shared dashboard
export type ActivityType = ApplicationActivityType | ProjectActivityType | SharedActivityType;

// Combined colors and labels for all activity types - used in shared dashboard
export const ALL_ACTIVITY_COLORS: Record<ActivityType, string> = {
  ...APPLICATION_ACTIVITY_COLORS,
  ...PROJECT_ACTIVITY_COLORS,
  ...SHARED_ACTIVITY_COLORS
};

export const ALL_ACTIVITY_LABELS: Record<ActivityType, string> = {
  ...APPLICATION_ACTIVITY_LABELS,
  ...PROJECT_ACTIVITY_LABELS,
  ...SHARED_ACTIVITY_LABELS
};

