// Contact Communications Types
// Handles communication history with contacts (table: contact_communications)

import { Database } from '@/integrations/supabase/types'
import { useTranslation } from 'react-i18next'

// Database table types (will be available after migration is applied)
export type CommunicationRow = Database['public']['Tables']['contact_communications']['Row']
export type CommunicationInsert = Database['public']['Tables']['contact_communications']['Insert']
export type CommunicationUpdate = Database['public']['Tables']['contact_communications']['Update']

// Communication type enum matching database constraint
export type CommunicationType = 
  | 'call'
  | 'email' 
  | 'meeting'
  | 'message'
  | 'whatsapp'
  | 'linkedin'
  | 'sms'
  | 'other'

// Main interface for contact communications
export interface ContactCommunication {
  id: string
  contact_id: string
  user_id: string
  communication_type: CommunicationType
  subject?: string
  notes: string
  summarized_notes?: string
  communication_date: string // ISO timestamp
  duration_minutes?: number
  project_id?: string
  is_project_related: boolean
  created_at: string
  updated_at: string
}

// Data for creating new communications
export interface CreateCommunicationData {
  contact_id: string
  communication_type: CommunicationType
  subject?: string
  notes: string
  communication_date: string
  duration_minutes?: number
  project_id?: string
  is_project_related?: boolean
}

// Data for updating existing communications
export interface UpdateCommunicationData {
  id: string
  communication_type?: CommunicationType
  subject?: string
  notes?: string
  summarized_notes?: string
  communication_date?: string
  duration_minutes?: number
  project_id?: string
  is_project_related?: boolean
}

// Communication with related contact and project data
export interface CommunicationWithDetails extends ContactCommunication {
  contact: {
    id: string
    name?: string
    company?: string
    email?: string
  }
  project?: {
    id: string
    project_name: string
    company_name: string
  }
}

// Communication statistics for contacts
export interface CommunicationStats {
  total_communications: number
  last_communication_date?: string
  communication_by_type: Record<CommunicationType, number>
  recent_communications: ContactCommunication[]
}

// AI summary request for communications
export interface CommunicationSummaryRequest {
  communication_id: string
  contact_id: string
  summary_type: 'brief' | 'detailed' | 'action_items' | 'follow_up'
  context_include_project?: boolean
}

// AI summary response
export interface CommunicationSummaryResponse {
  summary: string
  summary_type: string
  communication_id: string
  contact_name?: string
}

// Communication filtering and search
export interface CommunicationFilters {
  contact_id?: string
  communication_type?: CommunicationType
  project_id?: string
  is_project_related?: boolean
  date_from?: string
  date_to?: string
  search_text?: string
}

// Pagination parameters
export interface PaginationParams {
  page: number
  limit: number
  offset?: number
}

// Paginated response wrapper
export interface PaginatedResponse<T> {
  data: T[]
  pagination: {
    page: number
    limit: number
    total: number
    totalPages: number
    hasNextPage: boolean
    hasPreviousPage: boolean
  }
}

// Communication query with pagination
export interface CommunicationQuery extends CommunicationFilters, PaginationParams {}

// Communication form data with validation
export interface CommunicationFormData {
  contact_id: string
  communication_type: CommunicationType
  subject: string
  notes: string
  communication_date: Date
  communication_time: string
  duration_minutes: number | null
  project_id: string | null
  is_project_related: boolean
}

// Communication type metadata for UI
export interface CommunicationTypeInfo {
  type: CommunicationType
  label: string
  icon: string
  color: string
  supports_duration: boolean
  requires_subject: boolean
}

// Communication type configuration without hardcoded labels
export const COMMUNICATION_TYPE_CONFIG: Record<CommunicationType, Omit<CommunicationTypeInfo, 'label'>> = {
  call: {
    type: 'call',
    icon: 'Phone',
    color: 'blue',
    supports_duration: true,
    requires_subject: false
  },
  email: {
    type: 'email',
    icon: 'Mail',
    color: 'green',
    supports_duration: false,
    requires_subject: true
  },
  meeting: {
    type: 'meeting',
    icon: 'Users',
    color: 'purple',
    supports_duration: true,
    requires_subject: true
  },
  message: {
    type: 'message',
    icon: 'MessageCircle',
    color: 'orange',
    supports_duration: false,
    requires_subject: false
  },
  whatsapp: {
    type: 'whatsapp',
    icon: 'MessageCircle',
    color: 'green',
    supports_duration: false,
    requires_subject: false
  },
  linkedin: {
    type: 'linkedin',
    icon: 'Linkedin',
    color: 'blue',
    supports_duration: false,
    requires_subject: false
  },
  sms: {
    type: 'sms',
    icon: 'Smartphone',
    color: 'gray',
    supports_duration: false,
    requires_subject: false
  },
  other: {
    type: 'other',
    icon: 'MoreHorizontal',
    color: 'gray',
    supports_duration: false,
    requires_subject: false
  }
}

// Helper function to get localized communication types
export const useCommunicationTypes = (): Record<CommunicationType, CommunicationTypeInfo> => {
  const { t } = useTranslation('contacts')
  
  const types: Record<CommunicationType, CommunicationTypeInfo> = {} as Record<CommunicationType, CommunicationTypeInfo>
  
  Object.entries(COMMUNICATION_TYPE_CONFIG).forEach(([key, config]) => {
    types[key as CommunicationType] = {
      ...config,
      label: t(`communications.types.${key}`)
    }
  })
  
  return types
}

// Legacy constant for backward compatibility - will be deprecated
// Use useCommunicationTypes() hook instead
export const COMMUNICATION_TYPES: Record<CommunicationType, CommunicationTypeInfo> = {
  call: {
    type: 'call',
    label: 'Call', // Fallback English label
    icon: 'Phone',
    color: 'blue',
    supports_duration: true,
    requires_subject: false
  },
  email: {
    type: 'email',
    label: 'Email',
    icon: 'Mail',
    color: 'green',
    supports_duration: false,
    requires_subject: true
  },
  meeting: {
    type: 'meeting',
    label: 'Meeting',
    icon: 'Users',
    color: 'purple',
    supports_duration: true,
    requires_subject: true
  },
  message: {
    type: 'message',
    label: 'Message',
    icon: 'MessageCircle',
    color: 'orange',
    supports_duration: false,
    requires_subject: false
  },
  whatsapp: {
    type: 'whatsapp',
    label: 'WhatsApp',
    icon: 'MessageCircle',
    color: 'green',
    supports_duration: false,
    requires_subject: false
  },
  linkedin: {
    type: 'linkedin',
    label: 'LinkedIn',
    icon: 'Linkedin',
    color: 'blue',
    supports_duration: false,
    requires_subject: false
  },
  sms: {
    type: 'sms',
    label: 'SMS',
    icon: 'Smartphone',
    color: 'gray',
    supports_duration: false,
    requires_subject: false
  },
  other: {
    type: 'other',
    label: 'Other',
    icon: 'MoreHorizontal',
    color: 'gray',
    supports_duration: false,
    requires_subject: false
  }
}

// Communication export data
export interface CommunicationExportData {
  contact_name: string
  contact_company: string
  communication_type: string
  subject: string
  notes: string
  communication_date: string
  duration_minutes: number | null
  project_name: string | null
  is_project_related: boolean
}