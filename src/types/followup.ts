export type FollowUpStatus = 'scheduled' | 'sent' | 'dismissed' | 'cancelled';

export interface FollowUpTemplate {
  id: string;
  user_id: string;
  name: string;
  subject: string;
  body: string;
  trigger_days: number;
  status_trigger: string; // ApplicationStatus from existing types
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export interface ApplicationWithContact {
  id: string;
  project_name: string;
  company_name: string;
  status: string;
  application_date?: string;
  interview_date?: string;
  contact?: {
    name: string;
    email: string;
    phone: string;
  };
}

export interface ScheduledFollowUp {
  id: string;
  user_id: string;
  application_id: string;
  template_id: string;
  scheduled_date: string;
  status: FollowUpStatus;
  sent_at?: string;
  response_received: boolean;
  created_at: string;
  // Populated relations
  template?: FollowUpTemplate;
  application?: ApplicationWithContact;
}

export interface FollowUpHistory {
  id: string;
  user_id: string;
  application_id: string;
  template_id: string;
  sent_at: string;
  subject: string;
  body: string;
  response_received: boolean;
  response_date?: string;
  created_at: string;
  // Populated relations
  template?: FollowUpTemplate;
  application?: {
    id: string;
    project_name: string;
    contact_person: string;
    company_name: string;
  };
}

export interface CreateFollowUpTemplate {
  name: string;
  subject: string;
  body: string;
  trigger_days: number;
  status_trigger: string;
  is_active?: boolean;
}

export interface UpdateFollowUpTemplate extends Partial<CreateFollowUpTemplate> {
  id: string;
}

export interface CreateScheduledFollowUp {
  application_id: string;
  template_id: string;
  scheduled_date: string;
}

export interface UpdateScheduledFollowUp {
  id: string;
  scheduled_date?: string;
  status?: FollowUpStatus;
  sent_at?: string;
  response_received?: boolean;
}

export interface CreateFollowUpHistory {
  application_id: string;
  template_id: string;
  subject: string;
  body: string;
  response_received?: boolean;
  response_date?: string;
}

// Template placeholders that can be used in subject/body
export interface TemplateVariables {
  project_name: string;
  contact_person: string;
  company_name: string;
  user_name: string;
  trigger_days: number;
  application_date: string;
  interview_date?: string;
}

// Analytics interfaces
export interface FollowUpAnalytics {
  total_sent: number;
  response_rate: number;
  avg_response_time_days: number;
  success_by_template: Array<{
    template_id: string;
    template_name: string;
    sent_count: number;
    response_count: number;
    response_rate: number;
  }>;
  success_by_timing: Array<{
    trigger_days: number;
    sent_count: number;
    response_count: number;
    response_rate: number;
  }>;
}

export interface FollowUpNotification {
  id: string;
  application_id: string;
  template_name: string;
  project_name: string;
  company_name: string;
  scheduled_date: string;
  is_overdue: boolean;
  days_overdue?: number;
}