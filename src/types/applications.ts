// Application Types (migrated from freelance.ts)
// Handles project applications and opportunities (table: project_applications)

import { Database } from '@/integrations/supabase/types'

// Database table types
export type ApplicationRow = Database['public']['Tables']['project_applications']['Row']
export type ApplicationInsert = Database['public']['Tables']['project_applications']['Insert']
export type ApplicationUpdate = Database['public']['Tables']['project_applications']['Update']

export type ApplicationStatus =
  | 'not_applied'
  | 'recommended'
  | 'recruiter_contacted'
  | 'application_sent'
  | 'inquiry_received'
  | 'interview_scheduled'
  | 'interview_completed'
  | 'offer_received'
  | 'rejected'
  | 'project_completed';

export interface Application {
  id: string;
  user_id: string;
  project_name: string;
  company_name: string;
  contact_id?: string;
  project_description?: string;
  budget_range?: string;
  project_start_date?: string;
  project_end_date?: string;
  required_skills?: string[];
  application_date?: string;
  status: ApplicationStatus;
  application_text?: string;
  notes?: string;
  source?: string;
  listing_url?: string;
  application_url?: string;
  project_description_summary?: string;
  work_location_type?: 'remote' | 'onsite' | 'hybrid' | 'flexible';
  remote_percentage?: number;
  work_location_notes?: string;
  created_at: string;
  updated_at: string;
}

export interface CreateApplicationData {
  project_name: string;
  company_name: string;
  contact_id?: string;
  project_description?: string;
  budget_range?: string;
  project_start_date?: string;
  project_end_date?: string;
  required_skills?: string[];
  application_date?: string;
  status?: ApplicationStatus;
  application_text?: string;
  notes?: string;
  source?: string;
  listing_url?: string;
  work_location_type?: 'remote' | 'onsite' | 'hybrid' | 'flexible';
  remote_percentage?: number;
  work_location_notes?: string;
}

export interface UpdateApplicationData extends Partial<CreateApplicationData> {
  id: string;
  notes_date?: string; // For status change activities
}

export type ApplicationUpdateHandler = (
  applicationId: string, 
  updateData: Partial<Application>, 
  statusNotes?: { notes?: string; notes_date?: string }
) => void;

export interface ApplicationAnalysis {
  project_name?: string;
  company_name?: string;
  budget_range?: string;
  project_start_date?: string;
  project_end_date?: string;
  required_skills?: string[];
  key_requirements?: string[];
  project_description_summary?: string;
}

export const APPLICATION_STATUS_LABELS: Record<ApplicationStatus, string> = {
  not_applied: 'Noch nicht beworben',
  recommended: 'Empfohlen worden',
  recruiter_contacted: 'Recruiter-Kontakt',
  application_sent: 'Bewerbung gesendet',
  inquiry_received: 'Rückfrage erhalten',
  interview_scheduled: 'Interview geplant',
  interview_completed: 'Interview durchgeführt',
  offer_received: 'Zusage erhalten',
  rejected: 'Absage erhalten',
  project_completed: 'Projekt abgeschlossen'
};

// Hex colors for inline styles
export const APPLICATION_STATUS_COLORS: Record<ApplicationStatus, string> = {
  not_applied: '#6b7280', // gray-500
  recommended: '#06b6d4', // cyan-500
  recruiter_contacted: '#6366f1', // indigo-500
  application_sent: '#3b82f6', // blue-500
  inquiry_received: '#eab308', // yellow-500
  interview_scheduled: '#a855f7', // purple-500
  interview_completed: '#8b5cf6', // violet-500
  offer_received: '#10b981', // emerald-500
  rejected: '#ef4444', // red-500
  project_completed: '#059669' // emerald-600
};

// Tailwind classes for badge styling (matching activity style)
export const APPLICATION_STATUS_BADGE_CLASSES: Record<ApplicationStatus, string> = {
  not_applied: 'bg-gray-500/10 text-gray-600 border-gray-500/20',
  recommended: 'bg-cyan-500/10 text-cyan-600 border-cyan-500/20',
  recruiter_contacted: 'bg-indigo-500/10 text-indigo-600 border-indigo-500/20',
  application_sent: 'bg-blue-500/10 text-blue-600 border-blue-500/20',
  inquiry_received: 'bg-yellow-500/10 text-yellow-600 border-yellow-500/20',
  interview_scheduled: 'bg-purple-500/10 text-purple-600 border-purple-500/20',
  interview_completed: 'bg-violet-500/10 text-violet-600 border-violet-500/20',
  offer_received: 'bg-emerald-500/10 text-emerald-600 border-emerald-500/20',
  rejected: 'bg-red-500/10 text-red-600 border-red-500/20',
  project_completed: 'bg-emerald-600/10 text-emerald-700 border-emerald-600/20'
};

export const WORK_LOCATION_LABELS: Record<'remote' | 'onsite' | 'hybrid' | 'flexible', string> = {
  remote: 'Remote',
  onsite: 'Vor Ort',
  hybrid: 'Hybrid',
  flexible: 'Flexibel'
};

export const WORK_LOCATION_COLORS: Record<'remote' | 'onsite' | 'hybrid' | 'flexible', string> = {
  remote: 'bg-green-500/10 text-green-400 border-green-500/20',
  onsite: 'bg-orange-500/10 text-orange-400 border-orange-500/20',
  hybrid: 'bg-blue-500/10 text-blue-400 border-blue-500/20',
  flexible: 'bg-purple-500/10 text-purple-400 border-purple-500/20'
};

// Activity Log Types - Application related activities
export type ApplicationActivityType = 
  // Bewerbungs-Aktivitäten (Application/Akquise Phase)
  | 'application_created'
  | 'application_updated'
  | 'application_deleted'
  | 'application_sent'
  | 'application_status_changed'
  | 'application_converted_to_project'
  // KI-Aktivitäten für Bewerbungen
  | 'ai_analysis_generated'
  | 'ai_application_generated';

export interface ApplicationActivity {
  id: string;
  project_id: string; // References project_applications.id
  user_id: string;
  activity_type: ApplicationActivityType;
  description: string;
  notes?: string;
  notes_date?: string;
  created_at: string;
}

export const APPLICATION_ACTIVITY_COLORS: Record<ApplicationActivityType, string> = {
  // Bewerbungs-Aktivitäten (Application/Akquise Phase)
  application_created: 'bg-blue-500/10 text-blue-600 border-blue-500/20',
  application_updated: 'bg-orange-500/10 text-orange-600 border-orange-500/20',
  application_deleted: 'bg-red-500/10 text-red-600 border-red-500/20',
  application_sent: 'bg-green-500/10 text-green-600 border-green-500/20',
  application_status_changed: 'bg-primary/10 text-primary border-primary/20',
  application_converted_to_project: 'bg-emerald-500/10 text-emerald-600 border-emerald-500/20',
  // KI-Aktivitäten für Bewerbungen
  ai_analysis_generated: 'bg-indigo-500/10 text-indigo-600 border-indigo-500/20',
  ai_application_generated: 'bg-violet-500/10 text-violet-600 border-violet-500/20'
};

export const APPLICATION_ACTIVITY_LABELS: Record<ApplicationActivityType, string> = {
  // Bewerbungs-Aktivitäten (Application/Akquise Phase)
  application_created: 'Bewerbung erstellt',
  application_updated: 'Bewerbung aktualisiert',
  application_deleted: 'Bewerbung gelöscht',
  application_sent: 'Bewerbung versendet',
  application_status_changed: 'Bewerbungsstatus geändert',
  application_converted_to_project: 'Zu Projekt konvertiert',
  // KI-Aktivitäten für Bewerbungen
  ai_analysis_generated: 'KI-Analyse erstellt',
  ai_application_generated: 'Bewerbungstext generiert'
};

// Contact Management Types (shared with projects)
export interface Contact {
  id: string;
  user_id: string;
  name?: string;
  email?: string;
  phone?: string;
  company?: string;
  notes?: string;
  total_projects: number;
  successful_projects: number;
  created_at: string;
  updated_at: string;
}

export interface CreateContactData {
  name?: string;
  email?: string;
  phone?: string;
  company?: string;
  notes?: string;
}

export interface UpdateContactData extends Partial<CreateContactData> {
  id: string;
}

// Extended application interface with contact information
export interface ApplicationWithContact extends Application {
  contact?: Contact;
}

// Contact statistics and analytics
export interface ContactStats {
  total_projects: number;
  successful_projects: number;
  success_rate: number;
  avg_project_value?: number;
  last_contact_date?: string;
  most_recent_project?: string;
}

export interface ContactAnalytics extends Contact {
  stats: ContactStats;
  recent_applications: Application[];
}

