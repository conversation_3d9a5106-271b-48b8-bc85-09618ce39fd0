// Calendar Event Types
export type CalendarEventType = 
  | 'manual'
  | 'interview'
  | 'project_start'
  | 'project_end'
  | 'application_due'
  | 'follow_up' 
  | 'meeting';

export interface CalendarEvent {
  id: string;
  user_id: string;
  project_id?: string;
  title: string;
  description?: string;
  start_date: string; // ISO date string
  start_time?: string; // HH:MM format
  end_date?: string; // ISO date string  
  end_time?: string; // HH:MM format
  all_day: boolean;
  event_type: CalendarEventType;
  color: string;
  location?: string;
  completed: boolean;
  reminder_enabled: boolean;
  reminder_minutes_before?: number;
  created_automatically: boolean;
  source_status?: string;
  created_at: string;
  updated_at: string;
}

export interface CreateCalendarEventData {
  project_id?: string;
  reference_id?: string;
  reference_type?: string;
  title: string;
  description?: string;
  start_date: string;
  start_time?: string;
  end_date?: string;
  end_time?: string;
  all_day?: boolean;
  event_type?: CalendarEventType;
  color?: string;
  location?: string;
  completed?: boolean;
  reminder_enabled?: boolean;
  reminder_minutes_before?: number;
  created_automatically?: boolean;
  source_status?: string;
}

export interface UpdateCalendarEventData extends Partial<CreateCalendarEventData> {
  id: string;
}

// Calendar Event Labels (German)
export const EVENT_TYPE_LABELS: Record<CalendarEventType, string> = {
  manual: 'Manuell',
  interview: 'Interview',
  project_start: 'Projektstart',
  project_end: 'Projektende',
  application_due: 'Bewerbungsfrist',
  follow_up: 'Nachfassen',
  meeting: 'Meeting'
};

// Calendar Event Colors
export const EVENT_TYPE_COLORS: Record<CalendarEventType, string> = {
  manual: '#3b82f6',      // Blue
  interview: '#ef4444',   // Red
  project_start: '#22c55e', // Green
  project_end: '#f59e0b',   // Amber
  application_due: '#dc2626', // Red
  follow_up: '#8b5cf6',    // Violet
  meeting: '#06b6d4'       // Cyan
};

// Calendar View Types
export type CalendarView = 'month' | 'week' | 'day' | 'agenda';

export interface CalendarViewState {
  currentDate: Date;
  view: CalendarView;
}

// Calendar Event with Project Info (for joined queries)
export interface CalendarEventWithProject extends CalendarEvent {
  project?: {
    id: string;
    title: string;
    client_name: string;
    status: string;
  };
}

// Reminder Settings
export const REMINDER_OPTIONS = [
  { value: 15, label: '15 Minuten vorher' },
  { value: 30, label: '30 Minuten vorher' },
  { value: 60, label: '1 Stunde vorher' },
  { value: 120, label: '2 Stunden vorher' },
  { value: 1440, label: '1 Tag vorher' },
  { value: 2880, label: '2 Tage vorher' },
  { value: 10080, label: '1 Woche vorher' }
];

// Calendar Event Form Data
export interface CalendarEventFormData {
  title: string;
  description: string;
  project_id: string;
  start_date: string;
  start_time: string;
  end_date: string;
  end_time: string;
  all_day: boolean;
  event_type: CalendarEventType;
  color: string;
  location: string;
  reminder_enabled: boolean;
  reminder_minutes_before?: number;
}

// Default Event Colors
export const DEFAULT_EVENT_COLORS = [
  '#3b82f6', // Blue
  '#ef4444', // Red
  '#22c55e', // Green
  '#f59e0b', // Amber
  '#8b5cf6', // Violet
  '#06b6d4', // Cyan
  '#ec4899', // Pink
  '#84cc16', // Lime
  '#f97316', // Orange
  '#6366f1'  // Indigo
];