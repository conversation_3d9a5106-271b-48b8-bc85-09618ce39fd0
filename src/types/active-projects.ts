import { Database } from '@/integrations/supabase/types'

// Database table types
export type ActiveProject = Database['public']['Tables']['active_projects']['Row']
export type ActiveProjectInsert = Database['public']['Tables']['active_projects']['Insert']
export type ActiveProjectUpdate = Database['public']['Tables']['active_projects']['Update']

export type TimeEntry = Database['public']['Tables']['time_entries']['Row']
export type TimeEntryInsert = Database['public']['Tables']['time_entries']['Insert']
export type TimeEntryUpdate = Database['public']['Tables']['time_entries']['Update']

export type ProjectNote = Database['public']['Tables']['project_notes']['Row']
export type ProjectNoteInsert = Database['public']['Tables']['project_notes']['Insert']
export type ProjectNoteUpdate = Database['public']['Tables']['project_notes']['Update']

// Enums
export type ActiveProjectStatus = 'starting' | 'in_progress' | 'on_hold' | 'completing' | 'completed'
export type ActiveProjectPriority = 'low' | 'medium' | 'high' | 'urgent'
export type ProjectType = 'development' | 'design' | 'consulting' | 'marketing' | 'other'

export type TimeEntryCategory = 
  | 'development' 
  | 'meetings' 
  | 'documentation' 
  | 'design' 
  | 'testing' 
  | 'deployment' 
  | 'communication' 
  | 'research' 
  | 'other'

export type NoteType = 'general' | 'meeting' | 'decision' | 'issue' | 'idea' | 'todo'

// Labels for UI display
export const ACTIVE_PROJECT_STATUS_LABELS: Record<ActiveProjectStatus, string> = {
  starting: 'Startend',
  in_progress: 'In Bearbeitung',
  on_hold: 'Pausiert',
  completing: 'Abschließend',
  completed: 'Abgeschlossen'
}

export const ACTIVE_PROJECT_PRIORITY_LABELS: Record<ActiveProjectPriority, string> = {
  low: 'Niedrig',
  medium: 'Mittel', 
  high: 'Hoch',
  urgent: 'Dringend'
}

export const ACTIVE_PROJECT_TYPE_LABELS: Record<ProjectType, string> = {
  development: 'Development',
  design: 'Design',
  consulting: 'Consulting',
  marketing: 'Marketing',
  other: 'Sonstiges'
}

// Extended types with calculated fields
export interface ActiveProjectWithStats extends ActiveProject {
  total_hours?: number
  total_billable_hours?: number
  total_entries?: number
  last_activity?: string
  is_timer_running?: boolean
  current_timer_id?: string
  progress_percentage?: number
}

export interface TimeEntryWithDuration extends TimeEntry {
  calculated_duration?: number // in minutes
  formatted_duration?: string // e.g., "2h 30m"
}

export interface ProjectNoteWithMeta extends ProjectNote {
  tag_count?: number
  is_recent?: boolean
}

// Form types
export interface CreateActiveProjectForm {
  title: string
  client_name: string
  description?: string
  project_type: ProjectType
  hourly_rate?: number
  estimated_hours?: number
  start_date?: string
  planned_end_date?: string
  priority: ActiveProjectPriority
}

export interface CreateTimeEntryForm {
  project_id: string
  category: TimeEntryCategory
  description?: string
  start_time: string
  end_time?: string
  duration_minutes?: number
  billable: boolean
}

export interface CreateProjectNoteForm {
  project_id: string
  title?: string
  content: string
  note_type: NoteType
  tags: string[]
  is_pinned: boolean
}

// Timer management types
export interface TimerState {
  is_running: boolean
  current_entry_id?: string
  project_id?: string
  start_time?: string
  elapsed_minutes?: number
}

export interface TimerAction {
  type: 'START' | 'STOP' | 'PAUSE' | 'RESUME' | 'RESET'
  payload?: {
    project_id?: string
    category?: TimeEntryCategory
    description?: string
    entry_id?: string
  }
}

// Dashboard and statistics types
export interface ProjectStatistics {
  total_projects: number
  active_projects: number
  completed_projects: number
  total_hours_this_week: number
  total_hours_this_month: number
  average_hours_per_day: number
  most_productive_day: string
  favorite_category: TimeEntryCategory
}

export interface TimeStatistics {
  project_id: string
  project_title: string
  total_hours: number
  billable_hours: number
  non_billable_hours: number
  category_breakdown: Record<TimeEntryCategory, number>
  daily_hours: Array<{
    date: string
    hours: number
  }>
  weekly_hours: Array<{
    week: string
    hours: number
  }>
}

// API response types
export interface ActiveProjectsResponse {
  projects: ActiveProjectWithStats[]
  total_count: number
  has_more: boolean
}

export interface TimeEntriesResponse {
  entries: TimeEntryWithDuration[]
  total_count: number
  total_duration: number
  has_more: boolean
}

// Filter and search types
export interface ProjectFilters {
  status?: ActiveProjectStatus[]
  priority?: ActiveProjectPriority[]
  project_type?: ProjectType[]
  client_name?: string
  date_range?: {
    start: string
    end: string
  }
}

export interface TimeEntryFilters {
  project_id?: string
  category?: TimeEntryCategory[]
  billable?: boolean
  date_range?: {
    start: string
    end: string
  }
}

// Import/Export types
export interface ImportableProject {
  source_project_id: string
  title: string
  client_name: string
  description?: string
  hourly_rate?: number
  estimated_hours?: number
  contact_person?: string
  contact_email?: string
  contact_phone?: string
}

export interface ProjectExportData {
  project: ActiveProject
  time_entries: TimeEntry[]
  notes: ProjectNote[]
  statistics: TimeStatistics
}

// Validation schemas (for use with zod or similar)
export const TIME_ENTRY_CATEGORIES: TimeEntryCategory[] = [
  'development', 
  'meetings', 
  'documentation', 
  'design', 
  'testing', 
  'deployment', 
  'communication', 
  'research', 
  'other'
]

export const NOTE_TYPES: NoteType[] = [
  'general', 
  'meeting', 
  'decision', 
  'issue', 
  'idea', 
  'todo'
]

export const PROJECT_STATUSES: ActiveProjectStatus[] = [
  'starting', 
  'in_progress', 
  'on_hold', 
  'completing', 
  'completed'
]

export const PROJECT_PRIORITIES: ActiveProjectPriority[] = [
  'low', 
  'medium', 
  'high', 
  'urgent'
]

export const PROJECT_TYPES: ProjectType[] = [
  'development', 
  'design', 
  'consulting', 
  'marketing', 
  'other'
]