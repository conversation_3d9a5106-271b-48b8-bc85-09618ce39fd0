// Central notification system types

export type NotificationType = 
  | 'follow_up_due'
  | 'follow_up_overdue'
  | 'application_reminder'
  | 'interview_reminder'
  | 'project_deadline'
  | 'calendar_event'
  | 'system_update'
  | 'general';

export type NotificationPriority = 'low' | 'normal' | 'high' | 'urgent';

export type NotificationStatus = 'unread' | 'read' | 'dismissed';

export interface BaseNotification {
  id: string;
  type: NotificationType;
  priority: NotificationPriority;
  status: NotificationStatus;
  title: string;
  message: string;
  created_at: string;
  read_at?: string;
  dismissed_at?: string;
  expires_at?: string;
  action_url?: string;
  action_label?: string;
  metadata?: Record<string, unknown>;
}

// Specific notification types with typed metadata
export interface FollowUpDueNotification extends BaseNotification {
  type: 'follow_up_due';
  metadata: {
    follow_up_id: string;
    application_id: string;
    application_name: string;
    company_name: string;
    template_name: string;
    scheduled_date: string;
  };
}

export interface FollowUpOverdueNotification extends BaseNotification {
  type: 'follow_up_overdue';
  metadata: {
    follow_up_id: string;
    application_id: string;
    application_name: string;
    company_name: string;
    template_name: string;
    scheduled_date: string;
    days_overdue: number;
  };
}

export interface ApplicationReminderNotification extends BaseNotification {
  type: 'application_reminder';
  metadata: {
    application_id: string;
    application_name: string;
    company_name: string;
    days_since_application: number;
    status: string;
  };
}

export interface InterviewReminderNotification extends BaseNotification {
  type: 'interview_reminder';
  metadata: {
    application_id: string;
    application_name: string;
    company_name: string;
    interview_date: string;
    hours_until_interview: number;
  };
}

export interface CalendarEventNotification extends BaseNotification {
  type: 'calendar_event';
  metadata: {
    event_id: string;
    event_title: string;
    event_date: string;
    event_time?: string;
    minutes_until_event: number;
  };
}

export type Notification = 
  | FollowUpDueNotification
  | FollowUpOverdueNotification
  | ApplicationReminderNotification
  | InterviewReminderNotification
  | CalendarEventNotification
  | BaseNotification;

// Notification preferences
export interface NotificationPreferences {
  enabled: boolean;
  email_notifications: boolean;
  browser_notifications: boolean;
  follow_up_notifications: boolean;
  application_reminders: boolean;
  interview_reminders: boolean;
  calendar_reminders: boolean;
  quiet_hours_start?: string;
  quiet_hours_end?: string;
}

// Notification filters and grouping
export interface NotificationFilters {
  types?: NotificationType[];
  priorities?: NotificationPriority[];
  status?: NotificationStatus;
  date_from?: string;
  date_to?: string;
  unread_only?: boolean;
}

export interface NotificationGroup {
  type: NotificationType;
  count: number;
  latest_notification: Notification;
  notifications: Notification[];
}