import { Application, ApplicationActivity } from './applications';
import { UserSettings } from './settings';
import { CalendarEvent } from './calendar';

export type ExportFormat = 'pdf' | 'excel' | 'json';

export interface ExportOptions {
  format: ExportFormat;
  includeSettings?: boolean;
  includeNotes?: boolean;
  includeApplicationText?: boolean;
  dateRange?: {
    startDate?: string;
    endDate?: string;
  };
  statusFilter?: string[];
}

export interface ExportResult {
  success: boolean;
  fileName?: string;
  error?: string;
  downloadUrl?: string;
}

export interface ExportData {
  projects: Application[];
  settings?: UserSettings | null;
  activities: ApplicationActivity[];
  calendarEvents: CalendarEvent[];
  exportDate: string;
  version: string;
  totalProjects: number;
}

export interface PDFExportConfig {
  title: string;
  author?: string;
  subject?: string;
  keywords?: string;
  margins: {
    top: number;
    right: number;
    bottom: number;
    left: number;
  };
  fontSize: {
    title: number;
    heading: number;
    body: number;
    small: number;
  };
  colors: {
    primary: string;
    secondary: string;
    text: string;
    border: string;
  };
}

export interface ExcelExportConfig {
  sheetName: string;
  includeFormulas?: boolean;
  autoWidth?: boolean;
  freezeHeader?: boolean;
  headerStyle?: {
    font?: {
      bold?: boolean;
      size?: number;
      color?: string;
    };
    fill?: {
      fgColor?: string;
    };
    border?: boolean;
  };
}

export interface ExportProgress {
  stage: 'preparing' | 'processing' | 'generating' | 'downloading' | 'complete' | 'error';
  progress: number; // 0-100
  message: string;
}

// Default configurations
export const DEFAULT_PDF_CONFIG: PDFExportConfig = {
  title: 'Freelance Projects Export',
  author: 'Bid Buddy Board',
  subject: 'Project Data Export',
  keywords: 'freelance, projects, export',
  margins: {
    top: 20,
    right: 20,
    bottom: 20,
    left: 20,
  },
  fontSize: {
    title: 18,
    heading: 14,
    body: 10,
    small: 8,
  },
  colors: {
    primary: '#2563eb',
    secondary: '#64748b',
    text: '#1e293b',
    border: '#e2e8f0',
  },
};

export const DEFAULT_EXCEL_CONFIG: ExcelExportConfig = {
  sheetName: 'Freelance Projects',
  includeFormulas: false,
  autoWidth: true,
  freezeHeader: true,
  headerStyle: {
    font: {
      bold: true,
      size: 12,
      color: '#ffffff',
    },
    fill: {
      fgColor: '#2563eb',
    },
    border: true,
  },
};
