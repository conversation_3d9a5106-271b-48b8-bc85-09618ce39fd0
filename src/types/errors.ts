// Error types for consistent error handling across the application

export class SearchError extends <PERSON>rror {
  constructor(
    message: string,
    public code: string,
    public details?: unknown
  ) {
    super(message);
    this.name = 'SearchError';
  }
}

export class ValidationError extends Error {
  constructor(
    message: string,
    public field: string,
    public value?: unknown
  ) {
    super(message);
    this.name = 'ValidationError';
  }
}

export class NetworkError extends Error {
  constructor(
    message: string,
    public status?: number,
    public response?: unknown
  ) {
    super(message);
    this.name = 'NetworkError';
  }
}

export type AppError = SearchError | ValidationError | NetworkError;

export const ErrorCodes = {
  // Search errors
  SEARCH_QUERY_INVALID: 'SEARCH_QUERY_INVALID',
  SEARCH_QUERY_TOO_SHORT: 'SEARCH_QUERY_TOO_SHORT',
  SEARCH_QUERY_TOO_LONG: 'SEARCH_QUERY_TOO_LONG',
  SEARCH_DATABASE_ERROR: 'SEARCH_DATABASE_ERROR',
  SEARCH_PERMISSION_DENIED: 'SEARCH_PERMISSION_DENIED',
  
  // Network errors
  NETWORK_TIMEOUT: 'NETWORK_TIMEOUT',
  NETWORK_OFFLINE: 'NETWORK_OFFLINE',
  NETWORK_RATE_LIMITED: 'NETWORK_RATE_LIMITED',
  
  // Validation errors
  VALIDATION_REQUIRED_FIELD: 'VALIDATION_REQUIRED_FIELD',
  VALIDATION_INVALID_FORMAT: 'VALIDATION_INVALID_FORMAT',
} as const;

export function createSearchError(message: string, code: string, details?: unknown): SearchError {
  return new SearchError(message, code, details);
}

export function createValidationError(message: string, field: string, value?: unknown): ValidationError {
  return new ValidationError(message, field, value);
}

export function createNetworkError(message: string, status?: number, response?: unknown): NetworkError {
  return new NetworkError(message, status, response);
}

// User-friendly error messages
export const ErrorMessages = {
  [ErrorCodes.SEARCH_QUERY_INVALID]: 'Ungültige Suchanfrage. Bitte verwenden Sie nur Buchstaben, Zahlen und grundlegende Satzzeichen.',
  [ErrorCodes.SEARCH_QUERY_TOO_SHORT]: 'Suchanfrage zu kurz. Mindestens 2 Zeichen erforderlich.',
  [ErrorCodes.SEARCH_QUERY_TOO_LONG]: 'Suchanfrage zu lang. Maximal 100 Zeichen erlaubt.',
  [ErrorCodes.SEARCH_DATABASE_ERROR]: 'Fehler bei der Suche. Bitte versuchen Sie es später erneut.',
  [ErrorCodes.SEARCH_PERMISSION_DENIED]: 'Keine Berechtigung für diese Suchanfrage.',
  [ErrorCodes.NETWORK_TIMEOUT]: 'Zeitüberschreitung bei der Anfrage. Bitte versuchen Sie es erneut.',
  [ErrorCodes.NETWORK_OFFLINE]: 'Keine Internetverbindung verfügbar.',
  [ErrorCodes.NETWORK_RATE_LIMITED]: 'Zu viele Anfragen. Bitte warten Sie einen Moment.',
  [ErrorCodes.VALIDATION_REQUIRED_FIELD]: 'Dieses Feld ist erforderlich.',
  [ErrorCodes.VALIDATION_INVALID_FORMAT]: 'Ungültiges Format.',
} as const;

export function getErrorMessage(error: AppError): string {
  if (error instanceof SearchError && error.code in ErrorMessages) {
    return ErrorMessages[error.code as keyof typeof ErrorMessages];
  }
  if (error instanceof ValidationError && ErrorCodes.VALIDATION_INVALID_FORMAT in ErrorMessages) {
    return ErrorMessages[ErrorCodes.VALIDATION_INVALID_FORMAT];
  }
  if (error instanceof NetworkError && ErrorCodes.NETWORK_TIMEOUT in ErrorMessages) {
    return ErrorMessages[ErrorCodes.NETWORK_TIMEOUT];
  }
  
  // Fallback to original error message
  return error.message || 'Ein unbekannter Fehler ist aufgetreten.';
}