// Project Types (migrated from active-projects.ts)
// Handles active projects being worked on (table: projects)

import { Database } from '@/integrations/supabase/types'

// Database table types - now using the new 'projects' table
export type Project = Database['public']['Tables']['projects']['Row']
export type ProjectInsert = Database['public']['Tables']['projects']['Insert']
export type ProjectUpdate = Database['public']['Tables']['projects']['Update']

export type TimeEntry = Database['public']['Tables']['time_entries']['Row']
export type TimeEntryInsert = Database['public']['Tables']['time_entries']['Insert']
export type TimeEntryUpdate = Database['public']['Tables']['time_entries']['Update']

export type ProjectNote = Database['public']['Tables']['project_notes']['Row']
export type ProjectNoteInsert = Database['public']['Tables']['project_notes']['Insert']
export type ProjectNoteUpdate = Database['public']['Tables']['project_notes']['Update']

// Enums
export type ProjectStatus = 'starting' | 'in_progress' | 'on_hold' | 'completing' | 'completed'
export type ProjectPriority = 'low' | 'medium' | 'high' | 'urgent'
export type ProjectType = 'development' | 'design' | 'consulting' | 'marketing' | 'other'

export type TimeEntryCategory = 
  | 'development' 
  | 'meetings' 
  | 'documentation' 
  | 'design' 
  | 'testing' 
  | 'deployment' 
  | 'communication' 
  | 'research' 
  | 'other'

export type NoteType = 'general' | 'meeting' | 'decision' | 'issue' | 'idea' | 'todo'

// Labels for UI display
export const PROJECT_STATUS_LABELS: Record<ProjectStatus, string> = {
  starting: 'Startend',
  in_progress: 'In Bearbeitung',
  on_hold: 'Pausiert',
  completing: 'Abschließend',
  completed: 'Abgeschlossen'
}

export const PROJECT_PRIORITY_LABELS: Record<ProjectPriority, string> = {
  low: 'Niedrig',
  medium: 'Mittel', 
  high: 'Hoch',
  urgent: 'Dringend'
}

export const PROJECT_TYPE_LABELS: Record<ProjectType, string> = {
  development: 'Development',
  design: 'Design',
  consulting: 'Consulting',
  marketing: 'Marketing',
  other: 'Sonstiges'
}

// Hex colors for inline styles
export const PROJECT_STATUS_COLORS: Record<ProjectStatus, string> = {
  starting: '#6366f1', // indigo-500
  in_progress: '#10b981', // emerald-500
  on_hold: '#f59e0b', // amber-500
  completing: '#3b82f6', // blue-500
  completed: '#6b7280' // gray-500
}

// Tailwind classes for badge styling (matching activity style)
export const PROJECT_STATUS_BADGE_CLASSES: Record<ProjectStatus, string> = {
  starting: 'bg-indigo-500/10 text-indigo-600 border-indigo-500/20',
  in_progress: 'bg-emerald-500/10 text-emerald-600 border-emerald-500/20',
  on_hold: 'bg-amber-500/10 text-amber-600 border-amber-500/20',
  completing: 'bg-blue-500/10 text-blue-600 border-blue-500/20',
  completed: 'bg-gray-500/10 text-gray-600 border-gray-500/20'
}

export const PROJECT_PRIORITY_COLORS: Record<ProjectPriority, string> = {
  low: '#10b981', // emerald-500
  medium: '#f59e0b', // amber-500
  high: '#f97316', // orange-500
  urgent: '#ef4444' // red-500
}

// Extended types with calculated fields
export interface ProjectWithStats extends Project {
  total_hours?: number
  total_billable_hours?: number
  total_entries?: number
  last_activity?: string
  is_timer_running?: boolean
  current_timer_id?: string
  progress_percentage?: number
}

export interface TimeEntryWithDuration extends TimeEntry {
  calculated_duration?: number // in minutes
  formatted_duration?: string // e.g., "2h 30m"
}

export interface ProjectNoteWithMeta extends ProjectNote {
  tag_count?: number
  is_recent?: boolean
}

// Form types
export interface CreateProjectForm {
  title: string
  client_name: string
  description?: string
  project_type: ProjectType
  hourly_rate?: number
  estimated_hours?: number
  start_date?: string
  planned_end_date?: string
  priority: ProjectPriority
  source_application_id?: string // Updated: References applications table
}

export interface CreateTimeEntryForm {
  project_id: string
  category: TimeEntryCategory
  description?: string
  start_time: string
  end_time?: string
  duration_minutes?: number
  billable: boolean
}

export interface CreateProjectNoteForm {
  project_id: string
  title?: string
  content: string
  note_type: NoteType
  tags: string[]
  is_pinned: boolean
}

// Timer management types
export interface TimerState {
  is_running: boolean
  current_entry_id?: string
  project_id?: string
  start_time?: string
  elapsed_minutes?: number
}

export interface TimerAction {
  type: 'START' | 'STOP' | 'PAUSE' | 'RESUME' | 'RESET'
  payload?: {
    project_id?: string
    category?: TimeEntryCategory
    description?: string
    entry_id?: string
  }
}

// Dashboard and statistics types
export interface ProjectStatistics {
  total_projects: number
  active_projects: number
  completed_projects: number
  total_hours_this_week: number
  total_hours_this_month: number
  average_hours_per_day: number
  most_productive_day: string
  favorite_category: TimeEntryCategory
}

export interface TimeStatistics {
  project_id: string
  project_title: string
  total_hours: number
  billable_hours: number
  non_billable_hours: number
  category_breakdown: Record<TimeEntryCategory, number>
  daily_hours: Array<{
    date: string
    hours: number
  }>
  weekly_hours: Array<{
    week: string
    hours: number
  }>
}

// API response types
export interface ProjectsResponse {
  projects: ProjectWithStats[]
  total_count: number
  has_more: boolean
}

export interface TimeEntriesResponse {
  entries: TimeEntryWithDuration[]
  total_count: number
  total_duration: number
  has_more: boolean
}

// Filter and search types
export interface ProjectFilters {
  status?: ProjectStatus[]
  priority?: ProjectPriority[]
  project_type?: ProjectType[]
  client_name?: string
  date_range?: {
    start: string
    end: string
  }
}

export interface TimeEntryFilters {
  project_id?: string
  category?: TimeEntryCategory[]
  billable?: boolean
  date_range?: {
    start: string
    end: string
  }
}

// Import/Export types
export interface ImportableProject {
  source_application_id: string // Updated: was source_project_id
  title: string
  client_name: string
  description?: string
  hourly_rate?: number
  estimated_hours?: number
  contact_person?: string
  contact_email?: string
  contact_phone?: string
}

export interface ProjectExportData {
  project: Project
  time_entries: TimeEntry[]
  notes: ProjectNote[]
  statistics: TimeStatistics
}

// Activity Log Types - Project related activities
export type ProjectActivityType = 
  // Projekt-Aktivitäten
  | 'project_created'
  | 'project_updated'
  | 'project_deleted'
  | 'project_status_changed'
  // Zeiterfassung (Nur für Projekte)
  | 'time_tracking_started'
  | 'time_tracking_stopped'
  | 'time_entry_created'
  | 'time_entry_updated'
  | 'time_entry_deleted';

export interface ProjectActivity {
  id: string;
  project_id: string; // References projects.id
  user_id: string;
  activity_type: ProjectActivityType;
  description: string;
  notes?: string;
  notes_date?: string;
  created_at: string;
  updated_at: string;
}

export const PROJECT_ACTIVITY_COLORS: Record<ProjectActivityType, string> = {
  // Projekt-Aktivitäten
  project_created: 'bg-cyan-500/10 text-cyan-600 border-cyan-500/20',
  project_updated: 'bg-amber-500/10 text-amber-600 border-amber-500/20',
  project_deleted: 'bg-red-500/10 text-red-600 border-red-500/20',
  project_status_changed: 'bg-violet-500/10 text-violet-600 border-violet-500/20',
  // Zeiterfassung (Nur für Projekte)
  time_tracking_started: 'bg-green-500/10 text-green-600 border-green-500/20',
  time_tracking_stopped: 'bg-red-500/10 text-red-600 border-red-500/20',
  time_entry_created: 'bg-blue-500/10 text-blue-600 border-blue-500/20',
  time_entry_updated: 'bg-orange-500/10 text-orange-600 border-orange-500/20',
  time_entry_deleted: 'bg-red-500/10 text-red-600 border-red-500/20'
};

export const PROJECT_ACTIVITY_LABELS: Record<ProjectActivityType, string> = {
  // Projekt-Aktivitäten
  project_created: 'Projekt erstellt',
  project_updated: 'Projekt aktualisiert',
  project_deleted: 'Projekt gelöscht',
  project_status_changed: 'Projektstatus geändert',
  // Zeiterfassung (Nur für Projekte)
  time_tracking_started: 'Zeiterfassung gestartet',
  time_tracking_stopped: 'Zeiterfassung gestoppt',
  time_entry_created: 'Zeiteintrag erstellt',
  time_entry_updated: 'Zeiteintrag aktualisiert',
  time_entry_deleted: 'Zeiteintrag gelöscht'
};

// Validation schemas (for use with zod or similar)
export const TIME_ENTRY_CATEGORIES: TimeEntryCategory[] = [
  'development', 
  'meetings', 
  'documentation', 
  'design', 
  'testing', 
  'deployment', 
  'communication', 
  'research', 
  'other'
]

export const NOTE_TYPES: NoteType[] = [
  'general', 
  'meeting', 
  'decision', 
  'issue', 
  'idea', 
  'todo'
]

export const PROJECT_STATUSES: ProjectStatus[] = [
  'starting', 
  'in_progress', 
  'on_hold', 
  'completing', 
  'completed'
]

export const PROJECT_PRIORITIES: ProjectPriority[] = [
  'low', 
  'medium', 
  'high', 
  'urgent'
]

export const PROJECT_TYPES: ProjectType[] = [
  'development', 
  'design', 
  'consulting', 
  'marketing', 
  'other'
]

