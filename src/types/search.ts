// Global Search Types
// Handles comprehensive search across all user entities

export interface SearchResult {
  entity_type: 'application' | 'project' | 'contact' | 'calendar' | 'template' | 'note' | 'time_entry';
  entity_id: string;
  title: string;
  subtitle: string;
  description?: string;
  url_path: string;
  rank: number;
  highlight?: string;
  created_at: string;
}


export interface SearchGroup {
  type: string;
  label: string;
  icon: string;
  results: SearchResult[];
}

export interface SearchState {
  query: string;
  results: SearchResult[];
  loading: boolean;
  error: string | null;
  isOpen: boolean;
}

// Search configuration
export const ENTITY_CONFIG = {
  application: {
    label: 'Bewerbungen',
    icon: '📋',
    color: 'bg-blue-500/10 text-blue-600 border-blue-500/20',
  },
  project: {
    label: 'Projekte', 
    icon: '🚀',
    color: 'bg-emerald-500/10 text-emerald-600 border-emerald-500/20',
  },
  contact: {
    label: 'Kontakte',
    icon: '👥', 
    color: 'bg-violet-500/10 text-violet-600 border-violet-500/20',
  },
  calendar: {
    label: 'Termine',
    icon: '📅',
    color: 'bg-orange-500/10 text-orange-600 border-orange-500/20',
  },
  template: {
    label: 'Templates',
    icon: '📧',
    color: 'bg-indigo-500/10 text-indigo-600 border-indigo-500/20',
  },
  note: {
    label: 'Notizen',
    icon: '📝',
    color: 'bg-yellow-500/10 text-yellow-600 border-yellow-500/20',
  },
  time_entry: {
    label: 'Zeiterfassung',
    icon: '⏱️',
    color: 'bg-cyan-500/10 text-cyan-600 border-cyan-500/20',
  },
} as const;

export const SEARCH_SHORTCUTS = {
  OPEN_SEARCH: 'cmd+k',
  CLOSE_SEARCH: 'escape',
  NAVIGATE_DOWN: 'arrowdown',
  NAVIGATE_UP: 'arrowup', 
  SELECT_ITEM: 'enter',
} as const;

export const SEARCH_CONFIG = {
  MIN_QUERY_LENGTH: 2,
  DEBOUNCE_DELAY: 300,
  MAX_RESULTS: 50,
  QUICK_SEARCH_LIMIT: 10,
} as const;