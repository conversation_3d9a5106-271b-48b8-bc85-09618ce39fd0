import { Toaster } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import { AuthProvider } from "@/contexts/AuthContext";
import { ProtectedRoute } from "@/components/ProtectedRoute";
import { AccessProtectedRoute } from "@/components/AccessProtectedRoute";
import { InstallPrompt } from "@/components/pwa/InstallPrompt";
import { PWAUpdatePrompt } from "@/components/pwa/PWAUpdatePrompt";
import { useThemeColor } from "@/hooks/useThemeColor";
import { NotificationProvider } from "@/components/NotificationProvider";
import { AppLayout } from "@/components/layout/AppLayout";
import { useEffect, lazy, Suspense } from "react";
import { Skeleton } from "@/components/ui/skeleton";
import Auth from "./pages/Auth";
import AuthCallback from "./pages/AuthCallback";
import AccessCode from "./pages/AccessCode";
import NotFound from "./pages/NotFound";

// Lazy load heavy components
const CombinedDashboard = lazy(() => import("./pages/CombinedDashboard"));
const Applications = lazy(() => import("./pages/Applications"));
const Statistics = lazy(() => import("./pages/Statistics"));
const Settings = lazy(() => import("./pages/Settings"));
const Notifications = lazy(() => import("./pages/Notifications"));
const ProjectCreate = lazy(() => import("./pages/ProjectCreate"));
const ProjectEdit = lazy(() => import("./pages/ProjectEdit"));
const ProjectDetails = lazy(() => import("./pages/ProjectDetails"));
const Calendar = lazy(() => import("./pages/Calendar"));
const Contacts = lazy(() => import("./pages/Contacts").then(module => ({ default: module.Contacts })));
const ContactDetails = lazy(() => import("./pages/ContactDetails").then(module => ({ default: module.ContactDetails })));
const ContactCreateEdit = lazy(() => import("./pages/ContactCreateEdit").then(module => ({ default: module.ContactCreateEdit })));
const ActiveProjects = lazy(() => import("./pages/ActiveProjects"));
const ActiveProjectsTimer = lazy(() => import("./pages/ActiveProjectsTimer"));
const ActiveProjectsReports = lazy(() => import("./pages/ActiveProjectsReports"));
const ActiveProjectDetails = lazy(() => import("./pages/ActiveProjectDetails"));
const ActiveProjectEdit = lazy(() => import("./pages/ActiveProjectEdit"));
const ProjectNew = lazy(() => import("./pages/ProjectNew"));
const FollowUps = lazy(() => import("./pages/FollowUps"));

// Loading component for Suspense
const PageLoader = () => (
  <div className="flex items-center justify-center min-h-screen">
    <div className="space-y-4 w-full max-w-md">
      <Skeleton className="h-12 w-full" />
      <Skeleton className="h-4 w-3/4" />
      <Skeleton className="h-4 w-1/2" />
    </div>
  </div>
);

const queryClient = new QueryClient();

// New wrapper component for protected routes with AppLayout
const ProtectedLayout = ({ children }: { children: React.ReactNode }) => (
  <ProtectedRoute>
    <AppLayout>
      {children}
    </AppLayout>
  </ProtectedRoute>
);

const App = () => {
  // Apply theme colors to status bar
  useThemeColor();
  
  return (
    <QueryClientProvider client={queryClient}>
      <AuthProvider>
        <NotificationProvider>
          <TooltipProvider>
            <Toaster />
            <InstallPrompt />
            <PWAUpdatePrompt />
            <BrowserRouter>
        <Routes>
          {/* Public Routes - No access restriction */}
          <Route path="/access" element={<AccessCode />} />
          
          {/* Auth Routes - Require access code */}
          <Route path="/auth" element={
            <AccessProtectedRoute>
              <Auth />
            </AccessProtectedRoute>
          } />
          <Route path="/auth/callback" element={
            <AccessProtectedRoute>
              <AuthCallback />
            </AccessProtectedRoute>
          } />
          
          {/* Protected Routes with static AppLayout */}
          <Route path="/" element={
            <ProtectedLayout>
              <Suspense fallback={<PageLoader />}>
                <CombinedDashboard />
              </Suspense>
            </ProtectedLayout>
          } />
          <Route path="/applications" element={
            <ProtectedLayout>
              <Suspense fallback={<PageLoader />}>
                <Applications />
              </Suspense>
            </ProtectedLayout>
          } />
          <Route path="/applications/new" element={
            <ProtectedLayout>
              <Suspense fallback={<PageLoader />}>
                <ProjectCreate />
              </Suspense>
            </ProtectedLayout>
          } />
          <Route path="/applications/:id" element={
            <ProtectedLayout>
              <Suspense fallback={<PageLoader />}>
                <ProjectDetails />
              </Suspense>
            </ProtectedLayout>
          } />
          <Route path="/applications/edit/:id" element={
            <ProtectedLayout>
              <Suspense fallback={<PageLoader />}>
                <ProjectEdit />
              </Suspense>
            </ProtectedLayout>
          } />
          <Route path="/projects" element={
            <ProtectedLayout>
              <Suspense fallback={<PageLoader />}>
                <ActiveProjects />
              </Suspense>
            </ProtectedLayout>
          } />
          <Route path="/projects/new" element={
            <ProtectedLayout>
              <Suspense fallback={<PageLoader />}>
                <ProjectNew />
              </Suspense>
            </ProtectedLayout>
          } />
          <Route path="/projects/timer" element={
            <ProtectedLayout>
              <Suspense fallback={<PageLoader />}>
                <ActiveProjectsTimer />
              </Suspense>
            </ProtectedLayout>
          } />
          <Route path="/projects/reports" element={
            <ProtectedLayout>
              <Suspense fallback={<PageLoader />}>
                <ActiveProjectsReports />
              </Suspense>
            </ProtectedLayout>
          } />
          <Route path="/projects/:id" element={
            <ProtectedLayout>
              <Suspense fallback={<PageLoader />}>
                <ActiveProjectDetails />
              </Suspense>
            </ProtectedLayout>
          } />
          <Route path="/projects/edit/:id" element={
            <ProtectedLayout>
              <Suspense fallback={<PageLoader />}>
                <ActiveProjectEdit />
              </Suspense>
            </ProtectedLayout>
          } />
          <Route path="/contacts" element={
            <ProtectedLayout>
              <Suspense fallback={<PageLoader />}>
                <Contacts />
              </Suspense>
            </ProtectedLayout>
          } />
          <Route path="/contacts/new" element={
            <ProtectedLayout>
              <Suspense fallback={<PageLoader />}>
                <ContactCreateEdit />
              </Suspense>
            </ProtectedLayout>
          } />
          <Route path="/contacts/:id" element={
            <ProtectedLayout>
              <Suspense fallback={<PageLoader />}>
                <ContactDetails />
              </Suspense>
            </ProtectedLayout>
          } />
          <Route path="/contacts/:id/edit" element={
            <ProtectedLayout>
              <Suspense fallback={<PageLoader />}>
                <ContactCreateEdit />
              </Suspense>
            </ProtectedLayout>
          } />
          <Route path="/statistics" element={
            <ProtectedLayout>
              <Suspense fallback={<PageLoader />}>
                <Statistics />
              </Suspense>
            </ProtectedLayout>
          } />
          <Route path="/notifications" element={
            <ProtectedLayout>
              <Suspense fallback={<PageLoader />}>
                <Notifications />
              </Suspense>
            </ProtectedLayout>
          } />
          <Route path="/follow-ups" element={
            <ProtectedLayout>
              <Suspense fallback={<PageLoader />}>
                <FollowUps />
              </Suspense>
            </ProtectedLayout>
          } />
          <Route path="/settings" element={
            <ProtectedLayout>
              <Suspense fallback={<PageLoader />}>
                <Settings />
              </Suspense>
            </ProtectedLayout>
          } />
          <Route path="/calendar" element={
            <ProtectedLayout>
              <Suspense fallback={<PageLoader />}>
                <Calendar />
              </Suspense>
            </ProtectedLayout>
          } />
          
          {/* Backward compatibility redirects */}
          <Route path="/project/new" element={<ProtectedLayout><ProjectCreate /></ProtectedLayout>} />
          <Route path="/project/:id" element={<ProtectedLayout><ProjectDetails /></ProtectedLayout>} />
          <Route path="/project/edit/:id" element={<ProtectedLayout><ProjectEdit /></ProtectedLayout>} />
          <Route path="/active-projects" element={<ProtectedLayout><ActiveProjects /></ProtectedLayout>} />
          <Route path="/active-projects/timer" element={<ProtectedLayout><ActiveProjectsTimer /></ProtectedLayout>} />
          <Route path="/active-projects/reports" element={<ProtectedLayout><ActiveProjectsReports /></ProtectedLayout>} />
          <Route path="/active-projects/:id" element={<ProtectedLayout><ActiveProjectDetails /></ProtectedLayout>} />
          <Route path="/active-projects/edit/:id" element={<ProtectedLayout><ActiveProjectEdit /></ProtectedLayout>} />
          
          {/* ADD ALL CUSTOM ROUTES ABOVE THE CATCH-ALL "*" ROUTE */}
          <Route path="*" element={<NotFound />} />
        </Routes>
      </BrowserRouter>
          </TooltipProvider>
        </NotificationProvider>
      </AuthProvider>
    </QueryClientProvider>
  );
};

export default App;
