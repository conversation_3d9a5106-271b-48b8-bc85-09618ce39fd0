import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Badge } from '@/components/ui/badge';
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle } from '@/components/ui/alert-dialog';
import { useCalendarEvents } from '@/hooks/useCalendarEvents';
import { useTranslation } from '@/hooks/useTranslation';
import { format } from 'date-fns';
import { de, enUS } from 'date-fns/locale';
import { 
  Calendar, 
  Clock, 
  MapPin, 
  Bell, 
  Palette, 
  Loader2, 
  Edit2, 
  Trash2, 
  CheckCircle2, 
  Circle,
  User,
  Eye,
  ExternalLink
} from 'lucide-react';
import type { Application } from '@/types/applications';
import type { Project } from '@/types/projects';
import type { CalendarEventWithProject, CalendarEventType, UpdateCalendarEventData } from '@/types/calendar';
import { EVENT_TYPE_LABELS, REMINDER_OPTIONS, DEFAULT_EVENT_COLORS } from '@/types/calendar';

interface CalendarEventDetailsDialogProps {
  isOpen: boolean;
  onClose: () => void;
  event: CalendarEventWithProject | null;
  applications: Application[];
  projects: Project[];
}

export const CalendarEventDetailsDialog = ({ isOpen, onClose, event, applications, projects }: CalendarEventDetailsDialogProps) => {
  const { t, currentLanguage } = useTranslation('calendar');
  const [isEditing, setIsEditing] = useState(false);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  
  // Form state (for editing)
  const [title, setTitle] = useState('');
  const [description, setDescription] = useState('');
  const [projectId, setProjectId] = useState('');
  const [startDate, setStartDate] = useState('');
  const [startTime, setStartTime] = useState('');
  const [endDate, setEndDate] = useState('');
  const [endTime, setEndTime] = useState('');
  const [allDay, setAllDay] = useState(false);
  const [eventType, setEventType] = useState<CalendarEventType>('manual');
  const [color, setColor] = useState(DEFAULT_EVENT_COLORS[0]);
  const [location, setLocation] = useState('');
  const [reminderEnabled, setReminderEnabled] = useState(false);
  const [reminderMinutes, setReminderMinutes] = useState(30);

  const { updateEvent, deleteEvent, toggleEventCompletion, isUpdating, isDeleting, isToggling } = useCalendarEvents();
  
  // Get the appropriate date-fns locale based on current language
  const dateLocale = currentLanguage === 'en' ? enUS : de;

  // Initialize form when event changes
  useEffect(() => {
    if (event) {
      setTitle(event.title);
      setDescription(event.description || '');
      setProjectId(event.project_id || 'none');
      setStartDate(event.start_date);
      setStartTime(event.start_time || '');
      setEndDate(event.end_date || '');
      setEndTime(event.end_time || '');
      setAllDay(event.all_day);
      setEventType(event.event_type);
      setColor(event.color);
      setLocation(event.location || '');
      setReminderEnabled(event.reminder_enabled);
      setReminderMinutes(event.reminder_minutes_before || 30);
    }
  }, [event]);

  const handleEdit = () => {
    setIsEditing(true);
  };

  const handleCancelEdit = () => {
    setIsEditing(false);
    // Reset form to original values
    if (event) {
      setTitle(event.title);
      setDescription(event.description || '');
      setProjectId(event.project_id || 'none');
      setStartDate(event.start_date);
      setStartTime(event.start_time || '');
      setEndDate(event.end_date || '');
      setEndTime(event.end_time || '');
      setAllDay(event.all_day);
      setEventType(event.event_type);
      setColor(event.color);
      setLocation(event.location || '');
      setReminderEnabled(event.reminder_enabled);
      setReminderMinutes(event.reminder_minutes_before || 30);
    }
  };

  const handleSave = async () => {
    if (!event || !title.trim() || !startDate) {
      return;
    }

    try {
      const updateData: UpdateCalendarEventData = {
        id: event.id,
        title: title.trim(),
        description: description.trim() || undefined,
        project_id: (projectId && projectId !== 'none') ? projectId : undefined,
        start_date: startDate,
        start_time: allDay ? undefined : startTime || undefined,
        end_date: endDate || undefined,
        end_time: allDay ? undefined : endTime || undefined,
        all_day: allDay,
        event_type: eventType,
        color,
        location: location.trim() || undefined,
        reminder_enabled: reminderEnabled,
        reminder_minutes_before: reminderEnabled ? reminderMinutes : undefined,
      };

      await updateEvent(updateData);
      setIsEditing(false);
    } catch (error) {
      console.error('Error updating event:', error);
    }
  };

  const handleDelete = async () => {
    if (!event) return;

    try {
      await deleteEvent(event.id);
      setShowDeleteDialog(false);
      onClose();
    } catch (error) {
      console.error('Error deleting event:', error);
    }
  };

  const handleToggleCompletion = async () => {
    if (!event) return;

    try {
      await toggleEventCompletion({
        eventId: event.id,
        completed: !event.completed
      });
    } catch (error) {
      console.error('Error toggling completion:', error);
    }
  };

  const handleAllDayToggle = (checked: boolean) => {
    setAllDay(checked);
    if (checked) {
      setStartTime('');
      setEndTime('');
    } else if (!startTime) {
      const currentTime = format(new Date(), 'HH:mm');
      setStartTime(currentTime);
      // Set end time to 1 hour later
      const endTimeDate = new Date();
      endTimeDate.setHours(endTimeDate.getHours() + 1);
      setEndTime(format(endTimeDate, 'HH:mm'));
    }
  };

  const formatEventDateTime = (event: CalendarEventWithProject) => {
    const startDate = new Date(event.start_date);
    let dateStr = format(startDate, currentLanguage === 'en' ? 'EEEE, MMMM dd, yyyy' : 'EEEE, dd. MMMM yyyy', { locale: dateLocale });
    
    if (!event.all_day && event.start_time) {
      dateStr += currentLanguage === 'en' ? ` at ${event.start_time}` : ` um ${event.start_time}`;
      if (event.end_time) {
        dateStr += ` - ${event.end_time}`;
      }
    } else if (event.all_day) {
      dateStr += ` (${t('all_day')})`;
    }
    
    return dateStr;
  };

  if (!event) return null;

  return (
    <>
      <Dialog open={isOpen} onOpenChange={onClose}>
        <DialogContent className="sm:max-w-[600px] max-h-[90vh] flex flex-col">
          <DialogHeader>
            <DialogTitle className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <div
                  className="w-4 h-4 rounded-full"
                  style={{ backgroundColor: event.color }}
                />
                {isEditing ? t('dialog.edit_title') : t('dialog.details_title')}
              </div>
              <div className="flex items-center gap-2">
                {!isEditing && (
                  <>
                    <Button
                      size="sm"
                      variant="ghost"
                      onClick={handleToggleCompletion}
                      disabled={isToggling}
                    >
                      {event.completed ? (
                        <CheckCircle2 className="h-4 w-4 text-success" />
                      ) : (
                        <Circle className="h-4 w-4" />
                      )}
                    </Button>
                    {!event.created_automatically && (
                      <Button size="sm" variant="ghost" onClick={handleEdit}>
                        <Edit2 className="h-4 w-4" />
                      </Button>
                    )}
                    <Button 
                      size="sm" 
                      variant="ghost" 
                      onClick={() => setShowDeleteDialog(true)}
                      className="text-destructive hover:text-destructive"
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </>
                )}
              </div>
            </DialogTitle>
          </DialogHeader>

          <div className="flex-1 overflow-y-auto space-y-6">
            {isEditing ? (
              /* Edit Form */
              <form className="space-y-6">
                {/* Basic Information */}
                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="title">{t('form.title_required')}</Label>
                    <Input
                      id="title"
                      value={title}
                      onChange={(e) => setTitle(e.target.value)}
                      placeholder={t('placeholders.event_title')}
                      required
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="description">{t('form.description')}</Label>
                    <Textarea
                      id="description"
                      value={description}
                      onChange={(e) => setDescription(e.target.value)}
                      placeholder={t('placeholders.event_description')}
                      rows={3}
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="project">{t('form.link_project')}</Label>
                    <Select value={projectId || undefined} onValueChange={(value) => setProjectId(value || '')}>
                      <SelectTrigger>
                        <SelectValue placeholder={t('placeholders.select_project')} />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="none">{t('form.no_link')}</SelectItem>
                        {projects.map((project) => (
                          <SelectItem key={project.id} value={project.id}>
                            {project.project_name} - {project.company_name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                {/* Date and Time */}
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <Label className="text-base font-medium">{t('form.date_time')}</Label>
                    <div className="flex items-center space-x-2">
                      <Switch
                        id="all-day"
                        checked={allDay}
                        onCheckedChange={handleAllDayToggle}
                      />
                      <Label htmlFor="all-day" className="text-sm">Ganztägig</Label>
                    </div>
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="start-date">Startdatum *</Label>
                      <Input
                        id="start-date"
                        type="date"
                        value={startDate}
                        onChange={(e) => setStartDate(e.target.value)}
                        required
                      />
                    </div>
                    {!allDay && (
                      <div className="space-y-2">
                        <Label htmlFor="start-time">Startzeit</Label>
                        <Input
                          id="start-time"
                          type="time"
                          value={startTime}
                          onChange={(e) => setStartTime(e.target.value)}
                        />
                      </div>
                    )}
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="end-date">Enddatum</Label>
                      <Input
                        id="end-date"
                        type="date"
                        value={endDate}
                        onChange={(e) => setEndDate(e.target.value)}
                      />
                    </div>
                    {!allDay && (
                      <div className="space-y-2">
                        <Label htmlFor="end-time">Endzeit</Label>
                        <Input
                          id="end-time"
                          type="time"
                          value={endTime}
                          onChange={(e) => setEndTime(e.target.value)}
                        />
                      </div>
                    )}
                  </div>
                </div>

                {/* Event Type and Color */}
                <div className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="event-type">Typ</Label>
                      <Select value={eventType} onValueChange={(value: CalendarEventType) => setEventType(value)}>
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          {Object.entries(EVENT_TYPE_LABELS).map(([value, label]) => (
                            <SelectItem key={value} value={value}>
                              {label}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="space-y-2">
                      <Label className="flex items-center gap-2">
                        <Palette className="h-4 w-4" />
                        Farbe
                      </Label>
                      <div className="flex flex-wrap gap-2">
                        {DEFAULT_EVENT_COLORS.map((colorOption) => (
                          <button
                            key={colorOption}
                            type="button"
                            onClick={() => setColor(colorOption)}
                            className={`
                              w-6 h-6 rounded-full border-2 hover:scale-110 transition-transform
                              ${color === colorOption ? 'border-foreground' : 'border-transparent'}
                            `}
                            style={{ backgroundColor: colorOption }}
                          />
                        ))}
                      </div>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="location" className="flex items-center gap-2">
                      <MapPin className="h-4 w-4" />
                      Ort
                    </Label>
                    <Input
                      id="location"
                      value={location}
                      onChange={(e) => setLocation(e.target.value)}
                      placeholder="Ort des Termins..."
                    />
                  </div>
                </div>

                {/* Reminder Settings */}
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <Label className="text-base font-medium flex items-center gap-2">
                      <Bell className="h-4 w-4" />
                      Erinnerung
                    </Label>
                    <Switch
                      checked={reminderEnabled}
                      onCheckedChange={setReminderEnabled}
                    />
                  </div>

                  {reminderEnabled && (
                    <div className="space-y-2">
                      <Label htmlFor="reminder-time">Erinnerung vor Termin</Label>
                      <Select 
                        value={reminderMinutes.toString()} 
                        onValueChange={(value) => setReminderMinutes(parseInt(value))}
                      >
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          {REMINDER_OPTIONS.map((option) => (
                            <SelectItem key={option.value} value={option.value.toString()}>
                              {option.label}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                  )}
                </div>
              </form>
            ) : (
              /* View Mode */
              <div className="space-y-6">
                {/* Event Header */}
                <div className="space-y-3">
                  <div className="flex items-start justify-between gap-3">
                    <h2 className={`text-xl font-semibold ${event.completed ? 'line-through opacity-60' : ''}`}>
                      {event.title}
                    </h2>
                    <Badge variant="secondary">
                      {EVENT_TYPE_LABELS[event.event_type]}
                    </Badge>
                  </div>
                  
                  {event.description && (
                    <p className="text-muted-foreground">{event.description}</p>
                  )}
                </div>

                {/* Event Details */}
                <div className="space-y-4">
                  <div className="flex items-center gap-2 text-sm">
                    <Calendar className="h-4 w-4 text-muted-foreground" />
                    <span>{formatEventDateTime(event)}</span>
                  </div>

                  {event.location && (
                    <div className="flex items-center gap-2 text-sm">
                      <MapPin className="h-4 w-4 text-muted-foreground" />
                      <span>{event.location}</span>
                    </div>
                  )}

                  {event.project && (
                    <div className="flex items-center justify-between gap-2 text-sm">
                      <div className="flex items-center gap-2">
                        <User className="h-4 w-4 text-muted-foreground" />
                        <span>{event.project.project_name} - {event.project.company_name}</span>
                      </div>
                      <Button 
                        size="sm" 
                        variant="ghost"
                        onClick={() => {
                          if (event.project?.id && /^[a-zA-Z0-9\-_]{20,}$/.test(event.project.id)) {
                            window.open(`/project/${event.project.id}`, '_blank');
                          }
                        }}
                        title="Zum Projekt wechseln"
                      >
                        <ExternalLink className="h-4 w-4" />
                      </Button>
                    </div>
                  )}

                  {event.reminder_enabled && event.reminder_minutes_before && (
                    <div className="flex items-center gap-2 text-sm">
                      <Bell className="h-4 w-4 text-muted-foreground" />
                      <span>
                        Erinnerung {REMINDER_OPTIONS.find(opt => opt.value === event.reminder_minutes_before)?.label || `${event.reminder_minutes_before} Minuten vorher`}
                      </span>
                    </div>
                  )}

                  {event.created_automatically && (
                    <div className="flex items-center gap-2 text-sm text-muted-foreground">
                      <Eye className="h-4 w-4" />
                      <span>Automatisch erstellt aus Projekt-Status</span>
                    </div>
                  )}
                </div>

                {/* Completion Status */}
                <div className="flex items-center gap-2 p-3 bg-muted/20 rounded-lg">
                  {event.completed ? (
                    <CheckCircle2 className="h-5 w-5 text-success" />
                  ) : (
                    <Circle className="h-5 w-5 text-muted-foreground" />
                  )}
                  <span className="text-sm font-medium">
                    {event.completed ? 'Abgeschlossen' : 'Ausstehend'}
                  </span>
                </div>
              </div>
            )}
          </div>

          {/* Dialog Footer */}
          <div className="flex justify-end gap-3 pt-4 border-t">
            {isEditing ? (
              <>
                <Button type="button" variant="outline" onClick={handleCancelEdit}>
                  Abbrechen
                </Button>
                <Button onClick={handleSave} disabled={isUpdating || !title.trim() || !startDate}>
                  {isUpdating ? (
                    <>
                      <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                      Speichere...
                    </>
                  ) : (
                    'Änderungen speichern'
                  )}
                </Button>
              </>
            ) : (
              <Button onClick={onClose}>
                Schließen
              </Button>
            )}
          </div>
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Termin löschen</AlertDialogTitle>
            <AlertDialogDescription>
              Sind Sie sicher, dass Sie den Termin "{event.title}" löschen möchten? 
              Diese Aktion kann nicht rückgängig gemacht werden.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Abbrechen</AlertDialogCancel>
            <AlertDialogAction 
              onClick={handleDelete}
              disabled={isDeleting}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              {isDeleting ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Lösche...
                </>
              ) : (
                'Löschen'
              )}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
};