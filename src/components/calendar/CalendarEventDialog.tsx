import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Badge } from '@/components/ui/badge';
import { useCalendarEvents } from '@/hooks/useCalendarEvents';
import { useTranslation } from '@/hooks/useTranslation';
import { format } from 'date-fns';
import { de, enUS } from 'date-fns/locale';
import { Calendar, Clock, MapPin, Bell, Palette, Loader2 } from 'lucide-react';
import type { Application } from '@/types/applications';
import type { Project } from '@/types/projects';
import type { CalendarEventType, CreateCalendarEventData } from '@/types/calendar';
import { EVENT_TYPE_LABELS, REMINDER_OPTIONS, DEFAULT_EVENT_COLORS } from '@/types/calendar';

interface CalendarEventDialogProps {
  isOpen: boolean;
  onClose: () => void;
  selectedDate: Date | null;
  applications: Application[];
  projects: Project[];
}

export const CalendarEventDialog = ({ isOpen, onClose, selectedDate, applications, projects }: CalendarEventDialogProps) => {
  const { t, currentLanguage } = useTranslation('calendar');
  
  // Form state
  const [title, setTitle] = useState('');
  const [description, setDescription] = useState('');
  const [referenceType, setReferenceType] = useState<'none' | 'application' | 'project'>('none');
  const [referenceId, setReferenceId] = useState('');
  const [startDate, setStartDate] = useState('');
  const [startTime, setStartTime] = useState('');
  const [endDate, setEndDate] = useState('');
  const [endTime, setEndTime] = useState('');
  const [allDay, setAllDay] = useState(false);
  const [eventType, setEventType] = useState<CalendarEventType>('manual');
  const [color, setColor] = useState(DEFAULT_EVENT_COLORS[0]);
  const [location, setLocation] = useState('');
  const [reminderEnabled, setReminderEnabled] = useState(false);
  const [reminderMinutes, setReminderMinutes] = useState(30);

  const { createEvent, isCreating } = useCalendarEvents();
  
  // Get the appropriate date-fns locale based on current language
  const dateLocale = currentLanguage === 'en' ? enUS : de;

  // Initialize form when dialog opens
  useEffect(() => {
    if (isOpen && selectedDate) {
      const dateStr = format(selectedDate, 'yyyy-MM-dd');
      setStartDate(dateStr);
      setEndDate(dateStr);
      
      if (!allDay) {
        const currentTime = format(new Date(), 'HH:mm');
        setStartTime(currentTime);
        // Set end time to 1 hour later
        const endTimeDate = new Date();
        endTimeDate.setHours(endTimeDate.getHours() + 1);
        setEndTime(format(endTimeDate, 'HH:mm'));
      }
    }
  }, [isOpen, selectedDate, allDay]);

  // Reset form when dialog closes
  useEffect(() => {
    if (!isOpen) {
      setTitle('');
      setDescription('');
      setReferenceType('none');
      setReferenceId('');
      setStartDate('');
      setStartTime('');
      setEndDate('');
      setEndTime('');
      setAllDay(false);
      setEventType('manual');
      setColor(DEFAULT_EVENT_COLORS[0]);
      setLocation('');
      setReminderEnabled(false);
      setReminderMinutes(30);
    }
  }, [isOpen]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!title.trim() || !startDate) {
      return;
    }

    try {
      const eventData: CreateCalendarEventData = {
        title: title.trim(),
        description: description.trim() || undefined,
        project_id: referenceType === 'project' ? referenceId : undefined,
        reference_type: referenceType !== 'none' ? referenceType : undefined,
        reference_id: referenceType !== 'none' ? referenceId : undefined,
        start_date: startDate,
        start_time: allDay ? undefined : startTime || undefined,
        end_date: endDate || undefined,
        end_time: allDay ? undefined : endTime || undefined,
        all_day: allDay,
        event_type: eventType,
        color,
        location: location.trim() || undefined,
        reminder_enabled: reminderEnabled,
        reminder_minutes_before: reminderEnabled ? reminderMinutes : undefined,
        created_automatically: false,
      };

      await createEvent(eventData);
      onClose();
    } catch (error) {
      console.error('Error creating event:', error);
    }
  };

  const handleAllDayToggle = (checked: boolean) => {
    setAllDay(checked);
    if (checked) {
      setStartTime('');
      setEndTime('');
    } else {
      const currentTime = format(new Date(), 'HH:mm');
      setStartTime(currentTime);
      // Set end time to 1 hour later
      const endTimeDate = new Date();
      endTimeDate.setHours(endTimeDate.getHours() + 1);
      setEndTime(format(endTimeDate, 'HH:mm'));
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[600px] max-h-[90vh] flex flex-col">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Calendar className="h-5 w-5" />
            {t('dialog.create_title')}
          </DialogTitle>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="flex-1 overflow-y-auto space-y-6">
          {/* Basic Information */}
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="title">{t('form.title_required')}</Label>
              <Input
                id="title"
                value={title}
                onChange={(e) => setTitle(e.target.value)}
                placeholder={t('placeholders.event_title')}
                required
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="description">{t('form.description')}</Label>
              <Textarea
                id="description"
                value={description}
                onChange={(e) => setDescription(e.target.value)}
                placeholder={t('placeholders.event_description')}
                rows={3}
              />
            </div>

            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="reference-type">{t('form.link_type')}</Label>
                <Select value={referenceType} onValueChange={(value: 'none' | 'application' | 'project') => {
                  setReferenceType(value);
                  setReferenceId(''); // Reset selection when type changes
                }}>
                  <SelectTrigger>
                    <SelectValue placeholder={t('actions.select_link_type')} />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="none">{t('form.no_link')}</SelectItem>
                    <SelectItem value="application">{t('form.link_application')}</SelectItem>
                    <SelectItem value="project">{t('form.link_project')}</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {referenceType === 'application' && (
                <div className="space-y-2">
                  <Label htmlFor="application">{t('form.select_application')}</Label>
                  <Select value={referenceId || undefined} onValueChange={(value) => setReferenceId(value || '')}>
                    <SelectTrigger>
                      <SelectValue placeholder={t('actions.select_application')} />
                    </SelectTrigger>
                    <SelectContent>
                      {applications.map((application) => (
                        <SelectItem key={application.id} value={application.id}>
                          {application.project_name} - {application.company_name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              )}

              {referenceType === 'project' && (
                <div className="space-y-2">
                  <Label htmlFor="project">{t('form.select_project')}</Label>
                  <Select value={referenceId || undefined} onValueChange={(value) => setReferenceId(value || '')}>
                    <SelectTrigger>
                      <SelectValue placeholder={t('actions.select_project')} />
                    </SelectTrigger>
                    <SelectContent>
                      {projects.map((project) => (
                        <SelectItem key={project.id} value={project.id}>
                          {project.title} - {project.client_name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              )}
            </div>
          </div>

          {/* Date and Time */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <Label className="text-base font-medium">{t('form.date_time')}</Label>
              <div className="flex items-center space-x-2">
                <Switch
                  id="all-day"
                  checked={allDay}
                  onCheckedChange={handleAllDayToggle}
                />
                <Label htmlFor="all-day" className="text-sm">{t('all_day')}</Label>
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="start-date">{t('form.start_date')} *</Label>
                <Input
                  id="start-date"
                  type="date"
                  value={startDate}
                  onChange={(e) => setStartDate(e.target.value)}
                  required
                />
              </div>
              {!allDay && (
                <div className="space-y-2">
                  <Label htmlFor="start-time">{t('form.start_time')}</Label>
                  <Input
                    id="start-time"
                    type="time"
                    value={startTime}
                    onChange={(e) => setStartTime(e.target.value)}
                  />
                </div>
              )}
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="end-date">{t('form.end_date')}</Label>
                <Input
                  id="end-date"
                  type="date"
                  value={endDate}
                  onChange={(e) => setEndDate(e.target.value)}
                />
              </div>
              {!allDay && (
                <div className="space-y-2">
                  <Label htmlFor="end-time">{t('form.end_time')}</Label>
                  <Input
                    id="end-time"
                    type="time"
                    value={endTime}
                    onChange={(e) => setEndTime(e.target.value)}
                  />
                </div>
              )}
            </div>
          </div>

          {/* Event Type and Color */}
          <div className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="event-type">{t('form.event_type')}</Label>
                <Select value={eventType} onValueChange={(value: CalendarEventType) => setEventType(value)}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {Object.entries(EVENT_TYPE_LABELS).map(([value, label]) => (
                      <SelectItem key={value} value={value}>
                        {t(`event_types.${value}`)}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label className="flex items-center gap-2">
                  <Palette className="h-4 w-4" />
                  {t('form.color')}
                </Label>
                <div className="flex flex-wrap gap-2">
                  {DEFAULT_EVENT_COLORS.map((colorOption) => (
                    <button
                      key={colorOption}
                      type="button"
                      onClick={() => setColor(colorOption)}
                      className={`
                        w-8 h-8 rounded-full border-2 hover:scale-110 transition-transform
                        ${color === colorOption ? 'border-foreground' : 'border-transparent'}
                      `}
                      style={{ backgroundColor: colorOption }}
                    />
                  ))}
                </div>
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="location" className="flex items-center gap-2">
                <MapPin className="h-4 w-4" />
                {t('form.location')}
              </Label>
              <Input
                id="location"
                value={location}
                onChange={(e) => setLocation(e.target.value)}
                placeholder={t('placeholders.event_location')}
              />
            </div>
          </div>

          {/* Reminder Settings */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <Label className="text-base font-medium flex items-center gap-2">
                <Bell className="h-4 w-4" />
                {t('reminder')}
              </Label>
              <Switch
                checked={reminderEnabled}
                onCheckedChange={setReminderEnabled}
              />
            </div>

            {reminderEnabled && (
              <div className="space-y-2">
                <Label htmlFor="reminder-time">{t('form.reminder_before')}</Label>
                <Select 
                  value={reminderMinutes.toString()} 
                  onValueChange={(value) => setReminderMinutes(parseInt(value))}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {REMINDER_OPTIONS.map((option) => (
                      <SelectItem key={option.value} value={option.value.toString()}>
                        {t(`reminders.${option.value === 0 ? 'none' : option.value === 5 ? '5_min' : option.value === 15 ? '15_min' : option.value === 30 ? '30_min' : option.value === 60 ? '1_hour' : option.value === 1440 ? '1_day' : 'none'}`)}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            )}
          </div>
        </form>

        {/* Dialog Footer */}
        <div className="flex justify-end gap-3 pt-4 border-t">
          <Button type="button" variant="outline" onClick={onClose}>
            {t('actions.cancel')}
          </Button>
          <Button onClick={handleSubmit} disabled={isCreating || !title.trim() || !startDate}>
            {isCreating ? (
              <>
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                {t('actions.creating')}
              </>
            ) : (
              t('actions.create')
            )}
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
};