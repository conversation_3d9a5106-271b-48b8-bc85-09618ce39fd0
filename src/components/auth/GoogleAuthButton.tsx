import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { supabase } from '@/integrations/supabase/client';
import { toast } from '@/lib/toast';
import { Loader2, Chrome } from 'lucide-react';

interface GoogleAuthButtonProps {
  mode?: 'signin' | 'signup';
  className?: string;
}

export const GoogleAuthButton = ({ 
  mode = 'signin', 
  className = '' 
}: GoogleAuthButtonProps) => {
  const [loading, setLoading] = useState(false);

  const handleGoogleAuth = async () => {
    setLoading(true);
    
    try {
      const { data, error } = await supabase.auth.signInWithOAuth({
        provider: 'google',
        options: {
          // Use a simpler redirect that works with both PKCE and implicit flows
          redirectTo: `${window.location.origin}/`,
          // Request necessary scopes for user information
          scopes: 'https://www.googleapis.com/auth/userinfo.email https://www.googleapis.com/auth/userinfo.profile',
          // Additional query parameters for better UX
          queryParams: {
            access_type: 'offline',
            prompt: 'select_account', // Always show account picker
          },
        },
      });

      if (error) {
        throw error;
      }

      // Note: The user will be redirected to Google at this point
      // The actual sign-in completion happens in the callback handler
      
    } catch (error: unknown) {
      console.error('Google Auth Error:', error);
      const errorMessage = error instanceof Error ? error.message : 'Ein unerwarteter Fehler ist aufgetreten.';
      toast.error('Google-Anmeldung fehlgeschlagen', errorMessage);
      setLoading(false);
    }
  };

  const buttonText = mode === 'signin' 
    ? 'Mit Google anmelden' 
    : 'Mit Google registrieren';

  return (
    <Button
      type="button"
      variant="outline"
      onClick={handleGoogleAuth}
      disabled={loading}
      className={`w-full h-12 text-base font-medium border-2 hover:bg-accent/50 transition-all duration-300 ${className}`}
    >
      {loading ? (
        <Loader2 className="w-5 h-5 animate-spin" />
      ) : (
        <div className="flex items-center gap-3">
          <Chrome className="w-5 h-5" />
          {buttonText}
        </div>
      )}
    </Button>
  );
};