import React from 'react';
import { Link, useLocation } from 'react-router-dom';
import { ChevronRight, Home } from 'lucide-react';

export interface BreadcrumbItem {
  label: string;
  href?: string;
  current?: boolean;
}

interface BreadcrumbsProps {
  items?: BreadcrumbItem[];
  className?: string;
}

// Default breadcrumb mapping based on routes
const routeToBreadcrumb: Record<string, BreadcrumbItem[]> = {
  '/': [{ label: 'Dashboard', current: true }],
  '/applications': [
    { label: 'Dashboard', href: '/' },
    { label: 'Bewerbungen', current: true }
  ],
  '/applications/new': [
    { label: 'Dashboard', href: '/' },
    { label: 'Bewerbungen', href: '/applications' },
    { label: 'Neue Bewerbung', current: true }
  ],
  '/projects': [
    { label: 'Dashboard', href: '/' },
    { label: 'Aktive Projekte', current: true }
  ],
  '/projects/new': [
    { label: 'Dashboard', href: '/' },
    { label: 'Aktive Projekte', href: '/projects' },
    { label: 'Neues Projekt', current: true }
  ],
  '/statistics': [
    { label: 'Dashboard', href: '/' },
    { label: 'Statistiken', current: true }
  ],
  '/contacts': [
    { label: 'Dashboard', href: '/' },
    { label: 'Kontakte', current: true }
  ],
  '/contacts/new': [
    { label: 'Dashboard', href: '/' },
    { label: 'Kontakte', href: '/contacts' },
    { label: 'Neuer Kontakt', current: true }
  ],
  '/calendar': [
    { label: 'Dashboard', href: '/' },
    { label: 'Kalender', current: true }
  ],
  '/settings': [
    { label: 'Dashboard', href: '/' },
    { label: 'Einstellungen', current: true }
  ],
  '/follow-ups': [
    { label: 'Dashboard', href: '/' },
    { label: 'Follow-ups', current: true }
  ],
  '/notifications': [
    { label: 'Dashboard', href: '/' },
    { label: 'Benachrichtigungen', current: true }
  ]
};

export const Breadcrumbs: React.FC<BreadcrumbsProps> = ({
  items,
  className = ''
}) => {
  const location = useLocation();
  
  // Use provided items or generate from current path
  const breadcrumbItems = items || routeToBreadcrumb[location.pathname] || [
    { label: 'Dashboard', href: '/' },
    { label: 'Seite', current: true }
  ];

  // Don't show breadcrumbs on homepage or if only one item
  if (location.pathname === '/' || breadcrumbItems.length <= 1) {
    return null;
  }

  return (
    <nav 
      aria-label="Breadcrumb" 
      className={`flex items-center space-x-1 text-sm text-muted-foreground mb-6 ${className}`}
      role="navigation"
    >
      <ol className="flex items-center space-x-1 list-none">
        {/* Home icon for first item if it's dashboard */}
        {breadcrumbItems[0]?.label === 'Dashboard' && (
          <li className="flex items-center">
            <Link 
              to="/" 
              className="flex items-center hover:text-foreground transition-colors"
              title="Zum Dashboard"
            >
              <Home className="h-4 w-4" aria-hidden="true" />
              <span className="sr-only">Dashboard</span>
            </Link>
            <ChevronRight className="h-4 w-4 mx-1" aria-hidden="true" />
          </li>
        )}

        {breadcrumbItems.map((item, index) => (
          <li key={index} className="flex items-center">
            {item.current ? (
              <span 
                className="text-foreground font-medium" 
                aria-current="page"
              >
                {item.label}
              </span>
            ) : (
              <>
                <Link
                  to={item.href || '#'}
                  className="hover:text-foreground transition-colors"
                  title={`Zu ${item.label}`}
                >
                  {item.label}
                </Link>
                {index < breadcrumbItems.length - 1 && (
                  <ChevronRight className="h-4 w-4 mx-1" aria-hidden="true" />
                )}
              </>
            )}
          </li>
        ))}
      </ol>

      {/* Structured data for breadcrumbs */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify({
            "@context": "https://schema.org",
            "@type": "BreadcrumbList",
            "itemListElement": breadcrumbItems.map((item, index) => ({
              "@type": "ListItem",
              "position": index + 1,
              "name": item.label,
              ...(item.href && {
                "item": `https://lanzr.de${item.href}`
              })
            }))
          })
        }}
      />
    </nav>
  );
};