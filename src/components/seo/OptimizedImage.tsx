import React from 'react';
import { cn } from '@/lib/utils';

interface OptimizedImageProps extends React.ImgHTMLAttributes<HTMLImageElement> {
  src: string;
  alt: string;
  width?: number;
  height?: number;
  loading?: 'lazy' | 'eager';
  priority?: boolean;
  className?: string;
  sizes?: string;
}

export const OptimizedImage: React.FC<OptimizedImageProps> = ({
  src,
  alt,
  width,
  height,
  loading = 'lazy',
  priority = false,
  className,
  sizes,
  ...props
}) => {
  // For priority images, use eager loading
  const loadingStrategy = priority ? 'eager' : loading;

  // Generate responsive srcset if width is provided
  const generateSrcSet = (baseSrc: string, baseWidth?: number) => {
    if (!baseWidth) return undefined;
    
    const breakpoints = [1, 1.5, 2, 3];
    return breakpoints
      .map(scale => {
        const scaledWidth = Math.round(baseWidth * scale);
        return `${baseSrc}?w=${scaledWidth} ${scale}x`;
      })
      .join(', ');
  };

  // Default sizes for responsive images
  const defaultSizes = sizes || (width ? `${width}px` : '100vw');

  return (
    <img
      src={src}
      alt={alt}
      width={width}
      height={height}
      loading={loadingStrategy}
      decoding="async"
      srcSet={generateSrcSet(src, width)}
      sizes={defaultSizes}
      className={cn(
        'transition-opacity duration-300',
        className
      )}
      {...props}
      // Add structured data for important images
      {...(priority && {
        'data-priority': 'true'
      })}
    />
  );
};

// Specific components for common use cases
export const LogoImage: React.FC<Omit<OptimizedImageProps, 'alt'>> = (props) => (
  <OptimizedImage
    {...props}
    alt="Lanzr Logo - Freelance Projektmanagement"
    loading="eager"
    priority={true}
  />
);

export const ProfileImage: React.FC<Omit<OptimizedImageProps, 'loading'>> = (props) => (
  <OptimizedImage
    {...props}
    loading="lazy"
    className={cn('rounded-full object-cover', props.className)}
  />
);

export const ScreenshotImage: React.FC<Omit<OptimizedImageProps, 'loading'>> = (props) => (
  <OptimizedImage
    {...props}
    loading="lazy"
    className={cn('rounded-lg border shadow-md', props.className)}
  />
);

export const IconImage: React.FC<Omit<OptimizedImageProps, 'loading' | 'alt'> & { name: string }> = ({ name, ...props }) => (
  <OptimizedImage
    {...props}
    alt={`${name} Icon`}
    loading="lazy"
    className={cn('inline-block', props.className)}
  />
);