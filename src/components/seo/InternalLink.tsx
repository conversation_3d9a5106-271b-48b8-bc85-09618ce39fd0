import React from 'react';
import { Link } from 'react-router-dom';
import { cn } from '@/lib/utils';

interface InternalLinkProps {
  to: string;
  children: React.ReactNode;
  className?: string;
  title?: string;
  prefetch?: boolean;
  rel?: string;
}

// SEO-optimized internal link component
export const InternalLink: React.FC<InternalLinkProps> = ({
  to,
  children,
  className,
  title,
  prefetch = false,
  rel,
  ...props
}) => {
  return (
    <Link
      to={to}
      className={cn(
        'text-primary hover:text-primary/80 transition-colors duration-200',
        'underline decoration-primary/30 hover:decoration-primary/60',
        'underline-offset-4',
        className
      )}
      title={title}
      rel={rel}
      {...(prefetch && { 'data-prefetch': 'true' })}
      {...props}
    >
      {children}
    </Link>
  );
};

// Predefined internal links for common pages
export const DashboardLink: React.FC<{ children: React.ReactNode; className?: string }> = ({ children, className }) => (
  <InternalLink 
    to="/" 
    className={className}
    title="Zum Lanzr Dashboard - Projektübersicht und KPIs"
  >
    {children}
  </InternalLink>
);

export const ApplicationsLink: React.FC<{ children: React.ReactNode; className?: string }> = ({ children, className }) => (
  <InternalLink 
    to="/applications" 
    className={className}
    title="Bewerbungsmanagement - AI-gestützte Bewerbungserstellung"
  >
    {children}
  </InternalLink>
);

export const ProjectsLink: React.FC<{ children: React.ReactNode; className?: string }> = ({ children, className }) => (
  <InternalLink 
    to="/projects" 
    className={className}
    title="Aktive Projekte verwalten - Projektmanagement und Zeiterfassung"
  >
    {children}
  </InternalLink>
);

export const StatisticsLink: React.FC<{ children: React.ReactNode; className?: string }> = ({ children, className }) => (
  <InternalLink 
    to="/statistics" 
    className={className}
    title="Freelance Analytics - Detaillierte Statistiken und Erfolgsmessung"
  >
    {children}
  </InternalLink>
);

export const ContactsLink: React.FC<{ children: React.ReactNode; className?: string }> = ({ children, className }) => (
  <InternalLink 
    to="/contacts" 
    className={className}
    title="Kontaktmanagement - CRM für Freelancer und Lead-Verwaltung"
  >
    {children}
  </InternalLink>
);

export const CalendarLink: React.FC<{ children: React.ReactNode; className?: string }> = ({ children, className }) => (
  <InternalLink 
    to="/calendar" 
    className={className}
    title="Kalender - Terminplanung und Projekttermine verwalten"
  >
    {children}
  </InternalLink>
);

export const SettingsLink: React.FC<{ children: React.ReactNode; className?: string }> = ({ children, className }) => (
  <InternalLink 
    to="/settings" 
    className={className}
    title="Einstellungen - Profil und Präferenzen konfigurieren"
  >
    {children}
  </InternalLink>
);

// Navigation menu component with internal linking
interface NavigationMenuItem {
  label: string;
  href: string;
  description: string;
}

const navigationMenuItems: NavigationMenuItem[] = [
  {
    label: 'Dashboard',
    href: '/',
    description: 'Projektübersicht und wichtige KPIs auf einen Blick'
  },
  {
    label: 'Bewerbungen',
    href: '/applications',
    description: 'AI-gestützte Bewerbungserstellung und -management'
  },
  {
    label: 'Aktive Projekte',
    href: '/projects',
    description: 'Projektmanagement mit Zeiterfassung und Reporting'
  },
  {
    label: 'Statistiken',
    href: '/statistics',
    description: 'Detaillierte Analytics für Freelance-Erfolg'
  },
  {
    label: 'Kontakte',
    href: '/contacts',
    description: 'CRM und Lead-Management für Freelancer'
  },
  {
    label: 'Kalender',
    href: '/calendar',
    description: 'Terminplanung und Projekttermine'
  }
];

interface SEONavigationMenuProps {
  className?: string;
  orientation?: 'horizontal' | 'vertical';
}

export const SEONavigationMenu: React.FC<SEONavigationMenuProps> = ({
  className,
  orientation = 'horizontal'
}) => {
  return (
    <nav 
      className={cn(
        'navigation-menu',
        orientation === 'horizontal' ? 'flex flex-wrap gap-6' : 'flex flex-col space-y-4',
        className
      )}
      role="navigation"
      aria-label="Hauptnavigation"
    >
      {navigationMenuItems.map((item) => (
        <div key={item.href} className="navigation-item">
          <InternalLink
            to={item.href}
            className="font-medium text-base"
            title={item.description}
          >
            {item.label}
          </InternalLink>
          <p className="text-sm text-muted-foreground mt-1">
            {item.description}
          </p>
        </div>
      ))}
    </nav>
  );
};