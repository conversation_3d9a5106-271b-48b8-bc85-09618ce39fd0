import React, { createContext, useContext } from 'react';
import { AppSidebar } from '@/components/layout/AppSidebar';
import { SidebarProvider, SidebarInset } from '@/components/ui/sidebar';
import { NotificationBell } from '@/components/notifications';
import { GlobalSearchPalette } from '@/components/global-search';
import { QuickSearchBar } from '@/components/global-search/QuickSearchBar';
import { Button } from '@/components/ui/button';
import { Search, ChevronRight } from 'lucide-react';
import { toast } from '@/lib/toast';
import { useAuth } from '@/contexts/AuthContext';
import { supabase } from '@/integrations/supabase/client';
import { useCurrentPageConfig } from '@/hooks/usePageConfig';

interface PageHeaderContextType {
  title: string;
  description?: string;
  setPageHeader: (title: string, description?: string) => void;
}

const PageHeaderContext = createContext<PageHeaderContextType | null>(null);

export const usePageHeader = () => {
  const context = useContext(PageHeaderContext);
  if (!context) {
    throw new Error('usePageHeader must be used within AppLayout');
  }
  return context;
};

interface AppLayoutProps {
  children: React.ReactNode;
}

export const AppLayout = ({ children }: AppLayoutProps) => {
  const { user } = useAuth();
  const pageConfig = useCurrentPageConfig();
  
  // Simplified state management - use pageConfig directly for most cases
  const [customTitle, setCustomTitle] = React.useState<string | null>(null);
  const [customDescription, setCustomDescription] = React.useState<string | undefined>(undefined);
  const [isSearchOpen, setIsSearchOpen] = React.useState(false);

  // Use custom title/description if set, otherwise fall back to pageConfig
  const displayTitle = customTitle || pageConfig.title;
  const displayDescription = customDescription !== undefined ? customDescription : pageConfig.description;

  // Global keyboard shortcuts - only effect needed
  React.useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if ((e.metaKey || e.ctrlKey) && e.key === 'k') {
        e.preventDefault();
        setIsSearchOpen(prev => !prev);
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, []);

  // Reset custom title/description when route changes to use pageConfig
  React.useEffect(() => {
    setCustomTitle(null);
    setCustomDescription(undefined);
  }, [pageConfig.title, pageConfig.description]);

  const setPageHeader = React.useCallback((title: string, description?: string) => {
    setCustomTitle(title);
    setCustomDescription(description);
  }, []);

  // Stable context value - only changes when displayTitle/displayDescription actually change
  const pageHeaderValue = React.useMemo(() => ({
    title: displayTitle,
    description: displayDescription,
    setPageHeader
  }), [displayTitle, displayDescription, setPageHeader]);

  const handleSignOut = async () => {
    try {
      const { error } = await supabase.auth.signOut();
      if (error) throw error;
      
      toast.success('Abgemeldet', 'Sie wurden erfolgreich abgemeldet.');
    } catch (error: any) {
      toast.error('Fehler beim Abmelden', error.message);
    }
  };

  return (
    <SidebarProvider defaultOpen={true}>
      <PageHeaderContext.Provider value={pageHeaderValue}>
        <div className="min-h-screen flex w-full bg-background">
          <AppSidebar
            user={user}
            onSignOut={handleSignOut}
          />

          <SidebarInset className="flex-1 flex flex-col">
            {/* Mobile Header - with title and search icon */}
            <header className="border-b border-sidebar-border bg-sidebar md:hidden sticky top-0 z-10 h-14">
              <div className="flex items-center justify-between px-4 h-full gap-3">
                <div className="flex items-center gap-1 text-sm min-w-0 flex-1">
                  <img src="/logo_icon.png" alt="Lanzr" className="h-4 w-4 object-contain flex-shrink-0" />
                  <ChevronRight className="h-3 w-3 text-sidebar-foreground/60 flex-shrink-0 translate-y-[0.5px]" />
                  <span className="font-medium text-sidebar-foreground truncate">{displayTitle}</span>
                </div>
                <div className="flex items-center gap-2 flex-shrink-0">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setIsSearchOpen(true)}
                    className="h-8 w-8 p-0 text-sidebar-foreground hover:bg-sidebar-accent"
                  >
                    <Search className="h-4 w-4" />
                  </Button>
                  <NotificationBell />
                </div>
              </div>
            </header>

            {/* Desktop Header - with breadcrumb and search bar */}
            <header className="border-b border-sidebar-border bg-sidebar sticky top-0 z-10 hidden md:block h-14">
              <div className="flex items-center justify-between px-6 h-full gap-4">
                <div className="flex items-center gap-1 text-sm min-w-0">
                  <img src="/logo_icon.png" alt="Lanzr" className="h-5 w-5 object-contain flex-shrink-0" />
                  <ChevronRight className="h-3 w-3 text-sidebar-foreground/60 flex-shrink-0 translate-y-[0.5px]" />
                  <span className="font-medium text-sidebar-foreground">{displayTitle}</span>
                  {displayDescription && (
                    <>
                      <span className="text-sidebar-foreground/40 mx-1 leading-none">•</span>
                      <span className="text-sidebar-foreground/60 text-sm truncate">{displayDescription}</span>
                    </>
                  )}
                </div>
                <div className="flex items-center gap-2 flex-shrink-0">
                  <QuickSearchBar onGlobalSearchOpen={() => setIsSearchOpen(true)} />
                  <NotificationBell />
                </div>
              </div>
            </header>

            <main className="flex-1 py-3 px-2 sm:p-6 max-w-full overflow-x-hidden">
              {children}
            </main>
          </SidebarInset>
        </div>

        {/* Global Search Palette */}
        <GlobalSearchPalette 
          isOpen={isSearchOpen}
          onClose={() => setIsSearchOpen(false)}
        />
      </PageHeaderContext.Provider>
    </SidebarProvider>
  );
};