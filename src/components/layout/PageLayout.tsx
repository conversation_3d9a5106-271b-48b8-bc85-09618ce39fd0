import React from 'react';
import { usePageHeader } from './AppLayout';

interface PageLayoutProps {
  children: React.ReactNode;
  title: string;
  description?: string;
  headerActions?: React.ReactNode;
}

/**
 * Standardized page layout component that ensures consistent styling across all pages.
 * 
 * Features:
 * - Sets global page header through context
 * - Consistent spacing and responsive design
 * - Mobile-first responsive layout
 * - Optional header actions rendered within page content
 * 
 * Usage:
 * ```tsx
 * <PageLayout 
 *   title="Page Title" 
 *   description="Optional description"
 *   headerActions={<Button>Action</Button>}
 * >
 *   <YourPageContent />
 * </PageLayout>
 * ```
 */
export const PageLayout: React.FC<PageLayoutProps> = ({ 
  children, 
  title, 
  description,
  headerActions 
}) => {
  const { setPageHeader } = usePageHeader();

  // Set the global page header when component mounts or title/description changes
  React.useEffect(() => {
    setPageHeader(title, description);
  }, [title, description, setPageHeader]);

  return (
    <main className="px-2 sm:px-2 pb-3 sm:pb-6 space-y-4 sm:space-y-6 max-w-full overflow-x-hidden min-w-0" role="main">
      {/* Header Actions (if any) */}
      {headerActions && (
        <div className="flex justify-end w-full min-w-0" role="toolbar" aria-label="Seitenaktionen">
          <div className="flex items-center gap-2 sm:gap-4 flex-shrink-0">
            {headerActions}
          </div>
        </div>
      )}

      {/* Page Content */}
      <section className="space-y-4 sm:space-y-6 w-full min-w-0" role="region" aria-label="Hauptinhalt">
        {children}
      </section>
    </main>
  );
};