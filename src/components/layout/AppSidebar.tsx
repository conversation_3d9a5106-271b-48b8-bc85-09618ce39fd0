import React from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarMenuSub,
  SidebarMenuSubButton,
  SidebarMenuSubItem,
  SidebarSeparator,
  SidebarRail,
  SidebarTrigger,
  useSidebar
} from '@/components/ui/sidebar';
import { ThemeToggle } from '@/components/theme-toggle';
import { Button } from '@/components/ui/button';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { LogOut, User, BarChart3, Settings as SettingsIcon, PieChart, Calendar, Users, Clock, Timer, ChevronRight, FolderOpen, FileText, Target, Bell, TrendingUp } from 'lucide-react';
import { useIsMobile } from '@/hooks/use-mobile';
import { useUserSettings } from '@/hooks/useUserSettings';
import { useTranslation } from '@/hooks/useTranslation';
import { LanguageSwitcher } from '@/components/i18n/LanguageSwitcher';
import type { User as SupabaseUser } from '@supabase/supabase-js';

interface AppSidebarProps {
  user: SupabaseUser | null;
  onSignOut: () => void;
}

/**
 * AppSidebar - Collapsible sidebar component
 * 
 * Features:
 * - Collapsible with icon-only mode
 * - Keyboard shortcut: Cmd/Ctrl + B to toggle
 * - Tooltips in collapsed state
 * - Responsive design for mobile/desktop
 * - Auto-collapses sub-menus when collapsed
 */

export const AppSidebar: React.FC<AppSidebarProps> = ({
  user,
  onSignOut,
}) => {
  const { t, i18n } = useTranslation('common');
  
  const { setOpenMobile, state } = useSidebar();
  const isMobile = useIsMobile();
  const navigate = useNavigate();
  const location = useLocation();
  const [activeProjectsExpanded, setActiveProjectsExpanded] = React.useState(
    location.pathname.startsWith('/projects')
  );
  const [akquiseExpanded, setAkquiseExpanded] = React.useState(
    location.pathname.startsWith('/applications') || location.pathname.startsWith('/statistics')
  );
  const { settings } = useUserSettings();

  // Auto-collapse sub-menus when sidebar is collapsed
  React.useEffect(() => {
    if (state === 'collapsed') {
      setActiveProjectsExpanded(false);
      setAkquiseExpanded(false);
    } else {
      // Re-expand relevant sections when sidebar expands
      if (location.pathname.startsWith('/projects')) {
        setActiveProjectsExpanded(true);
      }
      if (location.pathname.startsWith('/applications') || location.pathname.startsWith('/statistics')) {
        setAkquiseExpanded(true);
      }
    }
  }, [state, location.pathname]);

  // Auto-expand sections when navigating to sub-pages
  React.useEffect(() => {
    if (location.pathname.startsWith('/projects')) {
      setActiveProjectsExpanded(true);
    }
    if (location.pathname.startsWith('/applications') || location.pathname.startsWith('/statistics')) {
      setAkquiseExpanded(true);
    }
  }, [location.pathname]);

  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(n => n[0])
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  const handleNavigation = (path: string) => {
    navigate(path);
    // Close sidebar on mobile after navigation
    if (isMobile) {
      setOpenMobile(false);
    }
  };

  const navigationItems = [
    {
      id: 'dashboard',
      label: t('navigation.dashboard'),
      icon: BarChart3,
      path: '/',
    },
    {
      id: 'contacts',
      label: t('navigation.contacts'),
      icon: Users,
      path: '/contacts',
    },
    {
      id: 'calendar',
      label: t('navigation.calendar'),
      icon: Calendar,
      path: '/calendar',
    },
    {
      id: 'settings',
      label: t('navigation.settings'),
      icon: SettingsIcon,
      path: '/settings',
    },
  ];

  const activeProjectsSubItems = [
    {
      id: 'projects-overview',
      label: t('navigation.projects'),
      icon: FolderOpen,
      path: '/projects',
    },
    {
      id: 'projects-timer',
      label: t('navigation.time_tracking'),
      icon: Timer,
      path: '/projects/timer',
    },
    {
      id: 'projects-reports',
      label: t('navigation.reports'),
      icon: BarChart3,
      path: '/projects/reports',
    },
  ];

  const akquiseSubItems = [
    {
      id: 'applications',
      label: t('navigation.applications'),
      icon: Target,
      path: '/applications',
    },
    {
      id: 'statistics',
      label: t('navigation.statistics'),
      icon: PieChart,
      path: '/statistics',
    },
  ];

  return (
    <Sidebar variant="sidebar" collapsible="icon">
      <SidebarHeader className="p-0">
        {/* User Profile Section */}
        {user && (
          <div className="px-4 py-3 group-data-[collapsible=icon]:px-2">
              <div className="flex items-center space-x-3 p-3 rounded-lg bg-sidebar-accent/50 group-data-[collapsible=icon]:justify-center group-data-[collapsible=icon]:p-2">
                <Avatar className="h-10 w-10 border border-sidebar-border group-data-[collapsible=icon]:h-8 group-data-[collapsible=icon]:w-8">
                  <AvatarImage 
                    src={settings?.profile_picture_url || ""} 
                    alt={settings?.full_name || "User"} 
                  />
                  <AvatarFallback className="bg-sidebar-accent text-sidebar-accent-foreground text-sm font-medium group-data-[collapsible=icon]:text-xs">
                    {settings?.full_name ? getInitials(settings.full_name) : <User className="h-5 w-5 group-data-[collapsible=icon]:h-4 group-data-[collapsible=icon]:w-4" />}
                  </AvatarFallback>
                </Avatar>
                <div className="min-w-0 flex-1 group-data-[collapsible=icon]:hidden">
                  <p className="text-sm font-medium text-sidebar-foreground">{t('general.welcome')}</p>
                  <p className="text-xs text-sidebar-foreground/70 truncate">
                    {settings?.full_name || user.email}
                  </p>
                </div>
              </div>
            </div>
        )}
      </SidebarHeader>

      <SidebarContent>
        <SidebarGroup>
          <SidebarGroupContent>
            <SidebarMenu>
              {/* Dashboard */}
              <SidebarMenuItem>
                <SidebarMenuButton
                  isActive={location.pathname === '/'}
                  onClick={() => handleNavigation('/')}
                  className="w-full"
                  tooltip={state === 'collapsed' ? t('navigation.dashboard') : undefined}
                >
                  <BarChart3 className="h-4 w-4" />
                  <span>{t('navigation.dashboard')}</span>
                </SidebarMenuButton>
              </SidebarMenuItem>

              {/* Akquise Section */}
              <SidebarMenuItem>
                <SidebarMenuButton
                  onClick={() => state === 'collapsed' 
                    ? handleNavigation('/applications') 
                    : setAkquiseExpanded(!akquiseExpanded)
                  }
                  className="w-full cursor-pointer"
                  isActive={location.pathname.startsWith('/applications') || location.pathname.startsWith('/statistics')}
                  tooltip={state === 'collapsed' ? t('navigation.acquisition') : undefined}
                >
                  <TrendingUp className="h-4 w-4" />
                  <span>{t('navigation.acquisition')}</span>
                  <ChevronRight 
                    className={`ml-auto h-4 w-4 transition-transform ${
                      akquiseExpanded ? 'rotate-90' : ''
                    }`} 
                  />
                </SidebarMenuButton>
                {akquiseExpanded && (
                  <SidebarMenuSub>
                    {akquiseSubItems.map((subItem) => (
                      <SidebarMenuSubItem key={subItem.id}>
                        <SidebarMenuSubButton
                          isActive={location.pathname === subItem.path}
                          onClick={() => handleNavigation(subItem.path)}
                          className="cursor-pointer"
                        >
                          <subItem.icon className="h-4 w-4" />
                          <span>{subItem.label}</span>
                        </SidebarMenuSubButton>
                      </SidebarMenuSubItem>
                    ))}
                  </SidebarMenuSub>
                )}
              </SidebarMenuItem>

              {/* Active Projects Section */}
              <SidebarMenuItem>
                <SidebarMenuButton
                  onClick={() => state === 'collapsed' 
                    ? handleNavigation('/projects') 
                    : setActiveProjectsExpanded(!activeProjectsExpanded)
                  }
                  className="w-full cursor-pointer"
                  isActive={location.pathname.startsWith('/projects')}
                  tooltip={state === 'collapsed' ? t('navigation.projects') : undefined}
                >
                  <Clock className="h-4 w-4" />
                  <span>{t('navigation.projects')}</span>
                  <ChevronRight 
                    className={`ml-auto h-4 w-4 transition-transform ${
                      activeProjectsExpanded ? 'rotate-90' : ''
                    }`} 
                  />
                </SidebarMenuButton>
                {activeProjectsExpanded && (
                  <SidebarMenuSub>
                    {activeProjectsSubItems.map((subItem) => (
                      <SidebarMenuSubItem key={subItem.id}>
                        <SidebarMenuSubButton
                          isActive={location.pathname === subItem.path}
                          onClick={() => handleNavigation(subItem.path)}
                          className="cursor-pointer"
                        >
                          <subItem.icon className="h-4 w-4" />
                          <span>{subItem.label}</span>
                        </SidebarMenuSubButton>
                      </SidebarMenuSubItem>
                    ))}
                  </SidebarMenuSub>
                )}
              </SidebarMenuItem>

              {/* Other navigation items */}
              {navigationItems.slice(1).map((item) => {
                // Add Follow-ups after Calendar
                if (item.id === 'calendar') {
                  return (
                    <React.Fragment key={item.id}>
                      <SidebarMenuItem>
                        <SidebarMenuButton
                          isActive={location.pathname === item.path}
                          onClick={() => handleNavigation(item.path)}
                          className="w-full"
                          tooltip={state === 'collapsed' ? item.label : undefined}
                        >
                          <item.icon className="h-4 w-4" />
                          <span>{item.label}</span>
                        </SidebarMenuButton>
                      </SidebarMenuItem>
                      {/* Follow-ups */}
                      <SidebarMenuItem>
                        <SidebarMenuButton
                          isActive={location.pathname === '/follow-ups'}
                          onClick={() => handleNavigation('/follow-ups')}
                          className="w-full"
                          tooltip={state === 'collapsed' ? t('navigation.followups') : undefined}
                        >
                          <Bell className="h-4 w-4" />
                          <span>{t('navigation.followups')}</span>
                        </SidebarMenuButton>
                      </SidebarMenuItem>
                    </React.Fragment>
                  );
                }
                
                return (
                  <SidebarMenuItem key={item.id}>
                    <SidebarMenuButton
                      isActive={location.pathname === item.path}
                      onClick={() => handleNavigation(item.path)}
                      className="w-full"
                      tooltip={state === 'collapsed' ? item.label : undefined}
                    >
                      <item.icon className="h-4 w-4" />
                      <span>{item.label}</span>
                    </SidebarMenuButton>
                  </SidebarMenuItem>
                );
              })}
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>
      </SidebarContent>

      <SidebarFooter className="p-0">
        <SidebarSeparator />

        {/* Theme Toggle, Language Switcher & Sidebar Toggle */}
        <div className="flex items-center justify-between px-4 py-3 group-data-[collapsible=icon]:px-2 group-data-[collapsible=icon]:flex-col group-data-[collapsible=icon]:gap-2">
          {/* Left side - Theme and Language */}
          <div className="flex items-center justify-center gap-2 group-data-[collapsible=icon]:flex-col">
            <ThemeToggle />
            <div className="group-data-[collapsible=icon]:hidden">
              <LanguageSwitcher variant="ghost" size="sm" />
            </div>
          </div>
          
          {/* Right side - Sidebar Toggle */}
          <div className="flex items-center justify-center">
            <SidebarTrigger className="text-sidebar-foreground" />
          </div>
        </div>

        <SidebarSeparator />

        {/* Sign Out Button */}
        <div className="px-4 py-3 group-data-[collapsible=icon]:px-2">
          <Button
            variant="outline"
            onClick={onSignOut}
            className="w-full justify-start text-sidebar-foreground border-sidebar-border hover:bg-sidebar-accent hover:text-sidebar-accent-foreground group-data-[collapsible=icon]:justify-center group-data-[collapsible=icon]:px-2"
            title={state === 'collapsed' ? t('general.logout') : undefined}
          >
            <LogOut className="h-4 w-4 group-data-[collapsible=icon]:mr-0 mr-3" />
            <span className="group-data-[collapsible=icon]:hidden">{t('general.logout')}</span>
          </Button>
        </div>
      </SidebarFooter>
      <SidebarRail />
    </Sidebar>
  );
};
