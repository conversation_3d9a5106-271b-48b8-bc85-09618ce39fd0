import { ReactNode, useEffect } from 'react';
import { Navigate } from 'react-router-dom';
import { AccessControl } from '@/lib/access-control';

interface AccessProtectedRouteProps {
  children: ReactNode;
}

export const AccessProtectedRoute = ({ children }: AccessProtectedRouteProps) => {
  // Check if access has been granted
  const hasAccess = AccessControl.hasAccess();

  if (!hasAccess) {
    return <Navigate to="/access" replace />;
  }

  return <>{children}</>;
};