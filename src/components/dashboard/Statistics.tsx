import React from 'react';
import { Application, APPLICATION_STATUS_LABELS as STATUS_LABELS, APPLICATION_STATUS_COLORS as STATUS_COLORS, ApplicationStatus as ProjectStatus } from '@/types/applications';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Button } from '@/components/ui/button';
import { 
  FileText, 
  Send, 
  MessageCircleQuestion, 
  Calendar, 
  CheckCircle2, 
  ThumbsUp, 
  XCircle, 
  Trophy,
  TrendingUp,
  TrendingDown,
  Minus,
  BarChart3,
  PieChart,
  Activity,
  Target,
  Users,
  Clock
} from 'lucide-react';
import { useApplications } from '@/hooks/useApplications';
import { useTranslation } from 'react-i18next';
import { useApplicationStatusLabel } from '@/lib/translations';

const statusIcons: Record<ProjectStatus, React.ReactNode> = {
  not_applied: <FileText className="h-4 w-4" />,
  application_sent: <Send className="h-4 w-4" />,
  inquiry_received: <MessageCircleQuestion className="h-4 w-4" />,
  interview_scheduled: <Calendar className="h-4 w-4" />,
  interview_completed: <CheckCircle2 className="h-4 w-4" />,
  offer_received: <ThumbsUp className="h-4 w-4" />,
  rejected: <XCircle className="h-4 w-4" />,
  project_completed: <Trophy className="h-4 w-4" />
};

const statusOrder: ProjectStatus[] = [
  'not_applied',
  'application_sent', 
  'inquiry_received',
  'interview_scheduled',
  'interview_completed',
  'offer_received',
  'project_completed',
  'rejected'
];

export const Statistics = () => {
  const { data: projects = [] } = useApplications();
  const { t } = useTranslation('pages');
  const getStatusLabel = useApplicationStatusLabel();
  const totalProjects = projects.length;
  
  // Calculate statistics for each status
  const statusStats = statusOrder.map(status => {
    const count = projects.filter(p => p.status === status).length;
    const percentage = totalProjects > 0 ? Math.round((count / totalProjects) * 100) : 0;
    return {
      status,
      count,
      percentage,
      label: getStatusLabel(status),
      color: STATUS_COLORS[status],
      icon: statusIcons[status]
    };
  });

  // Calculate detailed metrics
  const appliedProjects = projects.filter(p => p.status !== 'not_applied').length;
  const positiveOutcomes = projects.filter(p => 
    p.status === 'offer_received' || p.status === 'project_completed'
  ).length;
  const rejectedProjects = projects.filter(p => p.status === 'rejected').length;
  const inProgressProjects = projects.filter(p => 
    ['application_sent', 'inquiry_received', 'interview_scheduled', 'interview_completed'].includes(p.status)
  ).length;

  const successRate = appliedProjects > 0 ? Math.round((positiveOutcomes / appliedProjects) * 100) : 0;
  const rejectionRate = appliedProjects > 0 ? Math.round((rejectedProjects / appliedProjects) * 100) : 0;
  const applicationRate = totalProjects > 0 ? Math.round((appliedProjects / totalProjects) * 100) : 0;
  const interviewRate = appliedProjects > 0 ? Math.round((projects.filter(p => ['interview_scheduled', 'interview_completed'].includes(p.status)).length / appliedProjects) * 100) : 0;

  // Recent activity (last 30 days)
  const thirtyDaysAgo = new Date();
  thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
  const recentProjects = projects.filter(p => new Date(p.created_at) >= thirtyDaysAgo);
  const recentApplications = projects.filter(p => p.application_date && new Date(p.application_date) >= thirtyDaysAgo);

  return (
    <div className="space-y-6">

      {/* Key Performance Indicators */}
      <div className="grid grid-cols-2 sm:grid-cols-2 lg:grid-cols-4 gap-3 sm:gap-4">
        <Card className="bg-gradient-to-br from-primary/5 to-primary/10 border-primary/20">
          <CardContent className="p-3 sm:p-4">
            <div className="flex items-center justify-between">
              <div>
                <div className="text-xl sm:text-2xl font-bold text-primary">{totalProjects}</div>
                <div className="text-xs sm:text-sm text-muted-foreground">{t('statistics.total_projects_count')}</div>
              </div>
              <FileText className="h-5 w-5 text-primary opacity-60" />
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-blue-500/5 to-blue-500/10 border-blue-500/20">
          <CardContent className="p-3 sm:p-4">
            <div className="flex items-center justify-between">
              <div>
                <div className="text-xl sm:text-2xl font-bold text-blue-600">{applicationRate}%</div>
                <div className="text-xs sm:text-sm text-muted-foreground">{t('statistics.application_rate')}</div>
              </div>
              <Send className="h-5 w-5 text-blue-600 opacity-60" />
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-green-500/5 to-green-500/10 border-green-500/20">
          <CardContent className="p-3 sm:p-4">
            <div className="flex items-center justify-between">
              <div>
                <div className="text-xl sm:text-2xl font-bold text-green-600">{successRate}%</div>
                <div className="text-xs sm:text-sm text-muted-foreground">{t('statistics.success_rate')}</div>
              </div>
              <Target className="h-5 w-5 text-green-600 opacity-60" />
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-purple-500/5 to-purple-500/10 border-purple-500/20">
          <CardContent className="p-3 sm:p-4">
            <div className="flex items-center justify-between">
              <div>
                <div className="text-xl sm:text-2xl font-bold text-purple-600">{interviewRate}%</div>
                <div className="text-xs sm:text-sm text-muted-foreground">{t('statistics.interview_rate')}</div>
              </div>
              <Users className="h-5 w-5 text-purple-600 opacity-60" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Recent Activity */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-base flex items-center gap-2">
              <Clock className="h-4 w-4 text-blue-600" />
              {t('statistics.last_30_days')}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div className="flex justify-between">
                <span className="text-sm text-muted-foreground">{t('statistics.new_projects')}</span>
                <Badge variant="outline">{recentProjects.length}</Badge>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-muted-foreground">{t('statistics.applications')}</span>
                <Badge variant="outline">{recentApplications.length}</Badge>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-base flex items-center gap-2">
              <Activity className="h-4 w-4 text-green-600" />
              {t('statistics.activity_rate')}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div className="flex justify-between">
                <span className="text-sm text-muted-foreground">{t('statistics.daily_average')}</span>
                <span className="text-sm font-medium">{(recentApplications.length / 30).toFixed(1)}</span>
              </div>
              <Progress value={Math.min((recentApplications.length / 30) * 100, 100)} className="h-2" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-base flex items-center gap-2">
              <TrendingUp className="h-4 w-4 text-emerald-600" />
              {t('statistics.conversion')}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div className="flex justify-between">
                <span className="text-sm text-muted-foreground">{t('statistics.offers')}</span>
                <Badge variant="outline" className="text-emerald-600">{positiveOutcomes}</Badge>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-muted-foreground">{t('statistics.running')}</span>
                <Badge variant="outline" className="text-blue-600">{inProgressProjects}</Badge>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-base flex items-center gap-2">
              <XCircle className="h-4 w-4 text-red-600" />
              {t('statistics.rejections')}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div className="flex justify-between">
                <span className="text-sm text-muted-foreground">{t('statistics.rejection_rate')}</span>
                <span className="text-sm font-medium text-red-600">{rejectionRate}%</span>
              </div>
              <Progress value={rejectionRate} className="h-2" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Detailed Status Breakdown */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg sm:text-xl flex items-center gap-2">
            <PieChart className="h-5 w-5" />
            {t('statistics.status_distribution')}
          </CardTitle>
          <CardDescription>
            {t('statistics.status_breakdown_desc')}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
            {statusStats.map(({ status, count, percentage, label, color, icon }) => (
              <div 
                key={status}
                className="group relative p-4 rounded-lg border border-border/50 bg-card/50 hover:bg-card transition-colors cursor-pointer"
              >
                <div className="flex items-center justify-between mb-3">
                  <div className="flex items-center gap-2">
                    <div className="p-1.5 rounded-full bg-muted">
                      {icon}
                    </div>
                    <Badge variant="outline" className={`${color} text-xs`}>
                      {count}
                    </Badge>
                  </div>
                  <div className="text-sm text-muted-foreground">
                    {percentage}%
                  </div>
                </div>
                
                <div className="space-y-2">
                  <div className="text-sm font-medium text-foreground leading-tight">
                    {label}
                  </div>
                  <Progress 
                    value={percentage} 
                    className="h-2" 
                  />
                </div>

                {/* Hover effect overlay */}
                <div className="absolute inset-0 bg-primary/5 rounded-lg opacity-0 group-hover:opacity-100 transition-opacity pointer-events-none" />
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Performance Metrics */}
      <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-base flex items-center gap-2">
              <TrendingUp className="h-4 w-4 text-green-600" />
              {t('statistics.performance_metrics')}
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-sm text-muted-foreground">{t('statistics.application_rate')}</span>
                <span className="text-sm font-medium">{applicationRate}%</span>
              </div>
              <Progress value={applicationRate} className="h-2" />
            </div>
            
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-sm text-muted-foreground">{t('statistics.success_rate')}</span>
                <span className="text-sm font-medium text-green-600">{successRate}%</span>
              </div>
              <Progress value={successRate} className="h-2" />
            </div>

            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-sm text-muted-foreground">{t('statistics.interview_rate')}</span>
                <span className="text-sm font-medium text-purple-600">{interviewRate}%</span>
              </div>
              <Progress value={interviewRate} className="h-2" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-base flex items-center gap-2">
              <BarChart3 className="h-4 w-4 text-blue-600" />
              {t('statistics.pipeline_analysis')}
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-sm text-muted-foreground">{t('statistics.rejection_rate_label')}</span>
                <span className="text-sm font-medium text-red-600">{rejectionRate}%</span>
              </div>
              <Progress value={rejectionRate} className="h-2" />
            </div>
            
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-sm text-muted-foreground">{t('statistics.open_applications')}</span>
                <span className="text-sm font-medium text-blue-600">{inProgressProjects}</span>
              </div>
              <Progress 
                value={totalProjects > 0 ? (inProgressProjects / totalProjects) * 100 : 0} 
                className="h-2"
              />
            </div>

            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-sm text-muted-foreground">{t('statistics.completed')}</span>
                <span className="text-sm font-medium text-gray-600">
                  {projects.filter(p => ['offer_received', 'project_completed', 'rejected'].includes(p.status)).length}
                </span>
              </div>
              <Progress 
                value={totalProjects > 0 ? (projects.filter(p => ['offer_received', 'project_completed', 'rejected'].includes(p.status)).length / totalProjects) * 100 : 0} 
                className="h-2"
              />
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};