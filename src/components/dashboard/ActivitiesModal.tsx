import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Activity } from 'lucide-react';
import { format } from 'date-fns';
import { de } from 'date-fns/locale';
import { ALL_ACTIVITY_LABELS, ALL_ACTIVITY_COLORS } from '@/types/shared';
import type { ProjectActivity } from '@/types/applications';
import ReactMarkdown from 'react-markdown';
import { useTranslation } from 'react-i18next';
import { useActivityLabel } from '@/lib/translations';

interface ActivitiesModalProps {
  isOpen: boolean;
  onClose: () => void;
  activities: ProjectActivity[];
  isLoading?: boolean;
  applications?: Array<{id: string, project_name: string}>;
  projects?: Array<{id: string, title: string}>;
}

export const ActivitiesModal = ({ isOpen, onClose, activities, isLoading, applications = [], projects = [] }: ActivitiesModalProps) => {
  const navigate = useNavigate();
  const { t } = useTranslation('common');
  const getActivityLabel = useActivityLabel();
  const [searchTerm, setSearchTerm] = useState('');
  const [typeFilter, setTypeFilter] = useState<string>('all');

  // Filter activities based on search and type
  const filteredActivities = activities.filter(activity => {
    const searchLower = searchTerm.toLowerCase();
    const matchesSearch = searchTerm === '' || 
      activity.description.toLowerCase().includes(searchLower) ||
      activity.notes?.toLowerCase().includes(searchLower) ||
      getActivityLabel(activity.activity_type).toLowerCase().includes(searchLower);
    const matchesType = typeFilter === 'all' || activity.activity_type === typeFilter;
    return matchesSearch && matchesType;
  });

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] flex flex-col">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Activity className="h-5 w-5" />
            {t('activities.all_activities', { defaultValue: 'Alle Aktivitäten' })}
          </DialogTitle>
        </DialogHeader>
        
        <div className="flex flex-col sm:flex-row gap-4 mb-4">
          <div className="flex-1">
            <Input
              placeholder={t('activities.search_activities')}
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full"
            />
          </div>
          <div className="sm:w-48">
            <Select value={typeFilter} onValueChange={setTypeFilter}>
              <SelectTrigger>
                <SelectValue placeholder={t('activities.filter_type')} />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">{t('activities.all_types')}</SelectItem>
                {Object.keys(ALL_ACTIVITY_LABELS).map((type) => (
                  <SelectItem key={type} value={type}>
                    {getActivityLabel(type)}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </div>

        <div className="flex-1 overflow-y-auto">
          <div className="text-sm text-muted-foreground mb-4">
            {t('activities.activities_count', { count: filteredActivities.length, total: activities.length })}
          </div>
          
          {isLoading ? (
            <div className="text-center py-12">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
              <p className="text-muted-foreground">{t('activities.loading_activities')}</p>
            </div>
          ) : filteredActivities.length === 0 ? (
            <div className="text-center py-12">
              <Activity className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <p className="text-muted-foreground">
                {searchTerm || typeFilter !== 'all' 
                  ? t('activities.no_activities_found') 
                  : t('activities.no_activities_yet')}
              </p>
            </div>
          ) : (
            <div className="space-y-4">
              {filteredActivities.map((activity: ProjectActivity) => {
                // Determine navigation URL based on activity type
                let navigateUrl = '';
                let entityName = t('project_labels.unknown');
                
                if (activity.activity_type.includes('application') || activity.activity_type.includes('followup')) {
                  const application = applications.find(app => app.id === activity.project_id);
                  entityName = application?.project_name || t('project_labels.application');
                  navigateUrl = `/applications/${activity.project_id}`;
                } else {
                  const project = projects.find(proj => proj.id === activity.project_id);
                  entityName = project?.title || t('project_labels.project');
                  navigateUrl = `/projects/${activity.project_id}`;
                }
                
                return (
                  <div 
                    key={activity.id} 
                    className="flex gap-3 p-4 rounded-lg border bg-muted/20 hover:bg-muted/40 cursor-pointer transition-colors"
                    onClick={() => {
                      if (navigateUrl) {
                        navigate(navigateUrl);
                        onClose();
                      }
                    }}
                  >
                    <div className="flex-shrink-0 w-10 h-10 rounded-full bg-muted flex items-center justify-center">
                      <Activity className="h-5 w-5 text-muted-foreground" />
                    </div>
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center gap-2 mb-2">
                        <Badge 
                          variant="outline" 
                          className={`text-xs ${ALL_ACTIVITY_COLORS[activity.activity_type as keyof typeof ALL_ACTIVITY_COLORS] || 'text-muted-foreground'}`}
                        >
                          {getActivityLabel(activity.activity_type)}
                        </Badge>
                        <span className="text-sm text-muted-foreground">
                          {format(new Date(activity.created_at), 'dd.MM.yyyy HH:mm', { locale: de })}
                        </span>
                      </div>
                      <p className="text-sm font-medium text-primary mb-1">
                        {entityName}
                      </p>
                      <p className="text-sm text-foreground mb-1">
                        {activity.description}
                      </p>
                      {activity.notes && (
                        <div className="text-xs text-muted-foreground bg-muted/50 p-2 rounded prose prose-xs max-w-none">
                          <strong>{t('activities.notes')}:</strong>{' '}
                          <ReactMarkdown 
                            components={{
                              // Inline rendering for notes
                              p: ({children}) => <span>{children}</span>,
                              h1: ({children}) => <strong className="font-semibold">{children}</strong>,
                              h2: ({children}) => <strong className="font-semibold">{children}</strong>,
                              h3: ({children}) => <strong className="font-medium">{children}</strong>,
                              strong: ({children}) => <strong className="font-semibold">{children}</strong>,
                              em: ({children}) => <em className="italic">{children}</em>,
                              code: ({children}) => <code className="bg-muted px-1 py-0.5 rounded text-xs">{children}</code>,
                              ul: ({children}) => <div className="my-1">{children}</div>,
                              ol: ({children}) => <div className="my-1">{children}</div>,
                              li: ({children}) => <div className="ml-2">• {children}</div>,
                              blockquote: ({children}) => <div className="italic border-l-2 border-muted pl-2 my-1">"{children}"</div>,
                            }}
                          >
                            {activity.notes}
                          </ReactMarkdown>
                        </div>
                      )}
                    </div>
                  </div>
                );
              })}
            </div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
};