import { Application, APPLICATION_STATUS_LABELS, APPLICATION_STATUS_COLORS, ApplicationStatus } from '@/types/applications';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { 
  FileText, 
  Send, 
  MessageCircleQuestion, 
  Calendar, 
  CheckCircle2, 
  ThumbsUp, 
  XCircle, 
  Trophy,
  TrendingUp,
  TrendingDown,
  Minus
} from 'lucide-react';
import { useTranslation } from '@/hooks/useTranslation';

interface DashboardStatsProps {
  projects: Application[];
}

const statusIcons: Record<ApplicationStatus, React.ReactNode> = {
  not_applied: <FileText className="h-4 w-4" />,
  application_sent: <Send className="h-4 w-4" />,
  inquiry_received: <MessageCircleQuestion className="h-4 w-4" />,
  interview_scheduled: <Calendar className="h-4 w-4" />,
  interview_completed: <CheckCircle2 className="h-4 w-4" />,
  offer_received: <ThumbsUp className="h-4 w-4" />,
  rejected: <XCircle className="h-4 w-4" />,
  project_completed: <Trophy className="h-4 w-4" />
};

const statusOrder: ApplicationStatus[] = [
  'not_applied',
  'application_sent', 
  'inquiry_received',
  'interview_scheduled',
  'interview_completed',
  'offer_received',
  'project_completed',
  'rejected'
];

export const DashboardStats = ({ projects }: DashboardStatsProps) => {
  const { t } = useTranslation('pages');
  const totalProjects = projects.length;
  
  // Calculate success metrics
  const appliedProjects = projects.filter(p => p.status !== 'not_applied').length;
  const positiveOutcomes = projects.filter(p => 
    p.status === 'offer_received' || p.status === 'project_completed'
  ).length;
  const inProgressProjects = projects.filter(p => 
    ['application_sent', 'inquiry_received', 'interview_scheduled', 'interview_completed'].includes(p.status)
  ).length;

  const successRate = appliedProjects > 0 ? Math.round((positiveOutcomes / appliedProjects) * 100) : 0;

  return (
    <div className="mb-6 sm:mb-8">
      {/* Compact Overview Cards */}
      <div className="grid grid-cols-2 sm:grid-cols-4 gap-3 sm:gap-4">
        {/* Total Projects */}
        <Card className="min-w-0">
          <CardHeader className="pb-2">
            <CardTitle className="text-xs sm:text-sm font-medium flex items-center gap-1 sm:gap-2">
              <FileText className="h-3 w-3 sm:h-4 sm:w-4 text-blue-500 flex-shrink-0" />
              <span className="truncate">{t('dashboard.stats.total_projects')}</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-xl sm:text-2xl font-bold">{totalProjects}</div>
            <p className="text-xs text-muted-foreground truncate">{t('dashboard.stats.all_applications')}</p>
          </CardContent>
        </Card>

        {/* Success Rate */}
        <Card className="min-w-0">
          <CardHeader className="pb-2">
            <CardTitle className="text-xs sm:text-sm font-medium flex items-center gap-1 sm:gap-2">
              <TrendingUp className="h-3 w-3 sm:h-4 sm:w-4 text-green-500 flex-shrink-0" />
              <span className="truncate">{t('dashboard.stats.success_rate')}</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-xl sm:text-2xl font-bold">{successRate}%</div>
            <p className="text-xs text-muted-foreground truncate">{t('dashboard.stats.positive_responses')}</p>
          </CardContent>
        </Card>

        {/* In Progress */}
        <Card className="min-w-0">
          <CardHeader className="pb-2">
            <CardTitle className="text-xs sm:text-sm font-medium flex items-center gap-1 sm:gap-2">
              <Calendar className="h-3 w-3 sm:h-4 sm:w-4 text-orange-500 flex-shrink-0" />
              <span className="truncate">{t('dashboard.stats.in_progress')}</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-xl sm:text-2xl font-bold">{inProgressProjects}</div>
            <p className="text-xs text-muted-foreground truncate">{t('dashboard.stats.in_processing')}</p>
          </CardContent>
        </Card>

        {/* Positive Outcomes */}
        <Card className="min-w-0">
          <CardHeader className="pb-2">
            <CardTitle className="text-xs sm:text-sm font-medium flex items-center gap-1 sm:gap-2">
              <Trophy className="h-3 w-3 sm:h-4 sm:w-4 text-emerald-500 flex-shrink-0" />
              <span className="truncate">{t('dashboard.stats.offers')}</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-xl sm:text-2xl font-bold">{positiveOutcomes}</div>
            <p className="text-xs text-muted-foreground truncate">{t('dashboard.stats.successful_projects')}</p>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};