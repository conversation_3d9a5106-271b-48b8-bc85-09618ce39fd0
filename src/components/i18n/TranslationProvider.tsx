import { ReactNode, Suspense } from 'react';
import { I18nextProvider } from 'react-i18next';
import i18n from '@/i18n';

interface TranslationProviderProps {
  children: ReactNode;
}

/**
 * Translation provider component that wraps the app with i18n context
 * Includes Suspense for loading translations asynchronously
 */
export function TranslationProvider({ children }: TranslationProviderProps) {
  return (
    <I18nextProvider i18n={i18n}>
      <Suspense fallback={
        <div className="flex justify-center items-center h-screen text-lg font-system">
          Loading translations...
        </div>
      }>
        {children}
      </Suspense>
    </I18nextProvider>
  );
}