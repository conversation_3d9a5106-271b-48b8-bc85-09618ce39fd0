import { useEffect, useState, useRef, useMemo, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import { Search, ArrowRight, Loader2, X } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { debounce } from 'lodash';
import { GlobalSearchService } from '@/services/globalSearchService';
import { ENTITY_CONFIG, SEARCH_CONFIG } from '@/types/search';
import type { SearchResult, SearchGroup } from '@/types/search';
import { useTranslation } from 'react-i18next';

interface GlobalSearchPaletteProps {
  className?: string;
  isOpen: boolean;
  onClose: () => void;
}

const SAMPLE_SHORTCUTS = ['React', 'TypeScript', 'Projekt', 'Kontakt', 'Bewerbung'];

export const GlobalSearchPalette: React.FC<GlobalSearchPaletteProps> = ({ 
  className = '',
  isOpen,
  onClose
}) => {
  const navigate = useNavigate();
  const { t } = useTranslation('common');
  const [selectedIndex, setSelectedIndex] = useState(0);
  const [query, setQuery] = useState('');
  const [results, setResults] = useState<SearchResult[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const inputRef = useRef<HTMLInputElement>(null);
  const dialogRef = useRef<HTMLDivElement>(null);
  const currentRequestRef = useRef<AbortController | null>(null);

  // Debounced search function
  const debouncedSearch = useMemo(
    () => debounce(async (searchQuery: string) => {
      if (!GlobalSearchService.isValidQuery(searchQuery)) {
        setResults([]);
        setLoading(false);
        setError(null);
        return;
      }

      // Cancel previous request
      if (currentRequestRef.current) {
        currentRequestRef.current.abort();
      }

      // Create new abort controller
      const abortController = new AbortController();
      currentRequestRef.current = abortController;

      try {
        setLoading(true);
        setError(null);

        const sanitizedQuery = GlobalSearchService.sanitizeQuery(searchQuery);
        const searchResults = await GlobalSearchService.globalSearch(sanitizedQuery);

        // Check if this request is still current
        if (!abortController.signal.aborted) {
          setResults(searchResults);
          setLoading(false);

          // Track analytics
          await GlobalSearchService.trackSearchAnalytics(sanitizedQuery, searchResults.length);
        }
      } catch (err) {
        if (!abortController.signal.aborted) {
          console.error('Search error:', err);
          setLoading(false);
          setError('Search failed');
        }
      } finally {
        // Clean up the request reference
        if (currentRequestRef.current === abortController) {
          currentRequestRef.current = null;
        }
      }
    }, SEARCH_CONFIG.DEBOUNCE_DELAY),
    []
  );

  // Group results by entity type
  const groupedResults = useMemo((): SearchGroup[] => {
    const groups = GlobalSearchService.groupSearchResults(results);
    
    return Object.entries(groups).map(([type, results]) => ({
      type,
      label: ENTITY_CONFIG[type as keyof typeof ENTITY_CONFIG]?.label || type,
      icon: ENTITY_CONFIG[type as keyof typeof ENTITY_CONFIG]?.icon || '📄',
      results: results.sort((a, b) => b.rank - a.rank),
    }));
  }, [results]);

  // Check if there are any results
  const hasResults = useMemo(() => results.length > 0, [results]);

  // Check if query is too short
  const isQueryTooShort = useMemo(() => 
    query.length > 0 && query.length < SEARCH_CONFIG.MIN_QUERY_LENGTH,
    [query]
  );

  // Get result count text
  const resultCountText = useMemo(() => {
    if (loading) return t('search.searching');
    if (error) return t('search.search_failed');
    if (isQueryTooShort) return t('search.min_characters', { count: SEARCH_CONFIG.MIN_QUERY_LENGTH });
    if (!hasResults && query.length >= SEARCH_CONFIG.MIN_QUERY_LENGTH) {
      return t('search.no_results');
    }
    if (hasResults) {
      const count = results.length;
      return t('search.results_found', { count, plural: count !== 1 ? 's' : '' });
    }
    return '';
  }, [loading, error, isQueryTooShort, hasResults, query, results, t]);

  // Define handlers first
  const handleResultClick = useCallback((result: SearchResult) => {
    navigate(result.url_path);
    onClose();
  }, [navigate, onClose]);

  const handleShortcutClick = useCallback((shortcut: string) => {
    setQuery(shortcut);
    debouncedSearch(shortcut);
  }, [debouncedSearch]);

  const handleQueryChange = useCallback((newQuery: string) => {
    setQuery(newQuery);
    debouncedSearch(newQuery);
  }, [debouncedSearch]);

  // Get flattened results for keyboard navigation
  const flatResults = groupedResults.flatMap(group => group.results);

  // Keyboard shortcuts and navigation
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      // Global shortcuts
      if ((e.metaKey || e.ctrlKey) && e.key === 'k') {
        e.preventDefault();
        if (isOpen) {
          onClose();
        } else {
          // This will be handled by the parent component
        }
        return;
      }

      // Only handle these keys when search is open
      if (!isOpen) return;

      switch (e.key) {
        case 'Escape':
          e.preventDefault();
          onClose();
          break;
        case 'ArrowDown':
          e.preventDefault();
          setSelectedIndex(prev => 
            prev < flatResults.length - 1 ? prev + 1 : 0
          );
          break;
        case 'ArrowUp':
          e.preventDefault();
          setSelectedIndex(prev => 
            prev > 0 ? prev - 1 : flatResults.length - 1
          );
          break;
        case 'Enter':
          e.preventDefault();
          if (flatResults[selectedIndex]) {
            handleResultClick(flatResults[selectedIndex]);
          }
          break;
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [isOpen, flatResults, selectedIndex, onClose, handleResultClick]);

  // Auto-focus input when opened
  useEffect(() => {
    if (isOpen && inputRef.current) {
      inputRef.current.focus();
    }
  }, [isOpen]);

  // Reset selected index when results change
  useEffect(() => {
    setSelectedIndex(0);
  }, [groupedResults]);


  const getEntityConfig = (type: string) => {
    return ENTITY_CONFIG[type as keyof typeof ENTITY_CONFIG] || {
      label: type,
      icon: '📄',
      color: 'bg-gray-500/10 text-gray-600 border-gray-500/20',
    };
  };

  if (!isOpen) {
    return null;
  }

  return (
    <div className={`fixed inset-0 z-50 ${className}`}>
      {/* Backdrop */}
      <div 
        className="fixed inset-0 bg-black/50 backdrop-blur-sm" 
        onClick={onClose}
      />
      
      {/* Search Dialog */}
      <div 
        ref={dialogRef}
        className="fixed top-[15%] left-1/2 -translate-x-1/2 w-full max-w-2xl mx-auto px-4"
        role="dialog"
        aria-label={t('search.global_search')}
        aria-describedby="search-description"
      >
        <div id="search-description" className="sr-only">
          {t('search.search_description')}
        </div>
        
        <div className="rounded-lg border bg-background shadow-2xl">
          {/* Search Input */}
          <div className="flex items-center border-b px-4">
            <Search className="mr-3 h-5 w-5 shrink-0 text-muted-foreground" aria-hidden="true" />
            <Input
              ref={inputRef}
              placeholder={t('search.global_search')}
              value={query}
              onChange={(e) => handleQueryChange(e.target.value)}
              className="flex h-12 w-full bg-transparent py-3 text-sm border-0 outline-none ring-0 focus-visible:ring-0 placeholder:text-muted-foreground"
              aria-label={t('search.search_field_label')}
              aria-describedby="search-help"
            />
            <div id="search-help" className="sr-only">
              {t('search.search_help')}
            </div>
            {loading && (
              <Loader2 className="mr-2 h-4 w-4 animate-spin text-muted-foreground" aria-label={t('search.search_running')} />
            )}
            <Button
              variant="ghost"
              size="sm"
              onClick={onClose}
              className="h-8 w-8 p-0 ml-2"
            >
              <X className="h-4 w-4" />
            </Button>
          </div>

          {/* Content Area */}
          <div className="max-h-96 overflow-y-auto">
            {/* Error State */}
            {error && (
              <div className="p-6 text-center">
                <div className="text-red-500 mb-2">{t('search.error_occurred')}</div>
                <p className="text-sm text-muted-foreground">{error}</p>
              </div>
            )}

            {/* Query too short notice */}
            {isQueryTooShort && !loading && !error && (
              <div className="p-6 text-center text-sm text-muted-foreground">
                {t('search.min_characters_notice')}
              </div>
            )}

            {/* Empty State - No Results */}
            {!hasResults && !isQueryTooShort && !loading && !error && query.length > 0 && (
              <div className="p-6 text-center">
                <div className="text-muted-foreground mb-2">{t('search.no_results_icon')}</div>
                <p className="text-sm text-muted-foreground mb-4">
                  {t('search.no_results_for', { query })}
                </p>
                <div className="text-xs text-muted-foreground">
                  <strong>{t('search.search_tips')}</strong>
                </div>
              </div>
            )}

            {/* Search Suggestions when no query */}
            {query.length === 0 && !loading && (
              <div className="p-6">
                <div className="text-sm text-muted-foreground mb-4">
                  {t('search.search_suggestions')}
                </div>
                <div className="flex flex-wrap gap-2">
                  {SAMPLE_SHORTCUTS.map((shortcut) => (
                    <Badge 
                      key={shortcut}
                      variant="outline" 
                      className="cursor-pointer hover:bg-muted/50"
                      onClick={() => handleShortcutClick(shortcut)}
                    >
                      {shortcut}
                    </Badge>
                  ))}
                </div>
              </div>
            )}

            {/* Search Results */}
            {groupedResults.map((group) => (
              <div key={group.type} className="border-b last:border-b-0">
                {/* Group Header */}
                <div className="flex items-center gap-2 px-4 py-2 text-xs font-semibold text-muted-foreground uppercase tracking-wide bg-muted/20">
                  <span>{group.icon}</span>
                  <span>{group.label}</span>
                  <Badge variant="outline" className="ml-auto text-xs">
                    {group.results.length}
                  </Badge>
                </div>
                
                {/* Group Results */}
                {group.results.map((result) => {
                  const entityConfig = getEntityConfig(result.entity_type);
                  const globalIndex = flatResults.indexOf(result);
                  const isSelected = selectedIndex === globalIndex;
                  
                  return (
                    <div
                      key={`${result.entity_type}-${result.entity_id}`}
                      onClick={() => handleResultClick(result)}
                      className={`flex items-center gap-3 px-4 py-3 cursor-pointer border-l-2 transition-colors ${
                        isSelected 
                          ? 'border-primary bg-muted/50' 
                          : 'border-transparent hover:bg-muted/50'
                      }`}
                      role="option"
                      aria-label={`${result.title} - ${entityConfig.label}`}
                      aria-selected={isSelected}
                    >
                      {/* Entity Icon */}
                      <div className="flex-shrink-0">
                        <div className={`w-8 h-8 rounded-lg flex items-center justify-center text-xs ${entityConfig.color}`}>
                          {entityConfig.icon}
                        </div>
                      </div>
                      
                      {/* Content */}
                      <div className="flex-1 min-w-0">
                        <div className="flex items-start justify-between gap-2">
                          <div className="min-w-0 flex-1">
                            <div 
                              className="font-medium text-sm truncate text-foreground" 
                              dangerouslySetInnerHTML={{ 
                                __html: GlobalSearchService.sanitizeHTML(GlobalSearchService.formatHighlight(result.title, query))
                              }}
                            />
                            {result.description && (
                              <div className="text-xs text-muted-foreground truncate mt-0.5 opacity-75">
                                {result.description}
                              </div>
                            )}
                          </div>
                        </div>
                      </div>
                      
                      {/* Arrow Icon */}
                      <ArrowRight className="h-4 w-4 text-muted-foreground shrink-0" />
                    </div>
                  );
                })}
              </div>
            ))}
          </div>

          {/* Footer with shortcuts hint - only on desktop */}
          {isOpen && (
            <div className="border-t px-4 py-2 text-xs text-muted-foreground bg-muted/20 hidden sm:block">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-4">
                  <span className="flex items-center gap-1">
                    <kbd className="px-1.5 py-0.5 bg-muted rounded">↑↓</kbd>
                    {t('search.shortcuts.navigate')}
                  </span>
                  <span className="flex items-center gap-1">
                    <kbd className="px-1.5 py-0.5 bg-muted rounded">↵</kbd>
                    {t('search.shortcuts.select')}
                  </span>
                  <span className="flex items-center gap-1">
                    <kbd className="px-1.5 py-0.5 bg-muted rounded">Esc</kbd>
                    {t('search.shortcuts.close')}
                  </span>
                </div>
                {hasResults && (
                  <div className="text-xs">
                    {resultCountText}
                  </div>
                )}
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default GlobalSearchPalette;