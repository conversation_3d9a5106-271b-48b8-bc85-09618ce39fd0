import { useState, useMemo, useRef, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Search, ArrowRight, Command as CommandIcon } from 'lucide-react';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { useTranslation } from 'react-i18next';
import { debounce } from 'lodash';
import { GlobalSearchService } from '@/services/globalSearchService';
import { ENTITY_CONFIG, SEARCH_CONFIG } from '@/types/search';
import type { SearchResult, SearchGroup } from '@/types/search';

interface QuickSearchBarProps {
  placeholder?: string;
  className?: string;
  onGlobalSearchOpen?: () => void;
}

export const QuickSearchBar: React.FC<QuickSearchBarProps> = ({
  placeholder,
  className = "",
  onGlobalSearchOpen,
}) => {
  const navigate = useNavigate();
  const { t } = useTranslation('common');
  
  const defaultPlaceholder = placeholder || `⌘K ${t('search.global_search').toLowerCase()}`;
  const [query, setQuery] = useState('');
  const [results, setResults] = useState<SearchResult[]>([]);
  const [isOpen, setIsOpen] = useState(false);
  const [loading, setLoading] = useState(false);
  const inputRef = useRef<HTMLInputElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);

  // Debounced search function
  const debouncedSearch = useMemo(
    () => debounce(async (searchQuery: string) => {
      if (!GlobalSearchService.isValidQuery(searchQuery)) {
        setResults([]);
        setLoading(false);
        return;
      }

      setLoading(true);
      try {
        const sanitizedQuery = GlobalSearchService.sanitizeQuery(searchQuery);
        const searchResults = await GlobalSearchService.globalSearch(sanitizedQuery);
        setResults(searchResults);
      } catch (error) {
        console.error('Quick search error:', error);
        setResults([]);
      } finally {
        setLoading(false);
      }
    }, SEARCH_CONFIG.DEBOUNCE_DELAY),
    []
  );

  // Handle input change
  const handleInputChange = (value: string) => {
    setQuery(value);
    if (value.length > 0) {
      setIsOpen(true);
      debouncedSearch(value);
    } else {
      setIsOpen(false);
      setResults([]);
      setLoading(false);
    }
  };

  // Group results by entity type (same as GlobalSearchPalette)
  const groupedResults = useMemo((): SearchGroup[] => {
    const groups = GlobalSearchService.groupSearchResults(results);
    
    return Object.entries(groups).map(([type, results]) => ({
      type,
      label: ENTITY_CONFIG[type as keyof typeof ENTITY_CONFIG]?.label || type,
      icon: ENTITY_CONFIG[type as keyof typeof ENTITY_CONFIG]?.icon || '📄',
      results: results.sort((a, b) => b.rank - a.rank),
    }));
  }, [results]);

  // Handle result selection
  const handleResultSelect = (result: SearchResult) => {
    navigate(result.url_path);
    setIsOpen(false);
    setQuery('');
    setResults([]);
  };

  // Handle global search shortcut
  const handleGlobalSearchClick = () => {
    if (onGlobalSearchOpen) {
      onGlobalSearchOpen();
    }
    setIsOpen(false);
  };

  // Close on outside click
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (containerRef.current && !containerRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  // Keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      // If input is focused and user presses Cmd+K, open global search
      if (document.activeElement === inputRef.current && (e.metaKey || e.ctrlKey) && e.key === 'k') {
        e.preventDefault();
        handleGlobalSearchClick();
      }
      
      // Escape to close
      if (e.key === 'Escape' && isOpen) {
        setIsOpen(false);
        inputRef.current?.blur();
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [isOpen, onGlobalSearchOpen]);

  const getEntityConfig = (type: string) => {
    return ENTITY_CONFIG[type as keyof typeof ENTITY_CONFIG] || {
      label: type,
      icon: '📄',
      color: 'bg-gray-500/10 text-gray-600 border-gray-500/20',
    };
  };

  return (
    <div ref={containerRef} className={`relative ${className}`}>
      {/* Simple Search Input */}
      <div className="relative">
        <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
        <Input
          ref={inputRef}
          type="text"
          placeholder={defaultPlaceholder}
          value={query}
          onChange={(e) => handleInputChange(e.target.value)}
          onFocus={() => {
            if (query.length > 0) setIsOpen(true);
          }}
          className="pl-10 pr-4 w-[420px]"
        />
      </div>

      {/* Search Results Dropdown */}
      {isOpen && (query.length > 0 || results.length > 0) && (
        <div className="absolute top-full left-0 right-0 z-50 mt-1 bg-popover border rounded-md shadow-lg max-h-80 overflow-y-auto">
          {/* Grouped Search Results */}
          {groupedResults.map((group) => (
            <div key={group.type} className="border-b last:border-b-0">
              {/* Group Header */}
              <div className="flex items-center gap-2 px-4 py-2 text-xs font-semibold text-muted-foreground uppercase tracking-wide bg-muted/20">
                <span>{group.icon}</span>
                <span>{group.label}</span>
                <Badge variant="outline" className="ml-auto text-xs">
                  {group.results.length}
                </Badge>
              </div>
              
              {/* Group Results */}
              {group.results.map((result) => {
                const entityConfig = getEntityConfig(result.entity_type);
                
                return (
                  <div
                    key={`${result.entity_type}-${result.entity_id}`}
                    onClick={() => handleResultSelect(result)}
                    className="flex items-center gap-3 px-4 py-3 cursor-pointer border-l-2 border-transparent hover:bg-muted/50 transition-colors"
                  >
                    {/* Entity Icon */}
                    <div className="flex-shrink-0">
                      <div className={`w-8 h-8 rounded-lg flex items-center justify-center text-xs ${entityConfig.color}`}>
                        {entityConfig.icon}
                      </div>
                    </div>
                    
                    {/* Content */}
                    <div className="flex-1 min-w-0">
                      <div className="flex items-start justify-between gap-2">
                        <div className="min-w-0 flex-1">
                          <div 
                            className="font-medium text-sm truncate text-foreground" 
                            dangerouslySetInnerHTML={{ 
                              __html: GlobalSearchService.sanitizeHTML(GlobalSearchService.formatHighlight(result.title, query))
                            }}
                          />
                          {result.description && (
                            <div className="text-xs text-muted-foreground truncate mt-0.5 opacity-75">
                              {result.description}
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                    
                    {/* Arrow Icon */}
                    <ArrowRight className="h-4 w-4 text-muted-foreground shrink-0" />
                  </div>
                );
              })}
            </div>
          ))}

          {/* No Results */}
          {query.length >= SEARCH_CONFIG.MIN_QUERY_LENGTH && results.length === 0 && !loading && (
            <div className="p-4 text-center text-sm text-muted-foreground">
              {t('search.no_results')}
            </div>
          )}

          {/* Loading */}
          {loading && (
            <div className="p-4 text-center text-sm text-muted-foreground">
              {t('search.searching')}
            </div>
          )}

          {/* Query too short */}
          {query.length > 0 && query.length < SEARCH_CONFIG.MIN_QUERY_LENGTH && (
            <div className="p-4 text-center text-sm text-muted-foreground">
              {t('search.min_characters', { count: SEARCH_CONFIG.MIN_QUERY_LENGTH })}
            </div>
          )}

          {/* Global Search Option */}
          {onGlobalSearchOpen && (
            <div className="border-t p-2">
              <button
                onClick={handleGlobalSearchClick}
                className="w-full flex items-center gap-3 px-3 py-2 text-sm hover:bg-accent rounded-md text-muted-foreground hover:text-accent-foreground"
              >
                <CommandIcon className="h-4 w-4" />
                <span>{t('search.global_search')} öffnen</span>
                <div className="ml-auto flex items-center gap-1">
                  <kbd className="px-1.5 py-0.5 text-xs bg-muted rounded">⌘K</kbd>
                </div>
              </button>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default QuickSearchBar;