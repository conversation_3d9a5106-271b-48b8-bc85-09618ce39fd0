import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { AlertTriangle, FileUp, RefreshCw } from 'lucide-react';

interface ImportConfirmationDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: (overwriteDuplicates: boolean) => void;
  fileName: string;
}

export const ImportConfirmationDialog = ({ 
  isOpen, 
  onClose, 
  onConfirm, 
  fileName 
}: ImportConfirmationDialogProps) => {
  
  const handleOverwrite = () => {
    onConfirm(true);
    onClose();
  };

  const handleSkipDuplicates = () => {
    onConfirm(false);
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <div className="flex items-center gap-3">
            <div className="p-2 bg-amber-100 dark:bg-amber-900/30 rounded-full">
              <AlertTriangle className="h-6 w-6 text-amber-600" />
            </div>
            <DialogTitle>Daten importieren</DialogTitle>
          </div>
          <DialogDescription className="text-left mt-4">
            Sie sind dabei, die Datei <strong>"{fileName}"</strong> zu importieren.
            <br /><br />
            Wie soll mit bereits existierenden Projekten verfahren werden?
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4 pt-2">
          {/* Option 1: Überschreiben */}
          <div className="border rounded-lg p-4 space-y-3">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-blue-100 dark:bg-blue-900/30 rounded-full">
                <RefreshCw className="h-5 w-5 text-blue-600" />
              </div>
              <div>
                <h3 className="font-medium text-foreground">Bestehende Projekte überschreiben</h3>
                <p className="text-sm text-muted-foreground">
                  Vorhandene Projekte werden mit den neuen Daten aktualisiert
                </p>
              </div>
            </div>
            <Button 
              onClick={handleOverwrite}
              className="w-full"
              variant="default"
            >
              <RefreshCw className="h-4 w-4 mr-2" />
              Überschreiben & Importieren
            </Button>
          </div>

          {/* Option 2: Fehler bei Duplikaten */}
          <div className="border rounded-lg p-4 space-y-3">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-green-100 dark:bg-green-900/30 rounded-full">
                <FileUp className="h-5 w-5 text-green-600" />
              </div>
              <div>
                <h3 className="font-medium text-foreground">Nur neue Projekte importieren</h3>
                <p className="text-sm text-muted-foreground">
                  Import wird mit Fehlermeldung abgebrochen falls Duplikate gefunden werden
                </p>
              </div>
            </div>
            <Button 
              onClick={handleSkipDuplicates}
              className="w-full"
              variant="outline"
            >
              <FileUp className="h-4 w-4 mr-2" />
              Nur neue Projekte
            </Button>
          </div>

          {/* Abbrechen */}
          <div className="pt-2">
            <Button 
              onClick={onClose}
              className="w-full"
              variant="ghost"
            >
              Abbrechen
            </Button>
          </div>
        </div>

        {/* Zusätzliche Info */}
        <div className="bg-muted/30 p-3 rounded-lg text-sm text-muted-foreground">
          <div className="flex items-start gap-2">
            <AlertTriangle className="h-4 w-4 mt-0.5 flex-shrink-0" />
            <div>
              <strong>Hinweis:</strong> Duplikate werden erkannt durch:
              <ul className="list-disc list-inside mt-1 space-y-1">
                <li>Identische Listing-URL, oder</li>
                <li>Gleicher Projektname + Firmenname</li>
              </ul>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};