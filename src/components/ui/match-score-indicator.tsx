import { cn } from '@/lib/utils';

interface MatchScoreIndicatorProps {
  score: number | null;
  size?: 'sm' | 'md' | 'lg';
  className?: string;
  showLabel?: boolean;
}

export const MatchScoreIndicator = ({ 
  score, 
  size = 'md', 
  className,
  showLabel = true 
}: MatchScoreIndicatorProps) => {
  if (score === null || score === undefined) {
    return (
      <div className={cn("flex flex-col items-center gap-2", className)}>
        <div className={cn(
          "relative rounded-full border-2 border-muted flex items-center justify-center",
          {
            'w-16 h-16': size === 'sm',
            'w-20 h-20': size === 'md', 
            'w-24 h-24': size === 'lg'
          }
        )}>
          <span className={cn(
            "text-muted-foreground font-medium",
            {
              'text-sm': size === 'sm',
              'text-base': size === 'md',
              'text-lg': size === 'lg'
            }
          )}>
            ?
          </span>
        </div>
        {showLabel && (
          <span className="text-xs text-muted-foreground">Kein Score</span>
        )}
      </div>
    );
  }

  // Calculate color based on score
  const getScoreColor = (score: number) => {
    if (score >= 80) return 'text-green-600 border-green-500';
    if (score >= 60) return 'text-yellow-600 border-yellow-500'; 
    if (score >= 40) return 'text-orange-600 border-orange-500';
    return 'text-red-600 border-red-500';
  };

  const getScoreColorBg = (score: number) => {
    if (score >= 80) return 'from-green-500/20 to-green-600/20';
    if (score >= 60) return 'from-yellow-500/20 to-yellow-600/20';
    if (score >= 40) return 'from-orange-500/20 to-orange-600/20';
    return 'from-red-500/20 to-red-600/20';
  };

  // Calculate circle parameters
  const radius = size === 'sm' ? 28 : size === 'md' ? 36 : 44;
  const strokeWidth = 3;
  const normalizedRadius = radius - strokeWidth * 2;
  const circumference = normalizedRadius * 2 * Math.PI;
  const strokeDasharray = `${circumference} ${circumference}`;
  const strokeDashoffset = circumference - (score / 100) * circumference;

  return (
    <div className={cn("flex flex-col items-center gap-2", className)}>
      <div className="relative">
        {/* Background circle */}
        <svg
          height={radius * 2}
          width={radius * 2}
          className="transform -rotate-90"
        >
          {/* Background track */}
          <circle
            stroke="currentColor"
            className="text-muted/30"
            fill="transparent"
            strokeWidth={strokeWidth}
            r={normalizedRadius}
            cx={radius}
            cy={radius}
          />
          {/* Progress circle */}
          <circle
            stroke="currentColor"
            className={cn("transition-all duration-500 ease-in-out", getScoreColor(score).split(' ')[1])}
            fill="transparent"
            strokeWidth={strokeWidth}
            strokeDasharray={strokeDasharray}
            strokeDashoffset={strokeDashoffset}
            strokeLinecap="round"
            r={normalizedRadius}
            cx={radius}
            cy={radius}
          />
        </svg>
        
        {/* Score text in center */}
        <div className={cn(
          "absolute inset-0 flex items-center justify-center",
          "rounded-full bg-gradient-to-br",
          getScoreColorBg(score)
        )}>
          <span className={cn(
            "font-bold",
            getScoreColor(score).split(' ')[0],
            {
              'text-sm': size === 'sm',
              'text-lg': size === 'md',
              'text-xl': size === 'lg'
            }
          )}>
            {score}%
          </span>
        </div>
      </div>
      
      {showLabel && (
        <span className={cn(
          "font-medium",
          getScoreColor(score).split(' ')[0],
          {
            'text-xs': size === 'sm',
            'text-sm': size === 'md',
            'text-base': size === 'lg'
          }
        )}>
          Skill Match
        </span>
      )}
    </div>
  );
};