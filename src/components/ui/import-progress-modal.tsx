import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Progress } from '@/components/ui/progress';
import { Button } from '@/components/ui/button';
import { CheckCircle, AlertCircle, X } from 'lucide-react';
import { ImportProgress, ImportResult } from '@/services/importService';

interface ImportProgressModalProps {
  isOpen: boolean;
  onClose: () => void;
  progress: ImportProgress | null;
  result: ImportResult | null;
  isImporting: boolean;
}

export const ImportProgressModal = ({ 
  isOpen, 
  onClose, 
  progress, 
  result, 
  isImporting 
}: ImportProgressModalProps) => {
  const getProgressColor = () => {
    if (result?.success === false) return 'bg-red-500';
    if (result?.success === true) return 'bg-green-500';
    return 'bg-blue-500';
  };

  const getStatusIcon = () => {
    if (result?.success === false) return <AlertCircle className="h-6 w-6 text-red-500" />;
    if (result?.success === true) return <CheckCircle className="h-6 w-6 text-green-500" />;
    return null;
  };

  const getTitle = () => {
    if (result?.success === false) return 'Import Fehlgeschlagen';
    if (result?.success === true) return 'Import Erfolgreich';
    return 'Daten werden importiert...';
  };

  const getDescription = () => {
    if (result?.success === false) {
      return `${result.successCount} von ${result.successCount + result.errorCount} Projekten importiert`;
    }
    if (result?.success === true) {
      const parts = [`${result.successCount} Projekte`];
      if (result.activitiesImported) parts.push(`${result.activitiesImported} Activities`);
      if (result.calendarEventsImported) parts.push(`${result.calendarEventsImported} Termine`);
      return `Erfolgreich importiert: ${parts.join(', ')}`;
    }
    if (progress) {
      return progress.projectName || `${progress.current} von ${progress.total} Elementen`;
    }
    return 'Import wird vorbereitet...';
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <div className="flex items-center gap-3">
            {getStatusIcon()}
            <DialogTitle>{getTitle()}</DialogTitle>
            {!isImporting && (
              <Button
                variant="ghost"
                size="sm"
                onClick={onClose}
                className="ml-auto"
              >
                <X className="h-4 w-4" />
              </Button>
            )}
          </div>
          <DialogDescription>{getDescription()}</DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          {/* Progress Bar */}
          <div className="space-y-2">
            <div className="flex justify-between text-sm text-muted-foreground">
              <span>Fortschritt</span>
              <span>{progress?.percentage || (result ? 100 : 0)}%</span>
            </div>
            <Progress 
              value={progress?.percentage || (result ? 100 : 0)} 
              className="w-full"
              indicatorClassName={getProgressColor()}
            />
          </div>

          {/* Current Status */}
          {progress && isImporting && (
            <div className="bg-muted p-3 rounded-md">
              <p className="text-sm">
                <span className="font-medium">Aktuell:</span> {progress.projectName}
              </p>
              <p className="text-xs text-muted-foreground mt-1">
                {progress.current} von {progress.total} ({progress.percentage}%)
              </p>
            </div>
          )}

          {/* Results Summary */}
          {result && !isImporting && (
            <div className="space-y-3">
              {result.success && (
                <div className="bg-green-50 dark:bg-green-950 p-3 rounded-md border border-green-200 dark:border-green-800">
                  <div className="flex items-center gap-2 text-green-800 dark:text-green-200 font-medium mb-2">
                    <CheckCircle className="h-4 w-4" />
                    Import erfolgreich abgeschlossen
                  </div>
                  <div className="text-sm text-green-700 dark:text-green-300 space-y-1">
                    <div>✅ {result.successCount} Projekte importiert</div>
                    {result.activitiesImported > 0 && (
                      <div>✅ {result.activitiesImported} Activities importiert</div>
                    )}
                    {result.calendarEventsImported > 0 && (
                      <div>✅ {result.calendarEventsImported} Termine importiert</div>
                    )}
                    {result.duplicatesFound > 0 && (
                      <div>🔄 {result.duplicatesOverwritten || 0} von {result.duplicatesFound} Duplikaten überschrieben</div>
                    )}
                  </div>
                </div>
              )}

              {result.errorCount > 0 && (
                <div className="bg-red-50 dark:bg-red-950 p-3 rounded-md border border-red-200 dark:border-red-800">
                  <div className="flex items-center gap-2 text-red-800 dark:text-red-200 font-medium mb-2">
                    <AlertCircle className="h-4 w-4" />
                    {result.errorCount} Fehler aufgetreten
                  </div>
                  <div className="text-sm text-red-700 dark:text-red-300 max-h-32 overflow-y-auto space-y-1">
                    {result.errors.slice(0, 5).map((error, index) => (
                      <div key={index}>• {error}</div>
                    ))}
                    {result.errors.length > 5 && (
                      <div className="text-xs text-muted-foreground mt-2">
                        ... und {result.errors.length - 5} weitere Fehler
                      </div>
                    )}
                  </div>
                </div>
              )}

              {!result.success && (
                <div className="bg-red-50 dark:bg-red-950 p-3 rounded-md border border-red-200 dark:border-red-800">
                  <div className="flex items-center gap-2 text-red-800 dark:text-red-200 font-medium">
                    <AlertCircle className="h-4 w-4" />
                    Import fehlgeschlagen
                  </div>
                  <p className="text-sm text-red-700 dark:text-red-300 mt-1">
                    Keine Daten konnten importiert werden. Bitte prüfen Sie die Datei und versuchen Sie es erneut.
                  </p>
                </div>
              )}
            </div>
          )}

          {/* Action Buttons */}
          <div className="flex justify-end gap-2 pt-4">
            {!isImporting && (
              <Button onClick={onClose} variant="outline">
                Schließen
              </Button>
            )}
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};