import ReactMarkdown from 'react-markdown';
import { cn } from '@/lib/utils';
import { CheckCircle2, Circle, Star, Zap } from 'lucide-react';

interface MarkdownRendererProps {
  content: string;
  className?: string;
  allowedElements?: string[];
}

const preprocessContent = (content: string): string => {
  // Convert bullet points (•) to markdown lists and add separators
  const lines = content.split('\n');
  const processedLines: string[] = [];
  
  for (let i = 0; i < lines.length; i++) {
    const line = lines[i];
    const trimmedLine = line.trim();
    
    // Check if line starts with bullet point
    if (trimmedLine.startsWith('•')) {
      // Convert to markdown list item
      const listItem = trimmedLine.substring(1).trim();
      processedLines.push(`- ${listItem}`);
    } else if (trimmedLine.includes('**Original-Anfrage:**') || trimmedLine.includes('**Original Request:**')) {
      // Add separator before original request
      processedLines.push('');
      processedLines.push('---');
      processedLines.push('');
      processedLines.push(line);
    } else {
      processedLines.push(line);
    }
  }
  
  return processedLines.join('\n');
};

const getListIcon = (content: string) => {
  const text = content.toLowerCase();
  if (text.includes('aufgabe') || text.includes('verantwortung') || text.includes('task') || text.includes('responsibility')) {
    return <CheckCircle2 className="h-3 w-3 text-blue-500 mt-0.5 flex-shrink-0" />;
  }
  if (text.includes('technologie') || text.includes('skill') || text.includes('technology') || text.includes('tool')) {
    return <Zap className="h-3 w-3 text-green-500 mt-0.5 flex-shrink-0" />;
  }
  if (text.includes('detail') || text.includes('projekt')) {
    return <Star className="h-3 w-3 text-orange-500 mt-0.5 flex-shrink-0" />;
  }
  return <Circle className="h-2 w-2 text-muted-foreground mt-1 flex-shrink-0 fill-current" />;
};

export const MarkdownRenderer = ({ 
  content, 
  className,
  allowedElements = ['p', 'strong', 'em', 'br', 'span', 'ul', 'ol', 'li', 'hr']
}: MarkdownRendererProps) => {
  const processedContent = preprocessContent(content);
  
  return (
    <div className={cn("prose prose-sm dark:prose-invert max-w-none break-words overflow-wrap-anywhere word-break", className)}>
      <ReactMarkdown
        allowedElements={allowedElements}
        unwrapDisallowed={true}
        components={{
          p: ({ children }) => {
            // Check if this paragraph contains a heading (bold text followed by colon)
            const textContent = String(children);
            if (textContent.includes('**') && textContent.includes(':')) {
              return <div className="mb-3 last:mb-0 break-words">{children}</div>;
            }
            return <p className="mb-2 last:mb-0 break-words overflow-wrap-anywhere hyphens-auto">{children}</p>;
          },
          strong: ({ children }) => {
            const textContent = String(children);
            // If it's a heading with colon, make it a block element
            if (textContent.includes(':')) {
              return (
                <div className="font-semibold text-foreground text-base mb-2 block break-words">
                  {children}
                </div>
              );
            }
            return <strong className="font-semibold text-foreground break-words">{children}</strong>;
          },
          em: ({ children }) => <em className="italic">{children}</em>,
          br: () => <br />,
          hr: () => <hr className="my-6 border-t border-border/30" />,
          ul: ({ children }) => (
            <ul className="mb-3 space-y-2 list-none pl-0">
              {children}
            </ul>
          ),
          ol: ({ children }) => (
            <ol className="mb-3 space-y-2 list-none pl-0">
              {children}
            </ol>
          ),
          li: ({ children }) => {
            const textContent = String(children);
            const icon = getListIcon(textContent);
            
            return (
              <li className="flex items-start gap-3 text-base leading-relaxed break-words">
                <span className="text-muted-foreground mt-0.5 flex-shrink-0 text-lg leading-none">•</span>
                <span className="flex-1 min-w-0 break-words overflow-wrap-anywhere hyphens-auto">{children}</span>
              </li>
            );
          },
        }}
      >
        {processedContent}
      </ReactMarkdown>
    </div>
  );
};