import { Button } from '@/components/ui/button';
import {
  <PERSON><PERSON><PERSON>,
  PaginationContent,
  PaginationEllipsis,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from '@/components/ui/pagination';
import { cn } from '@/lib/utils';

interface DataPaginationProps {
  currentPage: number;
  totalPages: number;
  onPageChange: (page: number) => void;
  hasNextPage: boolean;
  hasPreviousPage: boolean;
  total?: number;
  limit?: number;
  className?: string;
  showInfo?: boolean;
}

export const DataPagination = ({
  currentPage,
  totalPages,
  onPageChange,
  hasNextPage,
  hasPreviousPage,
  total,
  limit,
  className,
  showInfo = true
}: DataPaginationProps) => {
  // Generate page numbers to show
  const getPageNumbers = () => {
    const pages: (number | string)[] = [];
    const maxVisiblePages = 5;
    
    if (totalPages <= maxVisiblePages) {
      // Show all pages if total is small
      for (let i = 1; i <= totalPages; i++) {
        pages.push(i);
      }
    } else {
      // Always show first page
      pages.push(1);
      
      if (currentPage > 3) {
        pages.push('...');
      }
      
      // Show pages around current page
      const start = Math.max(2, currentPage - 1);
      const end = Math.min(totalPages - 1, currentPage + 1);
      
      for (let i = start; i <= end; i++) {
        if (!pages.includes(i)) {
          pages.push(i);
        }
      }
      
      if (currentPage < totalPages - 2) {
        pages.push('...');
      }
      
      // Always show last page
      if (!pages.includes(totalPages)) {
        pages.push(totalPages);
      }
    }
    
    return pages;
  };

  const pageNumbers = getPageNumbers();

  // Calculate display info
  const startItem = total ? (currentPage - 1) * (limit || 10) + 1 : 0;
  const endItem = total ? Math.min(currentPage * (limit || 10), total) : 0;

  if (totalPages <= 1) {
    return null; // Don't show pagination if there's only one page
  }

  return (
    <div className={cn('flex flex-col gap-2 sm:flex-row sm:items-center sm:justify-between', className)}>
      {showInfo && total !== undefined && (
        <div className="text-sm text-muted-foreground order-2 sm:order-1">
          Zeige {startItem} bis {endItem} von {total} Einträgen
        </div>
      )}
      
      <Pagination className="order-1 sm:order-2">
        <PaginationContent>
          <PaginationItem>
            <Button
              variant="outline"
              size="sm"
              onClick={() => onPageChange(currentPage - 1)}
              disabled={!hasPreviousPage}
              className="h-9 px-3"
            >
              Zurück
            </Button>
          </PaginationItem>

          {pageNumbers.map((pageNumber, index) => {
            if (pageNumber === '...') {
              return (
                <PaginationItem key={`ellipsis-${index}`}>
                  <PaginationEllipsis />
                </PaginationItem>
              );
            }

            const page = pageNumber as number;
            const isCurrentPage = page === currentPage;

            return (
              <PaginationItem key={page}>
                <PaginationLink
                  onClick={() => onPageChange(page)}
                  isActive={isCurrentPage}
                  className="cursor-pointer"
                >
                  {page}
                </PaginationLink>
              </PaginationItem>
            );
          })}

          <PaginationItem>
            <Button
              variant="outline"
              size="sm"
              onClick={() => onPageChange(currentPage + 1)}
              disabled={!hasNextPage}
              className="h-9 px-3"
            >
              Weiter
            </Button>
          </PaginationItem>
        </PaginationContent>
      </Pagination>
    </div>
  );
};

// Compact pagination info component for mobile
export const PaginationInfo = ({
  currentPage,
  totalPages,
  total,
  limit
}: {
  currentPage: number;
  totalPages: number;
  total?: number;
  limit?: number;
}) => {
  const startItem = total ? (currentPage - 1) * (limit || 10) + 1 : 0;
  const endItem = total ? Math.min(currentPage * (limit || 10), total) : 0;

  return (
    <div className="text-center text-sm text-muted-foreground py-2">
      Seite {currentPage} von {totalPages}
      {total && (
        <span className="block sm:inline sm:ml-2">
          ({startItem}-{endItem} von {total})
        </span>
      )}
    </div>
  );
};