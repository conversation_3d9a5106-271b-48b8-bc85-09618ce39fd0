import { useState } from 'react';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { MarkdownRenderer } from '@/components/ui/markdown-renderer';
import { StatusUpdateButton } from './StatusUpdateButton';
import { DeleteConfirmationDialog } from './DeleteConfirmationDialog';
import { ApplicationWithContact, APPLICATION_STATUS_COLORS, WORK_LOCATION_LABELS, WORK_LOCATION_COLORS, ApplicationUpdateHandler } from '@/types/applications';
import { useApplicationStatusLabel } from '@/lib/translations';
import { useTranslation } from '@/hooks/useTranslation';
import { Calendar, Building2, User, Mail, Phone, Edit, Trash2, Eye, Code, FileText, ChevronDown, ChevronUp, Clock, CheckCircle, AlertCircle, XCircle, Plus, Send, Timer, CheckCircle2, Pause, MoreHorizontal, ExternalLink } from 'lucide-react';
import { FollowUpTimeline } from '@/components/followup/FollowUpTimeline';
import { FollowUpScheduler } from '@/components/followup/FollowUpScheduler';
import { FollowUpEditDialog } from '@/components/followup/FollowUpEditDialog';
import { useFollowUpSchedule } from '@/hooks/useFollowUpSchedule';
import { useFollowUpTemplates } from '@/hooks/useFollowUpTemplates';
import { FollowUpService } from '@/services/followUpService';
import { format } from 'date-fns';
import { de } from 'date-fns/locale';
import { formatFollowUpDate } from '@/lib/dateUtils';
import type { ScheduledFollowUp, FollowUpTemplate } from '@/types/followup';

// Follow-up related props that can be injected
interface FollowUpProps {
  scheduledFollowUps?: ScheduledFollowUp[];
  followUpTemplates?: FollowUpTemplate[];
  isFollowUpsLoading?: boolean;
  followUpError?: Error | null;
  onDeleteFollowUp?: (followUpId: string) => Promise<void>;
  onMarkFollowUpAsSent?: (followUpId: string) => Promise<void>;
  onEditFollowUp?: (followUp: ScheduledFollowUp) => void;
  onViewFollowUpTimeline?: (applicationId: string) => void;
}

interface ProjectCardProps {
  project: ApplicationWithContact;
  onEdit: (project: ApplicationWithContact) => void;
  onDelete: (id: string) => void;
  onView: (project: ApplicationWithContact) => void;
  onUpdateProject?: ApplicationUpdateHandler;
  isUpdating?: boolean;
  isDeleting?: boolean;
  // Optional follow-up props for decoupling
  followUp?: FollowUpProps;
}

export const ProjectCard = ({ project, onEdit, onDelete, onView, onUpdateProject, isUpdating = false, isDeleting = false, followUp }: ProjectCardProps) => {
  const { t } = useTranslation('common');
  const getStatusLabel = useApplicationStatusLabel();
  const [isDetailsExpanded, setIsDetailsExpanded] = useState(false);
  const [isDescriptionExpanded, setIsDescriptionExpanded] = useState(false);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [showFollowUpTimeline, setShowFollowUpTimeline] = useState(false);
  const [selectedFollowUpForEdit, setSelectedFollowUpForEdit] = useState<any>(null);
  
  // Always call hooks for backward compatibility (React Hook rules)
  const { 
    getScheduledByApplication, 
    error: hookFollowUpError, 
    isLoading: hookIsFollowUpsLoading, 
    deleteScheduledFollowUp, 
    markAsSent 
  } = useFollowUpSchedule();
  
  const { templates: hookTemplates } = useFollowUpTemplates();
  
  // Use props if provided, otherwise fallback to hooks
  const scheduledFollowUps = followUp?.scheduledFollowUps || (hookFollowUpError ? [] : getScheduledByApplication(project.id));
  const followUpTemplates = followUp?.followUpTemplates || hookTemplates;
  const isFollowUpsLoading = followUp?.isFollowUpsLoading || hookIsFollowUpsLoading;
  const followUpError = followUp?.followUpError || hookFollowUpError;
  const hasFollowUps = scheduledFollowUps.length > 0;
  
  // Action handlers that use props or fallback to hooks
  const handleDeleteFollowUp = followUp?.onDeleteFollowUp || deleteScheduledFollowUp;
  const handleMarkAsSent = followUp?.onMarkFollowUpAsSent || (async (followUpId: string) => {
    // For backward compatibility, we need to maintain the old markAsSent signature
    const selectedFollowUp = scheduledFollowUps.find(f => f.id === followUpId);
    if (selectedFollowUp) {
      const subject = selectedFollowUp.template?.subject || 'Follow-up';
      const body = selectedFollowUp.template?.body || t('project_card.followup_sent');
      await markAsSent({
        followUpId,
        actualSubject: subject,
        actualBody: body
      });
    }
  });
  const handleEditFollowUp = followUp?.onEditFollowUp || ((followUpToEdit: ScheduledFollowUp) => {
    setSelectedFollowUpForEdit(followUpToEdit);
  });
  const handleViewTimeline = followUp?.onViewFollowUpTimeline || (() => {
    setShowFollowUpTimeline(true);
  });


  // Helper function to get status icon and styling
  const getFollowUpStatusConfig = (status: string) => {
    switch (status) {
      case 'scheduled':
        return {
          icon: Timer,
          iconColor: 'text-blue-600',
          bgColor: 'bg-blue-50',
          borderColor: 'border-blue-200',
          textColor: 'text-blue-700',
          label: 'Geplant'
        };
      case 'sent':
        return {
          icon: Send,
          iconColor: 'text-green-600',
          bgColor: 'bg-green-50',
          borderColor: 'border-green-200',
          textColor: 'text-green-700',
          label: 'Gesendet'
        };
      case 'dismissed':
        return {
          icon: Pause,
          iconColor: 'text-gray-600',
          bgColor: 'bg-gray-50',
          borderColor: 'border-gray-200',
          textColor: 'text-gray-700',
          label: 'Pausiert'
        };
      default:
        return {
          icon: Clock,
          iconColor: 'text-gray-600',
          bgColor: 'bg-gray-50',
          borderColor: 'border-gray-200',
          textColor: 'text-gray-700',
          label: status
        };
    }
  };

  const formatDate = (dateString?: string) => {
    if (!dateString) return t('project_card.not_specified');
    try {
      return format(new Date(dateString), 'dd.MM.yyyy', { locale: de });
    } catch {
      return t('project_card.invalid_date');
    }
  };

  const handleDeleteClick = () => {
    setShowDeleteDialog(true);
  };

  const handleDeleteConfirm = () => {
    onDelete(project.id);
    setShowDeleteDialog(false);
  };

  const handleDeleteCancel = () => {
    setShowDeleteDialog(false);
  };

  return (
    <TooltipProvider>
      <Card 
        className="bg-gradient-card border-border/50 hover:border-primary/30 transition-all duration-300 hover:shadow-glow group w-full h-full flex flex-col"
      >
        <div className="flex flex-col h-full relative">

          {/* Action Buttons - Desktop only, hover to show */}
          <div className="absolute top-3 right-3 gap-1 opacity-0 group-hover:opacity-100 transition-all duration-200 z-20 hidden sm:flex">
            <Button
              variant="secondary"
              size="sm"
              onClick={(e) => {
                e.stopPropagation();
                onEdit(project);
              }}
              className="h-8 w-8 p-0 bg-background/80 backdrop-blur-sm border shadow-sm hover:bg-primary/10 hover:border-primary/20"
            >
              <Edit className="h-3.5 w-3.5" />
            </Button>
            <Button
              variant="secondary"
              size="sm"
              onClick={(e) => {
                e.stopPropagation();
                handleDeleteClick();
              }}
              disabled={isDeleting}
              className="h-8 w-8 p-0 bg-background/80 backdrop-blur-sm border shadow-sm hover:bg-destructive/10 hover:border-destructive/20 text-destructive disabled:opacity-50"
            >
              <Trash2 className="h-3.5 w-3.5" />
            </Button>
          </div>

          {/* SECTION 1: Project Title Header */}
          <div className="px-6 pt-6 pb-1">
            <div className="min-h-[3.5rem] flex items-start">
              <h3 
                className="font-semibold text-lg text-foreground hover:text-primary transition-colors leading-tight pr-16 line-clamp-2 cursor-pointer"
                onClick={() => onView(project)}
              >
                {project.project_name}
              </h3>
            </div>
          </div>

          {/* SECTION 2: Status Section */}
          <div className="px-6 pb-4 pt-2">
            <div className="bg-muted/20 rounded-lg p-4 border border-border/30">
              <div className="flex items-center gap-2 mb-3">
                {project.status === 'not_applied' && <Clock className="h-4 w-4 text-muted-foreground" />}
                {project.status === 'application_sent' && <Clock className="h-4 w-4 text-blue-500" />}
                {project.status === 'inquiry_received' && <AlertCircle className="h-4 w-4 text-yellow-500" />}
                {project.status === 'interview_scheduled' && <Calendar className="h-4 w-4 text-orange-500" />}
                {project.status === 'interview_completed' && <CheckCircle className="h-4 w-4 text-green-500" />}
                {project.status === 'offer_received' && <CheckCircle className="h-4 w-4 text-green-600" />}
                {project.status === 'rejected' && <XCircle className="h-4 w-4 text-red-500" />}
                {project.status === 'project_completed' && <CheckCircle className="h-4 w-4 text-green-700" />}
                <span className="text-sm font-medium text-foreground">
                  {getStatusLabel(project.status)}
                </span>
              </div>
              
              {project.status !== 'not_applied' && project.application_date && (
                <div className="text-xs text-muted-foreground mb-3">
                  {t('project_card.submitted')}: {formatDate(project.application_date)}
                </div>
              )}
              
              {onUpdateProject && (
                <StatusUpdateButton
                  project={project}
                  onUpdateProject={onUpdateProject}
                  isUpdating={isUpdating}
                  className="text-sm"
                />
              )}
            </div>
          </div>

          {/* SECTION 3: Contact & Company Information - Consistent Position with Increased Spacing */}
          <div className="px-6 pb-4 pt-2">
            <div className="bg-muted/20 rounded-lg p-4 border border-border/30">
              <div className="flex items-center gap-2 mb-4">
                <Building2 className="h-4 w-4 text-primary flex-shrink-0" />
                <span className="text-sm font-medium text-foreground">
                  {t('project_card.contact_and_company')}
                </span>
              </div>

              <div className="space-y-3">
                {/* Company Name */}
                <div className="font-semibold text-foreground" title={project.company_name}>
                  {project.company_name}
                </div>

                {/* Contact Person */}
                <div className="flex items-center gap-2">
                  <User className="h-4 w-4 text-muted-foreground flex-shrink-0" />
                  <span className="text-sm text-muted-foreground" title={project.contact?.name || t('project_card.not_specified')}>
                    {project.contact?.name || t('project_card.not_specified')}
                  </span>
                </div>

                {/* Contact Details */}
                <div className="grid grid-cols-1 gap-2">
                  <div className="flex items-center gap-2">
                    <Mail className="h-4 w-4 text-muted-foreground flex-shrink-0" />
                    {project.contact?.email ? (
                      <a
                        href={`mailto:${project.contact.email}`}
                        className="text-sm text-muted-foreground hover:text-primary transition-colors break-all"
                        title={project.contact.email}
                      >
                        {project.contact.email}
                      </a>
                    ) : (
                      <span className="text-sm text-muted-foreground" title={t('project_card.not_specified')}>
                        {t('project_card.not_specified')}
                      </span>
                    )}
                  </div>
                  <div className="flex items-center gap-2">
                    <Phone className="h-4 w-4 text-muted-foreground flex-shrink-0" />
                    <span className="text-sm text-muted-foreground" title={project.contact?.phone || t('project_card.not_specified')}>
                      {project.contact?.phone || t('project_card.not_specified')}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* SECTION 4: Project Details */}
          <div className="px-6 pb-4">
            <div className="bg-muted/20 rounded-lg p-4 border border-border/30">
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center gap-2">
                  <Calendar className="h-4 w-4 text-primary flex-shrink-0" />
                  <span className="text-sm font-medium text-foreground">
                    {t('project_card.project_details')}
                  </span>
                </div>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setIsDetailsExpanded(!isDetailsExpanded)}
                  className="h-6 w-6 p-0 hover:bg-primary/10"
                >
                  {isDetailsExpanded ? (
                    <ChevronUp className="h-4 w-4" />
                  ) : (
                    <ChevronDown className="h-4 w-4" />
                  )}
                </Button>
              </div>

              <div className="space-y-3">
                {/* Work Location */}
                <div className="text-sm text-muted-foreground">
                  <span className="font-medium text-foreground">{t('project_card.work_location')}:</span> {
                    project.work_location_type
                      ? `${WORK_LOCATION_LABELS[project.work_location_type]}${project.remote_percentage ? ` (${project.remote_percentage}%)` : ''}`
                      : t('project_card.not_specified')
                  }
                </div>

                {/* Project Start Date */}
                <div className="text-sm text-muted-foreground" title={project.project_start_date ? `${t('project_card.start_date')}: ${formatDate(project.project_start_date)}` : `${t('project_card.start_date')}: ${t('project_card.not_specified')}`}>
                  <span className="font-medium text-foreground">{t('project_card.start_date')}:</span> {
                    project.project_start_date ? formatDate(project.project_start_date) : t('project_card.not_specified')
                  }
                </div>

                {/* Project End Date */}
                <div className="text-sm text-muted-foreground" title={project.project_end_date ? `${t('project_card.end_date')}: ${formatDate(project.project_end_date)}` : `${t('project_card.end_date')}: ${t('project_card.not_specified')}`}>
                  <span className="font-medium text-foreground">{t('project_card.end_date')}:</span> {
                    project.project_end_date ? formatDate(project.project_end_date) : t('project_card.not_specified')
                  }
                </div>

                {/* Budget */}
                <div className="text-sm text-muted-foreground" title={project.budget_range ? `${t('project_card.budget')}: ${project.budget_range}` : `${t('project_card.budget')}: ${t('project_card.budget_not_specified')}`}>
                  <span className="font-medium text-foreground">{t('project_card.budget')}:</span> {project.budget_range || t('project_card.budget_not_specified')}
                </div>
              </div>
            </div>
          </div>

          {/* SECTION 5: Skills */}
          {project.required_skills && project.required_skills.length > 0 && isDetailsExpanded && (
            <div className="px-6 pb-4">
              <div className="bg-muted/20 rounded-lg p-4 border border-border/30">
                <div className="flex items-center gap-2 mb-4">
                  <Code className="h-4 w-4 text-primary flex-shrink-0" />
                  <span className="text-sm font-medium text-foreground">
                    {t('project_card.technologies')}
                  </span>
                </div>

                <div className="flex flex-wrap gap-2">
                  {project.required_skills.slice(0, 6).map((skill, index) => (
                    <Badge key={index} variant="secondary" className="text-sm">
                      {skill}
                    </Badge>
                  ))}
                  {project.required_skills.length > 6 && (
                    <Badge
                      variant="outline"
                      className="text-sm cursor-pointer hover:bg-accent"
                      title={t('project_card.all_skills', { skills: project.required_skills.join(', ') })}
                    >
                      +{project.required_skills.length - 6}
                    </Badge>
                  )}
                </div>
              </div>
            </div>
          )}

          {/* SECTION 6: Project Description */}
          {project.project_description && isDescriptionExpanded && (
            <div className="flex-1 px-6 pb-6">
              <div className="bg-muted/20 rounded-lg p-4 border border-border/30">
                <div className="flex items-center gap-2 mb-4">
                  <FileText className="h-4 w-4 text-primary flex-shrink-0" />
                  <span className="text-sm font-medium text-foreground">
                    {t('project_card.project_description')}
                  </span>
                </div>

                <div className="text-sm text-muted-foreground">
                  <div
                    className={`${!isDescriptionExpanded ? 'line-clamp-3' : ''} break-words leading-relaxed`}
                    title={!isDescriptionExpanded ? project.project_description : undefined}
                  >
                    <MarkdownRenderer 
                      content={project.project_description}
                      className="prose-sm prose-p:m-0 prose-p:leading-relaxed prose-strong:text-foreground"
                    />
                  </div>
                  {project.project_description.length > 150 && (
                    <Button
                      variant="link"
                      size="sm"
                      onClick={() => setIsDescriptionExpanded(!isDescriptionExpanded)}
                      className="h-auto p-0 text-sm text-primary mt-2"
                    >
                      {isDescriptionExpanded ? 'Weniger anzeigen' : 'Mehr anzeigen'}
                    </Button>
                  )}
                </div>
              </div>
            </div>
          )}

          {/* SECTION 7: Follow-up Timeline */}
          <div className="px-6 pb-4">
            <div className="bg-muted/20 rounded-lg p-4 border border-border/30">
              <div className="flex items-center justify-between mb-3">
                <div className="flex items-center gap-2">
                  <Mail className="h-4 w-4 text-primary flex-shrink-0" />
                  <span className="text-sm font-medium text-foreground">
                    {t('project_card.followups')}
                  </span>
                  {hasFollowUps && (
                    <Badge variant="secondary" className="text-xs">
                      {scheduledFollowUps.length}
                    </Badge>
                  )}
                  {followUpError && (
                    <Badge variant="destructive" className="text-xs">
                      {t('general.error', { defaultValue: 'Fehler' })}
                    </Badge>
                  )}
                  {isFollowUpsLoading && (
                    <Badge variant="outline" className="text-xs">
                      {t('project_card.loading')}
                    </Badge>
                  )}
                </div>
                <div className="flex items-center gap-4">
                  <FollowUpScheduler 
                    application={project}
                    trigger={
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={(e) => e.stopPropagation()}
                        className="h-7 px-3 text-xs"
                      >
                        <Plus className="h-3 w-3 mr-1" />
                        {t('project_card.schedule')}
                      </Button>
                    }
                  />
                </div>
              </div>

              {!hasFollowUps && !isFollowUpsLoading && !followUpError && (
                <div className="flex flex-col items-center justify-center py-6 px-4 rounded-lg bg-muted/20 border border-dashed border-muted-foreground/20">
                  <Clock className="h-8 w-8 text-muted-foreground/40 mb-2" />
                  <p className="text-sm text-muted-foreground font-medium mb-1">{t('project_card.no_followups_planned')}</p>
                  <p className="text-xs text-muted-foreground/70 text-center">
                    {t('project_card.followups_help_text')}
                  </p>
                </div>
              )}

              {isFollowUpsLoading && (
                <div className="flex items-center justify-center py-6 px-4 rounded-lg bg-muted/10">
                  <div className="flex items-center gap-3">
                    <div className="animate-spin rounded-full h-4 w-4 border-2 border-primary border-t-transparent"></div>
                    <span className="text-sm text-muted-foreground">{t('project_card.followups_loading')}</span>
                  </div>
                </div>
              )}

              {followUpError && (
                <div className="flex items-center justify-center py-6 px-4 rounded-lg bg-destructive/5 border border-destructive/20">
                  <div className="flex items-center gap-3">
                    <AlertCircle className="h-4 w-4 text-destructive" />
                    <span className="text-sm text-destructive">{t('project_card.followups_error')}</span>
                  </div>
                </div>
              )}

              {hasFollowUps && (
                <div className="space-y-3">
                  {scheduledFollowUps.slice(0, 2).map((followUp) => {
                    const statusConfig = getFollowUpStatusConfig(followUp.status);
                    const StatusIcon = statusConfig.icon;
                    
                    return (
                      <div 
                        key={followUp.id} 
                        className="group relative p-4 rounded-lg bg-card border border-border/50 hover:border-border hover:shadow-sm transition-all duration-200 cursor-pointer"
                        onClick={(e) => {
                          e.stopPropagation();
                          handleEditFollowUp(followUp);
                        }}
                      >
                        {/* Status indicator bar */}
                        <div className={`absolute left-0 top-0 bottom-0 w-1 rounded-l-lg ${statusConfig.bgColor} ${statusConfig.borderColor}`} />
                        
                        <div className="flex items-start justify-between gap-3 ml-2">
                          <div className="flex-1 min-w-0">
                            {/* Template name */}
                            <div className="mb-1">
                              <h4 className="text-sm font-medium text-foreground truncate">
                                {followUp.template?.name || t('project_card.followup_template')}
                              </h4>
                            </div>
                            
                            {/* Date only */}
                            <div className="text-xs text-muted-foreground">
                              <span className="font-medium">
                                {formatFollowUpDate(followUp.scheduled_date)}
                              </span>
                            </div>
                          </div>
                          
                          {/* Action menu */}
                          <div className="flex-shrink-0 flex items-center opacity-0 group-hover:opacity-100 transition-opacity">
                            <DropdownMenu>
                              <DropdownMenuTrigger asChild>
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={(e) => {
                                    e.stopPropagation();
                                  }}
                                  className="h-6 w-6 p-0 hover:bg-muted"
                                >
                                  <MoreHorizontal className="h-3 w-3" />
                                </Button>
                              </DropdownMenuTrigger>
                              <DropdownMenuContent align="end" className="w-56">
                                <DropdownMenuItem
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    handleEditFollowUp(followUp);
                                  }}
                                >
                                  <Edit className="h-4 w-4 mr-2" />
                                  {t('project_card.edit')}
                                </DropdownMenuItem>
                                
                                
                                {(followUp.template || followUp.template_id) && (
                                  <DropdownMenuItem
                                    onClick={async (e) => {
                                      e.stopPropagation();
                                      
                                      // Generate personalized email
                                      const templateVariables = {
                                        project_title: followUp.application?.project_name || followUp.application?.project_title || project.project_name,
                                        contact_person: followUp.application?.contact_person || project.contact?.name || '',
                                        company_name: followUp.application?.company_name || project.company_name,
                                        user_name: '', // Will be filled by service
                                        trigger_days: followUp.template?.trigger_days || 7,
                                        application_date: project.application_date || '',
                                        interview_date: project.interview_date || ''
                                      };

                                      const template = followUp.template || templates.find(t => t.id === followUp.template_id);
                                      if (!template) {
                                        alert('Template nicht gefunden');
                                        return;
                                      }

                                      const personalizedSubject = FollowUpService.personalizeTemplate(
                                        template.subject, 
                                        templateVariables
                                      );
                                      const personalizedBody = FollowUpService.personalizeTemplate(
                                        template.body, 
                                        templateVariables
                                      );

                                      const emailTo = project.contact?.email || '';
                                      const mailtoLink = FollowUpService.generateMailtoLink(
                                        emailTo,
                                        personalizedSubject,
                                        personalizedBody
                                      );

                                      window.open(mailtoLink);
                                    }}
                                  >
                                    <ExternalLink className="h-4 w-4 mr-2" />
                                    {t('project_card.open_email')}
                                  </DropdownMenuItem>
                                )}

                                {followUp.status === 'scheduled' && (
                                  <DropdownMenuItem
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      handleMarkAsSent(followUp.id);
                                    }}
                                  >
                                    <CheckCircle className="h-4 w-4 mr-2" />
                                    {t('project_card.mark_as_sent')}
                                  </DropdownMenuItem>
                                )}
                                
                                <DropdownMenuItem
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    if (window.confirm(t('project_card.delete_confirmation'))) {
                                      handleDeleteFollowUp(followUp.id);
                                    }
                                  }}
                                  className="text-destructive focus:text-destructive"
                                >
                                  <Trash2 className="h-4 w-4 mr-2" />
                                  {t('project_card.delete')}
                                </DropdownMenuItem>
                              </DropdownMenuContent>
                            </DropdownMenu>
                          </div>
                        </div>
                      </div>
                    );
                  })}
                  
                  {scheduledFollowUps.length > 2 && (
                    <div className="text-center p-2 text-xs text-muted-foreground">
                      {t('project_card.more_followups', { count: scheduledFollowUps.length - 2 })}
                    </div>
                  )}
                </div>
              )}
            </div>
          </div>

        </div>
      </Card>

      <DeleteConfirmationDialog
        isOpen={showDeleteDialog}
        onClose={handleDeleteCancel}
        onConfirm={handleDeleteConfirm}
        projectName={project.project_name}
        companyName={project.company_name}
        isDeleting={isDeleting}
      />

      {selectedFollowUpForEdit && (
        <FollowUpEditDialog
          followUp={selectedFollowUpForEdit}
          project={project}
          isOpen={!!selectedFollowUpForEdit}
          onClose={() => setSelectedFollowUpForEdit(null)}
        />
      )}
    </TooltipProvider>
  );
};
