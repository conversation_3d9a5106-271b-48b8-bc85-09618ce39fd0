import { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { supabase } from '@/integrations/supabase/client';
import { toast } from '@/lib/toast';
import { Brain, Loader2, Clock, Lightbulb, CheckCircle } from 'lucide-react';
import ReactMarkdown from 'react-markdown';

interface NotesSummaryButtonProps {
  projectId: string;
  projectName: string;
  notesCount: number;
  className?: string;
}

type SummaryType = 'timeline' | 'insights' | 'action_items';

interface SummaryResponse {
  summary: string;
  summary_type: SummaryType;
  activities_count: number;
  project_name: string;
}

export const NotesSummaryButton = ({ 
  projectId, 
  projectName, 
  notesCount,
  className = "" 
}: NotesSummaryButtonProps) => {
  const [isOpen, setIsOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [summaryType, setSummaryType] = useState<SummaryType>('timeline');
  const [summary, setSummary] = useState<string>('');

  const handleGenerateSummary = async (retryCount = 0) => {
    if (!summaryType) return;

    setIsLoading(true);
    try {
      const { data, error } = await supabase.functions.invoke('summarize-notes', {
        body: {
          project_id: projectId,
          summary_type: summaryType
        }
      });

      if (error) {
        console.error('Edge Function error:', error);
        
        // Retry once if it's a 400 error (could be cold start)
        if (retryCount === 0 && error.message?.includes('Edge Function returned a non-2xx status code')) {
          console.log('Retrying Edge Function call...');
          await new Promise(resolve => setTimeout(resolve, 1000)); // Wait 1 second
          return handleGenerateSummary(1); // Retry once
        }
        
        throw error;
      }

      const response = data as SummaryResponse;
      setSummary(response.summary);
      
      toast.success('AI-Zusammenfassung erstellt', `${response.activities_count} Notizen wurden analysiert.`);

    } catch (error) {
      console.error('Error generating summary:', error);
      const errorMsg = retryCount > 0 
        ? 'Zusammenfassung konnte auch nach Wiederholung nicht erstellt werden. Bitte versuchen Sie es später erneut.'
        : 'Zusammenfassung konnte nicht erstellt werden.';
      toast.error('Fehler', errorMsg);
    } finally {
      setIsLoading(false);
    }
  };

  const handleClose = () => {
    setIsOpen(false);
    setSummary('');
    setSummaryType('timeline');
  };

  const summaryTypeOptions = [
    {
      value: 'timeline' as const,
      label: 'Chronologische Timeline',
      description: 'Zeitlicher Verlauf aller Ereignisse',
      icon: <Clock className="h-4 w-4" />
    },
    {
      value: 'insights' as const,
      label: 'Erkenntnisse & Patterns',
      description: 'Trends, Chancen und Risiken identifizieren',
      icon: <Lightbulb className="h-4 w-4" />
    },
    {
      value: 'action_items' as const,
      label: 'Action Items & To-Dos',
      description: 'Konkrete nächste Schritte ableiten',
      icon: <CheckCircle className="h-4 w-4" />
    }
  ];

  if (notesCount === 0) {
    return null; // Don't show button if no notes exist
  }

  return (
    <>
      <Button
        variant="outline"
        size="sm"
        onClick={() => setIsOpen(true)}
        className={`${className} flex items-center gap-2`}
      >
        <Brain className="h-4 w-4" />
        AI-Zusammenfassung ({notesCount})
      </Button>

      <Dialog open={isOpen} onOpenChange={handleClose}>
        <DialogContent className="sm:max-w-[700px] max-h-[80vh] overflow-hidden flex flex-col">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Brain className="h-5 w-5" />
              AI-Notizen Zusammenfassung
            </DialogTitle>
            <DialogDescription>
              Projekt: <strong>{projectName}</strong> • {notesCount} Notizen verfügbar
            </DialogDescription>
          </DialogHeader>

          <div className="flex-1 overflow-auto space-y-4">
            {!summary ? (
              // Summary Type Selection
              <div className="space-y-4">
                <div>
                  <label className="text-sm font-medium">Art der Zusammenfassung</label>
                  <Select value={summaryType} onValueChange={(value) => setSummaryType(value as SummaryType)}>
                    <SelectTrigger className="mt-2">
                      <SelectValue placeholder="Wählen Sie eine Zusammenfassungsart" />
                    </SelectTrigger>
                    <SelectContent>
                      {summaryTypeOptions.map((option) => (
                        <SelectItem key={option.value} value={option.value}>
                          <div className="flex items-center gap-2">
                            {option.icon}
                            <div>
                              <div className="font-medium">{option.label}</div>
                              <div className="text-xs text-muted-foreground">{option.description}</div>
                            </div>
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>
            ) : (
              // Summary Display
              <div className="space-y-4">
                <div className="flex items-center gap-2 p-3 bg-muted/50 rounded-lg">
                  {summaryTypeOptions.find(opt => opt.value === summaryType)?.icon}
                  <span className="font-medium">
                    {summaryTypeOptions.find(opt => opt.value === summaryType)?.label}
                  </span>
                </div>
                
                <div className="prose prose-sm max-w-none dark:prose-invert">
                  <div className="p-4 bg-background border rounded-lg text-sm leading-relaxed">
                    <ReactMarkdown 
                      components={{
                        // Customize heading styles for better spacing
                        h1: ({ children }) => <h1 className="text-lg font-bold mb-3 mt-4 first:mt-0">{children}</h1>,
                        h2: ({ children }) => <h2 className="text-base font-semibold mb-2 mt-3 first:mt-0">{children}</h2>,
                        h3: ({ children }) => <h3 className="text-sm font-medium mb-2 mt-3 first:mt-0">{children}</h3>,
                        // Better list styling
                        ul: ({ children }) => <ul className="space-y-1 my-2">{children}</ul>,
                        ol: ({ children }) => <ol className="space-y-1 my-2">{children}</ol>,
                        li: ({ children }) => <li className="ml-4">{children}</li>,
                        // Paragraph spacing
                        p: ({ children }) => <p className="mb-2 last:mb-0">{children}</p>,
                        // Code blocks
                        code: ({ children }) => <code className="bg-muted px-1 py-0.5 rounded text-xs">{children}</code>,
                        pre: ({ children }) => <pre className="bg-muted p-2 rounded text-xs overflow-x-auto">{children}</pre>,
                        // Emphasis
                        strong: ({ children }) => <strong className="font-semibold">{children}</strong>,
                        em: ({ children }) => <em className="italic">{children}</em>,
                      }}
                    >
                      {summary}
                    </ReactMarkdown>
                  </div>
                </div>
              </div>
            )}
          </div>

          <DialogFooter className="flex-col sm:flex-row gap-2">
            {!summary ? (
              <>
                <Button variant="outline" onClick={handleClose}>
                  Abbrechen
                </Button>
                <Button 
                  onClick={handleGenerateSummary} 
                  disabled={isLoading || !summaryType}
                  className="flex items-center gap-2"
                >
                  {isLoading && <Loader2 className="h-4 w-4 animate-spin" />}
                  {isLoading ? 'Wird erstellt...' : 'Zusammenfassung erstellen'}
                </Button>
              </>
            ) : (
              <>
                <Button 
                  variant="outline" 
                  onClick={() => setSummary('')}
                  className="flex items-center gap-2"
                >
                  <Brain className="h-4 w-4" />
                  Neue Zusammenfassung
                </Button>
                <Button onClick={handleClose}>
                  Schließen
                </Button>
              </>
            )}
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
};