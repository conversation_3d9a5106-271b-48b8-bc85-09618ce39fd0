import React from 'react';
import { Timer, Send, Pause, Clock, Calendar, ChevronDown, Plus } from 'lucide-react';
import { Badge } from '@/components/ui/badge';

/**
 * Example component showcasing the improved Follow-up design patterns
 * This demonstrates the new visual hierarchy and styling approach
 */
export const FollowUpCardExample: React.FC = () => {
  // Example follow-up data
  const exampleFollowUps = [
    {
      id: '1',
      template: { name: 'Initial Follow-up' },
      status: 'scheduled' as const,
      scheduled_date: '2024-01-15'
    },
    {
      id: '2', 
      template: { name: 'Second Follow-up' },
      status: 'sent' as const,
      scheduled_date: '2024-01-10'
    }
  ];

  // Status configuration - same as in ProjectCard
  const getFollowUpStatusConfig = (status: string) => {
    switch (status) {
      case 'scheduled':
        return {
          icon: Timer,
          iconColor: 'text-blue-600',
          bgColor: 'bg-blue-50',
          borderColor: 'border-blue-200',
          textColor: 'text-blue-700',
          label: '<PERSON><PERSON><PERSON>'
        };
      case 'sent':
        return {
          icon: Send,
          iconColor: 'text-green-600',
          bgColor: 'bg-green-50',
          borderColor: 'border-green-200',
          textColor: 'text-green-700',
          label: 'Gesendet'
        };
      case 'dismissed':
        return {
          icon: Pause,
          iconColor: 'text-gray-600',
          bgColor: 'bg-gray-50',
          borderColor: 'border-gray-200',
          textColor: 'text-gray-700',
          label: 'Pausiert'
        };
      default:
        return {
          icon: Clock,
          iconColor: 'text-gray-600',
          bgColor: 'bg-gray-50',
          borderColor: 'border-gray-200',
          textColor: 'text-gray-700',
          label: status
        };
    }
  };

  return (
    <div className="max-w-md mx-auto p-4 space-y-4">
      <h2 className="text-lg font-semibold mb-4">Follow-up Design Examples</h2>
      
      {/* Improved Follow-up Cards */}
      <div className="space-y-3">
        {exampleFollowUps.map((followUp) => {
          const statusConfig = getFollowUpStatusConfig(followUp.status);
          const StatusIcon = statusConfig.icon;
          
          return (
            <div 
              key={followUp.id}
              className="group relative p-4 rounded-lg bg-card border border-border/50 hover:border-border hover:shadow-sm transition-all duration-200 cursor-pointer"
            >
              {/* Status indicator bar */}
              <div className={`absolute left-0 top-0 bottom-0 w-1 rounded-l-lg ${statusConfig.bgColor} ${statusConfig.borderColor}`} />
              
              <div className="flex items-start justify-between gap-3 ml-2">
                <div className="flex-1 min-w-0">
                  {/* Template name */}
                  <div className="flex items-center gap-2 mb-2">
                    <div className={`flex-shrink-0 p-1.5 rounded-md ${statusConfig.bgColor}`}>
                      <StatusIcon className={`h-3.5 w-3.5 ${statusConfig.iconColor}`} />
                    </div>
                    <h4 className="text-sm font-medium text-foreground truncate">
                      {followUp.template?.name || 'Follow-up Template'}
                    </h4>
                  </div>
                  
                  {/* Status and date row */}
                  <div className="flex items-center gap-3">
                    <Badge 
                      variant="secondary" 
                      className={`text-xs px-2 py-1 font-medium ${statusConfig.bgColor} ${statusConfig.borderColor} ${statusConfig.textColor} border`}
                    >
                      {statusConfig.label}
                    </Badge>
                    <div className="flex items-center gap-1.5 text-xs text-muted-foreground">
                      <Calendar className="h-3.5 w-3.5" />
                      <span className="font-medium">
                        {new Date(followUp.scheduled_date).toLocaleDateString('de-DE')}
                      </span>
                    </div>
                  </div>
                </div>
                
                {/* Hover indicator */}
                <div className="flex-shrink-0 opacity-0 group-hover:opacity-100 transition-opacity">
                  <ChevronDown className="h-4 w-4 text-muted-foreground" />
                </div>
              </div>
            </div>
          );
        })}

        {/* Show more button */}
        <div className="group flex items-center justify-center gap-2 p-3 rounded-lg bg-muted/30 hover:bg-muted/50 border border-dashed border-muted-foreground/30 hover:border-muted-foreground/50 cursor-pointer transition-all duration-200">
          <Plus className="h-4 w-4 text-muted-foreground group-hover:text-foreground transition-colors" />
          <span className="text-sm font-medium text-muted-foreground group-hover:text-foreground transition-colors">
            2 weitere Follow-ups anzeigen
          </span>
          <ChevronDown className="h-4 w-4 text-muted-foreground group-hover:text-foreground transition-colors" />
        </div>
      </div>

      {/* Empty state example */}
      <div className="mt-8">
        <h3 className="text-md font-medium mb-2">Empty State:</h3>
        <div className="flex flex-col items-center justify-center py-6 px-4 rounded-lg bg-muted/20 border border-dashed border-muted-foreground/20">
          <Clock className="h-8 w-8 text-muted-foreground/40 mb-2" />
          <p className="text-sm text-muted-foreground font-medium mb-1">Keine Follow-ups geplant</p>
          <p className="text-xs text-muted-foreground/70 text-center">
            Follow-ups helfen dabei, am Ball zu bleiben
          </p>
        </div>
      </div>

      {/* Loading state example */}
      <div className="mt-4">
        <h3 className="text-md font-medium mb-2">Loading State:</h3>
        <div className="flex items-center justify-center py-6 px-4 rounded-lg bg-muted/10">
          <div className="flex items-center gap-3">
            <div className="animate-spin rounded-full h-4 w-4 border-2 border-primary border-t-transparent"></div>
            <span className="text-sm text-muted-foreground">Follow-ups werden geladen...</span>
          </div>
        </div>
      </div>
    </div>
  );
};