import { useState, useCallback, useMemo } from 'react';
import { ApplicationActivity, ALL_ACTIVITY_LABELS, ALL_ACTIVITY_COLORS, ActivityType } from '@/types/shared';
import { useActivityLabel } from '@/lib/translations';
import { useProjectActivities, calculateStatusDurations, getActivitySummary } from '@/hooks/useProjectActivities';
import { useTranslation } from '@/hooks/useTranslation';
import { useFollowUpSchedule } from '@/hooks/useFollowUpSchedule';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Button } from '@/components/ui/button';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import { NotesSummaryButton } from './NotesSummaryButton';
import { 
  Clock, 
  FileText, 
  ArrowRight, 
  User, 
  Edit, 
  Send,
  Activity,
  TrendingUp,
  ChevronDown,
  ChevronUp,
  MessageSquare,
  CalendarDays,
  Brain,
  Trash2,
  CheckCircle,
  Upload,
  Download,
  Settings,
  Calendar,
  Plus,
  RefreshCw,
  Mail
} from 'lucide-react';
import { format, formatDistanceToNow } from 'date-fns';
import { de, enUS } from 'date-fns/locale';
import ReactMarkdown from 'react-markdown';

interface ProjectTimelineProps {
  projectId: string;
  projectName?: string;
}

const activityIcons: Record<ActivityType, React.ReactNode> = {
  // Application Activity Types
  application_created: <Plus className="h-4 w-4" />,
  application_updated: <Edit className="h-4 w-4" />,
  application_deleted: <Trash2 className="h-4 w-4" />,
  application_sent: <Send className="h-4 w-4" />,
  application_status_changed: <ArrowRight className="h-4 w-4" />,
  application_converted_to_project: <CheckCircle className="h-4 w-4" />,
  ai_analysis_generated: <Brain className="h-4 w-4" />,
  ai_application_generated: <Brain className="h-4 w-4" />,
  
  // Project Activity Types (korrekte Types)
  project_created: <FileText className="h-4 w-4" />,
  project_updated: <Edit className="h-4 w-4" />,
  project_deleted: <Trash2 className="h-4 w-4" />,
  project_status_changed: <ArrowRight className="h-4 w-4" />,
  time_tracking_started: <Clock className="h-4 w-4" />,
  time_tracking_stopped: <Clock className="h-4 w-4" />,
  time_entry_created: <Clock className="h-4 w-4" />,
  time_entry_updated: <Edit className="h-4 w-4" />,
  time_entry_deleted: <Trash2 className="h-4 w-4" />,
  
  // Shared Activity Types
  contact_created: <User className="h-4 w-4" />,
  contact_updated: <User className="h-4 w-4" />,
  contact_deleted: <Trash2 className="h-4 w-4" />,
  data_imported: <Upload className="h-4 w-4" />,
  data_exported: <Download className="h-4 w-4" />,
  bulk_operation_completed: <CheckCircle className="h-4 w-4" />,
  calendar_event_created: <Calendar className="h-4 w-4" />,
  calendar_event_updated: <Calendar className="h-4 w-4" />,
  calendar_event_deleted: <Trash2 className="h-4 w-4" />,
  followup_scheduled: <CalendarDays className="h-4 w-4" />,
  followup_sent: <Mail className="h-4 w-4" />,
  followup_marked_as_sent: <CheckCircle className="h-4 w-4" />,
  followup_deleted: <Trash2 className="h-4 w-4" />,
  file_uploaded: <Upload className="h-4 w-4" />,
  settings_updated: <Settings className="h-4 w-4" />,
  
  // Legacy activity types (for backward compatibility)
  status_changed: <ArrowRight className="h-4 w-4" />,
  created: <Plus className="h-4 w-4" />,
  updated: <Edit className="h-4 w-4" />,
  deleted: <Trash2 className="h-4 w-4" />
};

export const ProjectTimeline = ({ projectId, projectName = "Projekt" }: ProjectTimelineProps) => {
  const { data: activities = [], isLoading, error } = useProjectActivities(projectId);
  const { getScheduledByApplication } = useFollowUpSchedule();
  const getActivityLabel = useActivityLabel();
  const { t, i18n } = useTranslation('common');
  const dateLocale = i18n.language === 'de' ? de : enUS;
  const [expandedNotes, setExpandedNotes] = useState<Set<string>>(new Set());
  
  // Always call hooks in the same order
  const summary = useMemo(() => getActivitySummary(activities), [activities]);
  const statusDurations = useMemo(() => calculateStatusDurations(activities), [activities]);
  const activitiesWithNotes = useMemo(() => activities.filter(activity => activity.notes), [activities]);
  const followUpsCount = useMemo(() => getScheduledByApplication(projectId).length, [getScheduledByApplication, projectId]);

  const toggleNoteExpansion = useCallback((activityId: string) => {
    setExpandedNotes(prev => {
      const newExpanded = new Set(prev);
      if (newExpanded.has(activityId)) {
        newExpanded.delete(activityId);
      } else {
        newExpanded.add(activityId);
      }
      return newExpanded;
    });
  }, []);
  
  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Activity className="h-5 w-5" />
            {t('timeline.project_timeline')}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {[1, 2, 3].map((i) => (
              <div key={i} className="flex gap-3 sm:gap-4">
                <div className="w-8 h-8 rounded-full bg-muted animate-pulse flex-shrink-0" />
                <div className="flex-1 space-y-2">
                  <div className="h-4 bg-muted animate-pulse rounded w-3/4" />
                  <div className="h-3 bg-muted animate-pulse rounded w-1/2" />
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="text-lg text-destructive">{t('timeline.error_loading')}</CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-sm text-muted-foreground">
            {t('timeline.error_description')}
          </p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-4 sm:space-y-6">
      {/* Summary Cards */}
      <div className="grid grid-cols-2 sm:grid-cols-2 lg:grid-cols-4 gap-3 sm:gap-4">
        <Card className="bg-gradient-to-br from-blue-500/5 to-blue-500/10 border-blue-500/20">
          <CardContent className="p-3 sm:p-4">
            <div className="flex items-center justify-between">
              <div>
                <div className="text-lg sm:text-xl font-bold text-blue-600">{summary.totalActivities}</div>
                <div className="text-xs sm:text-sm text-muted-foreground">{t('timeline.activities')}</div>
              </div>
              <Activity className="h-4 w-4 sm:h-5 sm:w-5 text-blue-600 opacity-60" />
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-green-500/5 to-green-500/10 border-green-500/20">
          <CardContent className="p-3 sm:p-4">
            <div className="flex items-center justify-between">
              <div>
                <div className="text-lg sm:text-xl font-bold text-green-600">{summary.statusChanges}</div>
                <div className="text-xs sm:text-sm text-muted-foreground">{t('timeline.status_changes')}</div>
              </div>
              <TrendingUp className="h-4 w-4 sm:h-5 sm:w-5 text-green-600 opacity-60" />
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-purple-500/5 to-purple-500/10 border-purple-500/20">
          <CardContent className="p-3 sm:p-4">
            <div className="flex items-center justify-between">
              <div>
                <div className="text-lg sm:text-xl font-bold text-purple-600">{summary.projectUpdates}</div>
                <div className="text-xs sm:text-sm text-muted-foreground">{t('timeline.project_updates')}</div>
              </div>
              <Edit className="h-4 w-4 sm:h-5 sm:w-5 text-purple-600 opacity-60" />
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-orange-500/5 to-orange-500/10 border-orange-500/20">
          <CardContent className="p-3 sm:p-4">
            <div className="flex items-center justify-between">
              <div>
                <div className="text-lg sm:text-xl font-bold text-orange-600">{followUpsCount}</div>
                <div className="text-xs sm:text-sm text-muted-foreground">{t('timeline.followups')}</div>
              </div>
              <Mail className="h-4 w-4 sm:h-5 sm:w-5 text-orange-600 opacity-60" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Status Duration Analysis */}
      {Object.keys(statusDurations).length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Clock className="h-5 w-5" />
              {t('timeline.status_duration')}
            </CardTitle>
            <CardDescription className="mt-2">
              {t('timeline.status_duration_description')}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4">
              {Object.entries(statusDurations).map(([status, days]) => (
                <div key={status} className="flex items-center justify-between p-3 rounded-lg border bg-card/50">
                  <span className="text-sm text-muted-foreground">{status}</span>
                  <Badge variant="outline">{days} {days === 1 ? t('timeline.day') : t('timeline.days')}</Badge>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Timeline */}
      <Card>
        <CardHeader>
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3">
            <div>
              <CardTitle className="flex items-center gap-2">
                <Activity className="h-5 w-5" />
                {t('timeline.activity_timeline')}
              </CardTitle>
              <CardDescription className="mt-2">
                {t('timeline.activity_timeline_description')}
              </CardDescription>
            </div>
            
            {/* AI Summary Button */}
            <NotesSummaryButton
              projectId={projectId}
              projectName={projectName}
              notesCount={activitiesWithNotes.length}
              className="w-full sm:w-auto"
            />
          </div>
        </CardHeader>
        <CardContent>
          {activities.length === 0 ? (
            <div className="text-center py-6 sm:py-8">
              <Activity className="h-8 w-8 sm:h-12 sm:w-12 text-muted-foreground mx-auto mb-3 sm:mb-4" />
              <p className="text-sm sm:text-base text-muted-foreground">
                {t('timeline.no_activities')}
              </p>
            </div>
          ) : (
            <div className="relative">
              {/* Timeline Line - Hidden on very small screens */}
              <div className="absolute left-4 top-0 bottom-0 w-px bg-border hidden xs:block" />
              
              <div className="space-y-4 sm:space-y-6">
                {activities.map((activity, index) => {
                  const isLast = index === activities.length - 1;
                  
                  return (
                    <div key={activity.id} className="relative flex gap-3 sm:gap-4">
                      {/* Timeline Dot */}
                      <div className={`
                        w-8 h-8 rounded-full border-2 border-background flex items-center justify-center flex-shrink-0 z-10
                        ${ALL_ACTIVITY_COLORS[activity.activity_type]}
                      `}>
                        {activityIcons[activity.activity_type]}
                      </div>

                      {/* Content */}
                      <div className="flex-1 min-w-0 pb-4 sm:pb-6">
                        <div className="flex flex-col sm:flex-row sm:items-center gap-1 sm:gap-3 mb-2">
                          <Badge 
                            variant="outline" 
                            className={`${ALL_ACTIVITY_COLORS[activity.activity_type]} text-xs w-fit`}
                          >
                            {getActivityLabel(activity.activity_type)}
                          </Badge>
                          <div className="flex items-center gap-2 text-xs sm:text-sm text-muted-foreground">
                            <Clock className="h-3 w-3 sm:h-4 sm:w-4" />
                            <span className="hidden sm:inline">
                              {format(new Date(activity.created_at), 'dd.MM.yyyy, HH:mm', { locale: dateLocale })}
                            </span>
                            <span className="sm:hidden">
                              {format(new Date(activity.created_at), 'dd.MM.yy, HH:mm', { locale: dateLocale })}
                            </span>
                            <span className="text-xs">
                              ({formatDistanceToNow(new Date(activity.created_at), { 
                                addSuffix: true, 
                                locale: dateLocale 
                              })})
                            </span>
                          </div>
                        </div>

                        <div className="space-y-2">
                          <p className="text-sm sm:text-base text-foreground leading-relaxed">
                            {activity.description}
                          </p>

                          {/* Notes Section */}
                          {activity.notes && (
                            <div className="mt-3">
                              <Collapsible
                                open={expandedNotes.has(activity.id)}
                                onOpenChange={() => toggleNoteExpansion(activity.id)}
                              >
                                <CollapsibleTrigger asChild>
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    className="h-auto p-2 text-xs text-muted-foreground hover:text-foreground"
                                  >
                                    <MessageSquare className="h-3 w-3 mr-1" />
                                    {t('timeline.show_notes')}
                                    {expandedNotes.has(activity.id) ? (
                                      <ChevronUp className="h-3 w-3 ml-1" />
                                    ) : (
                                      <ChevronDown className="h-3 w-3 ml-1" />
                                    )}
                                  </Button>
                                </CollapsibleTrigger>
                                <CollapsibleContent className="mt-2">
                                  <div className="bg-muted/30 border border-border/50 rounded-lg p-3 space-y-2">
                                    {/* Custom Date if available */}
                                    {activity.notes_date && (
                                      <div className="flex items-center gap-2 text-xs text-muted-foreground border-b border-border/30 pb-2">
                                        <CalendarDays className="h-3 w-3" />
                                        <span>
                                          {t('timeline.notes_date', { date: format(new Date(activity.notes_date), 'dd.MM.yyyy, HH:mm', { locale: dateLocale }) })}
                                        </span>
                                      </div>
                                    )}
                                    
                                    {/* Notes Content */}
                                    <div className="text-sm text-foreground leading-relaxed prose prose-sm dark:prose-invert max-w-none">
                                      <ReactMarkdown
                                        disallowedElements={['script', 'iframe', 'object', 'embed', 'form', 'input']}
                                        unwrapDisallowed={true}
                                        components={{
                                          // Custom styling for markdown elements in notes
                                          strong: ({ children }) => <strong className="font-bold text-foreground">{children}</strong>,
                                          h1: ({ children }) => <h1 className="text-base font-bold mb-2 text-foreground">{children}</h1>,
                                          h2: ({ children }) => <h2 className="text-sm font-bold mb-2 text-foreground">{children}</h2>,
                                          h3: ({ children }) => <h3 className="text-xs font-bold mb-1 text-foreground">{children}</h3>,
                                          p: ({ children }) => <p className="mb-2 text-foreground last:mb-0">{children}</p>,
                                          ul: ({ children }) => <ul className="list-disc pl-4 mb-2 space-y-1">{children}</ul>,
                                          ol: ({ children }) => <ol className="list-decimal pl-4 mb-2 space-y-1">{children}</ol>,
                                          li: ({ children }) => <li className="text-foreground text-sm">{children}</li>,
                                          hr: () => <hr className="my-3 border-border" />,
                                          code: ({ children }) => <code className="bg-muted px-1 py-0.5 rounded text-xs font-mono">{children}</code>,
                                          pre: ({ children }) => <pre className="bg-muted p-2 rounded-lg overflow-x-auto text-xs">{children}</pre>,
                                          blockquote: ({ children }) => <blockquote className="border-l-4 border-primary/30 pl-3 italic text-muted-foreground">{children}</blockquote>,
                                          a: ({ children, href }) => (
                                            <a 
                                              href={href} 
                                              target="_blank" 
                                              rel="noopener noreferrer"
                                              className="text-primary hover:underline text-sm"
                                            >
                                              {children}
                                            </a>
                                          )
                                        }}
                                      >
                                        {activity.notes}
                                      </ReactMarkdown>
                                    </div>
                                  </div>
                                </CollapsibleContent>
                              </Collapsible>
                            </div>
                          )}
                        </div>

                        {/* Separator - Only show if not last item */}
                        {!isLast && (
                          <Separator className="mt-4 sm:mt-6" />
                        )}
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};