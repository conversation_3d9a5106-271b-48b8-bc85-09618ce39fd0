import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { MapPin } from 'lucide-react';
import { useTranslation } from '@/hooks/useTranslation';

interface ProjectFormWorkLocationProps {
  workLocationType: 'remote' | 'onsite' | 'hybrid' | 'flexible' | '';
  setWorkLocationType: (value: 'remote' | 'onsite' | 'hybrid' | 'flexible') => void;
  remotePercentage: string;
  setRemotePercentage: (value: string) => void;
  workLocationNotes: string;
  setWorkLocationNotes: (value: string) => void;
}

export const ProjectFormWorkLocation = ({
  workLocationType,
  setWorkLocationType,
  remotePercentage,
  setRemotePercentage,
  workLocationNotes,
  setWorkLocationNotes
}: ProjectFormWorkLocationProps) => {
  const { t } = useTranslation('applications');
  
  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <MapPin className="h-5 w-5" />
          {t('components.work_location.title')}
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="work-location-type">{t('components.work_location.work_location_type_label')}</Label>
            <Select value={workLocationType} onValueChange={(value: 'remote' | 'onsite' | 'hybrid' | 'flexible') => setWorkLocationType(value)}>
              <SelectTrigger>
                <SelectValue placeholder={t('components.work_location.work_location_placeholder')} />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="remote">{t('components.work_location.remote')}</SelectItem>
                <SelectItem value="onsite">{t('components.work_location.onsite')}</SelectItem>
                <SelectItem value="hybrid">{t('components.work_location.hybrid')}</SelectItem>
                <SelectItem value="flexible">{t('components.work_location.flexible')}</SelectItem>
              </SelectContent>
            </Select>
          </div>
          
          {(workLocationType === 'hybrid' || workLocationType === 'flexible') && (
            <div className="space-y-2">
              <Label htmlFor="remote-percentage">{t('components.work_location.remote_percentage_label')}</Label>
              <Input
                id="remote-percentage"
                type="number"
                min="0"
                max="100"
                value={remotePercentage}
                onChange={(e) => setRemotePercentage(e.target.value)}
                placeholder={t('components.work_location.remote_percentage_placeholder')}
              />
            </div>
          )}
        </div>
        
        <div className="space-y-2">
          <Label htmlFor="work-location-notes">{t('components.work_location.work_location_notes_label')}</Label>
          <Textarea
            id="work-location-notes"
            value={workLocationNotes}
            onChange={(e) => setWorkLocationNotes(e.target.value)}
            placeholder={t('components.work_location.work_location_notes_placeholder')}
            rows={2}
          />
        </div>
      </CardContent>
    </Card>
  );
};