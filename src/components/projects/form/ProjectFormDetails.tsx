import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { useTranslation } from '@/hooks/useTranslation';
import { Calendar, DollarSign, Plus, X } from 'lucide-react';

interface ProjectFormDetailsProps {
  projectStartDate: string;
  setProjectStartDate: (value: string) => void;
  projectEndDate: string;
  setProjectEndDate: (value: string) => void;
  budgetRange: string;
  setBudgetRange: (value: string) => void;
  projectDescription: string;
  setProjectDescription: (value: string) => void;
  requiredSkills: string[];
  setRequiredSkills: (skills: string[]) => void;
  currentSkill: string;
  setCurrentSkill: (value: string) => void;
}

export const ProjectFormDetails = ({
  projectStartDate,
  setProjectStartDate,
  projectEndDate,
  setProjectEndDate,
  budgetRange,
  setBudgetRange,
  projectDescription,
  setProjectDescription,
  requiredSkills,
  setRequiredSkills,
  currentSkill,
  setCurrentSkill
}: ProjectFormDetailsProps) => {
  const { t } = useTranslation('applications');
  const addSkill = () => {
    if (currentSkill.trim() && !requiredSkills.includes(currentSkill.trim())) {
      setRequiredSkills([...requiredSkills, currentSkill.trim()]);
      setCurrentSkill('');
    }
  };

  const removeSkill = (skillToRemove: string) => {
    setRequiredSkills(requiredSkills.filter(skill => skill !== skillToRemove));
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Calendar className="h-5 w-5" />
          {t('components.details.title')}
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="space-y-2">
            <Label htmlFor="project-start-date">{t('components.details.project_start_label')}</Label>
            <Input
              id="project-start-date"
              type="date"
              value={projectStartDate}
              onChange={(e) => setProjectStartDate(e.target.value)}
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="project-end-date">{t('components.details.project_end_label')}</Label>
            <Input
              id="project-end-date"
              type="date"
              value={projectEndDate}
              onChange={(e) => setProjectEndDate(e.target.value)}
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="budget-range">{t('components.details.budget_label')}</Label>
            <div className="flex items-center gap-2">
              <DollarSign className="h-4 w-4 text-muted-foreground" />
              <Input
                id="budget-range"
                value={budgetRange}
                onChange={(e) => setBudgetRange(e.target.value)}
                placeholder={t('components.details.budget_placeholder')}
              />
            </div>
          </div>
        </div>

        <div className="space-y-2">
          <Label htmlFor="project-description">{t('components.details.project_description_label')}</Label>
          <Textarea
            id="project-description"
            value={projectDescription}
            onChange={(e) => setProjectDescription(e.target.value)}
            rows={8}
            className="min-h-[200px]"
          />
        </div>

        <div className="space-y-2">
          <Label>{t('components.details.required_skills_label')}</Label>
          <div className="flex gap-2">
            <Input
              placeholder={t('components.details.skill_placeholder')}
              value={currentSkill}
              onChange={(e) => setCurrentSkill(e.target.value)}
              onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), addSkill())}
            />
            <Button type="button" onClick={addSkill} variant="outline">
              <Plus className="h-4 w-4" />
            </Button>
          </div>
          {requiredSkills.length > 0 && (
            <div className="flex flex-wrap gap-2 mt-2">
              {requiredSkills.map((skill, index) => (
                <Badge key={index} variant="secondary" className="pr-1">
                  {skill}
                  <button
                    type="button"
                    onClick={() => removeSkill(skill)}
                    className="ml-1 hover:text-destructive"
                  >
                    <X className="h-3 w-3" />
                  </button>
                </Badge>
              ))}
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
};