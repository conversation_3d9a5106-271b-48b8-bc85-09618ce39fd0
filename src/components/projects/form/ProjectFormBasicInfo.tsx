import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { useTranslation } from '@/hooks/useTranslation';
import { Users, Globe } from 'lucide-react';

interface ProjectFormBasicInfoProps {
  projectName: string;
  setProjectName: (value: string) => void;
  companyName: string;
  setCompanyName: (value: string) => void;
  source: string;
  setSource: (value: string) => void;
  listingUrl: string;
  setListingUrl: (value: string) => void;
}

export const ProjectFormBasicInfo = ({
  projectName,
  setProjectName,
  companyName,
  setCompanyName,
  source,
  setSource,
  listingUrl,
  setListingUrl
}: ProjectFormBasicInfoProps) => {
  const { t } = useTranslation('applications');
  
  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Users className="h-5 w-5" />
          {t('components.basic_info.title')}
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="project-name">{t('components.basic_info.project_name_label')}</Label>
            <Input
              id="project-name"
              value={projectName}
              onChange={(e) => setProjectName(e.target.value)}
              required
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="company-name">{t('components.basic_info.company_name_label')}</Label>
            <Input
              id="company-name"
              value={companyName}
              onChange={(e) => setCompanyName(e.target.value)}
              required
            />
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="source">{t('components.basic_info.source_label')}</Label>
            <div className="flex items-center gap-2">
              <Globe className="h-4 w-4 text-muted-foreground" />
              <Input
                id="source"
                value={source}
                onChange={(e) => setSource(e.target.value)}
                placeholder={t('components.basic_info.source_placeholder')}
              />
            </div>
          </div>
          <div className="space-y-2">
            <Label htmlFor="listing-url">{t('components.basic_info.listing_url_label')}</Label>
            <Input
              id="listing-url"
              type="url"
              value={listingUrl}
              onChange={(e) => setListingUrl(e.target.value)}
              placeholder="https://..."
            />
          </div>
        </div>
      </CardContent>
    </Card>
  );
};