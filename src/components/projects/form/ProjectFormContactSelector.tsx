import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { <PERSON><PERSON>, <PERSON>alog<PERSON>ontent, <PERSON><PERSON>Header, <PERSON><PERSON>Title, DialogTrigger } from '@/components/ui/dialog';
import { Users, X, User } from 'lucide-react';
import { Contact } from '@/types/freelance';
import { toast } from '@/lib/toast';
import { useTranslation } from '@/hooks/useTranslation';

interface ProjectFormContactSelectorProps {
  selectedContactId: string;
  contactName: string;
  setContactName: (value: string) => void;
  contactEmail: string;
  setContactEmail: (value: string) => void;
  contactPhone: string;
  setContactPhone: (value: string) => void;
  contactCompany: string;
  setContactCompany: (value: string) => void;
  contacts: Contact[];
  contactsLoading: boolean;
  onContactSelect: (contact: Contact) => void;
  onContactClear: () => void;
}

export const ProjectFormContactSelector = ({
  selectedContactId,
  contactName,
  setContactName,
  contactEmail,
  setContactEmail,
  contactPhone,
  setContactPhone,
  contactCompany,
  setContactCompany,
  contacts,
  contactsLoading,
  onContactSelect,
  onContactClear
}: ProjectFormContactSelectorProps) => {
  const { t } = useTranslation('applications');
  const { t: tForms } = useTranslation('forms');
  const [showContactSelector, setShowContactSelector] = useState(false);
  const [contactSearchTerm, setContactSearchTerm] = useState('');

  const handleSelectExistingContact = (contact: Contact) => {
    onContactSelect(contact);
    setShowContactSelector(false);
    setContactSearchTerm(''); // Reset search when closing
    toast.success(t('components.contact_selector.contact_selected_toast'), t('components.contact_selector.contact_selected_message', { name: contact.name || contact.email }));
  };

  // Filter contacts based on search term
  const filteredContacts = contacts.filter(contact => {
    if (!contactSearchTerm) return true;
    const searchLower = contactSearchTerm.toLowerCase();
    return (
      (contact.name?.toLowerCase() || '').includes(searchLower) ||
      (contact.email?.toLowerCase() || '').includes(searchLower) ||
      (contact.phone?.toLowerCase() || '').includes(searchLower) ||
      (contact.company?.toLowerCase() || '').includes(searchLower)
    );
  });

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <User className="h-5 w-5" />
          {t('components.contact_selector.title')}
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="flex items-center justify-between">
          <Label>{t('components.contact_selector.contact_label')}</Label>
          <div className="flex gap-2">
            <Dialog open={showContactSelector} onOpenChange={setShowContactSelector}>
              <DialogTrigger asChild>
                <Button type="button" variant="outline" size="sm">
                  <Users className="h-4 w-4 mr-2" />
                  {t('components.contact_selector.select_existing')}
                </Button>
              </DialogTrigger>
              <DialogContent className="max-w-md">
                <DialogHeader>
                  <DialogTitle>{t('components.contact_selector.select_contact_dialog')}</DialogTitle>
                </DialogHeader>
                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="contact-search">{t('components.contact_selector.search_contacts')}</Label>
                    <Input
                      id="contact-search"
                      placeholder={tForms('placeholders.contact_search')}
                      value={contactSearchTerm}
                      onChange={(e) => setContactSearchTerm(e.target.value)}
                    />
                  </div>
                  
                  <div className="space-y-2 max-h-96 overflow-y-auto">
                    {contactsLoading ? (
                      <div className="p-4 text-center">{t('components.contact_selector.loading_contacts')}</div>
                    ) : filteredContacts.length === 0 ? (
                      <div className="p-4 text-center text-muted-foreground">
                        {contactSearchTerm ? t('components.contact_selector.no_matching_contacts') : t('components.contact_selector.no_contacts')}
                      </div>
                    ) : (
                      filteredContacts.map(contact => (
                        <div
                          key={contact.id}
                          className="p-3 border rounded-lg cursor-pointer hover:bg-accent transition-colors"
                          onClick={() => handleSelectExistingContact(contact)}
                        >
                          <div className="font-medium">
                            {contact.name || contact.email || t('components.contact_selector.unnamed_contact')}
                          </div>
                          {contact.company && (
                            <div className="text-sm text-muted-foreground mt-1">
                              🏢 {contact.company}
                            </div>
                          )}
                          {contact.email && (
                            <div className="text-sm text-muted-foreground">
                              ✉️ {contact.email}
                            </div>
                          )}
                          {contact.phone && (
                            <div className="text-sm text-muted-foreground">
                              📞 {contact.phone}
                            </div>
                          )}
                        </div>
                      ))
                    )}
                  </div>
                </div>
              </DialogContent>
            </Dialog>
            {selectedContactId && (
              <Button type="button" variant="outline" size="sm" onClick={onContactClear}>
                <X className="h-4 w-4 mr-2" />
                {t('components.contact_selector.remove_contact')}
              </Button>
            )}
          </div>
        </div>
        
        {selectedContactId && (
          <div className="p-3 bg-primary/5 border border-primary/20 rounded-lg">
            <div className="text-sm font-medium text-primary mb-1">{t('components.contact_selector.selected_contact')}</div>
            <div className="text-sm">
              {contactName && <div><strong>{tForms('contact.name')}:</strong> {contactName}</div>}
              {contactEmail && <div><strong>{tForms('contact.email')}:</strong> {contactEmail}</div>}
              {contactPhone && <div><strong>{tForms('contact.phone')}:</strong> {contactPhone}</div>}
              {contactCompany && <div><strong>{tForms('contact.company')}:</strong> {contactCompany}</div>}
            </div>
          </div>
        )}

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="contact-name">{t('components.contact_selector.contact_person_label')}</Label>
            <Input
              id="contact-name"
              value={contactName}
              onChange={(e) => setContactName(e.target.value)}
              placeholder={t('components.contact_selector.contact_person_placeholder')}
              disabled={!!selectedContactId}
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="contact-email">{tForms('contact.email')}</Label>
            <Input
              id="contact-email"
              type="email"
              value={contactEmail}
              onChange={(e) => setContactEmail(e.target.value)}
              placeholder={tForms('placeholders.contact_email')}
              disabled={!!selectedContactId}
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="contact-phone">{tForms('contact.phone')}</Label>
            <Input
              id="contact-phone"
              value={contactPhone}
              onChange={(e) => setContactPhone(e.target.value)}
              placeholder={tForms('placeholders.contact_phone')}
              disabled={!!selectedContactId}
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="contact-company">{t('components.contact_selector.contact_company_label')}</Label>
            <Input
              id="contact-company"
              value={contactCompany}
              onChange={(e) => setContactCompany(e.target.value)}
              placeholder={t('components.contact_selector.contact_company_placeholder')}
              disabled={!!selectedContactId}
            />
          </div>
        </div>
      </CardContent>
    </Card>
  );
};