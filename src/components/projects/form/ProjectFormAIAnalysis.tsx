import { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { <PERSON><PERSON><PERSON>, Loader2 } from 'lucide-react';
import { toast } from '@/lib/toast';
import { useContacts } from '@/hooks/useContacts';
import { Contact, CreateContactData } from '@/types/freelance';
import { supabase } from '@/integrations/supabase/client';
import { useTranslation } from '@/hooks/useTranslation';

interface ProjectFormAIAnalysisProps {
  onAnalysisComplete: (analysis: any) => void;
  contacts: Contact[];
  onContactSelect: (contact: Contact) => void;
  onContactDataFill: (contactData: {
    name: string;
    email: string;
    phone: string;
    company: string;
  }) => void;
}

export const ProjectFormAIAnalysis = ({
  onAnalysisComplete,
  contacts,
  onContactSelect,
  onContactDataFill
}: ProjectFormAIAnalysisProps) => {
  const { t } = useTranslation('applications');
  const [projectText, setProjectText] = useState('');
  const [analyzingProject, setAnalyzingProject] = useState(false);
  const { createContact } = useContacts();

  const analyzeProject = async () => {
    if (!projectText.trim()) {
      toast.error(t('new_project.validation.required_fields_title'), t('components.ai_analysis.error_no_text'));
      return;
    }

    setAnalyzingProject(true);

    try {
      const { data, error } = await supabase.functions.invoke('analyze-project', {
        body: { projectText: projectText }
      });

      if (error) {
        throw new Error(error.message || 'Analysis error');
      }

      if (!data?.analysis) {
        throw new Error('No analysis data received');
      }

      const analysis = data.analysis;
      
      // Apply analyzed data to form
      onAnalysisComplete(analysis);

      // Handle contact information - populate contact fields and check for existing contact
      if (analysis.contact_person || analysis.contact_email || analysis.contact_phone) {
        // Fill contact fields with analyzed data
        const contactData = {
          name: analysis.contact_person || '',
          email: analysis.contact_email || '',
          phone: analysis.contact_phone || '',
          company: analysis.company_name || ''
        };
        
        onContactDataFill(contactData);

        // Check if a matching contact already exists
        const existingContact = contacts.find(contact => 
          (analysis.contact_email && contact.email === analysis.contact_email) ||
          (analysis.contact_person && analysis.company_name && 
           contact.name === analysis.contact_person && 
           contact.company === analysis.company_name)
        );

        if (existingContact) {
          // Show option to use existing contact
          // We'll use a simple info toast since Sonner doesn't have action support
          toast.info(t('components.ai_analysis.similar_contact_toast'), t('components.ai_analysis.similar_contact_message', { name: existingContact.name || existingContact.email }));
        } else {
          toast.success(t('components.ai_analysis.contact_data_recognized_toast'), t('components.ai_analysis.contact_data_recognized_message'));
        }
      }

      toast.success(t('components.ai_analysis.analysis_completed_toast'), t('components.ai_analysis.analysis_completed_message'));
    } catch (error: any) {
      toast.error('Analysis Error', error.message);
    } finally {
      setAnalyzingProject(false);
    }
  };

  return (
    <Card className="bg-gradient-to-r from-primary/5 to-secondary/5 border-primary/20">
      <CardHeader>
        <CardTitle className="flex items-center gap-2 text-primary">
          <Sparkles className="h-5 w-5" />
          {t('components.ai_analysis.title')}
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div>
          <Label htmlFor="projectText">{t('components.ai_analysis.project_text_label')}</Label>
          <Textarea
            id="projectText"
            placeholder={t('components.ai_analysis.project_text_placeholder')}
            value={projectText}
            onChange={(e) => setProjectText(e.target.value)}
            className="min-h-[120px] mt-2"
          />
        </div>
        <Button 
          type="button"
          onClick={analyzeProject}
          disabled={analyzingProject || !projectText.trim()}
          className="w-full"
        >
          {analyzingProject ? (
            <>
              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
              {t('components.ai_analysis.analyzing')}...
            </>
          ) : (
            <>
              <Sparkles className="h-4 w-4 mr-2" />
              {t('components.ai_analysis.analyze_button')}
            </>
          )}
        </Button>
      </CardContent>
    </Card>
  );
};