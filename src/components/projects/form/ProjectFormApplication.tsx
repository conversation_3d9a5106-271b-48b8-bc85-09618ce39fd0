import { useState } from 'react';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Wand2, Loader2 } from 'lucide-react';
import { ApplicationStatus, APPLICATION_STATUS_LABELS } from '@/types/applications';
import { toast } from '@/lib/toast';
import { supabase } from '@/integrations/supabase/client';
import { useTranslation } from '@/hooks/useTranslation';

interface ProjectFormApplicationProps {
  applicationDate: string;
  setApplicationDate: (value: string) => void;
  status: ApplicationStatus;
  setStatus: (value: ApplicationStatus) => void;
  applicationText: string;
  setApplicationText: (value: string) => void;
  notes: string;
  setNotes: (value: string) => void;
  hideStatusField?: boolean;
  projectDescription: string;
  companyName: string;
  projectName: string;
  requiredSkills: string[];
  contactName: string;
}

export const ProjectFormApplication = ({
  applicationDate,
  setApplicationDate,
  status,
  setStatus,
  applicationText,
  setApplicationText,
  notes,
  setNotes,
  hideStatusField = false,
  projectDescription,
  companyName,
  projectName,
  requiredSkills,
  contactName
}: ProjectFormApplicationProps) => {
  const { t } = useTranslation('applications');
  const [generatingApplication, setGeneratingApplication] = useState(false);

  const generateApplication = async () => {
    if (!projectDescription) {
      toast.error(t('new_project.validation.required_fields_title'), t('components.application.error_no_description'));
      return;
    }

    setGeneratingApplication(true);

    try {
      const { data, error } = await supabase.functions.invoke('generate-application', {
        body: {
          projectDescription,
          companyName,
          projectName,
          requiredSkills,
          contactPerson: contactName
        }
      });

      if (error) {
        console.error('Application generation error:', error);
        throw new Error(error.message || 'Generation error');
      }

      if (data?.applicationText) {
        setApplicationText(data.applicationText);
        toast.success(t('components.application.application_generated_toast'), t('components.application.application_generated_message'));
      } else {
        throw new Error(t('components.application.no_application_received'));
      }
    } catch (error: any) {
      toast.error('Generation Error', error.message);
    } finally {
      setGeneratingApplication(false);
    }
  };

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between gap-2">
          <CardTitle>{t('components.application.title')}</CardTitle>
          <Button 
            type="button"
            onClick={generateApplication}
            disabled={generatingApplication || !projectDescription}
            variant="outline"
            size="sm"
            className="flex-shrink-0"
          >
            {generatingApplication ? (
              <>
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                <span className="hidden sm:inline">{t('components.application.generating')}...</span>
                <span className="sm:hidden">...</span>
              </>
            ) : (
              <>
                <Wand2 className="h-4 w-4 mr-2" />
                <span className="hidden sm:inline">{t('components.application.generate_ai_application')}</span>
                <span className="sm:hidden">{t('components.application.generate_ai_application_short')}</span>
              </>
            )}
          </Button>
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="application-date">{t('components.application.application_date_label')}</Label>
            <Input
              id="application-date"
              type="date"
              value={applicationDate}
              onChange={(e) => setApplicationDate(e.target.value)}
            />
          </div>
          {!hideStatusField && (
            <div className="space-y-2">
              <Label htmlFor="status">{t('components.application.status_label')}</Label>
              <Select value={status} onValueChange={(value: ApplicationStatus) => setStatus(value)}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {Object.entries(APPLICATION_STATUS_LABELS).map(([value, label]) => (
                    <SelectItem key={value} value={value}>
                      {label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          )}
        </div>

        <div className="space-y-2">
          <Label htmlFor="application-text">{t('components.application.application_text_label')}</Label>
          <Textarea
            id="application-text"
            value={applicationText}
            onChange={(e) => setApplicationText(e.target.value)}
            placeholder={t('components.application.application_text_placeholder')}
            className="min-h-[200px]"
          />
        </div>

        <div className="space-y-2">
          <Label htmlFor="notes">{t('components.application.notes_label')}</Label>
          <Textarea
            id="notes"
            value={notes}
            onChange={(e) => setNotes(e.target.value)}
            placeholder={t('components.application.notes_placeholder')}
            rows={3}
          />
        </div>
      </CardContent>
    </Card>
  );
};