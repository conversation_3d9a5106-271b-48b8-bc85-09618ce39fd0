import { useState } from 'react';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Button } from '@/components/ui/button';
import { StatusNotesDialog } from './StatusNotesDialog';
import { Application, ApplicationStatus, APPLICATION_STATUS_LABELS, ApplicationUpdateHandler } from '@/types/applications';
import { ArrowRight } from 'lucide-react';
import { useApplicationStatusLabel } from '@/lib/translations';
import { useTranslation } from 'react-i18next';

interface StatusUpdateButtonProps {
  project: Application;
  onUpdateProject: ApplicationUpdateHandler;
  isUpdating?: boolean;
  className?: string;
}

export const StatusUpdateButton = ({ 
  project, 
  onUpdateProject, 
  isUpdating = false,
  className = ""
}: StatusUpdateButtonProps) => {
  const [selectedStatus, setSelectedStatus] = useState<ApplicationStatus | null>(null);
  const [showNotesDialog, setShowNotesDialog] = useState(false);
  const getStatusLabel = useApplicationStatusLabel();
  const { t } = useTranslation('common');

  const handleStatusChange = (newStatus: ApplicationStatus) => {
    if (newStatus === project.status) return;
    
    setSelectedStatus(newStatus);
    setShowNotesDialog(true);
  };

  const handleConfirmStatusChange = (notes?: string, notesDate?: string) => {
    if (!selectedStatus) return;

    const statusNotes = (notes || notesDate) ? { notes, notes_date: notesDate } : undefined;
    
    onUpdateProject(
      project.id,
      { status: selectedStatus },
      statusNotes
    );

    setSelectedStatus(null);
    setShowNotesDialog(false);
  };

  const handleCloseDialog = () => {
    setSelectedStatus(null);
    setShowNotesDialog(false);
  };

  const statusOptions = Object.keys(APPLICATION_STATUS_LABELS).map((value) => ({
    value: value as ApplicationStatus,
    label: getStatusLabel(value as ApplicationStatus)
  }));

  return (
    <>
      <div className={className}>
        <Select
          value={project.status}
          onValueChange={handleStatusChange}
          disabled={isUpdating}
        >
          <SelectTrigger 
            className="w-full"
            onClick={(e) => e.stopPropagation()}
          >
            <div className="flex items-center gap-2">
              <ArrowRight className="h-4 w-4" />
              <SelectValue placeholder={t('general.change_status', { defaultValue: 'Status ändern' })} />
            </div>
          </SelectTrigger>
          <SelectContent>
            {statusOptions.map((option) => (
              <SelectItem key={option.value} value={option.value}>
                {option.label}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      <StatusNotesDialog
        isOpen={showNotesDialog && !!selectedStatus}
        onClose={handleCloseDialog}
        onConfirm={handleConfirmStatusChange}
        currentStatus={project.status}
        newStatus={selectedStatus || project.status}
        isLoading={isUpdating}
        projectName={project.project_name}
        companyName={project.company_name}
      />
    </>
  );
};