import { 
  AlertDialog, 
  AlertDialogAction, 
  AlertDialogCancel, 
  AlertDialogContent, 
  AlertDialogDescription, 
  AlertDialogFooter, 
  AlertDialogHeader, 
  AlertDialogTitle 
} from '@/components/ui/alert-dialog';
import { Trash2, AlertTriangle } from 'lucide-react';

interface DeleteConfirmationDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  projectName: string;
  companyName: string;
  isDeleting?: boolean;
}

export const DeleteConfirmationDialog = ({
  isOpen,
  onClose,
  onConfirm,
  projectName,
  companyName,
  isDeleting = false
}: DeleteConfirmationDialogProps) => {
  return (
    <AlertDialog open={isOpen} onOpenChange={onClose}>
      <AlertDialogContent className="sm:max-w-[500px]">
        <AlertDialogHeader>
          <AlertDialogTitle className="flex items-center gap-2 text-destructive">
            <AlertTriangle className="h-5 w-5" />
            Projekt löschen?
          </AlertDialogTitle>
          <AlertDialogDescription className="space-y-3">
            <div className="text-sm">
              Sie sind dabei, das folgende Projekt <strong>unwiderruflich</strong> zu löschen:
            </div>
            
            <div className="bg-muted/50 rounded-lg p-3 border border-border/50">
              <div className="font-semibold text-foreground">{projectName}</div>
              <div className="text-sm text-muted-foreground">bei {companyName}</div>
            </div>
            
            <div className="text-sm text-muted-foreground">
              <strong className="text-destructive">Warnung:</strong> Diese Aktion kann nicht rückgängig gemacht werden. 
              Alle Projektdaten, Notizen und Aktivitäten werden permanent gelöscht.
            </div>
          </AlertDialogDescription>
        </AlertDialogHeader>
        
        <AlertDialogFooter className="gap-2">
          <AlertDialogCancel 
            onClick={onClose}
            disabled={isDeleting}
            className="w-full sm:w-auto"
          >
            Abbrechen
          </AlertDialogCancel>
          <AlertDialogAction
            onClick={onConfirm}
            disabled={isDeleting}
            className="w-full sm:w-auto bg-destructive hover:bg-destructive/90 text-destructive-foreground"
          >
            <Trash2 className="h-4 w-4 mr-2" />
            {isDeleting ? 'Wird gelöscht...' : 'Unwiderruflich löschen'}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
};