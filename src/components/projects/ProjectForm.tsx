import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { useApplications } from '@/hooks/useApplications';
import { useContacts } from '@/hooks/useContacts';
import { toast } from '@/lib/toast';
import { useTranslation } from '@/hooks/useTranslation';
import { CreateApplicationData, ApplicationStatus, ApplicationWithContact, Contact, CreateContactData } from '@/types/applications';
import { Loader2 } from 'lucide-react';

// Import sub-components
import { ProjectFormAIAnalysis } from './form/ProjectFormAIAnalysis';
import { ProjectFormBasicInfo } from './form/ProjectFormBasicInfo';
import { ProjectFormContactSelector } from './form/ProjectFormContactSelector';
import { ProjectFormWorkLocation } from './form/ProjectFormWorkLocation';
import { ProjectFormDetails } from './form/ProjectFormDetails';
import { ProjectFormApplication } from './form/ProjectFormApplication';
import { AIErrorBoundary } from '@/components/common/AIErrorBoundary';

interface ProjectFormProps {
  onClose: () => void;
  initialProject?: ApplicationWithContact;
  onSubmit?: (data: CreateApplicationData) => Promise<void>;
  hideStatusField?: boolean;
}

export const ProjectForm = ({ onClose, initialProject, onSubmit, hideStatusField = false }: ProjectFormProps) => {
  const { t } = useTranslation('applications');
  // Basic project info
  const [projectName, setProjectName] = useState('');
  const [companyName, setCompanyName] = useState('');
  const [selectedContactId, setSelectedContactId] = useState<string>('');
  const [projectDescription, setProjectDescription] = useState('');
  
  // Contact fields
  const [contactName, setContactName] = useState('');
  const [contactEmail, setContactEmail] = useState('');
  const [contactPhone, setContactPhone] = useState('');
  const [contactCompany, setContactCompany] = useState('');
  const [budgetRange, setBudgetRange] = useState('');
  const [projectStartDate, setProjectStartDate] = useState('');
  const [projectEndDate, setProjectEndDate] = useState('');
  const [applicationDate, setApplicationDate] = useState(new Date().toISOString().split('T')[0]);
  const [status, setStatus] = useState<ApplicationStatus>('not_applied');
  const [applicationText, setApplicationText] = useState('');
  const [notes, setNotes] = useState('');
  
  // New fields
  const [source, setSource] = useState('');
  const [listingUrl, setListingUrl] = useState('');
  const [workLocationType, setWorkLocationType] = useState<'remote' | 'onsite' | 'hybrid' | 'flexible' | ''>('');
  const [remotePercentage, setRemotePercentage] = useState('');
  const [workLocationNotes, setWorkLocationNotes] = useState('');
  const [requiredSkills, setRequiredSkills] = useState<string[]>([]);
  const [currentSkill, setCurrentSkill] = useState('');


  const { createApplication, isCreating } = useApplications();
  const { contacts, isLoading: contactsLoading, createContact } = useContacts();


  // Initialize form with initial project data - wait for contacts to be loaded
  useEffect(() => {
    if (initialProject && !contactsLoading) {
      
      setProjectName(initialProject.project_name || '');
      setCompanyName(initialProject.company_name || '');
      setSelectedContactId(initialProject.contact_id || '');
      setProjectDescription(initialProject.project_description || '');
      
      // Initialize contact fields from the linked contact or legacy fields
      if (initialProject.contact) {
        setContactName(initialProject.contact.name || '');
        setContactEmail(initialProject.contact.email || '');
        setContactPhone(initialProject.contact.phone || '');
        setContactCompany(initialProject.contact.company || '');
      } else {
        // Fallback for legacy data or empty
        setContactName('');
        setContactEmail('');
        setContactPhone('');
        setContactCompany('');
      }
      setBudgetRange(initialProject.budget_range || '');
      setProjectStartDate(initialProject.project_start_date || '');
      setProjectEndDate(initialProject.project_end_date || '');
      setApplicationDate(initialProject.application_date || new Date().toISOString().split('T')[0]);
      setStatus(initialProject.status || 'not_applied');
      setApplicationText(initialProject.application_text || '');
      setNotes(initialProject.notes || '');
      setSource(initialProject.source || '');
      setListingUrl(initialProject.listing_url || '');
      setWorkLocationType(initialProject.work_location_type || '');
      setRemotePercentage(initialProject.remote_percentage ? initialProject.remote_percentage.toString() : '');
      setWorkLocationNotes(initialProject.work_location_notes || '');
      setRequiredSkills(initialProject.required_skills || []);
    }
  }, [initialProject, contactsLoading, contacts]);

  const handleSelectExistingContact = (contact: Contact) => {
    setSelectedContactId(contact.id);
    setContactName(contact.name || '');
    setContactEmail(contact.email || '');
    setContactPhone(contact.phone || '');
    setContactCompany(contact.company || '');
  };

  const clearContactSelection = () => {
    setSelectedContactId('');
    setContactName('');
    setContactEmail('');
    setContactPhone('');
    setContactCompany('');
  };

  const handleAnalysisComplete = (analysis: any) => {
    // Apply analyzed data to form
    if (analysis.project_name) setProjectName(analysis.project_name);
    if (analysis.company_name) setCompanyName(analysis.company_name);
    if (analysis.budget_range) setBudgetRange(analysis.budget_range);
    if (analysis.project_start_date) setProjectStartDate(analysis.project_start_date);
    if (analysis.project_end_date) setProjectEndDate(analysis.project_end_date);
    if (analysis.work_location_type) setWorkLocationType(analysis.work_location_type);
    if (analysis.remote_percentage) setRemotePercentage(analysis.remote_percentage);
    if (analysis.required_skills && Array.isArray(analysis.required_skills)) setRequiredSkills(analysis.required_skills);
    if (analysis.project_description_summary) setProjectDescription(analysis.project_description_summary);
  };

  const handleContactDataFill = (contactData: {
    name: string;
    email: string;
    phone: string;
    company: string;
  }) => {
    setContactName(contactData.name);
    setContactEmail(contactData.email);
    setContactPhone(contactData.phone);
    setContactCompany(contactData.company);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!projectName || !companyName) {
      toast.error(t('new_project.validation.required_fields_title'), t('new_project.validation.required_fields_message'));
      return;
    }

    try {
      let contactId = selectedContactId;

      // Create contact if we have contact data but no existing contact selected
      if (!selectedContactId && (contactName || contactEmail)) {
        if (contactName || contactEmail) {
          const contactData: CreateContactData = {
            name: contactName || '',
            email: contactEmail || '',
            phone: contactPhone || '',
            company: contactCompany || companyName // Use project company as fallback
          };

          const newContact = await createContact(contactData);
          if (newContact) {
            contactId = newContact.id;
            toast.success(t('new_project.validation.contact_created_title'), t('new_project.validation.contact_created_message', { name: newContact.name || newContact.email }));
          }
        }
      }

      const applicationData: CreateApplicationData = {
        project_name: projectName,
        company_name: companyName,
        contact_id: contactId || undefined,
        project_description: projectDescription || undefined,
        budget_range: budgetRange || undefined,
        project_start_date: projectStartDate || undefined,
        project_end_date: projectEndDate || undefined,
        required_skills: requiredSkills.length > 0 ? requiredSkills : undefined,
        application_date: applicationDate || undefined,
        status: status,
        application_text: applicationText || undefined,
        notes: notes, // Allow empty string to delete notes
        source: source || undefined,
        listing_url: listingUrl || undefined,
        work_location_type: workLocationType || undefined,
        remote_percentage: remotePercentage ? parseInt(remotePercentage) : undefined,
        work_location_notes: workLocationNotes || undefined
      };

      if (onSubmit) {
        await onSubmit(applicationData);
      } else {
        await createApplication(applicationData);
      }
      onClose();
    } catch (error) {
      console.error('Error creating project:', error);
    }
  };

  return (
    <div className="space-y-6">
      {/* AI Analysis Section with Error Boundary */}
      <AIErrorBoundary>
        <ProjectFormAIAnalysis
          onAnalysisComplete={handleAnalysisComplete}
          contacts={contacts}
          onContactSelect={handleSelectExistingContact}
          onContactDataFill={handleContactDataFill}
        />
      </AIErrorBoundary>

      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Basic Project Info */}
        <ProjectFormBasicInfo
          projectName={projectName}
          setProjectName={setProjectName}
          companyName={companyName}
          setCompanyName={setCompanyName}
          source={source}
          setSource={setSource}
          listingUrl={listingUrl}
          setListingUrl={setListingUrl}
        />

        {/* Contact Selector */}
        <ProjectFormContactSelector
          selectedContactId={selectedContactId}
          contactName={contactName}
          setContactName={setContactName}
          contactEmail={contactEmail}
          setContactEmail={setContactEmail}
          contactPhone={contactPhone}
          setContactPhone={setContactPhone}
          contactCompany={contactCompany}
          setContactCompany={setContactCompany}
          contacts={contacts}
          contactsLoading={contactsLoading}
          onContactSelect={handleSelectExistingContact}
          onContactClear={clearContactSelection}
        />

        {/* Work Location */}
        <ProjectFormWorkLocation
          workLocationType={workLocationType}
          setWorkLocationType={setWorkLocationType}
          remotePercentage={remotePercentage}
          setRemotePercentage={setRemotePercentage}
          workLocationNotes={workLocationNotes}
          setWorkLocationNotes={setWorkLocationNotes}
        />

        {/* Project Details */}
        <ProjectFormDetails
          projectStartDate={projectStartDate}
          setProjectStartDate={setProjectStartDate}
          projectEndDate={projectEndDate}
          setProjectEndDate={setProjectEndDate}
          budgetRange={budgetRange}
          setBudgetRange={setBudgetRange}
          projectDescription={projectDescription}
          setProjectDescription={setProjectDescription}
          requiredSkills={requiredSkills}
          setRequiredSkills={setRequiredSkills}
          currentSkill={currentSkill}
          setCurrentSkill={setCurrentSkill}
        />

        {/* Application with Error Boundary */}
        <AIErrorBoundary>
          <ProjectFormApplication
            applicationDate={applicationDate}
            setApplicationDate={setApplicationDate}
            status={status}
            setStatus={setStatus}
            applicationText={applicationText}
            setApplicationText={setApplicationText}
            notes={notes}
            setNotes={setNotes}
            hideStatusField={hideStatusField}
            projectDescription={projectDescription}
            companyName={companyName}
            projectName={projectName}
            requiredSkills={requiredSkills}
            contactName={contactName}
          />
        </AIErrorBoundary>

        {/* Submit Buttons */}
        <div className="flex gap-4 justify-end pb-4">
          <Button type="button" variant="outline" onClick={onClose}>
            Abbrechen
          </Button>
          <Button type="submit" disabled={isCreating}>
            {isCreating ? (
              <>
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                Speichere...
              </>
            ) : (
              t('form.buttons.save_project')
            )}
          </Button>
        </div>
      </form>
    </div>
  );
};