import { useState } from 'react';
import { Di<PERSON>, DialogContent, DialogDescription, Di<PERSON>Footer, Di<PERSON>Header, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { supabase } from '@/integrations/supabase/client';
import { toast } from '@/lib/toast';
import { Calendar, FileText, MessageSquare, Sparkles, Loader2, RefreshCw, Eye } from 'lucide-react';
import { ApplicationStatus, APPLICATION_STATUS_LABELS } from '@/types/applications';
import { useApplicationStatusLabel } from '@/lib/translations';
import { useTranslation } from 'react-i18next';

interface StatusNotesDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: (notes?: string, notesDate?: string) => void;
  currentStatus: ApplicationStatus;
  newStatus: ApplicationStatus;
  isLoading?: boolean;
  projectName?: string;
  companyName?: string;
}

export const StatusNotesDialog = ({
  isOpen,
  onClose,
  onConfirm,
  currentStatus,
  newStatus,
  isLoading = false,
  projectName,
  companyName
}: StatusNotesDialogProps) => {
  const [notes, setNotes] = useState('');
  const [notesDate, setNotesDate] = useState('');
  const [useCustomDate, setUseCustomDate] = useState(false);
  const [interviewDate, setInterviewDate] = useState('');
  const [interviewTime, setInterviewTime] = useState('');
  const [isFormatting, setIsFormatting] = useState(false);
  const [showPreview, setShowPreview] = useState(false);
  const [formattedPreview, setFormattedPreview] = useState('');
  const getStatusLabel = useApplicationStatusLabel();
  const { t } = useTranslation('common');

  const handleConfirm = () => {
    const finalNotesDate = (() => {
      if (newStatus === 'interview_scheduled' && interviewDate) {
        return interviewTime ? `${interviewDate}T${interviewTime}` : interviewDate;
      }
      return useCustomDate && notesDate ? notesDate : undefined;
    })();
    
    onConfirm(notes.trim() || undefined, finalNotesDate);
    handleClose();
  };

  const handleClose = () => {
    setNotes('');
    setNotesDate('');
    setUseCustomDate(false);
    setShowPreview(false);
    setFormattedPreview('');
    setInterviewDate('');
    setInterviewTime('');
    onClose();
  };

  const handleFormatNotes = async () => {
    if (!notes.trim()) return;

    setIsFormatting(true);
    try {
      const { data, error } = await supabase.functions.invoke('format-note', {
        body: {
          raw_note: notes,
          context: {
            status_change: `${getStatusLabel(currentStatus)} → ${getStatusLabel(newStatus)}`,
            company_name: companyName,
            project_name: projectName
          }
        }
      });

      if (error) throw error;

      setFormattedPreview(data.formatted_note);
      setShowPreview(true);
      
      toast.success(t('status_change.notes_formatted'), t('status_change.check_preview'));

    } catch (error) {
      toast.error(t('status_change.formatting_failed'), t('status_change.try_again'));
    } finally {
      setIsFormatting(false);
    }
  };

  const handleUseFormatted = () => {
    setNotes(formattedPreview);
    setShowPreview(false);
    setFormattedPreview('');
  };

  const handleSkip = () => {
    onConfirm();
    handleClose();
  };

  const currentLabel = getStatusLabel(currentStatus);
  const newLabel = getStatusLabel(newStatus);

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent 
        className="sm:max-w-[600px] max-h-[90vh] flex flex-col"
        onClick={(e) => e.stopPropagation()}
      >
        <DialogHeader className="flex-shrink-0">
          <DialogTitle className="flex items-center gap-2">
            <MessageSquare className="h-5 w-5" />
            {t('status_change.title')}
          </DialogTitle>
          <DialogDescription>
            <div className="flex items-center gap-2 p-3 bg-muted/50 rounded-lg mt-2">
              <span className="text-sm">
                <strong>{t('status_change.status_change')}:</strong> {currentLabel} → {newLabel}
              </span>
            </div>
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4 flex-1 overflow-y-auto min-h-0">
          {/* Notes Textarea */}
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <Label htmlFor="status-notes" className="flex items-center gap-2">
                <FileText className="h-4 w-4" />
                {t('status_change.notes_optional')}
              </Label>
              {notes.trim() && (
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={handleFormatNotes}
                  disabled={isFormatting}
                  className="flex items-center gap-2"
                >
                  {isFormatting ? (
                    <Loader2 className="h-3 w-3 animate-spin" />
                  ) : (
                    <Sparkles className="h-3 w-3" />
                  )}
                  {isFormatting ? t('status_change.formatting') : t('status_change.ai_formatting')}
                </Button>
              )}
            </div>
            
            <Textarea
              id="status-notes"
              placeholder={t('status_change.notes_placeholder')}
              value={notes}
              onChange={(e) => setNotes(e.target.value)}
              className="min-h-[120px] resize-none"
            />
            <div className="text-xs text-muted-foreground">
              {t('status_change.characters', { count: notes.length })}
            </div>
          </div>

          {/* Formatted Preview */}
          {showPreview && (
            <div className="space-y-2">
              <Label className="flex items-center gap-2">
                <Eye className="h-4 w-4" />
                {t('status_change.ai_formatted_version')}
              </Label>
              <div className="bg-muted/50 border border-border rounded-lg p-3 min-h-[120px]">
                <div className="text-sm whitespace-pre-wrap leading-relaxed">
                  {formattedPreview}
                </div>
              </div>
              <div className="flex gap-2">
                <Button
                  type="button"
                  variant="default"
                  size="sm"
                  onClick={handleUseFormatted}
                  className="flex items-center gap-2"
                >
                  <RefreshCw className="h-3 w-3" />
                  {t('status_change.use_formatted')}
                </Button>
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={() => setShowPreview(false)}
                >
                  {t('status_change.close_preview')}
                </Button>
              </div>
            </div>
          )}

          {/* Interview Date/Time for interview_scheduled status */}
          {newStatus === 'interview_scheduled' && (
            <div className="space-y-3 p-4 bg-purple-50 dark:bg-purple-950/20 border border-purple-200 dark:border-purple-800 rounded-lg">
              <div className="flex items-center gap-2 text-purple-700 dark:text-purple-300">
                <Calendar className="h-4 w-4" />
                <Label className="font-medium">{t('status_change.interview_appointment')}</Label>
              </div>
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
                <div>
                  <Label htmlFor="interview-date" className="text-sm text-muted-foreground">
                    {t('status_change.date_required')}
                  </Label>
                  <Input
                    id="interview-date"
                    type="date"
                    value={interviewDate}
                    onChange={(e) => setInterviewDate(e.target.value)}
                    className="mt-1"
                    required
                  />
                </div>
                <div>
                  <Label htmlFor="interview-time" className="text-sm text-muted-foreground">
                    {t('status_change.time_required')}
                  </Label>
                  <Input
                    id="interview-time"
                    type="time"
                    value={interviewTime}
                    onChange={(e) => setInterviewTime(e.target.value)}
                    className="mt-1"
                    required
                  />
                </div>
              </div>
              <p className="text-xs text-muted-foreground">
                {t('status_change.calendar_info')}
              </p>
            </div>
          )}

          {/* Custom Date Option */}
          <div className="space-y-3">
            <div className="flex items-center space-x-2">
              <input
                type="checkbox"
                id="use-custom-date"
                checked={useCustomDate}
                onChange={(e) => setUseCustomDate(e.target.checked)}
                className="rounded border-gray-300"
              />
              <Label htmlFor="use-custom-date" className="flex items-center gap-2 text-sm">
                <Calendar className="h-4 w-4" />
                {t('status_change.use_custom_date')}
              </Label>
            </div>

            {useCustomDate && (
              <div className="ml-6">
                <Label htmlFor="notes-date" className="text-sm text-muted-foreground">
                  {t('status_change.note_date_time')}
                </Label>
                <Input
                  id="notes-date"
                  type="datetime-local"
                  value={notesDate}
                  onChange={(e) => setNotesDate(e.target.value)}
                  className="mt-1"
                />
              </div>
            )}
          </div>
        </div>

        <DialogFooter className="flex-col sm:flex-row gap-2 flex-shrink-0 border-t pt-4 mt-4">
          <Button
            variant="outline"
            onClick={handleSkip}
            disabled={isLoading}
            className="w-full sm:w-auto"
          >
            {t('status_change.change_without_notes')}
          </Button>
          <Button
            onClick={handleConfirm}
            disabled={isLoading || (newStatus === 'interview_scheduled' && (!interviewDate || !interviewTime))}
            className="w-full sm:w-auto"
          >
            {isLoading ? t('status_change.saving') : t('status_change.change_with_notes')}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};