import { use<PERSON><PERSON> } from '@/hooks/usePWA';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { RefreshCw, X } from 'lucide-react';

export function PWAUpdatePrompt() {
  const { needRefresh, updateServiceWorker, close } = usePWA();

  // Only show update prompt, not offline ready notification
  if (!needRefresh) {
    return null;
  }

  return (
    <Card className="fixed bottom-4 left-4 w-80 z-50 shadow-lg">
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div>
            <CardTitle className="text-sm font-semibold">
              App Update verfügbar
            </CardTitle>
            <CardDescription className="text-xs mt-1">
              Eine neue Version ist verfügbar. Aktualisieren?
            </CardDescription>
          </div>
          <Button
            variant="ghost"
            size="sm"
            className="h-6 w-6 p-0"
            onClick={close}
          >
            <X className="h-3 w-3" />
          </Button>
        </div>
      </CardHeader>
      <CardFooter className="pt-0">
        <Button
          onClick={() => updateServiceWorker(true)}
          size="sm"
          className="w-full h-8 text-xs"
        >
          <RefreshCw className="h-3 w-3 mr-1.5" />
          Aktualisieren
        </Button>
      </CardFooter>
    </Card>
  );
}