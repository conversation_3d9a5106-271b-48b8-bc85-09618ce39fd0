import { Component, ErrorInfo, ReactNode } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { AlertTriangle, RefreshCw } from 'lucide-react';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
  onError?: (error: Error, errorInfo: ErrorInfo) => void;
}

interface State {
  hasError: boolean;
  error: Error | null;
}

export class AIErrorBoundary extends Component<Props, State> {
  public state: State = {
    hasError: false,
    error: null,
  };

  public static getDerivedStateFromError(error: Error): State {
    return { hasError: true, error };
  }

  public componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    console.error('AI Operation Error:', error, errorInfo);
    
    // Call optional error handler
    if (this.props.onError) {
      this.props.onError(error, errorInfo);
    }
  }

  private handleRetry = () => {
    this.setState({ hasError: false, error: null });
  };

  public render() {
    if (this.state.hasError) {
      // Custom fallback UI or default error UI
      if (this.props.fallback) {
        return this.props.fallback;
      }

      return (
        <Card className="border-destructive/50 bg-destructive/5">
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-destructive">
              <AlertTriangle className="h-5 w-5" />
              KI-Operation fehlgeschlagen
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="text-sm text-muted-foreground">
              Bei der KI-Verarbeitung ist ein Fehler aufgetreten. Dies kann verschiedene Ursachen haben:
            </div>
            <ul className="text-sm text-muted-foreground list-disc list-inside space-y-1">
              <li>Temporäre Verbindungsprobleme</li>
              <li>KI-Service vorübergehend nicht verfügbar</li>
              <li>Ungültige oder zu komplexe Eingabedaten</li>
            </ul>
            
            {this.state.error && (
              <details className="text-xs text-muted-foreground mt-4">
                <summary className="cursor-pointer hover:text-foreground">
                  Technische Details anzeigen
                </summary>
                <pre className="mt-2 p-2 bg-muted rounded text-xs overflow-auto">
                  {this.state.error.message}
                </pre>
              </details>
            )}

            <div className="flex gap-2 pt-2">
              <Button 
                onClick={this.handleRetry}
                variant="outline"
                size="sm"
                className="flex items-center gap-2"
              >
                <RefreshCw className="h-4 w-4" />
                Erneut versuchen
              </Button>
              <Button 
                onClick={() => window.location.reload()}
                variant="ghost"
                size="sm"
              >
                Seite neu laden
              </Button>
            </div>
          </CardContent>
        </Card>
      );
    }

    return this.props.children;
  }
}

// Hook-Version für funktionale Komponenten
export const useAIErrorHandler = () => {
  const handleAIError = (error: Error, context?: string) => {
    console.error(`AI Error${context ? ` in ${context}` : ''}:`, error);
    
    // Hier könnte auch ein Error-Tracking-Service angebunden werden
    // z.B. Sentry, LogRocket, etc.
    
    return {
      title: 'KI-Operation fehlgeschlagen',
      description: error.message || 'Ein unerwarteter Fehler ist aufgetreten.',
      variant: 'destructive' as const
    };
  };

  return { handleAIError };
};