import React, { useState } from 'react';
import { <PERSON><PERSON>, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useProjects } from '@/hooks/useProjects';
import { useContacts } from '@/hooks/useContacts';
import { ProjectFormContactSelector } from '@/components/projects/form/ProjectFormContactSelector';
import type { ProjectInsert, ProjectStatus, ProjectPriority, ProjectType } from '@/types/projects';
import type { Contact } from '@/types/applications';

interface ProjectCreateDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export const ProjectCreateDialog: React.FC<ProjectCreateDialogProps> = ({
  open,
  onOpenChange,
}) => {
  const { createProject, isCreating } = useProjects();
  const { contacts, isLoading: contactsLoading } = useContacts();
  
  const [formData, setFormData] = useState<Partial<ProjectInsert>>({
    title: '',
    client_name: '',
    description: '',
    project_type: 'development',
    status: 'starting',
    priority: 'medium',
    hourly_rate: undefined,
    estimated_hours: undefined,
    start_date: undefined,
    planned_end_date: undefined,
    contact_id: undefined,
  });

  // Contact form fields
  const [contactName, setContactName] = useState('');
  const [contactEmail, setContactEmail] = useState('');
  const [contactPhone, setContactPhone] = useState('');
  const [contactCompany, setContactCompany] = useState('');
  const [selectedContactId, setSelectedContactId] = useState('');

  // Contact selection handlers
  const handleContactSelect = (contact: Contact) => {
    setSelectedContactId(contact.id);
    setContactName(contact.name || '');
    setContactEmail(contact.email || '');
    setContactPhone(contact.phone || '');
    setContactCompany(contact.company || '');
    setFormData(prev => ({ 
      ...prev, 
      contact_id: contact.id,
      client_name: contact.company || contact.name || ''
    }));
  };

  const handleContactClear = () => {
    setSelectedContactId('');
    setContactName('');
    setContactEmail('');
    setContactPhone('');
    setContactCompany('');
    setFormData(prev => ({ 
      ...prev, 
      contact_id: undefined 
    }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.title || !formData.client_name) {
      return;
    }

    const projectData: ProjectInsert = {
      title: formData.title,
      client_name: formData.client_name,
      description: formData.description || '',
      project_type: formData.project_type || 'development',
      status: formData.status || 'starting',
      priority: formData.priority || 'medium',
      hourly_rate: formData.hourly_rate,
      estimated_hours: formData.estimated_hours,
      start_date: formData.start_date,
      planned_end_date: formData.planned_end_date,
      contact_id: formData.contact_id,
    };

    createProject(projectData);
    
    // Reset form and close dialog
    setFormData({
      title: '',
      client_name: '',
      description: '',
      project_type: 'development',
      status: 'starting',
      priority: 'medium',
      hourly_rate: undefined,
      estimated_hours: undefined,
      start_date: undefined,
      planned_end_date: undefined,
      contact_id: undefined,
    });
    handleContactClear();
    onOpenChange(false);
  };

  const handleInputChange = (field: keyof ProjectInsert, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Neues aktives Projekt erstellen</DialogTitle>
          <DialogDescription>
            Erstelle ein neues Projekt für dein aktives Projektmanagement.
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Basic Info */}
          <div className="grid gap-4 md:grid-cols-2">
            <div className="space-y-2">
              <Label htmlFor="title">Projekttitel *</Label>
              <Input
                id="title"
                value={formData.title || ''}
                onChange={(e) => handleInputChange('title', e.target.value)}
                placeholder="Z.B. Website Redesign"
                required
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="client_name">Client Name *</Label>
              <Input
                id="client_name"
                value={formData.client_name || ''}
                onChange={(e) => handleInputChange('client_name', e.target.value)}
                placeholder="Z.B. Musterfirma GmbH"
                required
              />
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="description">Beschreibung</Label>
            <Textarea
              id="description"
              value={formData.description || ''}
              onChange={(e) => handleInputChange('description', e.target.value)}
              placeholder="Optionale Projektbeschreibung..."
              rows={3}
            />
          </div>

          {/* Contact Information */}
          <ProjectFormContactSelector
            selectedContactId={selectedContactId}
            contactName={contactName}
            setContactName={setContactName}
            contactEmail={contactEmail}
            setContactEmail={setContactEmail}
            contactPhone={contactPhone}
            setContactPhone={setContactPhone}
            contactCompany={contactCompany}
            setContactCompany={setContactCompany}
            contacts={contacts}
            contactsLoading={contactsLoading}
            onContactSelect={handleContactSelect}
            onContactClear={handleContactClear}
          />

          {/* Project Settings */}
          <div className="grid gap-4 md:grid-cols-3">
            <div className="space-y-2">
              <Label htmlFor="project_type">Projekttyp</Label>
              <Select 
                value={formData.project_type} 
                onValueChange={(value) => handleInputChange('project_type', value as ProjectType)}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="development">Development</SelectItem>
                  <SelectItem value="design">Design</SelectItem>
                  <SelectItem value="consulting">Consulting</SelectItem>
                  <SelectItem value="marketing">Marketing</SelectItem>
                  <SelectItem value="other">Other</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <Label htmlFor="status">Status</Label>
              <Select 
                value={formData.status} 
                onValueChange={(value) => handleInputChange('status', value as ProjectStatus)}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="starting">Startend</SelectItem>
                  <SelectItem value="in_progress">In Bearbeitung</SelectItem>
                  <SelectItem value="on_hold">Pausiert</SelectItem>
                  <SelectItem value="completing">Abschließend</SelectItem>
                  <SelectItem value="completed">Abgeschlossen</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <Label htmlFor="priority">Priorität</Label>
              <Select 
                value={formData.priority} 
                onValueChange={(value) => handleInputChange('priority', value as ProjectPriority)}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="low">Niedrig</SelectItem>
                  <SelectItem value="medium">Mittel</SelectItem>
                  <SelectItem value="high">Hoch</SelectItem>
                  <SelectItem value="urgent">Dringend</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Financial */}
          <div className="grid gap-4 md:grid-cols-2">
            <div className="space-y-2">
              <Label htmlFor="hourly_rate">Stundensatz (€)</Label>
              <Input
                id="hourly_rate"
                type="number"
                step="0.01"
                min="0"
                value={formData.hourly_rate || ''}
                onChange={(e) => handleInputChange('hourly_rate', e.target.value ? parseFloat(e.target.value) : undefined)}
                placeholder="0.00"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="estimated_hours">Geschätzte Stunden</Label>
              <Input
                id="estimated_hours"
                type="number"
                min="0"
                value={formData.estimated_hours || ''}
                onChange={(e) => handleInputChange('estimated_hours', e.target.value ? parseInt(e.target.value) : undefined)}
                placeholder="z.B. 40"
              />
            </div>
          </div>

          {/* Timeline */}
          <div className="grid gap-4 md:grid-cols-2">
            <div className="space-y-2">
              <Label htmlFor="start_date">Startdatum</Label>
              <Input
                id="start_date"
                type="date"
                value={formData.start_date || ''}
                onChange={(e) => handleInputChange('start_date', e.target.value || undefined)}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="planned_end_date">Geplantes Enddatum</Label>
              <Input
                id="planned_end_date"
                type="date"
                value={formData.planned_end_date || ''}
                onChange={(e) => handleInputChange('planned_end_date', e.target.value || undefined)}
              />
            </div>
          </div>

          {/* Actions */}
          <div className="flex justify-end gap-3 pt-4">
            <Button
              type="button"
              variant="outline"
              onClick={() => onOpenChange(false)}
              disabled={isCreating}
            >
              Abbrechen
            </Button>
            <Button type="submit" disabled={isCreating || !formData.title || !formData.client_name}>
              {isCreating ? 'Erstelle...' : 'Projekt erstellen'}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
};