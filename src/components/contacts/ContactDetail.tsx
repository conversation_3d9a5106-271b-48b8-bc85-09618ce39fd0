import { useState, useEffect } from 'react';
import { Contact, UpdateContactData, ContactAnalytics, ApplicationWithContact, APPLICATION_STATUS_LABELS, Application } from '@/types/applications';
import { useContacts } from '@/hooks/useContacts';
import { useApplications } from '@/hooks/useApplications';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { ContactForm } from './ContactForm';
import { CommunicationList, CommunicationSummary } from './communications';
import { CommunicationQuickActions } from './CommunicationQuickActions';
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from '@/components/ui/alert-dialog';
import { 
  User, 
  Mail, 
  Phone, 
  Building2, 
  Calendar,
  TrendingUp,
  FileText,
  Edit,
  Trash2,
  ExternalLink,
  MessageSquare,
  Plus
} from 'lucide-react';
import { format } from 'date-fns';
import { de } from 'date-fns/locale';

interface ContactDetailProps {
  contact: Contact;
  onUpdate: (data: UpdateContactData) => Promise<void>;
  onDelete: (contactId: string) => Promise<void>;
  onClose: () => void;
}

export const ContactDetail = ({ 
  contact, 
  onUpdate, 
  onDelete, 
  onClose 
}: ContactDetailProps) => {
  const [isEditing, setIsEditing] = useState(false);
  const [analytics, setAnalytics] = useState<ContactAnalytics | null>(null);
  const [isLoadingAnalytics, setIsLoadingAnalytics] = useState(true);
  const [initialLoading, setInitialLoading] = useState(true);
  
  const { getContactAnalytics } = useContacts();
  const { data: allProjects = [], isLoading: projectsLoading } = useApplications();
  const getProjectsForContact = (contactId: string) => {
    return allProjects.filter(project => project.contact_id === contactId);
  };
  
  const projects = getProjectsForContact(contact.id);

  // Load analytics data
  useEffect(() => {
    const loadAnalytics = async () => {
      setIsLoadingAnalytics(true);
      const data = await getContactAnalytics(contact.id);
      setAnalytics(data);
      setIsLoadingAnalytics(false);
    };
    
    loadAnalytics();
  }, [contact.id, getContactAnalytics]);

  // Initial loading management to prevent flicker
  useEffect(() => {
    const timer = setTimeout(() => {
      setInitialLoading(false);
    }, 300); // Show spinner for minimum 300ms to prevent flicker

    return () => clearTimeout(timer);
  }, []);

  // Reset initial loading when contact changes
  useEffect(() => {
    setInitialLoading(true);
    setAnalytics(null);
  }, [contact.id]);

  const handleUpdate = async (updateData: UpdateContactData) => {
    await onUpdate(updateData);
    setIsEditing(false);
  };

  const handleDelete = async () => {
    await onDelete(contact.id);
    onClose();
  };

  const successRate = contact.total_projects > 0 
    ? Math.round((contact.successful_projects / contact.total_projects) * 100)
    : 0;

  // Show loading spinner during initial load or when essential data is missing
  const isLoading = initialLoading || (isLoadingAnalytics && !analytics);

  if (isEditing) {
    return (
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-semibold">Kontakt bearbeiten</h3>
          <Button
            variant="outline"
            size="sm"
            onClick={() => setIsEditing(false)}
          >
            Abbrechen
          </Button>
        </div>
        <ContactForm
          contact={contact}
          onSubmit={handleUpdate}
          onCancel={() => setIsEditing(false)}
        />
      </div>
    );
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center p-12">
        <div className="text-center space-y-4">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
          <div className="text-sm text-muted-foreground">
            Lade Kontaktdaten und Projekte...
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-start justify-between">
        <div className="flex items-center gap-4">
          <div className="w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center">
            <User className="h-6 w-6 text-primary" />
          </div>
          <div>
            <h2 className="text-xl font-bold">
              {contact.name || 'Unbenannter Kontakt'}
            </h2>
            <p className="text-muted-foreground">
              {contact.company && (
                <span className="flex items-center gap-1">
                  <Building2 className="h-4 w-4" />
                  {contact.company}
                </span>
              )}
            </p>
          </div>
        </div>
        
        <div className="flex gap-2 flex-wrap">
          {/* Communication Quick Actions */}
          <CommunicationQuickActions 
            contact={contact}
            compact={true}
            onSuccess={() => {
              // Refresh any data if needed
              console.log('Communication created successfully');
            }}
          />
          
          <Button variant="outline" size="sm" onClick={() => setIsEditing(true)}>
            <Edit className="h-4 w-4 mr-2" />
            Bearbeiten
          </Button>
          
          <AlertDialog>
            <AlertDialogTrigger asChild>
              <Button variant="outline" size="sm" className="text-red-600 hover:text-red-700">
                <Trash2 className="h-4 w-4 mr-2" />
                Löschen
              </Button>
            </AlertDialogTrigger>
            <AlertDialogContent>
              <AlertDialogHeader>
                <AlertDialogTitle>Kontakt löschen?</AlertDialogTitle>
                <AlertDialogDescription>
                  Sind Sie sicher, dass Sie diesen Kontakt löschen möchten? 
                  {contact.total_projects > 0 && (
                    <>
                      <br />
                      <strong>Achtung:</strong> Dieser Kontakt hat {contact.total_projects} zugeordnete Projekte. 
                      Diese werden vom Kontakt getrennt, aber nicht gelöscht.
                    </>
                  )}
                </AlertDialogDescription>
              </AlertDialogHeader>
              <AlertDialogFooter>
                <AlertDialogCancel>Abbrechen</AlertDialogCancel>
                <AlertDialogAction onClick={handleDelete} className="bg-red-600 hover:bg-red-700">
                  Löschen
                </AlertDialogAction>
              </AlertDialogFooter>
            </AlertDialogContent>
          </AlertDialog>
        </div>
      </div>

      {/* Contact Information Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2 text-sm text-muted-foreground mb-2">
              <FileText className="h-4 w-4" />
              Projekte Gesamt
            </div>
            <div className="text-2xl font-bold">{contact.total_projects}</div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2 text-sm text-muted-foreground mb-2">
              <TrendingUp className="h-4 w-4" />
              Erfolgsquote
            </div>
            <div className="text-2xl font-bold text-green-600">{successRate}%</div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2 text-sm text-muted-foreground mb-2">
              <Calendar className="h-4 w-4" />
              Letzter Kontakt
            </div>
            <div className="text-sm font-medium">
              {analytics?.recent_applications[0]?.updated_at 
                ? format(new Date(analytics.recent_applications[0].updated_at), 'dd.MM.yyyy', { locale: de })
                : 'Nie'
              }
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Contact Details */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <User className="h-5 w-5" />
            Kontaktinformationen
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Contact Information */}
            <div className="space-y-4">
              {contact.email && (
                <div className="flex items-center gap-3 p-3 bg-muted/50 rounded-lg">
                  <div className="w-10 h-10 bg-blue-100 dark:bg-blue-900/20 rounded-full flex items-center justify-center flex-shrink-0">
                    <Mail className="h-5 w-5 text-blue-600 dark:text-blue-400" />
                  </div>
                  <div className="flex-1 min-w-0">
                    <div className="text-xs text-muted-foreground uppercase tracking-wide font-medium mb-1">
                      Email
                    </div>
                    <div className="font-medium text-sm truncate">
                      {contact.email}
                    </div>
                  </div>
                  <Button variant="ghost" size="sm" asChild className="flex-shrink-0">
                    <a href={`mailto:${contact.email}`} className="text-blue-600 hover:text-blue-700">
                      <ExternalLink className="h-4 w-4" />
                    </a>
                  </Button>
                </div>
              )}
              
              {contact.phone && (
                <div className="flex items-center gap-3 p-3 bg-muted/50 rounded-lg">
                  <div className="w-10 h-10 bg-green-100 dark:bg-green-900/20 rounded-full flex items-center justify-center flex-shrink-0">
                    <Phone className="h-5 w-5 text-green-600 dark:text-green-400" />
                  </div>
                  <div className="flex-1 min-w-0">
                    <div className="text-xs text-muted-foreground uppercase tracking-wide font-medium mb-1">
                      Telefon
                    </div>
                    <div className="font-medium text-sm">
                      {contact.phone}
                    </div>
                  </div>
                  <Button variant="ghost" size="sm" asChild className="flex-shrink-0">
                    <a href={`tel:${contact.phone}`} className="text-green-600 hover:text-green-700">
                      <ExternalLink className="h-4 w-4" />
                    </a>
                  </Button>
                </div>
              )}
              
              {contact.company && (
                <div className="flex items-center gap-3 p-3 bg-muted/50 rounded-lg">
                  <div className="w-10 h-10 bg-purple-100 dark:bg-purple-900/20 rounded-full flex items-center justify-center flex-shrink-0">
                    <Building2 className="h-5 w-5 text-purple-600 dark:text-purple-400" />
                  </div>
                  <div className="flex-1 min-w-0">
                    <div className="text-xs text-muted-foreground uppercase tracking-wide font-medium mb-1">
                      Unternehmen
                    </div>
                    <div className="font-medium text-sm">
                      {contact.company}
                    </div>
                  </div>
                </div>
              )}
            </div>

            {/* Timestamps */}
            <div className="space-y-4">
              <div className="flex items-center gap-3 p-3 bg-muted/50 rounded-lg">
                <div className="w-10 h-10 bg-orange-100 dark:bg-orange-900/20 rounded-full flex items-center justify-center flex-shrink-0">
                  <Calendar className="h-5 w-5 text-orange-600 dark:text-orange-400" />
                </div>
                <div className="flex-1 min-w-0">
                  <div className="text-xs text-muted-foreground uppercase tracking-wide font-medium mb-1">
                    Erstellt
                  </div>
                  <div className="font-medium text-sm">
                    {format(new Date(contact.created_at), 'dd.MM.yyyy HH:mm', { locale: de })}
                  </div>
                </div>
              </div>

              <div className="flex items-center gap-3 p-3 bg-muted/50 rounded-lg">
                <div className="w-10 h-10 bg-teal-100 dark:bg-teal-900/20 rounded-full flex items-center justify-center flex-shrink-0">
                  <Calendar className="h-5 w-5 text-teal-600 dark:text-teal-400" />
                </div>
                <div className="flex-1 min-w-0">
                  <div className="text-xs text-muted-foreground uppercase tracking-wide font-medium mb-1">
                    Aktualisiert
                  </div>
                  <div className="font-medium text-sm">
                    {format(new Date(contact.updated_at), 'dd.MM.yyyy HH:mm', { locale: de })}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Notes Section */}
      {contact.notes && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <MessageSquare className="h-5 w-5" />
              Notizen
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="whitespace-pre-wrap text-sm leading-relaxed">
              {contact.notes}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Main Content Tabs */}
      <Tabs defaultValue="overview" className="space-y-4">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview">Übersicht</TabsTrigger>
          <TabsTrigger value="communications">Kommunikation</TabsTrigger>
          <TabsTrigger value="projects">Projekte ({projects.length})</TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
        </TabsList>
        
        <TabsContent value="overview" className="space-y-4">
          {/* Communication Summary */}
          <CommunicationSummary
            contactId={contact.id}
            contactName={contact.name || contact.company}
            onViewAll={() => {
              // Switch to communications tab
              const tabsList = document.querySelector('[role="tablist"]');
              const communicationsTab = tabsList?.querySelector('[value="communications"]') as HTMLElement;
              communicationsTab?.click();
            }}
          />
          
          {/* Recent Projects Preview */}
          {projects.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center justify-between">
                  <span className="flex items-center gap-2">
                    <FileText className="h-5 w-5" />
                    Aktuelle Projekte
                  </span>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => {
                      const tabsList = document.querySelector('[role="tablist"]');
                      const projectsTab = tabsList?.querySelector('[value="projects"]') as HTMLElement;
                      projectsTab?.click();
                    }}
                  >
                    Alle anzeigen
                  </Button>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <ProjectList projects={projects.slice(0, 3)} />
                {projects.length > 3 && (
                  <p className="text-sm text-muted-foreground text-center mt-4">
                    ... und {projects.length - 3} weitere Projekte
                  </p>
                )}
              </CardContent>
            </Card>
          )}
        </TabsContent>
        
        <TabsContent value="communications" className="space-y-4">
          <CommunicationList
            contactId={contact.id}
            contactName={contact.name || contact.company}
            compact={false}
            maxHeight="70vh"
            showAddButton={true}
          />
        </TabsContent>
        
        <TabsContent value="projects" className="space-y-4">
          <Tabs defaultValue="all" className="space-y-4">
            <TabsList>
              <TabsTrigger value="all">Alle Projekte ({projects.length})</TabsTrigger>
              <TabsTrigger value="successful">
                Erfolgreich ({projects.filter(p => p.status === 'offer_received' || p.status === 'project_completed').length})
              </TabsTrigger>
              <TabsTrigger value="active">
                Aktiv ({projects.filter(p => !['rejected', 'project_completed'].includes(p.status)).length})
              </TabsTrigger>
            </TabsList>
        
        <TabsContent value="all" className="space-y-4">
          <ProjectList projects={projects} />
        </TabsContent>
        
        <TabsContent value="successful" className="space-y-4">
          <ProjectList 
            projects={projects.filter(p => p.status === 'offer_received' || p.status === 'project_completed')} 
          />
        </TabsContent>
        
            <TabsContent value="all" className="space-y-4">
              <ProjectList projects={projects} />
            </TabsContent>
            
            <TabsContent value="successful" className="space-y-4">
              <ProjectList 
                projects={projects.filter(p => p.status === 'offer_received' || p.status === 'project_completed')} 
              />
            </TabsContent>
            
            <TabsContent value="active" className="space-y-4">
              <ProjectList 
                projects={projects.filter(p => !['rejected', 'project_completed'].includes(p.status))} 
              />
            </TabsContent>
          </Tabs>
        </TabsContent>
        
        <TabsContent value="analytics" className="space-y-4">
          {/* Enhanced Analytics with Communication Data */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {/* Project Statistics */}
            <Card>
              <CardHeader className="pb-4">
                <CardTitle className="flex items-center gap-2 text-base">
                  <TrendingUp className="h-4 w-4" />
                  Projekt-Performance
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>Erfolgsquote</span>
                    <span className="font-medium">{successRate}%</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span>Gesamt Projekte</span>
                    <span className="font-medium">{contact.total_projects}</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span>Erfolgreich</span>
                    <span className="font-medium text-green-600">{contact.successful_projects}</span>
                  </div>
                </div>
              </CardContent>
            </Card>
            
            {/* Communication Statistics */}
            <Card>
              <CardHeader className="pb-4">
                <CardTitle className="flex items-center gap-2 text-base">
                  <MessageSquare className="h-4 w-4" />
                  Kommunikation
                </CardTitle>
              </CardHeader>
              <CardContent>
                <CommunicationSummary
                  contactId={contact.id}
                  contactName={contact.name || contact.company}
                  className="border-0 shadow-none p-0"
                />
              </CardContent>
            </Card>
            
            {/* Timeline */}
            <Card>
              <CardHeader className="pb-4">
                <CardTitle className="flex items-center gap-2 text-base">
                  <Calendar className="h-4 w-4" />
                  Aktivitäts-Timeline
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="text-xs text-muted-foreground space-y-2">
                  <div className="flex justify-between">
                    <span>Kontakt erstellt</span>
                    <span>{format(new Date(contact.created_at), 'dd.MM.yyyy', { locale: de })}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Letzte Aktualisierung</span>
                    <span>{format(new Date(contact.updated_at), 'dd.MM.yyyy', { locale: de })}</span>
                  </div>
                  {analytics?.recent_applications[0] && (
                    <div className="flex justify-between">
                      <span>Letztes Projekt</span>
                      <span>{format(new Date(analytics.recent_applications[0].updated_at), 'dd.MM.yyyy', { locale: de })}</span>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
};

// Project List Component
interface ProjectListProps {
  projects: Application[];
}

const ProjectList = ({ projects }: ProjectListProps) => {
  if (projects.length === 0) {
    return (
      <Card>
        <CardContent className="p-8 text-center">
          <FileText className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
          <p className="text-muted-foreground">Keine Projekte in dieser Kategorie</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-3">
      {projects.map((project) => (
        <Card key={project.id} className="hover:shadow-sm transition-shadow">
          <CardContent className="p-4">
            <div className="flex items-start justify-between">
              <div className="flex-1 min-w-0">
                <h4 className="font-medium truncate mb-1">{project.project_name}</h4>
                <p className="text-sm text-muted-foreground truncate mb-2">{project.company_name}</p>
                
                <div className="flex items-center gap-2">
                  <Badge variant="outline" className="text-xs">
                    {APPLICATION_STATUS_LABELS[project.status] || project.status}
                  </Badge>
                  {project.budget_range && (
                    <span className="text-xs text-muted-foreground">{project.budget_range}</span>
                  )}
                </div>
              </div>
              
              <div className="text-xs text-muted-foreground">
                {format(new Date(project.created_at), 'dd.MM.yy', { locale: de })}
              </div>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
};