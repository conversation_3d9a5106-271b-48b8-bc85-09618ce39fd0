import { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { format } from 'date-fns';
import { de } from 'date-fns/locale';
import { Calendar, Clock, Loader2, Sparkles } from 'lucide-react';

import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Calendar as CalendarComponent } from '@/components/ui/calendar';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { CommunicationTypeSelector } from './CommunicationTypeSelector';

import { useCreateCommunication, useUpdateCommunication } from '@/hooks/useContactCommunications';
import { useApplications } from '@/hooks/useApplications';
import { useBriefSummary } from '@/hooks/useCommunicationAI';
import { CommunicationType, CreateCommunicationData, COMMUNICATION_TYPES } from '@/types/communications';
import { AISummaryService, RateLimitError } from '@/services/aiSummaryService';
import { ValidationError, SecurityError } from '@/lib/validation/communicationValidation';
import { toast } from 'sonner';

const formSchema = z.object({
  communication_type: z.enum(['call', 'email', 'meeting', 'message', 'whatsapp', 'linkedin', 'sms', 'other']),
  subject: z.string().optional(),
  notes: z.string().min(1, 'Notizen sind erforderlich'),
  communication_date: z.date(),
  communication_time: z.string(),
  duration_minutes: z.number().min(0).optional(),
  project_id: z.string().optional(),
  is_project_related: z.boolean().default(false),
});

type FormData = z.infer<typeof formSchema>;

interface FormInitialData extends Partial<CreateCommunicationData> {
  communication_time?: string;
}

interface CommunicationFormProps {
  contactId: string;
  contactName?: string;
  onClose: () => void;
  onSuccess?: () => void;
  initialData?: FormInitialData;
  communicationId?: string;
}

export const CommunicationForm = ({ 
  contactId, 
  contactName, 
  onClose, 
  onSuccess,
  initialData,
  communicationId 
}: CommunicationFormProps) => {
  const [showProjectSelector, setShowProjectSelector] = useState(false);
  const [isGeneratingSummary, setIsGeneratingSummary] = useState(false);
  
  const createCommunication = useCreateCommunication();
  const updateCommunication = useUpdateCommunication();
  const generateBriefSummary = useBriefSummary();
  const { data: applications } = useApplications();

  // Filter applications for this contact
  const contactApplications = applications?.filter(app => app.contact_id === contactId) || [];

  const form = useForm<FormData>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      communication_type: initialData?.communication_type || 'call',
      subject: initialData?.subject || '',
      notes: initialData?.notes || '',
      communication_date: initialData?.communication_date ? new Date(initialData.communication_date) : new Date(),
      communication_time: initialData?.communication_time || format(new Date(), 'HH:mm'),
      duration_minutes: initialData?.duration_minutes || undefined,
      project_id: initialData?.project_id || '',
      is_project_related: initialData?.is_project_related || false,
    },
  });

  const communicationType = form.watch('communication_type');
  const isProjectRelated = form.watch('is_project_related');
  const notes = form.watch('notes');

  // Show/hide project selector based on project relation
  useEffect(() => {
    setShowProjectSelector(isProjectRelated);
    if (!isProjectRelated) {
      form.setValue('project_id', '');
    }
  }, [isProjectRelated, form]);

  const handleGenerateSummary = async () => {
    try {
      const currentNotes = notes.trim();
      
      // Validate notes
      const validation = AISummaryService.validateNotesForAI(currentNotes);
      if (!validation.isValid) {
        toast.error(`AI-Zusammenfassung nicht möglich: ${validation.reason}`);
        return;
      }

      setIsGeneratingSummary(true);
      
      // Show progress toast
      const progressToast = toast.loading('AI-Zusammenfassung wird erstellt...', {
        description: 'Dies kann bis zu 30 Sekunden dauern'
      });

      // Get contact info for context
      const subject = form.getValues('subject');
      const communicationType = form.getValues('communication_type');
      
      // Find project context if project-related
      const isProjectRelated = form.getValues('is_project_related');
      const projectId = form.getValues('project_id');
      let projectContext = undefined;
      
      if (isProjectRelated && projectId && contactApplications.length > 0) {
        const selectedProject = contactApplications.find(app => app.id === projectId);
        if (selectedProject) {
          projectContext = {
            projectName: selectedProject.project_name,
            companyName: selectedProject.company_name,
            status: selectedProject.status
          };
        }
      }

      // Generate AI summary
      const summary = await AISummaryService.generateSummary({
        notes: currentNotes,
        communicationType,
        subject,
        contactName: contactName,
        projectContext
      });

      // Dismiss progress toast
      toast.dismiss(progressToast);

      if (summary) {
        // Replace the notes field with the AI summary
        form.setValue('notes', summary);
        toast.success('AI-Zusammenfassung erfolgreich erstellt!');
      } else {
        toast.error('Fehler beim Erstellen der AI-Zusammenfassung');
      }

    } catch (error) {
      console.error('Error generating AI summary:', error);
      
      // Handle different error types with specific user messages
      if (error instanceof RateLimitError) {
        const resetDate = new Date(error.resetTime);
        const resetTime = resetDate.toLocaleTimeString();
        toast.error(`Rate Limit erreicht: ${error.message}`, {
          description: `Versuchen Sie es wieder um ${resetTime}. Verbleibende Anfragen: ${error.remaining}`
        });
      } else if (error instanceof ValidationError) {
        toast.error(`Validierungsfehler: ${error.message}`);
      } else if (error instanceof SecurityError) {
        toast.error('Sicherheitsfehler: Eingabe enthält nicht erlaubte Inhalte');
      } else {
        toast.error('Fehler bei der AI-Verarbeitung');
      }
    } finally {
      setIsGeneratingSummary(false);
    }
  };

  const onSubmit = async (data: FormData) => {
    try {
      // Combine date and time
      const communicationDateTime = new Date(data.communication_date);
      const [hours, minutes] = data.communication_time.split(':').map(Number);
      communicationDateTime.setHours(hours, minutes);

      const communicationData: CreateCommunicationData = {
        contact_id: contactId,
        communication_type: data.communication_type,
        subject: data.subject?.trim() || undefined,
        notes: data.notes.trim(),
        communication_date: communicationDateTime.toISOString(),
        duration_minutes: data.duration_minutes || undefined,
        project_id: data.project_id || undefined,
        is_project_related: data.is_project_related,
      };

      let result;
      if (communicationId) {
        // Update existing communication
        result = await updateCommunication.mutateAsync({
          id: communicationId,
          ...communicationData
        });
      } else {
        // Create new communication
        result = await createCommunication.mutateAsync(communicationData);
      }
      
      if (result) {
        // Auto-generate brief summary disabled - user can use AI button instead

        onSuccess?.();
        onClose();
      }
    } catch (error) {
      console.error(`Error ${communicationId ? 'updating' : 'creating'} communication:`, error);
    }
  };

  const typeInfo = COMMUNICATION_TYPES[communicationType];
  const supportsDuration = typeInfo?.supports_duration;
  const requiresSubject = typeInfo?.requires_subject;

  return (
    <div className="w-full">
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
          {/* Basic Information */}
          <div className="space-y-4">
            <FormField
              control={form.control}
              name="communication_type"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Art der Kommunikation</FormLabel>
                  <FormControl>
                    <CommunicationTypeSelector
                      value={field.value}
                      onValueChange={field.onChange}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Subject (conditional) */}
            {requiresSubject && (
              <FormField
                control={form.control}
                name="subject"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Betreff {requiresSubject && '*'}</FormLabel>
                    <FormControl>
                      <Input
                        placeholder="Betreff der Kommunikation"
                        {...field}
                        value={field.value || ''}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            )}
          </div>

          {/* Date and Time */}
          <div className="space-y-4">
            <Label className="text-base font-medium">Datum und Uhrzeit</Label>
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4">
              <FormField
                control={form.control}
                name="communication_date"
                render={({ field }) => (
                  <FormItem className="flex flex-col">
                    <FormLabel>Datum</FormLabel>
                    <Popover>
                      <PopoverTrigger asChild>
                        <FormControl>
                          <Button
                            variant="outline"
                            className="w-full pl-3 text-left font-normal h-10"
                          >
                            {field.value ? (
                              format(field.value, 'PPP', { locale: de })
                            ) : (
                              <span>Datum wählen</span>
                            )}
                            <Calendar className="ml-auto h-4 w-4 opacity-50" />
                          </Button>
                        </FormControl>
                      </PopoverTrigger>
                      <PopoverContent className="w-auto p-0" align="start">
                        <CalendarComponent
                          mode="single"
                          selected={field.value}
                          onSelect={field.onChange}
                          disabled={(date) =>
                            date > new Date() || date < new Date('1900-01-01')
                          }
                          initialFocus
                          locale={de}
                        />
                      </PopoverContent>
                    </Popover>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="communication_time"
                render={({ field }) => (
                  <FormItem className="flex flex-col">
                    <FormLabel>Uhrzeit</FormLabel>
                    <FormControl>
                      <div className="relative">
                        <Input
                          type="time"
                          {...field}
                          className="pl-10 h-10"
                        />
                        <Clock className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                      </div>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* Duration (conditional) */}
            {supportsDuration && (
              <FormField
                control={form.control}
                name="duration_minutes"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Dauer (Minuten)</FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        min="0"
                        placeholder="z.B. 30"
                        {...field}
                        value={field.value || ''}
                        onChange={(e) => field.onChange(e.target.value ? Number(e.target.value) : undefined)}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            )}
          </div>

          {/* Project Information */}
          <div className="space-y-4">
            <Label className="text-base font-medium">Projektbezug</Label>
            
            <FormField
              control={form.control}
              name="is_project_related"
              render={({ field }) => (
                <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                  <FormControl>
                    <Checkbox
                      checked={field.value}
                      onCheckedChange={field.onChange}
                    />
                  </FormControl>
                  <div className="space-y-1 leading-none">
                    <FormLabel>
                      Projektbezogene Kommunikation
                    </FormLabel>
                    <p className="text-sm text-muted-foreground">
                      Diese Kommunikation bezieht sich auf ein spezifisches Projekt
                    </p>
                  </div>
                </FormItem>
              )}
            />

            {/* Project Selector (conditional) */}
            {showProjectSelector && contactApplications.length > 0 && (
              <FormField
                control={form.control}
                name="project_id"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Zugehöriges Projekt</FormLabel>
                    <Select onValueChange={field.onChange} value={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Projekt auswählen" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {contactApplications.map((app) => (
                          <SelectItem key={app.id} value={app.id}>
                            <div className="flex flex-col">
                              <span className="font-medium">{app.project_name}</span>
                              <span className="text-sm text-muted-foreground">
                                {app.company_name} • {app.status}
                              </span>
                            </div>
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
            )}
          </div>

          {/* Notes and AI */}
          <div className="space-y-4">
            <Label className="text-base font-medium">Details</Label>
            
            <FormField
              control={form.control}
              name="notes"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Notizen *</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Beschreiben Sie die wichtigsten Punkte der Kommunikation..."
                      className="min-h-[120px] resize-none"
                      {...field}
                    />
                  </FormControl>
                  
                  {/* AI Summary Button */}
                  <div className="flex justify-between items-center pt-2">
                    <div className="flex gap-2">
                      <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        onClick={handleGenerateSummary}
                        disabled={isGeneratingSummary || notes.length < 30}
                        className="text-xs"
                      >
                        {isGeneratingSummary ? (
                          <>
                            <Loader2 className="mr-1 h-3 w-3 animate-spin" />
                            Generiere...
                          </>
                        ) : (
                          <>
                            <Sparkles className="mr-1 h-3 w-3" />
                            AI-Zusammenfassung
                          </>
                        )}
                      </Button>
                    </div>
                    <span className="text-sm text-muted-foreground">
                      {notes.length} Zeichen
                      {notes.length > 200 && (
                        <span className="text-blue-600 ml-1">
                          (Optimaler Bereich für AI)
                        </span>
                      )}
                    </span>
                  </div>
                  
                  <div className="flex justify-between">
                    <FormMessage />
                  </div>
                </FormItem>
              )}
            />
          </div>

          </form>
        </Form>

        {/* Dialog Footer */}
        <div className="flex justify-end gap-3 pt-4 border-t mt-6">
          <Button type="button" variant="outline" onClick={onClose}>
            Abbrechen
          </Button>
          <Button 
            onClick={form.handleSubmit(onSubmit)} 
            disabled={createCommunication.isPending || updateCommunication.isPending}
          >
            {(createCommunication.isPending || updateCommunication.isPending) ? (
              <>
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                Speichern...
              </>
            ) : (
              'Speichern'
            )}
          </Button>
        </div>
    </div>
  );
};