import { format } from 'date-fns';
import { de } from 'date-fns/locale';
import { memo } from 'react';
import { 
  MessageSquare, 
  Phone,
  Mail,
  Users,
  MessageCircle,
  ChevronRight
} from 'lucide-react';

import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Skeleton } from '@/components/ui/skeleton';

import { useCommunicationStats } from '@/hooks/useContactCommunications';
import { COMMUNICATION_TYPES } from '@/types/communications';
import { cn } from '@/lib/utils';

interface CommunicationSummaryProps {
  contactId: string;
  contactName?: string;
  onViewAll?: () => void;
  className?: string;
  headless?: boolean;
}

const iconMap = {
  call: Phone,
  email: Mail,
  meeting: Users,
  message: MessageCircle,
  whatsapp: MessageCircle,
  linkedin: MessageCircle,
  sms: MessageCircle,
  other: MessageCircle,
};

export const CommunicationSummary = memo(({
  contactId,
  contactName,
  onViewAll,
  className,
  headless = false
}: CommunicationSummaryProps) => {
  const { data: stats, isLoading: statsLoading } = useCommunicationStats(contactId);

  if (statsLoading) {
    return (
      <Card className={className}>
        <CardHeader className="pb-3">
          <Skeleton className="h-6 w-48" />
        </CardHeader>
        <CardContent className="space-y-3">
          <Skeleton className="h-12 w-full" />
          <Skeleton className="h-12 w-full" />
          <Skeleton className="h-12 w-full" />
        </CardContent>
      </Card>
    );
  }

  const emptyContent = (
    <div className="text-center py-8 text-muted-foreground">
      <MessageSquare className="h-12 w-12 mx-auto mb-3 opacity-40" />
      <p className="text-sm">Noch keine Kommunikationen vorhanden</p>
    </div>
  );

  const communicationsContent = (
    <div className="space-y-2">
      {stats.recent_communications.slice(0, 3).map((comm) => {
        const typeInfo = COMMUNICATION_TYPES[comm.communication_type];
        const Icon = iconMap[comm.communication_type];
        
        return (
          <div
            key={comm.id}
            className="flex items-start gap-3 p-3 bg-muted/20 rounded-lg hover:bg-muted/30 transition-colors"
          >
            <div className={cn(
              "p-1.5 rounded-md flex-shrink-0",
              typeInfo?.color === 'blue' && "bg-blue-100 text-blue-700 dark:bg-blue-900/30 dark:text-blue-300",
              typeInfo?.color === 'green' && "bg-green-100 text-green-700 dark:bg-green-900/30 dark:text-green-300",
              typeInfo?.color === 'purple' && "bg-purple-100 text-purple-700 dark:bg-purple-900/30 dark:text-purple-300",
              typeInfo?.color === 'orange' && "bg-orange-100 text-orange-700 dark:bg-orange-900/30 dark:text-orange-300",
              typeInfo?.color === 'gray' && "bg-gray-100 text-gray-700 dark:bg-gray-800 dark:text-gray-300"
            )}>
              <Icon className="h-4 w-4" />
            </div>
            
            <div className="flex-1 min-w-0">
              <div className="flex items-center gap-2 mb-1">
                <span className="text-sm font-medium text-foreground">
                  {typeInfo?.label}
                </span>
                <span className="text-xs text-muted-foreground">
                  {format(new Date(comm.communication_date), 'dd.MM.yyyy', { locale: de })}
                </span>
              </div>
              
              {comm.subject && (
                <div className="text-sm font-medium truncate mb-1 text-foreground">
                  {comm.subject}
                </div>
              )}
              
              <div className="text-sm text-muted-foreground line-clamp-2">
                {comm.notes.substring(0, 100)}
                {comm.notes.length > 100 && '...'}
              </div>
            </div>
          </div>
        );
      })}
    </div>
  );

  if (headless) {
    return (
      <div className={className}>
        {!stats || stats.total_communications === 0 ? emptyContent : communicationsContent}
      </div>
    );
  }

  if (!stats || stats.total_communications === 0) {
    return (
      <Card className={className}>
        <CardHeader className="pb-3">
          <CardTitle className="text-lg font-semibold">
            Letzte Kommunikationen
          </CardTitle>
        </CardHeader>
        <CardContent>
          {emptyContent}
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={className}>
      <CardHeader className="pb-3">
        <CardTitle className="text-lg font-semibold">
          Letzte Kommunikationen
        </CardTitle>
      </CardHeader>

      <CardContent className="space-y-3">
        {communicationsContent}

        {/* View All Button */}
        {onViewAll && (
          <div className="pt-2">
            <Button
              variant="outline"
              size="sm"
              onClick={onViewAll}
              className="w-full"
            >
              Alle anzeigen
              <ChevronRight className="h-4 w-4 ml-1" />
            </Button>
          </div>
        )}
      </CardContent>
    </Card>
  );
});