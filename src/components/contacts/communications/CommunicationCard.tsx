import { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { format, formatDistanceToNow } from 'date-fns';
import { de } from 'date-fns/locale';
import {
  Phone,
  Mail,
  Users,
  MessageCircle,
  Linkedin,
  Smartphone,
  MoreHorizontal,
  Clock,
  Edit3,
  Trash2,
  ExternalLink,
  Sparkles,
  ChevronDown,
  ChevronUp,
  Copy,
  Loader2
} from 'lucide-react';

import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuSeparator, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from '@/components/ui/alert-dialog';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { ScrollArea } from '@/components/ui/scroll-area';

import { ContactCommunication, CommunicationType, COMMUNICATION_TYPES } from '@/types/communications';
import { useDeleteCommunication } from '@/hooks/useContactCommunications';
import { useBriefSummary, useDetailedSummary, useActionItems, useFollowUpRecommendations } from '@/hooks/useCommunicationAI';
import { cn } from '@/lib/utils';
import { toast } from 'sonner';
import { MarkdownRenderer } from '@/components/ui/markdown-renderer';

interface CommunicationCardProps {
  communication: ContactCommunication;
  contactName?: string;
  projectName?: string;
  onEdit?: (communication: ContactCommunication) => void;
  onProjectClick?: (projectId: string) => void;
  compact?: boolean;
  className?: string;
}

const iconMap = {
  call: Phone,
  email: Mail,
  meeting: Users,
  message: MessageCircle,
  whatsapp: MessageCircle,
  linkedin: Linkedin,
  sms: Smartphone,
  other: MoreHorizontal,
};

export const CommunicationCard = ({
  communication,
  contactName,
  projectName,
  onEdit,
  onProjectClick,
  compact = false,
  className
}: CommunicationCardProps) => {
  const { t } = useTranslation('contacts');
  const [isExpanded, setIsExpanded] = useState(false);
  const [aiSummary, setAiSummary] = useState<string | null>(communication.summarized_notes || null);
  const [showAiSummary, setShowAiSummary] = useState(false);
  const [showAiDialog, setShowAiDialog] = useState(false);
  const [aiDialogTitle, setAiDialogTitle] = useState('');
  const [loadingAiType, setLoadingAiType] = useState<'brief' | 'detailed' | 'action_items' | 'follow_up' | null>(null);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);

  const deleteCommunication = useDeleteCommunication();
  const generateBriefSummary = useBriefSummary();
  const generateDetailedSummary = useDetailedSummary();
  const generateActionItems = useActionItems();
  const generateFollowUpRecommendations = useFollowUpRecommendations();

  const typeInfo = COMMUNICATION_TYPES[communication.communication_type];
  const Icon = iconMap[communication.communication_type];
  const communicationDate = new Date(communication.communication_date);

  const handleDelete = async () => {
    try {
      await deleteCommunication.mutateAsync(communication.id);
      setShowDeleteDialog(false);
      toast.success(t('communications.deleted_success'));
    } catch (error) {
      console.error('Error deleting communication:', error);
      toast.error(t('communications.delete_error'));
    }
  };

  const handleCopyNotes = () => {
    navigator.clipboard.writeText(communication.notes);
  };

  const handleGenerateAI = async (type: 'brief' | 'detailed' | 'action_items' | 'follow_up') => {
    try {
      // Set loading state
      setLoadingAiType(type);
      
      // Show progress toast
      const typeLabels = {
        brief: t('communications.ai.brief_summary'),
        detailed: t('communications.ai.detailed_analysis'), 
        action_items: t('communications.ai.action_items'),
        follow_up: t('communications.ai.follow_up')
      };
      
      const progressToast = toast.loading(t('communications.ai.generating', { type: typeLabels[type] }), {
        description: t('communications.ai.generating_desc')
      });
      
      let result: string | null = null;
      
      switch (type) {
        case 'brief':
          result = await generateBriefSummary.mutateAsync({
            communicationId: communication.id,
            contactId: communication.contact_id,
            includeProjectContext: communication.is_project_related
          });
          break;
        case 'detailed':
          result = await generateDetailedSummary.mutateAsync({
            communicationId: communication.id,
            contactId: communication.contact_id,
            includeProjectContext: communication.is_project_related
          });
          break;
        case 'action_items':
          result = await generateActionItems.mutateAsync({
            communicationId: communication.id,
            contactId: communication.contact_id,
            includeProjectContext: communication.is_project_related
          });
          break;
        case 'follow_up':
          result = await generateFollowUpRecommendations.mutateAsync({
            communicationId: communication.id,
            contactId: communication.contact_id,
            includeProjectContext: communication.is_project_related
          });
          break;
      }

      // Dismiss progress toast
      toast.dismiss(progressToast);

      if (result) {
        setAiSummary(result);
        setAiDialogTitle(typeLabels[type]);
        setShowAiDialog(true);
        
        // Show success toast
        toast.success(t('communications.ai.success', { type: typeLabels[type] }));
      } else {
        toast.error(t('communications.ai.error', { type: typeLabels[type].toLowerCase() }));
      }
    } catch (error) {
      console.error('Error generating AI summary:', error);
      toast.error(t('communications.ai.processing_error'));
    } finally {
      // Always clear loading state
      setLoadingAiType(null);
    }
  };


  return (
    <TooltipProvider>
      <Card className={cn(
        "transition-all hover:shadow-md",
        className
      )}>
        <CardHeader className={cn("pb-2", compact && "py-3")}>
          <div className="flex items-start justify-between">
            <div className="flex items-center gap-3 min-w-0 flex-1">
              <div className={cn(
                "p-2 rounded-lg",
                typeInfo?.color === 'blue' && "bg-blue-100 text-blue-700",
                typeInfo?.color === 'green' && "bg-green-100 text-green-700",
                typeInfo?.color === 'purple' && "bg-purple-100 text-purple-700",
                typeInfo?.color === 'orange' && "bg-orange-100 text-orange-700",
                typeInfo?.color === 'gray' && "bg-gray-100 text-gray-700"
              )}>
                <Icon className="h-4 w-4" />
              </div>
              
              <div className="min-w-0 flex-1">
                <div className="flex items-center gap-2 flex-wrap">
                  <Badge variant="secondary" className="text-xs">
                    {typeInfo?.label}
                  </Badge>
                  
                  {communication.duration_minutes && (
                    <div className="flex items-center gap-1 text-xs text-muted-foreground">
                      <Clock className="h-3 w-3" />
                      <span>{communication.duration_minutes}min</span>
                    </div>
                  )}
                  
                  {communication.is_project_related && projectName && (
                    <Badge 
                      variant="outline" 
                      className="text-xs cursor-pointer hover:bg-accent"
                      onClick={() => communication.project_id && onProjectClick?.(communication.project_id)}
                    >
                      <ExternalLink className="h-3 w-3 mr-1" />
                      {projectName}
                    </Badge>
                  )}
                </div>
                
                {communication.subject && (
                  <h4 className="font-medium text-sm mt-1 truncate">
                    {communication.subject}
                  </h4>
                )}
                
                <p className="text-xs text-muted-foreground">
                  {format(communicationDate, 'PPP', { locale: de })} um {format(communicationDate, 'HH:mm')}
                  <span className="ml-1">
                    ({formatDistanceToNow(communicationDate, { addSuffix: true, locale: de })})
                  </span>
                </p>
              </div>
            </div>

            <div className="flex items-center gap-1 flex-shrink-0">
              {/* AI Actions Dropdown */}
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" size="sm" className="h-8 w-8 p-0" disabled={!!loadingAiType}>
                    {loadingAiType ? (
                      <Loader2 className="h-4 w-4 animate-spin" />
                    ) : (
                      <Sparkles className="h-4 w-4" />
                    )}
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuItem 
                    onClick={() => handleGenerateAI('brief')}
                    disabled={!!loadingAiType}
                  >
                    {loadingAiType === 'brief' ? (
                      <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    ) : (
                      <Sparkles className="h-4 w-4 mr-2" />
                    )}
                    {t('communications.ai.brief_summary')}
                  </DropdownMenuItem>
                  <DropdownMenuItem 
                    onClick={() => handleGenerateAI('detailed')}
                    disabled={!!loadingAiType}
                  >
                    {loadingAiType === 'detailed' ? (
                      <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    ) : (
                      <Sparkles className="h-4 w-4 mr-2" />
                    )}
                    {t('communications.ai.detailed_analysis')}
                  </DropdownMenuItem>
                  <DropdownMenuItem 
                    onClick={() => handleGenerateAI('action_items')}
                    disabled={!!loadingAiType}
                  >
                    {loadingAiType === 'action_items' ? (
                      <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    ) : (
                      <Sparkles className="h-4 w-4 mr-2" />
                    )}
                    {t('communications.ai.action_items')}
                  </DropdownMenuItem>
                  <DropdownMenuItem 
                    onClick={() => handleGenerateAI('follow_up')}
                    disabled={!!loadingAiType}
                  >
                    {loadingAiType === 'follow_up' ? (
                      <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    ) : (
                      <Sparkles className="h-4 w-4 mr-2" />
                    )}
                    {t('communications.ai.follow_up')}
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>

              {/* More Actions Dropdown */}
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                    <MoreHorizontal className="h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  {onEdit && (
                    <DropdownMenuItem onClick={() => onEdit(communication)}>
                      <Edit3 className="h-4 w-4 mr-2" />
                      {t('communications.edit')}
                    </DropdownMenuItem>
                  )}
                  <DropdownMenuItem onClick={handleCopyNotes}>
                    <Copy className="h-4 w-4 mr-2" />
                    {t('communications.copy_notes')}
                  </DropdownMenuItem>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem 
                    onClick={() => setShowDeleteDialog(true)}
                    className="text-destructive focus:text-destructive"
                  >
                    <Trash2 className="h-4 w-4 mr-2" />
                    {t('communications.delete')}
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>

              {/* Expand/Collapse */}
              <Button
                variant="ghost"
                size="sm"
                className="h-8 w-8 p-0"
                onClick={() => setIsExpanded(!isExpanded)}
              >
                {isExpanded ? (
                  <ChevronUp className="h-4 w-4" />
                ) : (
                  <ChevronDown className="h-4 w-4" />
                )}
              </Button>
            </div>
          </div>
        </CardHeader>

        {(isExpanded || !compact) && (
          <CardContent className="pt-0">
            {/* Notes */}
            <div className="space-y-3">
              <div>
                <h5 className="text-sm font-medium mb-2">{t('communications.notes')}</h5>
                <div className="text-sm text-muted-foreground whitespace-pre-wrap bg-muted/30 p-3 rounded-md">
                  {communication.notes}
                </div>
              </div>

              {/* AI Summary Preview */}
              {(aiSummary || communication.summarized_notes) && (
                <>
                  <Separator />
                  <div>
                    <div className="flex items-center justify-between mb-2">
                      <h5 className="text-sm font-medium flex items-center gap-2">
                        <Sparkles className="h-4 w-4 text-blue-500" />
                        {t('communications.available')}
                      </h5>
                      <div className="flex gap-1">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => setShowAiSummary(!showAiSummary)}
                          className="text-xs"
                        >
                          {showAiSummary ? t('communications.hide') : t('communications.preview')}
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => {
                            setAiDialogTitle(t('communications.ai.dialog_title'));
                            setShowAiDialog(true);
                          }}
                          className="text-xs"
                        >
                          {t('communications.show_full')}
                        </Button>
                      </div>
                    </div>
                    
                    {showAiSummary && (
                      <div className="text-sm bg-blue-50 dark:bg-blue-950/30 border border-blue-200 dark:border-blue-800/30 p-3 rounded-md max-h-32 overflow-hidden relative">
                        <MarkdownRenderer 
                          content={(aiSummary || communication.summarized_notes || '').substring(0, 200) + '...'} 
                          className="text-sm"
                        />
                        <div className="absolute bottom-0 left-0 right-0 h-8 bg-gradient-to-t from-blue-50 dark:from-blue-950/30 to-transparent pointer-events-none" />
                      </div>
                    )}
                  </div>
                </>
              )}

            </div>
          </CardContent>
        )}
      </Card>
      
      {/* Delete Confirmation Dialog */}
      <AlertDialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>{t('communications.delete_confirm_title')}</AlertDialogTitle>
            <AlertDialogDescription>
              {t('communications.delete_confirm_message')}
            </AlertDialogDescription>
            
            {/* Communication Details - outside description to avoid nesting issues */}
            <div className="space-y-2 pt-2">
              {communication.subject && (
                <div className="font-medium text-sm">
                  {t('communications.delete_confirm_subject', { subject: communication.subject })}
                </div>
              )}
              <div className="text-sm text-muted-foreground">
                {t('communications.delete_confirm_date', { date: format(new Date(communication.communication_date), 'PPP', { locale: de }) })}
              </div>
            </div>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>{t('communications.delete_cancel')}</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDelete}
              disabled={deleteCommunication.isPending}
              className="bg-destructive hover:bg-destructive/90"
            >
              {deleteCommunication.isPending ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  {t('communications.delete_loading')}
                </>
              ) : (
                <>
                  <Trash2 className="h-4 w-4 mr-2" />
                  {t('communications.delete_confirm')}
                </>
              )}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* AI Summary Dialog */}
      <Dialog open={showAiDialog} onOpenChange={setShowAiDialog}>
        <DialogContent className="max-w-4xl max-h-[90vh] flex flex-col">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Sparkles className="h-5 w-5 text-blue-500" />
              {aiDialogTitle}
              {communication.subject && (
                <span className="text-sm font-normal text-muted-foreground">
                  - {communication.subject}
                </span>
              )}
            </DialogTitle>
          </DialogHeader>
          
          <ScrollArea className="flex-1 max-h-[70vh] pr-4">
            <div className="space-y-4">
              {/* Communication Context */}
              <div className="text-sm text-muted-foreground pl-4 bg-muted/50 py-2 rounded">
                <div className="font-medium mb-1">{t('communications.ai.communication_context', { date: format(communicationDate, 'PPP', { locale: de }) })}</div>
                <div className="flex items-center gap-2 text-xs">
                  <Badge variant="secondary">{typeInfo?.label}</Badge>
                  {communication.duration_minutes && (
                    <span className="flex items-center gap-1">
                      <Clock className="h-3 w-3" />
                      {communication.duration_minutes}min
                    </span>
                  )}
                  {projectName && (
                    <Badge variant="outline">{projectName}</Badge>
                  )}
                </div>
              </div>

              {/* AI Summary Content */}
              <div className="prose prose-sm max-w-none dark:prose-invert">
                <MarkdownRenderer 
                  content={aiSummary || communication.summarized_notes || ''} 
                  className="text-sm leading-relaxed"
                />
              </div>

              {/* Copy Action */}
              <div className="flex justify-end pt-4 border-t">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    navigator.clipboard.writeText(aiSummary || communication.summarized_notes || '');
                    toast.success(t('communications.copy_success'));
                  }}
                  className="flex items-center gap-2"
                >
                  <Copy className="h-4 w-4" />
                  {t('communications.ai.copy')}
                </Button>
              </div>
            </div>
          </ScrollArea>
        </DialogContent>
      </Dialog>
    </TooltipProvider>
  );
};