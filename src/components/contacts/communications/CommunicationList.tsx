import { useState, useMemo, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { format, startOfDay, endOfDay, subDays, subWeeks, subMonths } from 'date-fns';
import { de } from 'date-fns/locale';
import { Filter, Search, Calendar, RotateCcw, Plus, Download, MessageSquare } from 'lucide-react';

import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Skeleton } from '@/components/ui/skeleton';
import { ScrollArea } from '@/components/ui/scroll-area';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription } from '@/components/ui/dialog';

import { CommunicationCard } from './CommunicationCard';
import { CommunicationForm } from './CommunicationForm';
import { useCommunicationsWithDetailsPaginated } from '@/hooks/useContactCommunications';
import { useApplications } from '@/hooks/useApplications';
import { CommunicationType, CommunicationFilters, COMMUNICATION_TYPES } from '@/types/communications';
import { DataPagination } from '@/components/ui/data-pagination';
import { cn } from '@/lib/utils';

interface CommunicationListProps {
  contactId?: string;
  contactName?: string;
  projectId?: string;
  compact?: boolean;
  maxHeight?: string;
  showAddButton?: boolean;
  className?: string;
}

export const CommunicationList = ({
  contactId,
  contactName,
  projectId,
  compact = false,
  maxHeight = "600px",
  showAddButton = true,
  className
}: CommunicationListProps) => {
  const { t } = useTranslation('contacts');
  const [showForm, setShowForm] = useState(false);
  const [editingCommunication, setEditingCommunication] = useState<any>(null);
  const [searchText, setSearchText] = useState('');
  const [typeFilter, setTypeFilter] = useState<CommunicationType | 'all'>('all');
  const [dateFilter, setDateFilter] = useState<'all' | 'today' | 'week' | 'month'>('all');
  const [projectFilter, setProjectFilter] = useState<'all' | 'project' | 'non_project'>('all');
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize] = useState(compact ? 10 : 25); // Smaller page sizes for better performance

  // Build filters with stable object references
  const filters: CommunicationFilters = useMemo(() => {
    const result: CommunicationFilters = {};
    
    if (contactId) result.contact_id = contactId;
    if (projectId) result.project_id = projectId;
    if (typeFilter !== 'all') result.communication_type = typeFilter;
    if (searchText.trim()) result.search_text = searchText.trim();
    
    // Date filtering with memoized date calculations
    switch (dateFilter) {
      case 'today':
        const today = new Date();
        result.date_from = startOfDay(today).toISOString();
        result.date_to = endOfDay(today).toISOString();
        break;
      case 'week':
        result.date_from = subWeeks(new Date(), 1).toISOString();
        break;
      case 'month':
        result.date_from = subMonths(new Date(), 1).toISOString();
        break;
    }
    
    // Project relation filtering
    switch (projectFilter) {
      case 'project':
        result.is_project_related = true;
        break;
      case 'non_project':
        result.is_project_related = false;
        break;
    }
    
    return result;
  }, [contactId, projectId, typeFilter, searchText, dateFilter, projectFilter]);

  // Memoize the query object to prevent unnecessary API calls
  const queryObject = useMemo(() => ({
    ...filters,
    page: currentPage,
    limit: pageSize
  }), [filters, currentPage, pageSize]);

  // Use paginated hook with memoized query object
  const { data: communicationsResponse, isLoading, refetch } = useCommunicationsWithDetailsPaginated(queryObject);
  const { data: applications } = useApplications();

  // Extract data and pagination info
  const communications = communicationsResponse?.data || [];
  const pagination = communicationsResponse?.pagination;

  // Helper to get project name
  const getProjectName = (projectId: string) => {
    return applications?.find(app => app.id === projectId)?.project_name;
  };

  const clearFilters = useCallback(() => {
    setSearchText('');
    setTypeFilter('all');
    setDateFilter('all');
    setProjectFilter('all');
    setCurrentPage(1); // Reset to first page when clearing filters
  }, []);

  // Handle page changes
  const handlePageChange = useCallback((page: number) => {
    setCurrentPage(page);
  }, []);

  // Reset to first page when filters change - memoized to prevent recreations
  const handleFilterChange = useCallback((filterSetter: (value: any) => void, value: any) => {
    filterSetter(value);
    setCurrentPage(1);
  }, []);

  const hasActiveFilters = searchText || typeFilter !== 'all' || dateFilter !== 'all' || projectFilter !== 'all';

  const exportCommunications = useCallback(() => {
    if (!communications) return;
    
    const csvData = communications.map(comm => ({
      [t('communications.created')]: format(new Date(comm.communication_date), 'PPP HH:mm', { locale: de }),
      [t('communications.type_filter')]: COMMUNICATION_TYPES[comm.communication_type]?.label,
      [t('communications.subject', { defaultValue: 'Subject' })]: comm.subject || '',
      [t('communications.notes')]: comm.notes.replace(/\n/g, ' '),
      [t('communications.duration', { defaultValue: 'Duration' })]: comm.duration_minutes ? `${comm.duration_minutes} min` : '',
      [t('communications.project', { defaultValue: 'Project' })]: comm.project?.project_name || '',
      [t('communications.project_related')]: comm.is_project_related ? t('communications.yes', { defaultValue: 'Yes' }) : t('communications.no', { defaultValue: 'No' })
    }));
    
    // Simple CSV export (you might want to use a proper CSV library)
    const csvContent = [
      Object.keys(csvData[0] || {}).join(','),
      ...csvData.map(row => Object.values(row).map(val => `"${val}"`).join(','))
    ].join('\n');
    
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `kommunikationen_${contactName || 'export'}_${format(new Date(), 'yyyy-MM-dd')}.csv`;
    link.click();
    URL.revokeObjectURL(url);
  }, [communications, contactName]);

  if (showForm) {
    return (
      <div className={className}>
        <CommunicationForm
          contactId={contactId!}
          contactName={contactName}
          onClose={() => setShowForm(false)}
          onSuccess={() => {
            setShowForm(false);
            refetch();
          }}
        />
      </div>
    );
  }

  return (
    <Card className={className}>
      <CardHeader className="pb-4 px-3 sm:px-6">
        <div className="flex flex-col space-y-4 sm:space-y-0 sm:flex-row sm:items-center justify-between">
          <div className="min-w-0 flex-1">
            <CardTitle className="flex items-center gap-2">
              <MessageSquare className="h-5 w-5" />
              <span className="truncate">{t('communications.title')}</span>
              {pagination && (
                <Badge variant="secondary" className="text-xs flex-shrink-0">
                  {pagination.total || communications.length}
                </Badge>
              )}
            </CardTitle>
            {contactName && (
              <p className="text-sm text-muted-foreground mt-1 truncate">
                {t('communications.description', { contactName })}
              </p>
            )}
          </div>
          
          {showAddButton && contactId && (
            <Button 
              onClick={() => setShowForm(true)} 
              size="sm"
              className="w-full sm:w-auto flex-shrink-0"
            >
              <Plus className="h-4 w-4 mr-2" />
              <span className="sm:inline">{t('communications.new_communication')}</span>
            </Button>
          )}
        </div>

        {/* Filters - Mobile-First Design */}
        <div className="space-y-3">
          {/* Search Input - Full Width on Mobile */}
          <div className="relative w-full">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder={t('communications.search_placeholder')}
              value={searchText}
              onChange={(e) => handleFilterChange(setSearchText, e.target.value)}
              className="pl-10 w-full"
            />
          </div>
          
          {/* Filter Row - Responsive Grid Layout */}
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-2 w-full">
            <Select value={typeFilter} onValueChange={(value: any) => handleFilterChange(setTypeFilter, value)}>
              <SelectTrigger className="w-full h-9">
                <SelectValue placeholder={t('communications.type_filter')} />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">{t('communications.all_types')}</SelectItem>
                {Object.values(COMMUNICATION_TYPES).map(type => (
                  <SelectItem key={type.type} value={type.type}>
                    {type.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            
            <Select value={dateFilter} onValueChange={(value: any) => handleFilterChange(setDateFilter, value)}>
              <SelectTrigger className="w-full h-9">
                <SelectValue placeholder={t('communications.date_filter')} />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">{t('communications.all_dates')}</SelectItem>
                <SelectItem value="today">{t('communications.today')}</SelectItem>
                <SelectItem value="week">{t('communications.last_week')}</SelectItem>
                <SelectItem value="month">{t('communications.last_month')}</SelectItem>
              </SelectContent>
            </Select>
            
            {!projectId && (
              <Select value={projectFilter} onValueChange={(value: any) => handleFilterChange(setProjectFilter, value)}>
                <SelectTrigger className="w-full h-9">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">{t('communications.all')}</SelectItem>
                  <SelectItem value="project">{t('communications.project_related')}</SelectItem>
                  <SelectItem value="non_project">{t('communications.general')}</SelectItem>
                </SelectContent>
              </Select>
            )}
            
            {/* Action Buttons - Mobile Responsive */}
            <div className="flex items-center gap-1 col-span-1 sm:col-span-2 lg:col-span-1 justify-end lg:justify-start">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => refetch()}
                className="h-9 px-2 flex-shrink-0"
                title="Aktualisieren"
              >
                <RotateCcw className="h-3 w-3" />
              </Button>
              
              {communications && communications.length > 0 && (
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button 
                      variant="ghost" 
                      size="sm" 
                      className="h-9 px-2 flex-shrink-0"
                      title="Exportieren"
                    >
                      <Download className="h-3 w-3" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuItem onClick={exportCommunications}>
                      <Download className="h-4 w-4 mr-2" />
                      Als CSV exportieren
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              )}
            </div>
          </div>

          {/* Clear Filters Action - Mobile Responsive */}
          {hasActiveFilters && (
            <div className="flex justify-center sm:justify-start">
              <Button
                variant="ghost"
                size="sm"
                onClick={clearFilters}
                className="h-9 px-3 text-sm"
              >
                <RotateCcw className="h-3 w-3 mr-2" />
                Filter zurücksetzen
              </Button>
            </div>
          )}
        </div>
      </CardHeader>

      <CardContent className="p-0">
        <div className="px-3 sm:px-6 pb-6">
          {isLoading ? (
            <div className="space-y-3">
              {Array.from({ length: 3 }, (_, i) => (
                <div key={i} className="space-y-2">
                  <Skeleton className="h-4 w-1/4" />
                  <Skeleton className="h-20 w-full" />
                </div>
              ))}
            </div>
          ) : !communications || communications.length === 0 ? (
            <div className="text-center py-8">
              <div className="text-muted-foreground">
                {hasActiveFilters ? (
                  <>
                    <p>Keine Kommunikationen entsprechen den aktuellen Filtern.</p>
                    <Button
                      variant="link"
                      onClick={clearFilters}
                      className="mt-2"
                    >
                      Filter zurücksetzen
                    </Button>
                  </>
                ) : (
                  <>
                    <p>Noch keine Kommunikationen vorhanden.</p>
                    {showAddButton && contactId && (
                      <Button
                        onClick={() => setShowForm(true)}
                        className="mt-4"
                      >
                        <Plus className="h-4 w-4 mr-2" />
                        Erste Kommunikation hinzufügen
                      </Button>
                    )}
                  </>
                )}
              </div>
            </div>
          ) : (
            <ScrollArea 
              className={cn(
                "w-full",
                // Responsive max-height based on screen size
                compact 
                  ? "max-h-[300px] sm:max-h-[400px] md:max-h-[500px]"
                  : "max-h-[400px] sm:max-h-[500px] md:max-h-[600px] lg:max-h-[700px]"
              )}
            >
              <div className="space-y-3 p-1"> {/* Added padding to prevent edge clipping */}
                {communications.map((communication) => (
                  <CommunicationCard
                    key={communication.id}
                    communication={communication}
                    contactName={communication.contact?.name || communication.contact?.company}
                    projectName={communication.project?.project_name}
                    compact={compact}
                    onProjectClick={(projectId) => {
                      // Handle project navigation if needed
                      console.log('Navigate to project:', projectId);
                    }}
                    onEdit={(comm) => {
                      setEditingCommunication(comm);
                      setShowForm(true);
                    }}
                  />
                ))}
              </div>
            </ScrollArea>
          )}

          {/* Pagination */}
          {pagination && pagination.totalPages > 1 && (
            <div className="px-3 sm:px-6 pb-6">
              <DataPagination
                currentPage={pagination.page}
                totalPages={pagination.totalPages}
                onPageChange={handlePageChange}
                hasNextPage={pagination.hasNextPage}
                hasPreviousPage={pagination.hasPreviousPage}
                total={pagination.total}
                limit={pagination.limit}
                className="mt-4"
              />
            </div>
          )}
        </div>
      </CardContent>

      {/* Edit Communication Dialog */}
      <Dialog open={showForm} onOpenChange={(open) => {
        setShowForm(open);
        if (!open) {
          setEditingCommunication(null);
        }
      }}>
        <DialogContent className="sm:max-w-[600px] max-h-[90vh] flex flex-col">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <MessageSquare className="h-5 w-5" />
              {editingCommunication ? 'Kommunikation bearbeiten' : 'Neue Kommunikation'}
              {contactName && ` - ${contactName}`}
            </DialogTitle>
          </DialogHeader>

          <div className="flex-1 overflow-y-auto">
            <CommunicationForm
              contactId={contactId!}
              contactName={contactName || ''}
              onClose={() => {
                setShowForm(false);
                setEditingCommunication(null);
              }}
              onSuccess={() => {
                setShowForm(false);
                setEditingCommunication(null);
                refetch();
              }}
              initialData={editingCommunication ? {
                communication_type: editingCommunication.communication_type,
                subject: editingCommunication.subject,
                notes: editingCommunication.notes,
                communication_date: new Date(editingCommunication.communication_date),
                communication_time: format(new Date(editingCommunication.communication_date), 'HH:mm'),
                duration_minutes: editingCommunication.duration_minutes,
                project_id: editingCommunication.project_id,
                is_project_related: editingCommunication.is_project_related
              } : undefined}
              communicationId={editingCommunication?.id}
            />
          </div>
        </DialogContent>
      </Dialog>
    </Card>
  );
};