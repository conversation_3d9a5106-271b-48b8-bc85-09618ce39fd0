import { 
  Phone, 
  Mail, 
  Users, 
  MessageCircle, 
  Linkedin, 
  Smartphone, 
  MoreHorizontal 
} from 'lucide-react';
import { CommunicationType, COMMUNICATION_TYPES } from '@/types/communications';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';

interface CommunicationTypeSelectorProps {
  value: CommunicationType;
  onValueChange: (value: CommunicationType) => void;
  className?: string;
}

const iconMap = {
  call: Phone,
  email: Mail,
  meeting: Users,
  message: MessageCircle,
  whatsapp: MessageCircle,
  linkedin: Linkedin,
  sms: Smartphone,
  other: MoreHorizontal,
};

const colorMap = {
  blue: 'border-blue-500 bg-blue-50 text-blue-700 hover:bg-blue-100',
  green: 'border-green-500 bg-green-50 text-green-700 hover:bg-green-100',
  purple: 'border-purple-500 bg-purple-50 text-purple-700 hover:bg-purple-100',
  orange: 'border-orange-500 bg-orange-50 text-orange-700 hover:bg-orange-100',
  gray: 'border-gray-500 bg-gray-50 text-gray-700 hover:bg-gray-100',
};

export const CommunicationTypeSelector = ({
  value,
  onValueChange,
  className
}: CommunicationTypeSelectorProps) => {
  const types = Object.values(COMMUNICATION_TYPES);

  return (
    <div className={cn("grid grid-cols-2 sm:grid-cols-4 gap-2", className)}>
      {types.map((type) => {
        const Icon = iconMap[type.type];
        const isSelected = value === type.type;
        const colorClass = colorMap[type.color as keyof typeof colorMap] || colorMap.gray;

        return (
          <Button
            key={type.type}
            type="button"
            variant="outline"
            size="sm"
            onClick={() => onValueChange(type.type)}
            className={cn(
              "h-16 flex flex-col items-center justify-center gap-1 transition-all",
              "hover:scale-105 active:scale-95",
              isSelected && colorClass,
              !isSelected && "hover:bg-accent"
            )}
          >
            <Icon className="h-4 w-4" />
            <span className="text-xs font-medium">{type.label}</span>
          </Button>
        );
      })}
    </div>
  );
};