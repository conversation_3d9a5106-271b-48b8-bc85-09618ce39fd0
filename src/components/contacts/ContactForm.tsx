import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { CreateContactData, UpdateContactData, Contact } from '@/types/applications';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { useTranslation } from '@/hooks/useTranslation';

const createContactSchema = (tErrors: (key: string) => string) => z.object({
  name: z.string().optional(),
  email: z.string().email(tErrors('validation.email_invalid')).optional().or(z.literal('')),
  phone: z.string().optional(),
  company: z.string().optional(),
  notes: z.string().optional()
});

type ContactFormData = z.infer<ReturnType<typeof createContactSchema>>;

interface ContactFormProps {
  contact?: Contact;
  onSubmit: (data: CreateContactData | UpdateContactData) => Promise<void>;
  onCancel: () => void;
  isLoading?: boolean;
}

export const ContactForm = ({ 
  contact, 
  onSubmit, 
  onCancel, 
  isLoading = false 
}: ContactFormProps) => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { t } = useTranslation('forms');
  const tCommon = useTranslation('common').t;
  const tErrors = useTranslation('errors').t;
  
  const contactSchema = createContactSchema(tErrors);
  
  const form = useForm<ContactFormData>({
    resolver: zodResolver(contactSchema),
    defaultValues: {
      name: contact?.name || '',
      email: contact?.email || '',
      phone: contact?.phone || '',
      company: contact?.company || '',
      notes: contact?.notes || ''
    }
  });

  const handleSubmit = async (data: ContactFormData) => {
    try {
      setIsSubmitting(true);
      
      // Clean up empty strings
      const cleanData = Object.entries(data).reduce((acc, [key, value]) => {
        if (value && value.trim() !== '') {
          acc[key as keyof ContactFormData] = value.trim();
        }
        return acc;
      }, {} as Partial<ContactFormData>);

      // Validate that we have at least one meaningful field
      if (!cleanData.name && !cleanData.email && !cleanData.company) {
        form.setError('root', {
          message: tErrors('validation.contact_min_required')
        });
        return;
      }

      if (contact) {
        // Update existing contact
        await onSubmit({
          id: contact.id,
          ...cleanData
        } as UpdateContactData);
      } else {
        // Create new contact
        await onSubmit(cleanData as CreateContactData);
      }
    } catch (error) {
      console.error('Error submitting contact form:', error);
      form.setError('root', {
        message: tErrors('general.unexpected_error')
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-4">
        <FormField
          control={form.control}
          name="name"
          render={({ field }) => (
            <FormItem>
              <FormLabel>{t('contact.name')}</FormLabel>
              <FormControl>
                <Input
                  placeholder={t('placeholders.contact_name')}
                  {...field}
                  disabled={isLoading || isSubmitting}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="email"
          render={({ field }) => (
            <FormItem>
              <FormLabel>{t('contact.email')}</FormLabel>
              <FormControl>
                <Input
                  type="email"
                  placeholder={t('placeholders.contact_email')}
                  {...field}
                  disabled={isLoading || isSubmitting}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="phone"
          render={({ field }) => (
            <FormItem>
              <FormLabel>{t('contact.phone')}</FormLabel>
              <FormControl>
                <Input
                  type="tel"
                  placeholder={t('placeholders.contact_phone')}
                  {...field}
                  disabled={isLoading || isSubmitting}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="company"
          render={({ field }) => (
            <FormItem>
              <FormLabel>{t('contact.company')}</FormLabel>
              <FormControl>
                <Input
                  placeholder={t('placeholders.contact_company')}
                  {...field}
                  disabled={isLoading || isSubmitting}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="notes"
          render={({ field }) => (
            <FormItem>
              <FormLabel>{t('contact.notes')}</FormLabel>
              <FormControl>
                <Textarea
                  placeholder={t('placeholders.contact_notes')}
                  rows={3}
                  {...field}
                  disabled={isLoading || isSubmitting}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        {form.formState.errors.root && (
          <div className="text-sm text-red-600 bg-red-50 p-3 rounded-md">
            {form.formState.errors.root.message}
          </div>
        )}

        <div className="flex justify-end gap-2 pt-4">
          <Button
            type="button"
            variant="outline"
            onClick={onCancel}
            disabled={isLoading || isSubmitting}
          >
            {tCommon('actions.cancel')}
          </Button>
          <Button
            type="submit"
            disabled={isLoading || isSubmitting}
          >
            {isSubmitting ? tCommon('actions.saving') : contact ? tCommon('actions.update') : tCommon('actions.create')}
          </Button>
        </div>
      </form>
    </Form>
  );
};