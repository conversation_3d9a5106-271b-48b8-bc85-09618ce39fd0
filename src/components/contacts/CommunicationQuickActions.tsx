import { useState } from 'react';
import { 
  MessageSquare, 
  Phone, 
  Mail, 
  Users, 
  Plus 
} from 'lucide-react';

import { Button } from '@/components/ui/button';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { Badge } from '@/components/ui/badge';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { CommunicationForm } from './communications/CommunicationForm';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription } from '@/components/ui/dialog';

import { CommunicationType } from '@/types/communications';
import { Contact } from '@/types/applications';

interface CommunicationQuickActionsProps {
  contact: Contact;
  onSuccess?: () => void;
  compact?: boolean;
  showLabel?: boolean;
  projectId?: string;
  projectName?: string;
}

export const CommunicationQuickActions = ({ 
  contact, 
  onSuccess,
  compact = false,
  showLabel = true,
  projectId,
  projectName
}: CommunicationQuickActionsProps) => {
  const [showForm, setShowForm] = useState(false);
  const [selectedType, setSelectedType] = useState<CommunicationType>('call');

  const quickActions = [
    { type: 'call' as CommunicationType, icon: Phone, label: 'Anruf', color: 'blue' },
    { type: 'email' as CommunicationType, icon: Mail, label: 'E-Mail', color: 'green' },
    { type: 'meeting' as CommunicationType, icon: Users, label: 'Meeting', color: 'purple' },
    { type: 'message' as CommunicationType, icon: MessageSquare, label: 'Nachricht', color: 'orange' },
  ];

  const handleQuickAction = (type: CommunicationType) => {
    setSelectedType(type);
    setShowForm(true);
  };

  const handleFormSuccess = () => {
    setShowForm(false);
    onSuccess?.();
  };

  if (compact) {
    return (
      <>
        <TooltipProvider>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" className="p-2 sm:px-3 sm:py-2">
                <MessageSquare className="h-4 w-4" />
                {showLabel && <span className="hidden sm:inline sm:ml-2">Kommunikation</span>}
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              {quickActions.map((action) => {
                const Icon = action.icon;
                return (
                  <DropdownMenuItem
                    key={action.type}
                    onClick={() => handleQuickAction(action.type)}
                  >
                    <Icon className="h-4 w-4 mr-2" />
                    {action.label}
                  </DropdownMenuItem>
                );
              })}
            </DropdownMenuContent>
          </DropdownMenu>
        </TooltipProvider>

        <Dialog open={showForm} onOpenChange={setShowForm}>
          <DialogContent className="w-full max-w-[95vw] sm:max-w-2xl max-h-[90vh] overflow-y-auto p-0">
            <div className="sticky top-0 z-10 bg-background border-b px-6 py-4">
              <DialogHeader>
                <DialogTitle>Neue Kommunikation - {contact.name || contact.company}</DialogTitle>
                <DialogDescription>
                  {projectName 
                    ? `Erstellen Sie eine neue Kommunikation für das Projekt "${projectName}"`
                    : "Erstellen Sie eine neue Kommunikation für diesen Kontakt"
                  }
                </DialogDescription>
              </DialogHeader>
            </div>
            <div className="px-6 pb-6">
              <CommunicationForm
                contactId={contact.id}
                contactName={contact.name || contact.company}
                onClose={() => setShowForm(false)}
                onSuccess={handleFormSuccess}
                initialData={{ 
                  communication_type: selectedType,
                  ...(projectId && {
                    project_id: projectId,
                    is_project_related: true
                  })
                }}
              />
            </div>
          </DialogContent>
        </Dialog>
      </>
    );
  }

  return (
    <>
      <div className="flex items-center gap-2">
        {quickActions.map((action) => {
          const Icon = action.icon;
          return (
            <TooltipProvider key={action.type}>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleQuickAction(action.type)}
                    className="h-8 w-8 p-0"
                  >
                    <Icon className="h-4 w-4" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  <p>{action.label} mit {contact.name || contact.company}</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          );
        })}
        
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="outline" size="sm" className="h-8 w-8 p-0">
              <Plus className="h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuItem onClick={() => handleQuickAction('whatsapp')}>
              <MessageSquare className="h-4 w-4 mr-2" />
              WhatsApp
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => handleQuickAction('linkedin')}>
              <MessageSquare className="h-4 w-4 mr-2" />
              LinkedIn
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => handleQuickAction('sms')}>
              <MessageSquare className="h-4 w-4 mr-2" />
              SMS
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => handleQuickAction('other')}>
              <MessageSquare className="h-4 w-4 mr-2" />
              Sonstiges
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>

      <Dialog open={showForm} onOpenChange={setShowForm}>
        <DialogContent className="w-full max-w-[95vw] sm:max-w-2xl max-h-[90vh] overflow-y-auto p-0">
          <div className="sticky top-0 z-10 bg-background border-b px-6 py-4">
            <DialogHeader>
              <DialogTitle>Neue Kommunikation - {contact.name || contact.company}</DialogTitle>
              <DialogDescription>
                {projectName 
                  ? `Erstellen Sie eine neue Kommunikation für das Projekt "${projectName}"`
                  : "Erstellen Sie eine neue Kommunikation für diesen Kontakt"
                }
              </DialogDescription>
            </DialogHeader>
          </div>
          <div className="px-6 pb-6">
            <CommunicationForm
              contactId={contact.id}
              contactName={contact.name || contact.company}
              onClose={() => setShowForm(false)}
              onSuccess={handleFormSuccess}
              initialData={{ 
                communication_type: selectedType,
                ...(projectId && {
                  project_id: projectId,
                  is_project_related: true
                })
              }}
            />
          </div>
        </DialogContent>
      </Dialog>
    </>
  );
};