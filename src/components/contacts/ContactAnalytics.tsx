import { useMemo } from 'react';
import { Contact } from '@/types/applications';
import { <PERSON>, <PERSON><PERSON><PERSON>nt, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';
import { 
  TrendingUp, 
  Users, 
  Building2, 
  Award,
  Target,
  Activity,
  BarChart3,
  PieChart
} from 'lucide-react';

interface ContactAnalyticsProps {
  contacts: Contact[];
}

export const ContactAnalytics = ({ contacts }: ContactAnalyticsProps) => {
  const analytics = useMemo(() => {
    // Basic metrics
    const totalContacts = contacts.length;
    const activeContacts = contacts.filter(c => c.total_projects > 0).length;
    const totalProjects = contacts.reduce((sum, c) => sum + c.total_projects, 0);
    const totalSuccessfulProjects = contacts.reduce((sum, c) => sum + c.successful_projects, 0);
    const overallSuccessRate = totalProjects > 0 ? (totalSuccessfulProjects / totalProjects) * 100 : 0;

    // Company analysis
    const companiesMap = contacts.reduce((acc, contact) => {
      if (contact.company) {
        if (!acc[contact.company]) {
          acc[contact.company] = {
            name: contact.company,
            contactCount: 0,
            totalProjects: 0,
            successfulProjects: 0
          };
        }
        acc[contact.company].contactCount++;
        acc[contact.company].totalProjects += contact.total_projects;
        acc[contact.company].successfulProjects += contact.successful_projects;
      }
      return acc;
    }, {} as Record<string, {
      name: string;
      contactCount: number;
      totalProjects: number;
      successfulProjects: number;
    }>);

    const topCompanies = Object.values(companiesMap)
      .sort((a, b) => b.totalProjects - a.totalProjects)
      .slice(0, 10);

    // Performance tiers
    const performanceTiers = {
      excellent: contacts.filter(c => c.total_projects > 0 && (c.successful_projects / c.total_projects) >= 0.7),
      good: contacts.filter(c => c.total_projects > 0 && (c.successful_projects / c.total_projects) >= 0.3 && (c.successful_projects / c.total_projects) < 0.7),
      poor: contacts.filter(c => c.total_projects > 0 && (c.successful_projects / c.total_projects) < 0.3),
      inactive: contacts.filter(c => c.total_projects === 0)
    };

    // Top performers
    const topPerformers = contacts
      .filter(c => c.total_projects >= 2) // Only consider contacts with at least 2 projects
      .sort((a, b) => {
        const aRate = a.successful_projects / a.total_projects;
        const bRate = b.successful_projects / b.total_projects;
        if (aRate === bRate) {
          return b.total_projects - a.total_projects; // Secondary sort by total projects
        }
        return bRate - aRate;
      })
      .slice(0, 10);

    // Activity distribution
    const projectDistribution = {
      '0': contacts.filter(c => c.total_projects === 0).length,
      '1': contacts.filter(c => c.total_projects === 1).length,
      '2-5': contacts.filter(c => c.total_projects >= 2 && c.total_projects <= 5).length,
      '6-10': contacts.filter(c => c.total_projects >= 6 && c.total_projects <= 10).length,
      '10+': contacts.filter(c => c.total_projects > 10).length
    };

    return {
      totalContacts,
      activeContacts,
      totalProjects,
      totalSuccessfulProjects,
      overallSuccessRate,
      topCompanies,
      performanceTiers,
      topPerformers,
      projectDistribution
    };
  }, [contacts]);

  return (
    <div className="space-y-6">
      {/* Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <Users className="h-5 w-5 text-blue-600" />
              <div>
                <div className="text-2xl font-bold">{analytics.totalContacts}</div>
                <div className="text-xs text-muted-foreground">Gesamt Kontakte</div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <Activity className="h-5 w-5 text-green-600" />
              <div>
                <div className="text-2xl font-bold">{analytics.activeContacts}</div>
                <div className="text-xs text-muted-foreground">Aktive Kontakte</div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <Target className="h-5 w-5 text-purple-600" />
              <div>
                <div className="text-2xl font-bold">{analytics.totalProjects}</div>
                <div className="text-xs text-muted-foreground">Gesamt Projekte</div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <TrendingUp className="h-5 w-5 text-emerald-600" />
              <div>
                <div className="text-2xl font-bold">{Math.round(analytics.overallSuccessRate)}%</div>
                <div className="text-xs text-muted-foreground">Erfolgsquote</div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="performance" className="space-y-4">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="performance">Performance</TabsTrigger>
          <TabsTrigger value="companies">Unternehmen</TabsTrigger>
          <TabsTrigger value="distribution">Verteilung</TabsTrigger>
          <TabsTrigger value="insights">Insights</TabsTrigger>
        </TabsList>

        {/* Performance Analysis */}
        <TabsContent value="performance" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Performance Tiers */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Award className="h-5 w-5" />
                  Kontakt Performance
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">Exzellent (≥70%)</span>
                    <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
                      {analytics.performanceTiers.excellent.length}
                    </Badge>
                  </div>
                  <Progress 
                    value={(analytics.performanceTiers.excellent.length / analytics.totalContacts) * 100} 
                    className="h-2"
                  />
                  
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">Gut (30-69%)</span>
                    <Badge variant="outline" className="bg-yellow-50 text-yellow-700 border-yellow-200">
                      {analytics.performanceTiers.good.length}
                    </Badge>
                  </div>
                  <Progress 
                    value={(analytics.performanceTiers.good.length / analytics.totalContacts) * 100} 
                    className="h-2"
                  />
                  
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">Schwach (&lt;30%)</span>
                    <Badge variant="outline" className="bg-red-50 text-red-700 border-red-200">
                      {analytics.performanceTiers.poor.length}
                    </Badge>
                  </div>
                  <Progress 
                    value={(analytics.performanceTiers.poor.length / analytics.totalContacts) * 100} 
                    className="h-2"
                  />
                  
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">Inaktiv</span>
                    <Badge variant="outline" className="bg-gray-50 text-gray-700 border-gray-200">
                      {analytics.performanceTiers.inactive.length}
                    </Badge>
                  </div>
                  <Progress 
                    value={(analytics.performanceTiers.inactive.length / analytics.totalContacts) * 100} 
                    className="h-2"
                  />
                </div>
              </CardContent>
            </Card>

            {/* Top Performers */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <TrendingUp className="h-5 w-5" />
                  Top Performer
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {analytics.topPerformers.slice(0, 5).map((contact, index) => {
                    const successRate = Math.round((contact.successful_projects / contact.total_projects) * 100);
                    return (
                      <div key={contact.id} className="flex items-center justify-between p-3 bg-muted/30 rounded-lg">
                        <div className="flex items-center gap-3">
                          <div className="w-6 h-6 bg-primary text-white rounded-full flex items-center justify-center text-xs font-bold">
                            {index + 1}
                          </div>
                          <div>
                            <div className="font-medium text-sm">
                              {contact.name || contact.email || 'Unbekannt'}
                            </div>
                            <div className="text-xs text-muted-foreground">
                              {contact.company}
                            </div>
                          </div>
                        </div>
                        <div className="text-right">
                          <div className="font-bold text-green-600">{successRate}%</div>
                          <div className="text-xs text-muted-foreground">
                            {contact.successful_projects}/{contact.total_projects}
                          </div>
                        </div>
                      </div>
                    );
                  })}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Company Analysis */}
        <TabsContent value="companies" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Building2 className="h-5 w-5" />
                Top Unternehmen nach Projektanzahl
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {analytics.topCompanies.map((company, index) => {
                  const successRate = company.totalProjects > 0 
                    ? Math.round((company.successfulProjects / company.totalProjects) * 100)
                    : 0;
                  
                  return (
                    <div key={company.name} className="space-y-2">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <span className="font-medium">{company.name}</span>
                          <Badge variant="outline" className="text-xs">
                            {company.contactCount} Kontakte
                          </Badge>
                        </div>
                        <div className="text-right">
                          <div className="font-bold">{company.totalProjects} Projekte</div>
                          <div className="text-sm text-green-600">{successRate}% Erfolg</div>
                        </div>
                      </div>
                      <Progress value={successRate} className="h-2" />
                    </div>
                  );
                })}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Distribution Analysis */}
        <TabsContent value="distribution" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <BarChart3 className="h-5 w-5" />
                Projektverteilung pro Kontakt
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {Object.entries(analytics.projectDistribution).map(([range, count]) => (
                  <div key={range} className="space-y-2">
                    <div className="flex items-center justify-between">
                      <span className="font-medium">{range} Projekte</span>
                      <Badge variant="outline">{count} Kontakte</Badge>
                    </div>
                    <Progress 
                      value={(count / analytics.totalContacts) * 100} 
                      className="h-2"
                    />
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Insights */}
        <TabsContent value="insights" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
            <Card>
              <CardHeader>
                <CardTitle>📈 Positive Trends</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3 text-sm">
                <div className="p-3 bg-green-500/10 rounded-lg border border-green-500/20 dark:bg-green-500/20 dark:border-green-500/30">
                  <strong>Hohe Aktivität:</strong> {analytics.activeContacts} von {analytics.totalContacts} Kontakten sind aktiv 
                  ({Math.round((analytics.activeContacts / analytics.totalContacts) * 100)}%)
                </div>
                
                {analytics.performanceTiers.excellent.length > 0 && (
                  <div className="p-3 bg-green-500/10 rounded-lg border border-green-500/20 dark:bg-green-500/20 dark:border-green-500/30">
                    <strong>Top Performer:</strong> {analytics.performanceTiers.excellent.length} Kontakte haben eine Erfolgsquote von über 70%
                  </div>
                )}
                
                {analytics.overallSuccessRate > 50 && (
                  <div className="p-3 bg-green-500/10 rounded-lg border border-green-500/20 dark:bg-green-500/20 dark:border-green-500/30">
                    <strong>Gute Erfolgsquote:</strong> Durchschnittlich {Math.round(analytics.overallSuccessRate)}% Ihrer Projekte sind erfolgreich
                  </div>
                )}
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>⚠️ Verbesserungspotenzial</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3 text-sm">
                {analytics.performanceTiers.inactive.length > analytics.activeContacts && (
                  <div className="p-3 bg-yellow-500/10 rounded-lg border border-yellow-500/20 dark:bg-yellow-500/20 dark:border-yellow-500/30">
                    <strong>Viele inaktive Kontakte:</strong> {analytics.performanceTiers.inactive.length} Kontakte haben noch keine Projekte
                  </div>
                )}
                
                {analytics.performanceTiers.poor.length > 0 && (
                  <div className="p-3 bg-red-500/10 rounded-lg border border-red-500/20 dark:bg-red-500/20 dark:border-red-500/30">
                    <strong>Schwache Performer:</strong> {analytics.performanceTiers.poor.length} Kontakte haben eine niedrige Erfolgsquote (&lt;30%)
                  </div>
                )}
                
                {analytics.overallSuccessRate < 30 && (
                  <div className="p-3 bg-red-500/10 rounded-lg border border-red-500/20 dark:bg-red-500/20 dark:border-red-500/30">
                    <strong>Niedrige Erfolgsquote:</strong> Nur {Math.round(analytics.overallSuccessRate)}% Ihrer Projekte sind erfolgreich
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
};