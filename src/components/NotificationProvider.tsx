import { useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { NotificationManager } from '@/services/notificationManager';

interface NotificationProviderProps {
  children: React.ReactNode;
}

export const NotificationProvider = ({ children }: NotificationProviderProps) => {
  const { isAuthenticated, isLoading } = useAuth();

  useEffect(() => {
    // Don't initialize while still loading auth state
    if (isLoading) return;

    // Initialize notification system only for authenticated users
    if (isAuthenticated) {
      NotificationManager.initialize(true);
    } else {
      // Cleanup if user signs out
      NotificationManager.cleanup();
    }

    // Cleanup on unmount
    return () => {
      NotificationManager.cleanup();
    };
  }, [isAuthenticated, isLoading]);

  return <>{children}</>;
};