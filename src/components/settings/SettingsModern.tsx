import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { useTranslation } from 'react-i18next';
import { PageLayout } from '@/components/layout/PageLayout';
import { useCurrentPageConfig } from '@/hooks/usePageConfig';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Separator } from '@/components/ui/separator';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { useUserSettings } from '@/hooks/useUserSettings';
import { useApplications } from '@/hooks/useApplications';
import { useExport } from '@/hooks/useExport';
import { useImport } from '@/hooks/useImport';
import { toast } from '@/lib/toast';
import { ImportProgressModal } from '@/components/ui/import-progress-modal';
import { ImportConfirmationDialog } from '@/components/ui/import-confirmation-dialog';
import { 
  Download, 
  Upload, 
  Save, 
  Calendar, 
  Clock, 
  User, 
  Globe, 
  Phone, 
  Mail, 
  MapPin, 
  FileText, 
  CheckCircle, 
  FileSpreadsheet, 
  FileType,
  Settings as SettingsIcon,
  Briefcase,
  DollarSign,
  Star,
  Shield,
  Database,
  Eye,
  Edit3,
  UserCheck,
  Building,
  Lock,
  Key,
  ShieldCheck,
  RotateCcw,
  CalendarDays,
  CalendarCheck,
  Timer,
  StickyNote,
  UserCircle,
  AtSign,
  Smartphone,
  ExternalLink,
  Home,
  Coins,
  TrendingUp
} from 'lucide-react';
import { FollowUpSettings } from './FollowUpSettings';
import { supabase } from '@/integrations/supabase/client';
import { PasswordService, type PasswordChangeRequest } from '@/services/passwordService';

export const SettingsModern = () => {
  const { settings, saveSettings, isSaving, refetchSettings } = useUserSettings();
  const { t } = useTranslation('settings');
  const { applications: projects } = useApplications();
  const { exportToPDF, exportToExcel, exportToJSON, isExporting, progress: exportProgress } = useExport({
    projects: projects || [],
    settings
  });
  const { importFromFile, isImporting, progress: importProgress, result, showModal, closeModal } = useImport();

  // Profile data state
  const [fullName, setFullName] = useState('');
  const [address, setAddress] = useState('');
  const [website, setWebsite] = useState('');
  const [phone, setPhone] = useState('');
  const [professionalEmail, setProfessionalEmail] = useState('');
  const [hourlyRate, setHourlyRate] = useState('');

  // Availability state
  const [availabilityStartDate, setAvailabilityStartDate] = useState('');
  const [availabilityEndDate, setAvailabilityEndDate] = useState('');
  const [availabilityHours, setAvailabilityHours] = useState('40');
  const [availabilityNotes, setAvailabilityNotes] = useState('');

  // CV Upload state
  const [isUploadingCV, setIsUploadingCV] = useState(false);
  const [cvFileName, setCvFileName] = useState<string | null>(null);
  const [isDragOver, setIsDragOver] = useState(false);

  // Profile Picture state
  const [isUploadingProfilePicture, setIsUploadingProfilePicture] = useState(false);
  const [profilePictureUrl, setProfilePictureUrl] = useState<string | null>(null);

  // Import Confirmation state
  const [showImportConfirmation, setShowImportConfirmation] = useState(false);
  const [pendingImportFile, setPendingImportFile] = useState<File | null>(null);

  // Password change state
  const [currentPassword, setCurrentPassword] = useState('');
  const [newPassword, setNewPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [isChangingPassword, setIsChangingPassword] = useState(false);

  // Update state when settings load
  useEffect(() => {
    if (settings) {
      setFullName(settings.full_name || '');
      setAddress(settings.address || '');
      setWebsite(settings.website || '');
      setPhone(settings.phone || '');
      setProfessionalEmail(settings.professional_email || '');
      setHourlyRate(settings.hourly_rate_eur?.toString() || '');
      setAvailabilityStartDate(settings.availability_start_date || '');
      setAvailabilityEndDate(settings.availability_end_date || '');
      setAvailabilityHours(settings.availability_hours_per_week?.toString() || '40');
      setAvailabilityNotes(settings.availability_notes || '');
      
      // Check if CV exists
      if (settings.cv_pdf_url) {
        const fileName = settings.cv_pdf_url.split('/').pop();
        setCvFileName(fileName || 'CV.pdf');
      } else {
        setCvFileName(null);
      }

      // Set profile picture URL
      setProfilePictureUrl(settings.profile_picture_url || null);
    }
  }, [settings]);

  const processCVFile = async (file: File) => {

    if (file.type !== 'application/pdf') {
      toast.error(t('cv.errors.invalid_file_type'), t('cv.errors.pdf_required'));
      return;
    }

    setIsUploadingCV(true);
    
    try {
      const { data: { session } } = await supabase.auth.getSession();
      if (!session?.user) {
        throw new Error(t('cv.auth_error'));
      }

      // Delete old CV file if exists
      if (settings?.cv_pdf_url) {
        const oldUrl = settings.cv_pdf_url;
        const urlParts = oldUrl.split('/');
        const oldFileName = urlParts[urlParts.length - 1];
        const userId = urlParts[urlParts.length - 2];
        const oldFilePath = `${userId}/${oldFileName}`;
        
        await supabase.storage
          .from('cv-uploads')
          .remove([oldFilePath]);
      }

      // Upload new PDF to storage with consistent filename
      const fileName = `${session.user.id}/cv_${Date.now()}.pdf`;
      const { data: uploadData, error: uploadError } = await supabase.storage
        .from('cv-uploads')
        .upload(fileName, file, {
          contentType: 'application/pdf',
          upsert: true
        });

      if (uploadError) throw uploadError;

      // Get public URL
      const { data: urlData } = supabase.storage
        .from('cv-uploads')
        .getPublicUrl(fileName);

      // Save URL to user settings
      const { error: updateError } = await supabase
        .from('user_settings')
        .upsert({
          user_id: session.user.id,
          cv_pdf_url: urlData.publicUrl,
          updated_at: new Date().toISOString()
        }, {
          onConflict: 'user_id'
        });

      if (updateError) throw updateError;

      setCvFileName(file.name);
      await refetchSettings(); // Refresh settings to get the updated data
      
      toast.success(t('cv.errors.upload_success_title'), t('cv.errors.upload_success_message'));
      
    } catch (error) {
      console.error('CV upload error:', error);
      toast.error(t('cv.errors.upload_error_title'), error.message || t('cv.errors.upload_error_message'));
    } finally {
      setIsUploadingCV(false);
    }
  };

  const handleCVUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;
    
    await processCVFile(file);
    // Reset file input
    event.target.value = '';
  };

  const handleDrop = async (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
    
    if (isUploadingCV) return;
    
    const files = Array.from(e.dataTransfer.files);
    const pdfFile = files.find(file => file.type === 'application/pdf');
    
    if (!pdfFile) {
      toast.error(t('cv.errors.invalid_file_type'), t('cv.errors.pdf_required'));
      return;
    }
    
    await processCVFile(pdfFile);
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    if (!isUploadingCV) {
      setIsDragOver(true);
    }
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
  };

  const handleCVDelete = async () => {
    if (!settings?.cv_pdf_url) return;

    setIsUploadingCV(true);
    
    try {
      const { data: { session } } = await supabase.auth.getSession();
      if (!session?.user) {
        throw new Error(t('cv.auth_error'));
      }

      // Parse the CV URL to get the file path
      const cvUrl = settings.cv_pdf_url;
      const urlParts = cvUrl.split('/');
      const fileName = urlParts[urlParts.length - 1];
      const userId = urlParts[urlParts.length - 2];
      const filePath = `${userId}/${fileName}`;
      
      // Delete file from storage
      const { error: deleteError } = await supabase.storage
        .from('cv-uploads')
        .remove([filePath]);

      if (deleteError) throw deleteError;

      // Update user settings to remove CV URL
      const { error: updateError } = await supabase
        .from('user_settings')
        .update({
          cv_pdf_url: null,
          updated_at: new Date().toISOString()
        })
        .eq('user_id', session.user.id);

      if (updateError) throw updateError;

      setCvFileName(null);
      await refetchSettings(); // Refresh settings to get the updated data
      
      toast.success(t('cv.delete_success_title'), t('cv.delete_success_message'));
      
    } catch (error) {
      console.error('CV delete error:', error);
      toast.error(t('cv.delete_error_title'), error.message || t('cv.delete_error_message'));
    } finally {
      setIsUploadingCV(false);
    }
  };

  const handleProfilePictureUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    // Validate file type
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];
    if (!allowedTypes.includes(file.type)) {
      toast.error(t('profile_picture.errors.invalid_file_type'), t('profile_picture.errors.format_required'));
      return;
    }

    // Validate file size (max 5MB)
    if (file.size > 5 * 1024 * 1024) {
      toast.error(t('profile_picture.errors.file_too_large'), t('profile_picture.errors.size_limit'));
      return;
    }

    setIsUploadingProfilePicture(true);
    
    try {
      const { data: { session } } = await supabase.auth.getSession();
      if (!session?.user) {
        throw new Error(t('cv.auth_error'));
      }

      // Delete old profile picture if exists
      if (profilePictureUrl) {
        const oldFileName = profilePictureUrl.split('/').pop();
        if (oldFileName) {
          await supabase.storage
            .from('profile-pictures')
            .remove([`${session.user.id}/${oldFileName}`]);
        }
      }

      // Upload new profile picture
      const fileExt = file.name.split('.').pop();
      const fileName = `${session.user.id}/profile_${Date.now()}.${fileExt}`;
      
      const { data: uploadData, error: uploadError } = await supabase.storage
        .from('profile-pictures')
        .upload(fileName, file, {
          contentType: file.type,
          upsert: true
        });

      if (uploadError) throw uploadError;

      // Get public URL
      const { data: urlData } = supabase.storage
        .from('profile-pictures')
        .getPublicUrl(fileName);

      // Save URL to user settings
      const { error: updateError } = await supabase
        .from('user_settings')
        .upsert({
          user_id: session.user.id,
          profile_picture_url: urlData.publicUrl,
          updated_at: new Date().toISOString()
        }, {
          onConflict: 'user_id'
        });

      if (updateError) throw updateError;

      setProfilePictureUrl(urlData.publicUrl);
      
      toast.success(t('profile_picture.errors.upload_success_title'), t('profile_picture.errors.upload_success_message'));
      
    } catch (error) {
      console.error('Profile picture upload error:', error);
      toast.error(t('profile_picture.errors.upload_error_title'), error.message || t('profile_picture.errors.upload_error_message'));
    } finally {
      setIsUploadingProfilePicture(false);
      // Reset file input
      event.target.value = '';
    }
  };

  const handleSaveSettings = async (e: React.FormEvent) => {
    e.preventDefault();
    
    try {
      await saveSettings({
        // Profile data
        full_name: fullName || undefined,
        address: address || undefined,
        website: website || undefined,
        phone: phone || undefined,
        professional_email: professionalEmail || undefined,
        hourly_rate_eur: hourlyRate ? parseInt(hourlyRate) : undefined,
        // Availability data
        availability_start_date: availabilityStartDate || undefined,
        availability_end_date: availabilityEndDate || undefined,
        availability_hours_per_week: availabilityHours ? parseInt(availabilityHours) : undefined,
        availability_notes: availabilityNotes || undefined
      });
    } catch (error) {
      console.error('Error saving settings:', error);
    }
  };

  const handleImportData = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    // Validate file type
    if (!file.name.toLowerCase().endsWith('.json')) {
      toast.error(t('data.import.errors.invalid_file_type'), t('data.import.errors.json_required'));
      event.target.value = '';
      return;
    }

    // Validate file size (50MB limit)
    const maxSize = 50 * 1024 * 1024; // 50MB in bytes
    if (file.size > maxSize) {
      toast.error(t('data.import.errors.file_too_large'), t('data.import.errors.size_limit', { size: (file.size / 1024 / 1024).toFixed(1) }));
      event.target.value = '';
      return;
    }

    // Store file and show confirmation dialog
    setPendingImportFile(file);
    setShowImportConfirmation(true);
    
    // Reset file input immediately
    event.target.value = '';
  };

  const handleImportConfirmation = async (overwriteDuplicates: boolean) => {
    if (!pendingImportFile) return;

    try {
      await importFromFile(pendingImportFile, { overwriteDuplicates });
    } finally {
      setPendingImportFile(null);
    }
  };

  const handleImportCancel = () => {
    setShowImportConfirmation(false);
    setPendingImportFile(null);
  };

  const handlePasswordChange = async (e: React.FormEvent) => {
    e.preventDefault();
    
    setIsChangingPassword(true);
    
    const request: PasswordChangeRequest = {
      currentPassword,
      newPassword,
      confirmPassword
    };

    const result = await PasswordService.changePassword(request);

    if (result.success) {
      // Clear form on success
      setCurrentPassword('');
      setNewPassword('');
      setConfirmPassword('');
      
      toast.success(t('security.success.title'), t('security.success.message'));
    } else {
      toast.error(t('security.errors.title'), result.error || t('security.errors.message'));
    }

    setIsChangingPassword(false);
  };

  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(n => n[0])
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  const completionStats = {
    profileComplete: !!(fullName && professionalEmail && phone && hourlyRate),
    availabilitySet: !!(availabilityStartDate && availabilityHours),
    cvUploaded: !!cvFileName
  };

  const completionCount = Object.values(completionStats).filter(Boolean).length;
  const completionPercentage = Math.round((completionCount / 3) * 100);
  const pageConfig = useCurrentPageConfig();

  return (
    <PageLayout
      title={pageConfig.title}
      description={pageConfig.description}
    >

        {/* Profile Hero Section */}
        <Card className="mb-6 sm:mb-8 shadow-sm bg-gradient-to-r from-primary/5 via-primary/2 to-background border">
          <CardContent className="p-6">
            <div className="flex flex-col sm:flex-row items-start sm:items-center gap-6">
              {/* Avatar */}
              <div className="relative group">
                <div 
                  className="cursor-pointer relative"
                  onClick={() => document.getElementById('profile-picture-upload')?.click()}
                >
                  <Avatar className="w-20 h-20 sm:w-24 sm:h-24 border-4 border-background shadow-lg transition-all group-hover:brightness-75">
                    <AvatarImage src={profilePictureUrl || ""} alt={fullName} />
                    <AvatarFallback className="text-xl font-bold bg-primary text-primary-foreground">
                      {fullName ? getInitials(fullName) : <User className="h-8 w-8" />}
                    </AvatarFallback>
                  </Avatar>
                  
                  {/* Overlay for upload hint */}
                  <div className="absolute inset-0 bg-black/50 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-200 flex items-center justify-center">
                    {isUploadingProfilePicture ? (
                      <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-white"></div>
                    ) : (
                      <Upload className="h-6 w-6 text-white" />
                    )}
                  </div>
                </div>
                
                <div className="absolute -bottom-1 -right-1 bg-background rounded-full p-1 shadow-md">
                  <UserCheck className="h-4 w-4 text-primary" />
                </div>
                
                {/* Hidden file input */}
                <Input
                  id="profile-picture-upload"
                  type="file"
                  accept="image/jpeg,image/jpg,image/png,image/webp"
                  onChange={handleProfilePictureUpload}
                  disabled={isUploadingProfilePicture}
                  className="hidden"
                />
              </div>

              {/* Profile Info */}
              <div className="flex-1 space-y-4">
                <div>
                  <h2 className="text-xl sm:text-2xl font-bold text-foreground">
                    {fullName || t('hero.your_name')}
                  </h2>
                  <div className="flex flex-wrap items-center gap-4 mt-2">
                    {professionalEmail && (
                      <div className="flex items-center gap-1 text-sm text-muted-foreground">
                        <Mail className="h-4 w-4" />
                        {professionalEmail}
                      </div>
                    )}
                    {phone && (
                      <div className="flex items-center gap-1 text-sm text-muted-foreground">
                        <Phone className="h-4 w-4" />
                        {phone}
                      </div>
                    )}
                    {hourlyRate && (
                      <div className="flex items-center gap-1 text-sm font-medium text-primary">
                        <DollarSign className="h-4 w-4" />
                        €{hourlyRate}/h
                      </div>
                    )}
                  </div>
                </div>

                {/* Completion Status */}
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">{t('profile.completion')}</span>
                    <span className="text-sm text-muted-foreground">{completionPercentage}%</span>
                  </div>
                  <Progress value={completionPercentage} className="h-2" />
                  <div className="flex flex-wrap gap-2 mt-2">
                    <Badge variant={completionStats.profileComplete ? "default" : "secondary"} className="text-xs">
                      <User className="h-3 w-3 mr-1" />
                      {t('profile.completion_badges.profile')} {completionStats.profileComplete ? '✓' : '○'}
                    </Badge>
                    <Badge variant={completionStats.availabilitySet ? "default" : "secondary"} className="text-xs">
                      <Calendar className="h-3 w-3 mr-1" />
                      {t('profile.completion_badges.availability')} {completionStats.availabilitySet ? '✓' : '○'}
                    </Badge>
                    <Badge variant={completionStats.cvUploaded ? "default" : "secondary"} className="text-xs">
                      <FileText className="h-3 w-3 mr-1" />
                      {t('profile.completion_badges.cv')} {completionStats.cvUploaded ? '✓' : '○'}
                    </Badge>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Settings Content with Tabs */}
        <Tabs defaultValue="profile" className="space-y-6">
          <TabsList className="grid w-full grid-cols-2 sm:grid-cols-5 h-auto p-1">
            <TabsTrigger 
              value="profile" 
              className="flex items-center gap-2 py-2 px-3 data-[state=active]:bg-primary data-[state=active]:text-primary-foreground"
            >
              <User className="h-4 w-4" />
              <span className="hidden sm:inline">{t('tabs.profile')}</span>
            </TabsTrigger>
            <TabsTrigger 
              value="cv" 
              className="flex items-center gap-2 py-2 px-3 data-[state=active]:bg-primary data-[state=active]:text-primary-foreground"
            >
              <FileText className="h-4 w-4" />
              <span className="hidden sm:inline">{t('tabs.cv')}</span>
            </TabsTrigger>
            <TabsTrigger 
              value="security" 
              className="flex items-center gap-2 py-2 px-3 data-[state=active]:bg-primary data-[state=active]:text-primary-foreground"
            >
              <Lock className="h-4 w-4" />
              <span className="hidden sm:inline">{t('tabs.security')}</span>
            </TabsTrigger>
            <TabsTrigger 
              value="followup" 
              className="flex items-center gap-2 py-2 px-3 data-[state=active]:bg-primary data-[state=active]:text-primary-foreground"
            >
              <Mail className="h-4 w-4" />
              <span className="hidden sm:inline">{t('tabs.followup')}</span>
            </TabsTrigger>
            <TabsTrigger 
              value="data" 
              className="flex items-center gap-2 py-2 px-3 data-[state=active]:bg-primary data-[state=active]:text-primary-foreground"
            >
              <Database className="h-4 w-4" />
              <span className="hidden sm:inline">{t('tabs.data')}</span>
            </TabsTrigger>
          </TabsList>

          {/* Profile Tab */}
          <TabsContent value="profile" className="space-y-6">
            <Card className="shadow-sm">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <User className="h-5 w-5" />
                  {t('profile.title')}
                </CardTitle>
                <CardDescription>
                  {t('profile.description')}
                </CardDescription>
              </CardHeader>
              <CardContent>
                <form onSubmit={handleSaveSettings} className="space-y-6">
                  {/* Personal Information */}
                  <div className="space-y-4">
                    <div className="flex items-center gap-2 mb-4">
                      <UserCircle className="h-4 w-4 text-blue-600" />
                      <h3 className="font-medium">{t('profile.personal_data')}</h3>
                    </div>
                    
                    <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="full-name" className="flex items-center gap-2">
                          <User className="h-4 w-4 text-slate-600" />
                          {t('profile.labels.full_name')}
                        </Label>
                        <Input
                          id="full-name"
                          value={fullName}
                          onChange={(e) => setFullName(e.target.value)}
                          placeholder={t('profile.placeholders.full_name')}
                        />
                      </div>
                      
                      <div className="space-y-2">
                        <Label htmlFor="professional-email" className="flex items-center gap-2">
                          <AtSign className="h-4 w-4 text-amber-600" />
                          {t('profile.labels.professional_email')}
                        </Label>
                        <Input
                          id="professional-email"
                          type="email"
                          value={professionalEmail}
                          onChange={(e) => setProfessionalEmail(e.target.value)}
                          placeholder={t('profile.placeholders.professional_email')}
                        />
                      </div>
                    </div>

                    <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="phone" className="flex items-center gap-2">
                          <Smartphone className="h-4 w-4 text-green-600" />
                          {t('profile.labels.phone')}
                        </Label>
                        <Input
                          id="phone"
                          type="tel"
                          value={phone}
                          onChange={(e) => setPhone(e.target.value)}
                          placeholder={t('profile.placeholders.phone')}
                        />
                      </div>
                      
                      <div className="space-y-2">
                        <Label htmlFor="website" className="flex items-center gap-2">
                          <ExternalLink className="h-4 w-4 text-indigo-600" />
                          {t('profile.labels.website')}
                        </Label>
                        <Input
                          id="website"
                          type="url"
                          value={website}
                          onChange={(e) => setWebsite(e.target.value)}
                          placeholder={t('profile.placeholders.website')}
                        />
                      </div>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="address" className="flex items-center gap-2">
                        <Home className="h-4 w-4 text-teal-600" />
                        {t('profile.labels.address')}
                      </Label>
                      <Textarea
                        id="address"
                        value={address}
                        onChange={(e) => setAddress(e.target.value)}
                        placeholder={t('profile.placeholders.address')}
                        rows={3}
                      />
                    </div>
                  </div>

                  <Separator />

                  {/* Financial Information */}
                  <div className="space-y-4">
                    <div className="flex items-center gap-2 mb-4">
                      <TrendingUp className="h-4 w-4 text-emerald-600" />
                      <h3 className="font-medium">{t('profile.financial_info')}</h3>
                    </div>
                    
                    <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="hourly-rate" className="flex items-center gap-2">
                          <Coins className="h-4 w-4 text-yellow-600" />
                          {t('profile.labels.hourly_rate')}
                        </Label>
                        <div className="flex items-center gap-2">
                          <span className="text-muted-foreground">€</span>
                          <Input
                            id="hourly-rate"
                            type="number"
                            min="1"
                            max="1000"
                            value={hourlyRate}
                            onChange={(e) => setHourlyRate(e.target.value)}
                            placeholder={t('profile.placeholders.hourly_rate')}
                            className="flex-1"
                          />
                          <span className="text-sm text-muted-foreground whitespace-nowrap">{t('profile.labels.hourly_rate_suffix')}</span>
                        </div>
                        <p className="text-xs text-muted-foreground">
                          {t('profile.labels.hourly_rate_note')}
                        </p>
                      </div>
                    </div>
                  </div>

                  <Separator />

                  {/* Availability Information */}
                  <div className="space-y-4">
                    <div className="flex items-center gap-2 mb-4">
                      <CalendarCheck className="h-4 w-4 text-primary" />
                      <h3 className="font-medium">{t('profile.availability_info')}</h3>
                    </div>
                    
                    <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="start-date" className="flex items-center gap-2">
                          <CalendarDays className="h-4 w-4 text-green-600" />
                          {t('profile.labels.available_from')}
                        </Label>
                        <Input
                          id="start-date"
                          type="date"
                          value={availabilityStartDate}
                          onChange={(e) => setAvailabilityStartDate(e.target.value)}
                        />
                      </div>
                      
                      <div className="space-y-2">
                        <Label htmlFor="end-date" className="flex items-center gap-2">
                          <Calendar className="h-4 w-4 text-red-600" />
                          {t('profile.labels.available_until')}
                        </Label>
                        <Input
                          id="end-date"
                          type="date"
                          value={availabilityEndDate}
                          onChange={(e) => setAvailabilityEndDate(e.target.value)}
                        />
                      </div>
                    </div>
                    
                    <div className="space-y-2">
                      <Label htmlFor="hours" className="flex items-center gap-2">
                        <Timer className="h-4 w-4 text-blue-600" />
                        {t('profile.labels.hours_per_week')}
                      </Label>
                      <div className="flex items-center gap-4">
                        <Input
                          id="hours"
                          type="number"
                          min="1"
                          max="80"
                          value={availabilityHours}
                          onChange={(e) => setAvailabilityHours(e.target.value)}
                          className="w-32"
                        />
                        <span className="text-sm text-muted-foreground">{t('profile.labels.hours_per_week_suffix')}</span>
                      </div>
                      <p className="text-xs text-muted-foreground">
                        {t('profile.labels.hours_per_week_note')}
                      </p>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="notes" className="flex items-center gap-2">
                        <StickyNote className="h-4 w-4 text-purple-600" />
                        {t('profile.labels.availability_notes')}
                      </Label>
                      <Textarea
                        id="notes"
                        placeholder={t('profile.placeholders.availability_notes')}
                        value={availabilityNotes}
                        onChange={(e) => setAvailabilityNotes(e.target.value)}
                        rows={3}
                      />
                    </div>
                  </div>

                  <Button type="submit" disabled={isSaving} className="w-full sm:w-auto">
                    {isSaving ? (
                      <>
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-background mr-2"></div>
                        {t('profile.buttons.saving')}
                      </>
                    ) : (
                      <>
                        <Save className="h-4 w-4 mr-2" />
                        {t('profile.buttons.save')}
                      </>
                    )}
                  </Button>
                </form>
              </CardContent>
            </Card>
          </TabsContent>

          {/* CV Tab */}
          <TabsContent value="cv" className="space-y-6">
            <Card className="shadow-sm">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <FileText className="h-5 w-5" />
                  {t('cv.title')}
                </CardTitle>
                <CardDescription>
                  {t('cv.description')}
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                {/* CV Status */}
                {cvFileName ? (
                  <div className="p-4 bg-success/10 border border-success/20 rounded-lg">
                    <div className="flex items-start justify-between gap-4">
                      <div className="flex items-center gap-3">
                        <div className="p-2 bg-success/20 rounded-full">
                          <CheckCircle className="h-6 w-6 text-success" />
                        </div>
                        <div>
                          <h3 className="font-medium text-foreground">{t('cv.upload_success')}</h3>
                          <p className="text-sm text-muted-foreground mt-1">{cvFileName}</p>
                          <div className="flex items-center gap-4 mt-2">
                            <Badge variant="outline" className="text-xs">
                              <Shield className="h-3 w-3 mr-1" />
                              {t('cv.security_badges.secure')}
                            </Badge>
                            <Badge variant="outline" className="text-xs">
                              <Star className="h-3 w-3 mr-1" />
                              {t('cv.security_badges.ai_ready')}
                            </Badge>
                          </div>
                        </div>
                      </div>
                      
                      <div className="flex items-center gap-2">
                        <Button 
                          size="sm"
                          variant="outline"
                          onClick={async () => {
                            if (settings?.cv_pdf_url) {
                              try {
                                const urlParts = settings.cv_pdf_url.split('/');
                                const fileName = urlParts[urlParts.length - 1];
                                const userId = urlParts[urlParts.length - 2];
                                const filePath = `${userId}/${fileName}`;
                                
                                const { data: signedUrlData, error: signedUrlError } = await supabase.storage
                                  .from('cv-uploads')
                                  .createSignedUrl(filePath, 60);
                                
                                if (signedUrlError) {
                                  toast.error(t('cv.errors.load_error'), t('cv.errors.load_failed'));
                                  return;
                                }
                                
                                window.open(signedUrlData.signedUrl, '_blank', 'noopener,noreferrer');
                              } catch (error) {
                                toast.error(t('cv.errors.open_error'), t('cv.errors.open_failed'));
                              }
                            }
                          }}
                        >
                          <Eye className="h-4 w-4 mr-1" />
                          {t('cv.view')}
                        </Button>
                        <Button 
                          size="sm"
                          variant="destructive"
                          onClick={handleCVDelete}
                          disabled={isUploadingCV}
                        >
                          {isUploadingCV ? (
                            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-background mr-1"></div>
                          ) : (
                            <FileText className="h-4 w-4 mr-1" />
                          )}
                          {t('cv.delete')}
                        </Button>
                      </div>
                    </div>
                  </div>
                ) : (
                  <div className="p-4 border-2 border-dashed border-muted-foreground/25 rounded-lg text-center">
                    <FileText className="h-12 w-12 text-muted-foreground mx-auto mb-3" />
                    <h3 className="font-medium text-foreground mb-1">{t('cv.no_cv_title')}</h3>
                    <p className="text-sm text-muted-foreground mb-4">
                      {t('cv.no_cv_message')}
                    </p>
                  </div>
                )}
                
                {/* Upload Section */}
                <div className="space-y-4">
                  <Label className="text-base font-medium">
                    {cvFileName ? t('cv.replace_cv') : t('cv.upload_cv')}
                  </Label>
                  
                  {/* Custom Upload Area */}
                  <div 
                    onClick={() => !isUploadingCV && document.getElementById('cv-upload')?.click()}
                    onDrop={handleDrop}
                    onDragOver={handleDragOver}
                    onDragLeave={handleDragLeave}
                    className={`
                      relative border-2 border-dashed rounded-lg p-6 text-center cursor-pointer transition-all
                      ${isUploadingCV 
                        ? 'border-primary/50 bg-primary/5 cursor-not-allowed' 
                        : isDragOver
                          ? 'border-primary bg-primary/10'
                          : 'border-muted-foreground/25 hover:border-primary/50 hover:bg-primary/5'
                      }
                    `}
                  >
                    <input
                      id="cv-upload"
                      type="file"
                      accept=".pdf"
                      onChange={handleCVUpload}
                      disabled={isUploadingCV}
                      className="hidden"
                    />
                    
                    <div className="flex flex-col items-center gap-3">
                      {isUploadingCV ? (
                        <>
                          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
                          <div className="space-y-1">
                            <p className="text-sm font-medium text-primary">{t('cv.uploading')}</p>
                            <p className="text-xs text-muted-foreground">
                              {t('cv.upload_progress')}
                            </p>
                          </div>
                        </>
                      ) : (
                        <>
                          <div className="p-3 bg-primary/10 rounded-full">
                            <Upload className="h-6 w-6 text-primary" />
                          </div>
                          <div className="space-y-1">
                            <p className="text-sm font-medium text-foreground">
                              {cvFileName ? t('cv.click_to_replace') : t('cv.click_to_upload')}
                            </p>
                            <p className="text-xs text-muted-foreground">
                              {t('cv.drag_drop_hint')}
                            </p>
                          </div>
                        </>
                      )}
                    </div>
                    
                    {!isUploadingCV && (
                      <div className="absolute inset-0 rounded-lg border-2 border-transparent hover:border-primary/20 transition-colors"></div>
                    )}
                  </div>
                  
                  <div className="text-sm text-muted-foreground space-y-2 bg-muted/30 p-4 rounded-lg">
                    <div className="flex items-center gap-2">
                      <CheckCircle className="h-4 w-4 text-success flex-shrink-0" />
                      <span>{t('cv.requirements.pdf_only')}</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <CheckCircle className="h-4 w-4 text-success flex-shrink-0" />
                      <span>{t('cv.requirements.auto_use')}</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <CheckCircle className="h-4 w-4 text-success flex-shrink-0" />
                      <span>{t('cv.requirements.secure')}</span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Data Management Tab */}
          <TabsContent value="data" className="space-y-6">
            <Card className="shadow-sm">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Database className="h-5 w-5" />
                  {t('data.title')}
                </CardTitle>
                <CardDescription>
                  {t('data.description')}
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-8">
                {/* Export Section */}
                <div className="space-y-6">
                  <div className="flex items-center gap-2">
                    <Download className="h-5 w-5 text-primary" />
                    <h3 className="text-lg font-medium">{t('data.export.title')}</h3>
                  </div>

                  {/* Progress indicator */}
                  {exportProgress && (
                    <div className="space-y-3 p-4 bg-primary/5 border border-primary/20 rounded-lg">
                      <div className="flex items-center justify-between text-sm">
                        <span className="font-medium">{exportProgress.message}</span>
                        <Badge variant="outline">{exportProgress.progress}%</Badge>
                      </div>
                      <Progress value={exportProgress.progress} className="h-2" />
                    </div>
                  )}

                  {/* Export Options */}
                  <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
                    <Card className="p-4 hover:shadow-md transition-shadow cursor-pointer">
                      <div className="space-y-3">
                        <div className="flex items-center gap-2">
                          <FileType className="h-5 w-5 text-red-500" />
                          <span className="font-medium">{t('data.export.pdf.title')}</span>
                        </div>
                        <p className="text-xs text-muted-foreground">
                          {t('data.export.pdf.description')}
                        </p>
                        <Button
                          onClick={() => exportToPDF({ includeSettings: true, includeNotes: true })}
                          variant="outline"
                          disabled={isExporting}
                          className="w-full"
                          size="sm"
                        >
                          <Download className="h-4 w-4 mr-2" />
                          {t('data.export.pdf.button')}
                        </Button>
                      </div>
                    </Card>

                    <Card className="p-4 hover:shadow-md transition-shadow cursor-pointer">
                      <div className="space-y-3">
                        <div className="flex items-center gap-2">
                          <FileSpreadsheet className="h-5 w-5 text-green-500" />
                          <span className="font-medium">{t('data.export.excel.title')}</span>
                        </div>
                        <p className="text-xs text-muted-foreground">
                          {t('data.export.excel.description')}
                        </p>
                        <Button
                          onClick={() => exportToExcel({ includeSettings: true, includeNotes: true })}
                          variant="outline"
                          disabled={isExporting}
                          className="w-full"
                          size="sm"
                        >
                          <Download className="h-4 w-4 mr-2" />
                          {t('data.export.excel.button')}
                        </Button>
                      </div>
                    </Card>

                    <Card className="p-4 hover:shadow-md transition-shadow cursor-pointer">
                      <div className="space-y-3">
                        <div className="flex items-center gap-2">
                          <Database className="h-5 w-5 text-blue-500" />
                          <span className="font-medium">{t('data.export.json.title')}</span>
                        </div>
                        <p className="text-xs text-muted-foreground">
                          {t('data.export.json.description')}
                        </p>
                        <Button
                          onClick={() => exportToJSON({ includeSettings: true })}
                          variant="outline"
                          disabled={isExporting}
                          className="w-full"
                          size="sm"
                        >
                          <Download className="h-4 w-4 mr-2" />
                          {t('data.export.json.button')}
                        </Button>
                      </div>
                    </Card>
                  </div>
                </div>

                <Separator />

                {/* Import Section */}
                <div className="space-y-6">
                  <div className="flex items-center gap-2">
                    <Upload className="h-5 w-5 text-primary" />
                    <h3 className="text-lg font-medium">{t('data.import.title')}</h3>
                  </div>

                  <div className="p-4 bg-amber-50 dark:bg-amber-950/20 border border-amber-200 dark:border-amber-800 rounded-lg">
                    <div className="flex items-start gap-3">
                      <Shield className="h-5 w-5 text-amber-600 mt-0.5 flex-shrink-0" />
                      <div>
                        <h4 className="font-medium text-amber-800 dark:text-amber-200">{t('data.import.warning_title')}</h4>
                        <p className="text-sm text-amber-700 dark:text-amber-300 mt-1">
                          {t('data.import.warning_message')}
                        </p>
                      </div>
                    </div>
                  </div>

                  <div className="space-y-4">
                    <Input
                      id="import-file"
                      type="file"
                      accept=".json"
                      onChange={handleImportData}
                      className="hidden"
                    />
                    <Button
                      variant="outline"
                      className="w-full sm:w-auto"
                      onClick={() => document.getElementById('import-file')?.click()}
                      disabled={isImporting}
                    >
                      {isImporting ? (
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-current mr-2"></div>
                      ) : (
                        <Upload className="h-4 w-4 mr-2" />
                      )}
                      {isImporting ? t('data.import.importing') : t('data.import.button')}
                    </Button>
                    
                    <p className="text-sm text-muted-foreground">
                      {t('data.import.note')}
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Security Tab */}
          <TabsContent value="security" className="space-y-6">
            <Card className="shadow-sm">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Lock className="h-5 w-5" />
                  {t('security.title')}
                </CardTitle>
                <CardDescription>
                  {t('security.description')}
                </CardDescription>
              </CardHeader>
              <CardContent>
                <form onSubmit={handlePasswordChange} className="space-y-6">
                  <div className="space-y-4">
                    <div className="flex items-center gap-2 mb-4">
                      <ShieldCheck className="h-4 w-4 text-primary" />
                      <h3 className="font-medium">{t('security.change_password')}</h3>
                    </div>
                    
                    <div className="space-y-4">
                      <div className="space-y-2">
                        <Label htmlFor="current-password" className="flex items-center gap-2">
                          <Lock className="h-4 w-4 text-muted-foreground" />
                          {t('security.labels.current_password')}
                        </Label>
                        <Input
                          id="current-password"
                          type="password"
                          value={currentPassword}
                          onChange={(e) => setCurrentPassword(e.target.value)}
                          placeholder={t('security.placeholders.current_password')}
                          required
                        />
                        <p className="text-xs text-muted-foreground">
                          {t('security.notes.current_password')}
                        </p>
                      </div>
                      
                      <div className="space-y-2">
                        <Label htmlFor="new-password" className="flex items-center gap-2">
                          <Key className="h-4 w-4 text-green-600" />
                          {t('security.labels.new_password')}
                        </Label>
                        <Input
                          id="new-password"
                          type="password"
                          value={newPassword}
                          onChange={(e) => setNewPassword(e.target.value)}
                          placeholder={t('security.placeholders.new_password')}
                          required
                          minLength={6}
                        />
                        <p className="text-xs text-muted-foreground">
                          {t('security.notes.new_password')}
                        </p>
                      </div>
                      
                      <div className="space-y-2">
                        <Label htmlFor="confirm-password" className="flex items-center gap-2">
                          <RotateCcw className="h-4 w-4 text-blue-600" />
                          {t('security.labels.confirm_password')}
                        </Label>
                        <Input
                          id="confirm-password"
                          type="password"
                          value={confirmPassword}
                          onChange={(e) => setConfirmPassword(e.target.value)}
                          placeholder={t('security.placeholders.confirm_password')}
                          required
                        />
                      </div>
                    </div>
                  </div>

                  <Button type="submit" disabled={isChangingPassword || !currentPassword || !newPassword || !confirmPassword} className="w-full sm:w-auto">
                    {isChangingPassword ? (
                      <>
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-background mr-2"></div>
                        {t('security.buttons.changing')}
                      </>
                    ) : (
                      <>
                        <Lock className="h-4 w-4 mr-2" />
                        {t('security.buttons.change_password')}
                      </>
                    )}
                  </Button>
                </form>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Follow-up Tab */}
          <TabsContent value="followup" className="space-y-6">
            <FollowUpSettings />
          </TabsContent>
        </Tabs>

      {/* Import Confirmation Dialog */}
      <ImportConfirmationDialog
        isOpen={showImportConfirmation}
        onClose={handleImportCancel}
        onConfirm={handleImportConfirmation}
        fileName={pendingImportFile?.name || ''}
      />

      {/* Import Progress Modal */}
      <ImportProgressModal
        isOpen={showModal}
        onClose={closeModal}
        progress={importProgress}
        result={result}
        isImporting={isImporting}
      />
    </PageLayout>
  );
};