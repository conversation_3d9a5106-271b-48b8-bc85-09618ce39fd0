import { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { useTranslation } from 'react-i18next';
import { Button } from '@/components/ui/button';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { 
  Mail, 
  Bell, 
  Clock, 
  TrendingUp, 
  Settings as SettingsIcon,
  Plus,
  Calendar
} from 'lucide-react';
import { FollowUpTemplateManager } from '@/components/followup/FollowUpTemplateManager';
import { useFollowUpTemplates } from '@/hooks/useFollowUpTemplates';
import { useFollowUpHistory } from '@/hooks/useFollowUpHistory';
import { useFollowUpSchedule } from '@/hooks/useFollowUpSchedule';

export const FollowUpSettings = () => {
  const [notificationsEnabled, setNotificationsEnabled] = useState(true);
  const [autoScheduleEnabled, setAutoScheduleEnabled] = useState(true);
  const { t } = useTranslation('followup');
  
  const { templates, activeTemplates, createDefaultTemplates } = useFollowUpTemplates();
  const { analytics } = useFollowUpHistory();
  const { dueFollowUps } = useFollowUpSchedule();

  const handleNotificationPermission = async () => {
    if (!("Notification" in window)) {
      alert(t('settings.notifications.unsupported_browser'));
      return;
    }

    if (Notification.permission === "granted") {
      setNotificationsEnabled(true);
    } else if (Notification.permission !== "denied") {
      const permission = await Notification.requestPermission();
      setNotificationsEnabled(permission === "granted");
    }
  };

  return (
    <div className="space-y-6">
      {/* Overview Stats */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-blue-100 dark:bg-blue-900 rounded-lg">
                <Mail className="w-5 h-5 text-blue-600 dark:text-blue-400" />
              </div>
              <div>
                <p className="text-sm text-muted-foreground">{t('settings.stats.active_templates')}</p>
                <p className="text-2xl font-semibold">{activeTemplates.length}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-orange-100 dark:bg-orange-900 rounded-lg">
                <Clock className="w-5 h-5 text-orange-600 dark:text-orange-400" />
              </div>
              <div>
                <p className="text-sm text-muted-foreground">{t('settings.stats.due_followups')}</p>
                <p className="text-2xl font-semibold">{dueFollowUps.length}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-green-100 dark:bg-green-900 rounded-lg">
                <TrendingUp className="w-5 h-5 text-green-600 dark:text-green-400" />
              </div>
              <div>
                <p className="text-sm text-muted-foreground">{t('settings.stats.response_rate')}</p>
                <p className="text-2xl font-semibold">
                  {analytics ? `${Math.round(analytics.response_rate)}%` : '0%'}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* General Settings */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <SettingsIcon className="w-5 h-5" />
            {t('settings.general.title')}
          </CardTitle>
          <CardDescription>
            {t('settings.general.description')}
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Auto-scheduling */}
          <div className="flex items-center justify-between p-4 border rounded-lg">
            <div className="space-y-1">
              <Label className="text-base font-medium">{t('settings.general.auto_followups')}</Label>
              <p className="text-sm text-muted-foreground">
                {t('settings.general.auto_followups_desc')}
              </p>
            </div>
            <Switch
              checked={autoScheduleEnabled}
              onCheckedChange={setAutoScheduleEnabled}
            />
          </div>

          {/* Notifications */}
          <div className="flex items-center justify-between p-4 border rounded-lg">
            <div className="space-y-1">
              <Label className="text-base font-medium">{t('settings.general.desktop_notifications')}</Label>
              <p className="text-sm text-muted-foreground">
                {t('settings.general.desktop_notifications_desc')}
              </p>
            </div>
            <div className="flex items-center gap-3">
              {Notification.permission === "granted" && (
                <Badge variant="secondary" className="text-green-700 bg-green-100">
                  {t('settings.general.activated')}
                </Badge>
              )}
              {Notification.permission === "denied" && (
                <Badge variant="secondary" className="text-red-700 bg-red-100">
                  {t('settings.general.blocked')}
                </Badge>
              )}
              <Switch
                checked={notificationsEnabled && Notification.permission === "granted"}
                onCheckedChange={handleNotificationPermission}
                disabled={Notification.permission === "denied"}
              />
            </div>
          </div>

          {/* Daily Digest */}
          <div className="flex items-center justify-between p-4 border rounded-lg">
            <div className="space-y-1">
              <Label className="text-base font-medium">{t('settings.general.daily_digest')}</Label>
              <p className="text-sm text-muted-foreground">
                {t('settings.general.daily_digest_desc')}
              </p>
              <Badge variant="outline" className="text-xs">
                {t('settings.general.coming_soon')}
              </Badge>
            </div>
            <Switch disabled />
          </div>
        </CardContent>
      </Card>

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Calendar className="w-5 h-5" />
            {t('settings.quick_actions.title')}
          </CardTitle>
          <CardDescription>
            {t('settings.quick_actions.description')}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {templates.length === 0 && (
              <Button 
                variant="outline" 
                onClick={createDefaultTemplates}
                className="flex items-center gap-2 h-auto py-4 px-6"
              >
                <Plus className="w-5 h-5" />
                <div className="text-left">
                  <div className="font-medium">{t('settings.quick_actions.create_default_templates')}</div>
                  <div className="text-sm text-muted-foreground">
                    {t('settings.quick_actions.create_default_templates_desc')}
                  </div>
                </div>
              </Button>
            )}
            
            {dueFollowUps.length > 0 && (
              <Button 
                variant="outline"
                className="flex items-center gap-2 h-auto py-4 px-6"
              >
                <Bell className="w-5 h-5 text-orange-500" />
                <div className="text-left">
                  <div className="font-medium">{t('settings.quick_actions.show_due_followups')}</div>
                  <div className="text-sm text-muted-foreground">
                    {t('settings.quick_actions.due_followups_waiting', { count: dueFollowUps.length, plural: dueFollowUps.length > 1 ? 's' : '' })}
                  </div>
                </div>
                <Badge variant="secondary" className="ml-auto">
                  {dueFollowUps.length}
                </Badge>
              </Button>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Analytics Preview */}
      {analytics && analytics.total_sent > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <TrendingUp className="w-5 h-5" />
              {t('settings.performance.title')}
            </CardTitle>
            <CardDescription>
              {t('settings.performance.description')}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="text-center">
                <p className="text-3xl font-bold text-blue-600 dark:text-blue-400">
                  {analytics.total_sent}
                </p>
                <p className="text-sm text-muted-foreground">{t('settings.performance.sent_followups')}</p>
              </div>
              <div className="text-center">
                <p className="text-3xl font-bold text-green-600 dark:text-green-400">
                  {Math.round(analytics.response_rate)}%
                </p>
                <p className="text-sm text-muted-foreground">{t('settings.stats.response_rate')}</p>
              </div>
              <div className="text-center">
                <p className="text-3xl font-bold text-purple-600 dark:text-purple-400">
                  {Math.round(analytics.avg_response_time_days)}
                </p>
                <p className="text-sm text-muted-foreground">{t('settings.performance.avg_response_time')}</p>
              </div>
            </div>
            
            {analytics.success_by_template.length > 0 && (
              <>
                <Separator className="my-6" />
                <div>
                  <h4 className="font-medium mb-3">{t('settings.performance.top_templates')}</h4>
                  <div className="space-y-2">
                    {analytics.success_by_template
                      .sort((a, b) => b.response_rate - a.response_rate)
                      .slice(0, 3)
                      .map((template, index) => (
                        <div key={template.template_id} className="flex items-center justify-between p-3 bg-muted/50 rounded-lg">
                          <div className="flex items-center gap-3">
                            <Badge variant={index === 0 ? "default" : "secondary"}>
                              #{index + 1}
                            </Badge>
                            <span className="font-medium">{template.template_name}</span>
                          </div>
                          <div className="text-right">
                            <div className="font-semibold text-green-600 dark:text-green-400">
                              {Math.round(template.response_rate)}%
                            </div>
                            <div className="text-xs text-muted-foreground">
                              {t('settings.performance.responses_count', { responses: template.response_count, total: template.sent_count })}
                            </div>
                          </div>
                        </div>
                      ))}
                  </div>
                </div>
              </>
            )}
          </CardContent>
        </Card>
      )}

      <Separator />

      {/* Template Management */}
      <div>
        <div className="mb-4">
          <h3 className="text-lg font-semibold">{t('settings.template_management.title')}</h3>
          <p className="text-sm text-muted-foreground">
            {t('settings.template_management.description')}
          </p>
        </div>
        <FollowUpTemplateManager />
      </div>
    </div>
  );
};