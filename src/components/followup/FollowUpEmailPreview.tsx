import { useState } from 'react';
import { format } from 'date-fns';
import { de } from 'date-fns/locale';
import { createFollowUpISOString } from '@/lib/dateUtils';
import { Calendar, Mail, ExternalLink, Copy, Edit, Clock } from 'lucide-react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { FollowUpService } from '@/services/followUpService';
import { useUserSettings } from '@/hooks/useUserSettings';
import { useFollowUpSchedule } from '@/hooks/useFollowUpSchedule';
import type { FollowUpTemplate, TemplateVariables } from '@/types/followup';
import type { ApplicationWithContact } from '@/types/applications';

interface FollowUpEmailPreviewProps {
  isOpen: boolean;
  onClose: () => void;
  template: FollowUpTemplate;
  application: ApplicationWithContact;
  onSchedule: (scheduleDate?: string) => void;
  defaultScheduleDate: string;
}

export const FollowUpEmailPreview = ({
  isOpen,
  onClose,
  template,
  application,
  onSchedule,
  defaultScheduleDate
}: FollowUpEmailPreviewProps) => {
  const [scheduleDate, setScheduleDate] = useState(defaultScheduleDate);
  const [scheduleTime, setScheduleTime] = useState('09:00');
  const [isEditing, setIsEditing] = useState(false);
  const [editedSubject, setEditedSubject] = useState('');
  const [editedBody, setEditedBody] = useState('');

  const { settings } = useUserSettings();
  const { isCreatingFromTemplate } = useFollowUpSchedule();

  // Create template variables for personalization
  const templateVariables: TemplateVariables = {
    project_name: application.project_name,
    contact_person: application.contact?.name || application.contact_person || 'Sehr geehrte Damen und Herren',
    company_name: application.company_name,
    user_name: settings?.full_name || 'Ihr Name',
    trigger_days: template.trigger_days,
    application_date: application.created_at ? format(new Date(application.created_at), 'dd.MM.yyyy', { locale: de }) : '',
    interview_date: application.interview_date ? format(new Date(application.interview_date), 'dd.MM.yyyy', { locale: de }) : undefined
  };

  // Generate personalized email content
  const personalizedSubject = FollowUpService.personalizeTemplate(template.subject, templateVariables);
  const personalizedBody = FollowUpService.personalizeTemplate(template.body, templateVariables);

  // Use edited content if in editing mode, otherwise use personalized template
  const finalSubject = isEditing ? editedSubject : personalizedSubject;
  const finalBody = isEditing ? editedBody : personalizedBody;

  // Generate mailto link
  const recipientEmail = application.contact?.email || '';
  const mailtoLink = FollowUpService.generateMailtoLink(
    recipientEmail,
    finalSubject,
    finalBody
  );

  const handleEditToggle = () => {
    if (!isEditing) {
      setEditedSubject(personalizedSubject);
      setEditedBody(personalizedBody);
    }
    setIsEditing(!isEditing);
  };

  const handleCopyToClipboard = async (text: string, type: string) => {
    try {
      await navigator.clipboard.writeText(text);
      // Could show a toast here
      console.log(`${type} copied to clipboard`);
    } catch (error) {
      console.error('Failed to copy to clipboard:', error);
    }
  };

  const handleSchedule = () => {
    // Validate inputs before scheduling
    if (!scheduleDate || !scheduleTime) {
      console.error('Missing date or time for scheduling');
      return;
    }
    
    // Use timezone-safe function to create ISO string
    const isoString = createFollowUpISOString(scheduleDate, scheduleTime);
    onSchedule(isoString);
  };

  const handleOpenInEmailClient = () => {
    window.open(mailtoLink, '_blank');
  };

  // Validate date to prevent Invalid time value errors
  const formattedScheduleDate = scheduleDate && !isNaN(new Date(scheduleDate).getTime()) 
    ? new Date(scheduleDate) 
    : new Date();

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Mail className="w-5 h-5" />
            Email-Vorschau: {template.name}
          </DialogTitle>
          <DialogDescription>
            Überprüfe und bearbeite deine Follow-up Email bevor du sie planst oder sendest.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Schedule Date Selection */}
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-base flex items-center gap-2">
                <Calendar className="w-4 h-4" />
                Terminplanung
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="schedule-date">Geplant für (Datum):</Label>
                  <Input
                    id="schedule-date"
                    type="date"
                    value={scheduleDate}
                    onChange={(e) => setScheduleDate(e.target.value)}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="schedule-time">Uhrzeit:</Label>
                  <div className="relative">
                    <Clock className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                    <Input
                      id="schedule-time"
                      type="time"
                      value={scheduleTime}
                      onChange={(e) => setScheduleTime(e.target.value)}
                      className="pl-10"
                    />
                  </div>
                </div>
              </div>
              <div className="mt-4 flex items-center justify-center">
                <Badge variant="outline" className="text-sm">
                  Geplant für: {format(formattedScheduleDate, 'dd.MM.yyyy', { locale: de })} um {scheduleTime} Uhr
                </Badge>
              </div>
            </CardContent>
          </Card>

          {/* Email Content */}
          <Card>
            <CardHeader className="flex flex-row items-center justify-between pb-3">
              <CardTitle className="text-base">Email-Inhalt</CardTitle>
              <div className="flex items-center gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleEditToggle}
                >
                  <Edit className="w-4 h-4" />
                  {isEditing ? 'Vorschau' : 'Bearbeiten'}
                </Button>
              </div>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Email Header Info */}
              <div className="bg-muted/50 rounded-lg p-4 space-y-2">
                <div className="flex items-center justify-between text-sm">
                  <span className="font-medium">An:</span>
                  <span className="text-muted-foreground">
                    {recipientEmail || 'Keine Email-Adresse hinterlegt'}
                  </span>
                </div>
                <div className="flex items-center justify-between text-sm">
                  <span className="font-medium">Von:</span>
                  <span className="text-muted-foreground">
                    {settings?.email || 'Deine Email-Adresse'}
                  </span>
                </div>
              </div>

              {/* Subject */}
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <Label>Betreff:</Label>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handleCopyToClipboard(finalSubject, 'Betreff')}
                  >
                    <Copy className="w-4 h-4" />
                  </Button>
                </div>
                {isEditing ? (
                  <Input
                    value={editedSubject}
                    onChange={(e) => setEditedSubject(e.target.value)}
                    placeholder="Email-Betreff"
                  />
                ) : (
                  <div className="bg-background border rounded-md p-3 font-medium">
                    {personalizedSubject}
                  </div>
                )}
              </div>

              {/* Body */}
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <Label>Nachricht:</Label>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handleCopyToClipboard(finalBody, 'Nachricht')}
                  >
                    <Copy className="w-4 h-4" />
                  </Button>
                </div>
                {isEditing ? (
                  <Textarea
                    value={editedBody}
                    onChange={(e) => setEditedBody(e.target.value)}
                    placeholder="Email-Text"
                    className="min-h-[200px]"
                  />
                ) : (
                  <div className="bg-background border rounded-md p-3 whitespace-pre-wrap text-sm">
                    {personalizedBody}
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Template Variables Used */}
          {!isEditing && (
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-base">Verwendete Platzhalter</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-3 text-sm">
                  <div className="flex justify-between">
                    <span className="font-mono text-xs bg-muted px-2 py-1 rounded">
                      {'{project_name}'}
                    </span>
                    <span className="text-muted-foreground">
                      {templateVariables.project_name}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="font-mono text-xs bg-muted px-2 py-1 rounded">
                      {'{contact_person}'}
                    </span>
                    <span className="text-muted-foreground">
                      {templateVariables.contact_person}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="font-mono text-xs bg-muted px-2 py-1 rounded">
                      {'{company_name}'}
                    </span>
                    <span className="text-muted-foreground">
                      {templateVariables.company_name}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="font-mono text-xs bg-muted px-2 py-1 rounded">
                      {'{user_name}'}
                    </span>
                    <span className="text-muted-foreground">
                      {templateVariables.user_name}
                    </span>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}
        </div>

        <DialogFooter className="flex-col sm:flex-row gap-2">
          <div className="flex gap-2">
            <Button variant="outline" onClick={onClose}>
              Abbrechen
            </Button>
            {recipientEmail && (
              <Button
                variant="outline"
                onClick={handleOpenInEmailClient}
                className="flex items-center gap-2"
              >
                <ExternalLink className="w-4 h-4" />
                In Email-Client öffnen
              </Button>
            )}
          </div>
          <Button 
            onClick={handleSchedule} 
            disabled={isCreatingFromTemplate || !scheduleDate || !scheduleTime || isNaN(new Date(scheduleDate + 'T' + scheduleTime + ':00').getTime())}
            className="flex items-center gap-2"
          >
            {isCreatingFromTemplate && (
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white" />
            )}
            <Calendar className="w-4 h-4" />
            Follow-up planen
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};