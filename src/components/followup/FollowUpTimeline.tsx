import { useState } from 'react';
import { format } from 'date-fns';
import { de } from 'date-fns/locale';
import { 
  Calendar, 
  Clock, 
  Mail, 
  CheckCircle, 
  XCircle, 
  AlertCircle,
  Edit,
  Trash2,
  ExternalLink,
  MoreHorizontal
} from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog';
import { useFollowUpSchedule } from '@/hooks/useFollowUpSchedule';
import { useFollowUpHistory } from '@/hooks/useFollowUpHistory';
import { FollowUpService } from '@/services/followUpService';
import { cn } from '@/lib/utils';
import type { ApplicationWithContact } from '@/types/applications';
import type { ScheduledFollowUp, FollowUpHistory } from '@/types/followup';

interface FollowUpTimelineProps {
  application: ApplicationWithContact;
  compact?: boolean;
}

export const FollowUpTimeline = ({ application, compact = false }: FollowUpTimelineProps) => {
  const {
    getScheduledByApplication,
    markAsSent,
    deleteScheduledFollowUp,
    dismissFollowUp,
    isMarking,
    isDeleting,
    isDismissing
  } = useFollowUpSchedule();

  const {
    getHistoryByApplication,
    markAsResponded,
    unmarkResponse,
    isMarking: isMarkingHistory
  } = useFollowUpHistory();

  const scheduledFollowUps = getScheduledByApplication(application.id);
  const followUpHistory = getHistoryByApplication(application.id);

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'scheduled':
        return <Clock className="w-4 h-4 text-blue-500" />;
      case 'sent':
        return <Mail className="w-4 h-4 text-green-500" />;
      case 'dismissed':
        return <XCircle className="w-4 h-4 text-gray-500" />;
      case 'cancelled':
        return <XCircle className="w-4 h-4 text-red-500" />;
      default:
        return <AlertCircle className="w-4 h-4 text-yellow-500" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'scheduled':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300';
      case 'sent':
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300';
      case 'dismissed':
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300';
      case 'cancelled':
        return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300';
      default:
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300';
    }
  };

  const isOverdue = (scheduledDate: string) => {
    return new Date(scheduledDate) < new Date() && new Date().getHours() > 12; // Grace period until noon
  };

  const handleMarkAsSent = (followUp: ScheduledFollowUp) => {
    if (!followUp.template) return;
    
    // Generate personalized content for history
    const personalizedSubject = FollowUpService.personalizeTemplate(
      followUp.template.subject,
      {
        project_name: application.project_name,
        contact_person: application.contact?.name || application.contact_person || '',
        company_name: application.company_name,
        user_name: 'Your Name', // TODO: Get from user settings
        trigger_days: followUp.template.trigger_days,
        application_date: application.created_at || '',
        interview_date: application.interview_date
      }
    );

    const personalizedBody = FollowUpService.personalizeTemplate(
      followUp.template.body,
      {
        project_name: application.project_name,
        contact_person: application.contact?.name || application.contact_person || '',
        company_name: application.company_name,
        user_name: 'Your Name', // TODO: Get from user settings
        trigger_days: followUp.template.trigger_days,
        application_date: application.created_at || '',
        interview_date: application.interview_date
      }
    );

    markAsSent({
      followUpId: followUp.id,
      actualSubject: personalizedSubject,
      actualBody: personalizedBody
    });
  };

  const handleOpenInEmailClient = (followUp: ScheduledFollowUp) => {
    if (!followUp.template) return;
    
    const recipientEmail = application.contact?.email || '';
    const personalizedSubject = FollowUpService.personalizeTemplate(
      followUp.template.subject,
      {
        project_name: application.project_name,
        contact_person: application.contact?.name || application.contact_person || '',
        company_name: application.company_name,
        user_name: 'Your Name', // TODO: Get from user settings
        trigger_days: followUp.template.trigger_days,
        application_date: application.created_at || '',
        interview_date: application.interview_date
      }
    );

    const personalizedBody = FollowUpService.personalizeTemplate(
      followUp.template.body,
      {
        project_name: application.project_name,
        contact_person: application.contact?.name || application.contact_person || '',
        company_name: application.company_name,
        user_name: 'Your Name', // TODO: Get from user settings
        trigger_days: followUp.template.trigger_days,
        application_date: application.created_at || '',
        interview_date: application.interview_date
      }
    );

    const mailtoLink = FollowUpService.generateMailtoLink(
      recipientEmail,
      personalizedSubject,
      personalizedBody
    );

    window.open(mailtoLink, '_blank');
  };

  // Combine and sort timeline items
  const timelineItems = [
    ...scheduledFollowUps.map(item => ({ ...item, type: 'scheduled' as const })),
    ...followUpHistory.map(item => ({ ...item, type: 'history' as const }))
  ].sort((a, b) => {
    const dateA = new Date(a.type === 'scheduled' ? a.scheduled_date : a.sent_at);
    const dateB = new Date(b.type === 'scheduled' ? b.scheduled_date : b.sent_at);
    return dateB.getTime() - dateA.getTime(); // Newest first
  });

  if (timelineItems.length === 0) {
    return (
      <Card className={cn(compact && "border-0 shadow-none")}>
        <CardContent className={cn("text-center py-8 text-muted-foreground", compact && "py-4")}>
          <Mail className="w-8 h-8 mx-auto mb-2 opacity-50" />
          <p>Noch keine Follow-ups für diese Bewerbung.</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={cn(compact && "border-0 shadow-none")}>
      {!compact && (
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Mail className="w-5 h-5" />
            Follow-up Timeline
          </CardTitle>
        </CardHeader>
      )}
      <CardContent className={cn(!compact && "pt-0")}>
        <div className="space-y-4">
          {timelineItems.map((item, index) => {
            const isScheduled = item.type === 'scheduled';
            const scheduledItem = item as ScheduledFollowUp;
            const historyItem = item as FollowUpHistory;
            
            const date = isScheduled ? scheduledItem.scheduled_date : historyItem.sent_at;
            const templateName = isScheduled ? scheduledItem.template?.name : historyItem.template?.name;
            
            return (
              <div key={`${item.type}-${item.id}`} className="flex gap-4">
                {/* Timeline indicator */}
                <div className="flex flex-col items-center">
                  <div className="flex items-center justify-center w-8 h-8 rounded-full border-2 bg-background">
                    {isScheduled ? (
                      getStatusIcon(scheduledItem.status)
                    ) : (
                      historyItem.response_received ? (
                        <CheckCircle className="w-4 h-4 text-green-500" />
                      ) : (
                        <Mail className="w-4 h-4 text-blue-500" />
                      )
                    )}
                  </div>
                  {index < timelineItems.length - 1 && (
                    <div className="w-0.5 h-8 bg-border mt-2" />
                  )}
                </div>

                {/* Content */}
                <div className="flex-1 min-w-0">
                  <Card className="hover:shadow-sm transition-shadow">
                    <CardContent className="pt-4">
                      <div className="flex items-start justify-between gap-2">
                        <div className="flex-1 space-y-2">
                          <div className="flex items-center gap-2 flex-wrap">
                            <span className="font-medium text-sm">
                              {templateName || 'Unbekanntes Template'}
                            </span>
                            {isScheduled ? (
                              <>
                                <Badge 
                                  variant="secondary" 
                                  className={getStatusColor(scheduledItem.status)}
                                >
                                  {scheduledItem.status === 'scheduled' && isOverdue(scheduledItem.scheduled_date) 
                                    ? 'Überfällig' 
                                    : scheduledItem.status}
                                </Badge>
                                {scheduledItem.status === 'scheduled' && isOverdue(scheduledItem.scheduled_date) && (
                                  <Badge variant="destructive" className="text-xs">
                                    <AlertCircle className="w-3 h-3 mr-1" />
                                    Überfällig
                                  </Badge>
                                )}
                              </>
                            ) : (
                              <>
                                <Badge variant="secondary" className="bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300">
                                  Gesendet
                                </Badge>
                                {historyItem.response_received && (
                                  <Badge variant="secondary" className="bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300">
                                    Antwort erhalten
                                  </Badge>
                                )}
                              </>
                            )}
                          </div>
                          
                          <div className="text-xs text-muted-foreground flex items-center gap-4">
                            <span className="flex items-center gap-1">
                              <Calendar className="w-3 h-3" />
                              {format(new Date(date), 'dd.MM.yyyy HH:mm', { locale: de })}
                            </span>
                            {isScheduled && scheduledItem.status === 'scheduled' && (
                              <span className="text-blue-600 dark:text-blue-400">
                                Geplant
                              </span>
                            )}
                          </div>

                          {/* Subject preview for history items */}
                          {!isScheduled && !compact && (
                            <div className="text-sm">
                              <span className="font-medium">Betreff: </span>
                              <span className="text-muted-foreground">
                                {historyItem.subject}
                              </span>
                            </div>
                          )}
                        </div>

                        {/* Actions */}
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" size="sm">
                              <MoreHorizontal className="w-4 h-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            {isScheduled && scheduledItem.status === 'scheduled' && (
                              <>
                                <DropdownMenuItem
                                  onClick={() => handleOpenInEmailClient(scheduledItem)}
                                >
                                  <ExternalLink className="w-4 h-4 mr-2" />
                                  In Email-Client öffnen
                                </DropdownMenuItem>
                                <DropdownMenuItem
                                  onClick={() => handleMarkAsSent(scheduledItem)}
                                  disabled={isMarking}
                                >
                                  <CheckCircle className="w-4 h-4 mr-2" />
                                  Als gesendet markieren
                                </DropdownMenuItem>
                                <DropdownMenuItem
                                  onClick={() => dismissFollowUp(scheduledItem.id)}
                                  disabled={isDismissing}
                                >
                                  <XCircle className="w-4 h-4 mr-2" />
                                  Ablehnen
                                </DropdownMenuItem>
                                <DropdownMenuSeparator />
                                <AlertDialog>
                                  <AlertDialogTrigger asChild>
                                    <DropdownMenuItem
                                      className="text-destructive"
                                      onSelect={(e) => e.preventDefault()}
                                    >
                                      <Trash2 className="w-4 h-4 mr-2" />
                                      Löschen
                                    </DropdownMenuItem>
                                  </AlertDialogTrigger>
                                  <AlertDialogContent>
                                    <AlertDialogHeader>
                                      <AlertDialogTitle>Follow-up löschen</AlertDialogTitle>
                                      <AlertDialogDescription>
                                        Möchtest du dieses geplante Follow-up wirklich löschen?
                                      </AlertDialogDescription>
                                    </AlertDialogHeader>
                                    <AlertDialogFooter>
                                      <AlertDialogCancel>Abbrechen</AlertDialogCancel>
                                      <AlertDialogAction
                                        onClick={() => deleteScheduledFollowUp(scheduledItem.id)}
                                        className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                                      >
                                        Löschen
                                      </AlertDialogAction>
                                    </AlertDialogFooter>
                                  </AlertDialogContent>
                                </AlertDialog>
                              </>
                            )}
                            
                            {!isScheduled && (
                              <>
                                {!historyItem.response_received ? (
                                  <DropdownMenuItem
                                    onClick={() => markAsResponded({ historyId: historyItem.id })}
                                    disabled={isMarkingHistory}
                                  >
                                    <CheckCircle className="w-4 h-4 mr-2" />
                                    Als beantwortet markieren
                                  </DropdownMenuItem>
                                ) : (
                                  <DropdownMenuItem
                                    onClick={() => unmarkResponse(historyItem.id)}
                                    disabled={isMarkingHistory}
                                  >
                                    <XCircle className="w-4 h-4 mr-2" />
                                    Antwort entfernen
                                  </DropdownMenuItem>
                                )}
                              </>
                            )}
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </div>
            );
          })}
        </div>
      </CardContent>
    </Card>
  );
};