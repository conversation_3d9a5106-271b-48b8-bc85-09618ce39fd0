import { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, Di<PERSON>Header, <PERSON><PERSON>Title, DialogFooter } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Calendar } from '@/components/ui/calendar';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { useFollowUpSchedule } from '@/hooks/useFollowUpSchedule';
import { useFollowUpTemplates } from '@/hooks/useFollowUpTemplates';
import { FollowUpService } from '@/services/followUpService';
import { ScheduledFollowUp } from '@/types/followup';
import { ApplicationWithContact } from '@/types/applications';
import { format } from 'date-fns';
import { de } from 'date-fns/locale';
import { parseFollowUpDate, parseFollowUpTime, createFollowUpISOString } from '@/lib/dateUtils';
import { Calendar as CalendarIcon, Clock, Mail, User, Building2, Trash2 } from 'lucide-react';
import { cn } from '@/lib/utils';

interface FollowUpEditDialogProps {
  followUp: ScheduledFollowUp;
  project: ApplicationWithContact;
  isOpen: boolean;
  onClose: () => void;
}

export const FollowUpEditDialog = ({ followUp, project, isOpen, onClose }: FollowUpEditDialogProps) => {
  const [selectedDate, setSelectedDate] = useState<Date | undefined>(
    followUp.scheduled_date ? parseFollowUpDate(followUp.scheduled_date) : undefined
  );
  const [selectedTime, setSelectedTime] = useState(
    followUp.scheduled_date ? parseFollowUpTime(followUp.scheduled_date) : '09:00'
  );
  const [selectedTemplateId, setSelectedTemplateId] = useState(followUp.template_id || followUp.template?.id || '');
  const [customSubject, setCustomSubject] = useState('');
  const [customBody, setCustomBody] = useState('');

  const { updateScheduledFollowUp, deleteScheduledFollowUp, isUpdating, isDeleting } = useFollowUpSchedule();
  const { templates } = useFollowUpTemplates();

  const selectedTemplate = templates.find(t => t.id === selectedTemplateId);

  const handleSave = () => {
    if (!selectedDate || !selectedTemplateId) return;

    // Use timezone-safe function to create ISO string
    const isoString = createFollowUpISOString(selectedDate, selectedTime);

    updateScheduledFollowUp({
      id: followUp.id,
      scheduled_date: isoString,
      template_id: selectedTemplateId,
      // Add custom fields if needed
      ...(customSubject && { custom_subject: customSubject }),
      ...(customBody && { custom_body: customBody })
    });

    onClose();
  };

  const handleDelete = () => {
    if (window.confirm('Sind Sie sicher, dass Sie dieses Follow-up löschen möchten?')) {
      deleteScheduledFollowUp(followUp.id);
      onClose();
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="w-[95vw] max-w-2xl max-h-[90vh] overflow-y-auto mx-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Mail className="h-5 w-5 text-primary" />
            Follow-up bearbeiten
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6 py-4">
          {/* Application Info */}
          <div className="bg-muted/30 rounded-lg p-4 space-y-2">
            <div className="flex items-center gap-2 text-sm font-medium">
              <Building2 className="h-4 w-4 text-primary" />
              Bewerbung Details
            </div>
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 text-sm">
              <div>
                <span className="text-muted-foreground">Projekt:</span>
                <div className="font-medium break-words">{project.project_name || 'Unbekannt'}</div>
              </div>
              <div>
                <span className="text-muted-foreground">Unternehmen:</span>
                <div className="font-medium break-words">{project.company_name || 'Unbekannt'}</div>
              </div>
              <div>
                <span className="text-muted-foreground">Kontakt:</span>
                <div className="font-medium break-words">{project.contact?.name || 'Nicht angegeben'}</div>
              </div>
              <div>
                <span className="text-muted-foreground">Follow-up Status:</span>
                <Badge 
                  variant={followUp.status === 'scheduled' ? 'default' : 'secondary'}
                  className="ml-1"
                >
                  {followUp.status === 'scheduled' ? 'Geplant' : followUp.status === 'sent' ? 'Gesendet' : followUp.status}
                </Badge>
              </div>
            </div>
          </div>

          {/* Template Selection */}
          <div className="space-y-2">
            <Label htmlFor="template">Follow-up Template</Label>
            <Select value={selectedTemplateId} onValueChange={setSelectedTemplateId}>
              <SelectTrigger id="template">
                <SelectValue placeholder="Template auswählen" />
              </SelectTrigger>
              <SelectContent>
                {templates.map((template) => (
                  <SelectItem key={template.id} value={template.id}>
                    <div className="flex items-center justify-between w-full">
                      <span>{template.name}</span>
                      <Badge variant="outline" className="ml-2">
                        {template.trigger_days} Tage
                      </Badge>
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            {selectedTemplate && (
              <div className="text-sm text-muted-foreground">
                Trigger: {selectedTemplate.trigger_days} Tage nach {selectedTemplate.status_trigger || 'Bewerbung'}
              </div>
            )}
          </div>

          {/* Date and Time Selection */}
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label>Geplantes Datum</Label>
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    className={cn(
                      "w-full justify-start text-left font-normal",
                      !selectedDate && "text-muted-foreground"
                    )}
                  >
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {selectedDate ? format(selectedDate, 'dd.MM.yyyy', { locale: de }) : 'Datum wählen'}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0" align="start">
                  <Calendar
                    mode="single"
                    selected={selectedDate}
                    onSelect={setSelectedDate}
                    initialFocus
                  />
                </PopoverContent>
              </Popover>
            </div>

            <div className="space-y-2">
              <Label htmlFor="time">Uhrzeit</Label>
              <div className="relative">
                <Clock className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                <Input
                  id="time"
                  type="time"
                  value={selectedTime}
                  onChange={(e) => setSelectedTime(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
          </div>

          {/* Template Preview */}
          {selectedTemplate && (
            <div className="space-y-4 border rounded-lg p-4">
              <div className="font-medium text-sm flex items-center gap-2">
                <Mail className="h-4 w-4" />
                Template Vorschau (personalisiert)
              </div>
              
              <div className="space-y-3">
                <div>
                  <Label className="text-xs text-muted-foreground">Betreff</Label>
                  <div className="mt-1 p-2 bg-muted/50 rounded text-sm">
                    {(() => {
                      const templateVariables = {
                        project_name: project.project_name || 'Projektname',
                        contact_person: project.contact?.name || 'Ansprechpartner',
                        company_name: project.company_name || 'Firmenname',
                        user_name: 'Ihr Name',
                        trigger_days: selectedTemplate.trigger_days,
                        application_date: project.application_date || new Date().toISOString().split('T')[0],
                        interview_date: project.interview_date || ''
                      };
                      return FollowUpService.personalizeTemplate(selectedTemplate.subject, templateVariables);
                    })()}
                  </div>
                </div>
                
                <div>
                  <Label className="text-xs text-muted-foreground">Nachricht</Label>
                  <div className="mt-1 p-2 bg-muted/50 rounded text-sm max-h-32 overflow-y-auto whitespace-pre-wrap">
                    {(() => {
                      const templateVariables = {
                        project_name: project.project_name || 'Projektname',
                        contact_person: project.contact?.name || 'Ansprechpartner',
                        company_name: project.company_name || 'Firmenname',
                        user_name: 'Ihr Name',
                        trigger_days: selectedTemplate.trigger_days,
                        application_date: project.application_date || new Date().toISOString().split('T')[0],
                        interview_date: project.interview_date || ''
                      };
                      return FollowUpService.personalizeTemplate(selectedTemplate.body, templateVariables);
                    })()}
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Custom Overrides */}
          <div className="space-y-4">
            <div className="font-medium text-sm">Anpassungen (Optional)</div>
            
            <div className="space-y-2">
              <Label htmlFor="customSubject">Benutzerdefinierten Betreff</Label>
              <Input
                id="customSubject"
                placeholder="Leer lassen für Template-Betreff"
                value={customSubject}
                onChange={(e) => setCustomSubject(e.target.value)}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="customBody">Benutzerdefinierte Nachricht</Label>
              <Textarea
                id="customBody"
                placeholder="Leer lassen für Template-Nachricht"
                value={customBody}
                onChange={(e) => setCustomBody(e.target.value)}
                rows={4}
              />
            </div>
          </div>
        </div>

        <DialogFooter className="flex flex-col sm:flex-row gap-2 sm:justify-between">
          <Button
            variant="destructive"
            onClick={handleDelete}
            disabled={isDeleting}
            className="w-full sm:w-auto order-2 sm:order-1"
          >
            <Trash2 className="h-4 w-4 mr-2" />
            {isDeleting ? 'Wird gelöscht...' : 'Löschen'}
          </Button>
          
          <div className="flex flex-col sm:flex-row gap-2 order-1 sm:order-2">
            <Button variant="outline" onClick={onClose} className="w-full sm:w-auto">
              Abbrechen
            </Button>
            <Button 
              onClick={handleSave}
              disabled={isUpdating || !selectedDate || !selectedTemplateId}
              className="w-full sm:w-auto"
            >
              {isUpdating ? 'Wird gespeichert...' : 'Speichern'}
            </Button>
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};