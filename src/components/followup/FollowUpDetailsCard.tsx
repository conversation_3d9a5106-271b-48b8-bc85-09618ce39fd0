import { useState } from 'react';
import { <PERSON>, Card<PERSON>ontent, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { useFollowUpSchedule } from '@/hooks/useFollowUpSchedule';
import { useFollowUpTemplates } from '@/hooks/useFollowUpTemplates';
import { FollowUpService } from '@/services/followUpService';
import { FollowUpEditDialog } from './FollowUpEditDialog';
import { FollowUpScheduler } from './FollowUpScheduler';
import { ApplicationWithContact } from '@/types/applications';
import { format } from 'date-fns';
import { de } from 'date-fns/locale';
import { formatFollowUpDate } from '@/lib/dateUtils';
import { useTranslation } from '@/hooks/useTranslation';
import { 
  Mail, 
  Plus, 
  Timer, 
  Send, 
  Pause, 
  Clock, 
  MoreHorizontal, 
  Edit, 
  ExternalLink, 
  CheckCircle, 
  Trash2,
  AlertCircle
} from 'lucide-react';

interface FollowUpDetailsCardProps {
  application: ApplicationWithContact;
}

export const FollowUpDetailsCard = ({ application }: FollowUpDetailsCardProps) => {
  const [selectedFollowUpForEdit, setSelectedFollowUpForEdit] = useState<any>(null);
  const { t } = useTranslation('followup');
  
  const { 
    getScheduledByApplication, 
    error: followUpError, 
    isLoading: isFollowUpsLoading, 
    deleteScheduledFollowUp, 
    markAsSent 
  } = useFollowUpSchedule();
  
  const { templates } = useFollowUpTemplates();
  
  const scheduledFollowUps = followUpError ? [] : getScheduledByApplication(application.id);
  const hasFollowUps = scheduledFollowUps.length > 0;

  const getFollowUpStatusConfig = (status: string) => {
    switch (status) {
      case 'scheduled':
        return {
          icon: Timer,
          label: t('scheduled'),
          bgColor: 'bg-blue-50',
          textColor: 'text-blue-700',
          iconColor: 'text-blue-600',
          borderColor: 'border-blue-200'
        };
      case 'sent':
        return {
          icon: Send,
          label: t('sent'),
          bgColor: 'bg-green-50',
          textColor: 'text-green-700',
          iconColor: 'text-green-600',
          borderColor: 'border-green-200'
        };
      case 'dismissed':
        return {
          icon: Pause,
          label: t('card.dismissed'),
          bgColor: 'bg-gray-50',
          textColor: 'text-gray-700',
          iconColor: 'text-gray-600',
          borderColor: 'border-gray-200'
        };
      default:
        return {
          icon: Clock,
          label: status,
          bgColor: 'bg-gray-50',
          textColor: 'text-gray-700',
          iconColor: 'text-gray-600',
          borderColor: 'border-gray-200'
        };
    }
  };

  if (followUpError) {
    return (
      <Card className="shadow-sm">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Mail className="h-5 w-5" />
            {t('title')}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-6 px-4 rounded-lg bg-destructive/5 border border-destructive/20">
            <div className="flex items-center gap-3">
              <AlertCircle className="h-4 w-4 text-destructive" />
              <span className="text-sm text-destructive">{t('card.error_loading')}</span>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <>
      <Card className="shadow-sm">
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Mail className="h-5 w-5" />
              {t('title')}
              {hasFollowUps && (
                <Badge variant="secondary" className="text-xs">
                  {scheduledFollowUps.length}
                </Badge>
              )}
            </div>
            <FollowUpScheduler 
              application={application}
              trigger={
                <Button
                  variant="outline"
                  size="sm"
                  className="h-7 px-3 text-xs"
                >
                  <Plus className="h-3 w-3 mr-1" />
                  {t('timing.schedule')}
                </Button>
              }
            />
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {isFollowUpsLoading && (
            <div className="flex items-center justify-center py-6 px-4 rounded-lg bg-muted/10">
              <div className="flex items-center gap-3">
                <div className="animate-spin rounded-full h-4 w-4 border-2 border-primary border-t-transparent"></div>
                <span className="text-sm text-muted-foreground">{t('card.loading')}</span>
              </div>
            </div>
          )}

          {!hasFollowUps && !isFollowUpsLoading && (
            <div className="flex flex-col items-center justify-center py-8 px-4 rounded-lg bg-muted/20 border border-dashed border-muted-foreground/20">
              <Clock className="h-8 w-8 text-muted-foreground/40 mb-3" />
              <p className="text-sm text-muted-foreground font-medium mb-1">{t('card.no_followups_planned')}</p>
              <p className="text-xs text-muted-foreground/70 text-center">
                {t('card.help_text')}
              </p>
            </div>
          )}

          {hasFollowUps && (
            <div className="space-y-3">
              {scheduledFollowUps.map((followUp) => {
                const statusConfig = getFollowUpStatusConfig(followUp.status);
                const StatusIcon = statusConfig.icon;
                
                return (
                  <div 
                    key={followUp.id} 
                    className="group relative p-4 rounded-lg bg-card border border-border/50 hover:border-border hover:shadow-sm transition-all duration-200 cursor-pointer"
                    onClick={(e) => {
                      e.stopPropagation();
                      setSelectedFollowUpForEdit(followUp);
                    }}
                  >
                    {/* Status indicator bar */}
                    <div className={`absolute left-0 top-0 bottom-0 w-1 rounded-l-lg ${statusConfig.bgColor} ${statusConfig.borderColor}`} />
                    
                    <div className="flex items-start justify-between gap-3 ml-2">
                      <div className="flex-1 min-w-0">
                        {/* Template name */}
                        <div className="mb-1">
                          <h4 className="text-sm font-medium text-foreground truncate">
                            {followUp.template?.name || t('card.template_fallback')}
                          </h4>
                        </div>
                        
                        {/* Status and date */}
                        <div className="flex items-center gap-3 text-xs text-muted-foreground">
                          <div className="flex items-center gap-1">
                            <StatusIcon className="h-3 w-3" />
                            <span>{statusConfig.label}</span>
                          </div>
                          <span>•</span>
                          <span className="font-medium">
                            {formatFollowUpDate(followUp.scheduled_date)}
                          </span>
                        </div>
                      </div>
                      
                      {/* Action menu */}
                      <div className="flex-shrink-0 flex items-center opacity-0 group-hover:opacity-100 transition-opacity">
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={(e) => {
                                e.stopPropagation();
                              }}
                              className="h-6 w-6 p-0 hover:bg-muted"
                            >
                              <MoreHorizontal className="h-3 w-3" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end" className="w-56">
                            <DropdownMenuItem
                              onClick={(e) => {
                                e.stopPropagation();
                                setSelectedFollowUpForEdit(followUp);
                              }}
                            >
                              <Edit className="h-4 w-4 mr-2" />
                              {t('edit')}
                            </DropdownMenuItem>
                            
                            {(followUp.template || followUp.template_id) && (
                              <DropdownMenuItem
                                onClick={async (e) => {
                                  e.stopPropagation();
                                  
                                  // Generate personalized email
                                  const templateVariables = {
                                    project_name: application.project_name,
                                    contact_person: application.contact?.name || '',
                                    company_name: application.company_name,
                                    user_name: '',
                                    trigger_days: followUp.template?.trigger_days || 7,
                                    application_date: application.application_date || '',
                                    interview_date: application.interview_date || ''
                                  };

                                  const template = followUp.template || templates.find(t => t.id === followUp.template_id);
                                  if (!template) {
                                    alert(t('card.template_not_found'));
                                    return;
                                  }

                                  const personalizedSubject = FollowUpService.personalizeTemplate(
                                    template.subject, 
                                    templateVariables
                                  );
                                  const personalizedBody = FollowUpService.personalizeTemplate(
                                    template.body, 
                                    templateVariables
                                  );

                                  const emailTo = application.contact?.email || '';
                                  const mailtoLink = FollowUpService.generateMailtoLink(
                                    emailTo,
                                    personalizedSubject,
                                    personalizedBody
                                  );

                                  window.open(mailtoLink);
                                }}
                              >
                                <ExternalLink className="h-4 w-4 mr-2" />
                                {t('card.open_email')}
                              </DropdownMenuItem>
                            )}

                            {followUp.status === 'scheduled' && (
                              <DropdownMenuItem
                                onClick={(e) => {
                                  e.stopPropagation();
                                  const subject = followUp.template?.subject || 'Follow-up';
                                  const body = followUp.template?.body || t('card.followup_sent_fallback');
                                  markAsSent({
                                    followUpId: followUp.id,
                                    actualSubject: subject,
                                    actualBody: body
                                  });
                                }}
                              >
                                <CheckCircle className="h-4 w-4 mr-2" />
                                {t('card.mark_as_sent')}
                              </DropdownMenuItem>
                            )}
                            
                            <DropdownMenuItem
                              onClick={(e) => {
                                e.stopPropagation();
                                if (window.confirm(t('card.delete_confirmation'))) {
                                  deleteScheduledFollowUp(followUp.id);
                                }
                              }}
                              className="text-destructive focus:text-destructive"
                            >
                              <Trash2 className="h-4 w-4 mr-2" />
                              {t('card.delete')}
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          )}
        </CardContent>
      </Card>

      {selectedFollowUpForEdit && (
        <FollowUpEditDialog
          followUp={selectedFollowUpForEdit}
          project={application}
          isOpen={!!selectedFollowUpForEdit}
          onClose={() => setSelectedFollowUpForEdit(null)}
        />
      )}
    </>
  );
};