import { useState } from 'react';
import { useFollowUpHistory } from '@/hooks/useFollowUpHistory';
import { useTranslation } from 'react-i18next';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Skeleton } from '@/components/ui/skeleton';
import { 
  Bar<PERSON>hart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer,
  PieChart, Pie, Cell, LineChart, Line
} from 'recharts';
import { 
  TrendingUp, 
  Target, 
  Clock, 
  Mail, 
  CheckCircle, 
  AlertTriangle,
  Calendar,
  Award
} from 'lucide-react';

const COLORS = ['#3b82f6', '#10b981', '#f59e0b', '#ef4444', '#8b5cf6', '#06b6d4'];

export const FollowUpAnalytics = () => {
  const { analytics, isLoading, error } = useFollowUpHistory();
  const [selectedTab, setSelectedTab] = useState('overview');
  const { t } = useTranslation('followup');

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="grid grid-cols-2 sm:grid-cols-4 gap-2 sm:gap-3 w-full overflow-x-hidden">
          {[...Array(4)].map((_, i) => (
            <Card key={i} className="min-w-0">
              <CardHeader className="pb-2">
                <Skeleton className="h-4 w-24" />
              </CardHeader>
              <CardContent>
                <Skeleton className="h-8 w-16 mb-2" />
                <Skeleton className="h-3 w-20" />
              </CardContent>
            </Card>
          ))}
        </div>
        <Card>
          <CardContent className="p-6">
            <Skeleton className="h-64 w-full" />
          </CardContent>
        </Card>
      </div>
    );
  }

  if (error || !analytics) {
    return (
      <Card>
        <CardContent className="py-12">
          <div className="text-center">
            <AlertTriangle className="w-12 h-12 mx-auto mb-4 text-yellow-500 opacity-50" />
            <h3 className="text-lg font-semibold mb-2">{t('analytics.no_data_title')}</h3>
            <p className="text-muted-foreground">
              {t('analytics.no_data_message')}
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  const responseRate = analytics.total_sent > 0 ? (analytics.response_rate * 100).toFixed(1) : '0';
  const avgResponseTime = analytics.avg_response_time_days > 0 ? analytics.avg_response_time_days.toFixed(1) : '0';

  // Template performance data for charts
  const templateData = analytics.success_by_template.map(template => ({
    name: template.template_name.length > 20 
      ? template.template_name.substring(0, 20) + '...' 
      : template.template_name,
    fullName: template.template_name,
    sent: template.sent_count,
    responses: template.response_count,
    rate: (template.response_rate * 100).toFixed(1)
  }));

  // Timing performance data
  const timingData = analytics.success_by_timing
    .sort((a, b) => a.trigger_days - b.trigger_days)
    .map(timing => ({
      days: t('analytics.labels.after_days', { 
      days: timing.trigger_days, 
      unit: timing.trigger_days !== 1 ? t('analytics.labels.days_plural') : t('analytics.labels.day_singular')
    }),
      sent: timing.sent_count,
      responses: timing.response_count,
      rate: (timing.response_rate * 100).toFixed(1)
    }));

  // Pie chart data for template distribution
  const pieData = analytics.success_by_template.map((template, index) => ({
    name: template.template_name,
    value: template.sent_count,
    color: COLORS[index % COLORS.length]
  }));

  return (
    <div className="space-y-6">
      {/* Key Metrics Cards */}
      <div className="grid grid-cols-2 sm:grid-cols-4 gap-2 sm:gap-3 w-full overflow-x-hidden">
        <Card className="min-w-0">
          <CardHeader className="pb-2">
            <CardTitle className="text-xs sm:text-sm font-medium flex items-center gap-1 sm:gap-2">
              <Mail className="h-3 w-3 sm:h-4 sm:w-4 text-blue-500 flex-shrink-0" />
              <span className="truncate">{t('analytics.metrics.sent_followups')}</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-xl sm:text-2xl font-bold">{analytics.total_sent}</div>
            <p className="text-xs text-muted-foreground truncate">{t('analytics.metrics.total_sent')}</p>
          </CardContent>
        </Card>

        <Card className="min-w-0">
          <CardHeader className="pb-2">
            <CardTitle className="text-xs sm:text-sm font-medium flex items-center gap-1 sm:gap-2">
              <Target className="h-3 w-3 sm:h-4 sm:w-4 text-green-500 flex-shrink-0" />
              <span className="truncate">{t('analytics.metrics.response_rate')}</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-xl sm:text-2xl font-bold">{responseRate}%</div>
            <p className="text-xs text-muted-foreground truncate">{t('analytics.metrics.success_rate')}</p>
          </CardContent>
        </Card>

        <Card className="min-w-0">
          <CardHeader className="pb-2">
            <CardTitle className="text-xs sm:text-sm font-medium flex items-center gap-1 sm:gap-2">
              <Clock className="h-3 w-3 sm:h-4 sm:w-4 text-orange-500 flex-shrink-0" />
              <span className="truncate">{t('analytics.metrics.avg_response_time')}</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-xl sm:text-2xl font-bold">{avgResponseTime}</div>
            <p className="text-xs text-muted-foreground truncate">{t('analytics.metrics.avg_days')}</p>
          </CardContent>
        </Card>

        <Card className="min-w-0">
          <CardHeader className="pb-2">
            <CardTitle className="text-xs sm:text-sm font-medium flex items-center gap-1 sm:gap-2">
              <Award className="h-3 w-3 sm:h-4 sm:w-4 text-purple-500 flex-shrink-0" />
              <span className="truncate">{t('analytics.metrics.best_template')}</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            {analytics.success_by_template.length > 0 ? (
              <>
                <div className="text-xl sm:text-2xl font-bold">{analytics.success_by_template[0]?.template_name || 'N/A'}</div>
                <p className="text-xs text-muted-foreground truncate">
                  {(analytics.success_by_template[0]?.response_rate * 100 || 0).toFixed(1)}% {t('analytics.metrics.success_rate')}
                </p>
              </>
            ) : (
              <>
                <div className="text-xl sm:text-2xl font-bold">-</div>
                <p className="text-xs text-muted-foreground truncate">{t('analytics.metrics.no_data')}</p>
              </>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Analytics Tabs */}
      <Tabs value={selectedTab} onValueChange={setSelectedTab}>
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="overview">{t('analytics.tabs.overview')}</TabsTrigger>
          <TabsTrigger value="templates">{t('analytics.tabs.templates')}</TabsTrigger>
          <TabsTrigger value="timing">{t('analytics.tabs.timing')}</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Template Distribution */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">{t('analytics.charts.template_distribution')}</CardTitle>
              </CardHeader>
              <CardContent>
                {pieData.length > 0 ? (
                  <ResponsiveContainer width="100%" height={300}>
                    <PieChart>
                      <Pie
                        data={pieData}
                        cx="50%"
                        cy="50%"
                        outerRadius={100}
                        fill="#8884d8"
                        dataKey="value"
                        label={({ name, value }) => `${name}: ${value}`}
                      >
                        {pieData.map((entry, index) => (
                          <Cell key={`cell-${index}`} fill={entry.color} />
                        ))}
                      </Pie>
                      <Tooltip />
                    </PieChart>
                  </ResponsiveContainer>
                ) : (
                  <div className="h-[300px] flex items-center justify-center text-muted-foreground">
                    {t('analytics.charts.no_data_available')}
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Success Rate Trend */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">{t('analytics.charts.template_performance')}</CardTitle>
              </CardHeader>
              <CardContent>
                {templateData.length > 0 ? (
                  <div className="space-y-4">
                    {templateData.slice(0, 5).map((template, index) => (
                      <div key={index} className="space-y-2">
                        <div className="flex justify-between items-center">
                          <span className="text-sm font-medium truncate" title={template.fullName}>
                            {template.name}
                          </span>
                          <Badge variant="secondary">
                            {template.rate}%
                          </Badge>
                        </div>
                        <Progress value={parseFloat(template.rate)} className="h-2" />
                        <div className="text-xs text-muted-foreground">
                          {template.responses}/{template.sent} {t('analytics.labels.responses')}
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="h-[300px] flex items-center justify-center text-muted-foreground">
                    {t('analytics.charts.no_template_data')}
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="templates" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">{t('analytics.charts.template_performance_detail')}</CardTitle>
            </CardHeader>
            <CardContent>
              {templateData.length > 0 ? (
                <ResponsiveContainer width="100%" height={400}>
                  <BarChart data={templateData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="name" />
                    <YAxis />
                    <Tooltip 
                      formatter={(value: any, name: string) => [
                        value, 
                        name === 'sent' ? t('analytics.tooltip.sent') : name === 'responses' ? t('analytics.tooltip.responses') : name
                      ]}
                      labelFormatter={(label) => {
                        const template = templateData.find(t => t.name === label);
                        return template?.fullName || label;
                      }}
                    />
                    <Bar dataKey="sent" fill="#3b82f6" name="sent" />
                    <Bar dataKey="responses" fill="#10b981" name="responses" />
                  </BarChart>
                </ResponsiveContainer>
              ) : (
                <div className="h-[400px] flex items-center justify-center text-muted-foreground">
                  {t('analytics.charts.no_template_data')}
                </div>
              )}
            </CardContent>
          </Card>

          {/* Template Success Rates Table */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">{t('analytics.charts.template_success_rates')}</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {analytics.success_by_template.map((template, index) => (
                  <div key={index} className="flex items-center justify-between p-4 border rounded-lg">
                    <div className="flex-1">
                      <h4 className="font-medium">{template.template_name}</h4>
                      <div className="flex gap-4 text-sm text-muted-foreground mt-1">
                        <span>{t('analytics.labels.sent')}: {template.sent_count}</span>
                        <span>{t('analytics.labels.responses')}: {template.response_count}</span>
                      </div>
                    </div>
                    <Badge 
                      variant={
                        template.response_rate >= 0.3 ? "default" :
                        template.response_rate >= 0.15 ? "secondary" : "outline"
                      }
                      className="ml-4"
                    >
                      {(template.response_rate * 100).toFixed(1)}%
                    </Badge>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="timing" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">{t('analytics.charts.timing_performance')}</CardTitle>
            </CardHeader>
            <CardContent>
              {timingData.length > 0 ? (
                <ResponsiveContainer width="100%" height={400}>
                  <LineChart data={timingData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="days" />
                    <YAxis />
                    <Tooltip 
                      formatter={(value: any, name: string) => [
                        name === 'rate' ? `${value}%` : value,
                        name === 'sent' ? t('analytics.tooltip.sent') : name === 'responses' ? t('analytics.tooltip.responses') : t('analytics.tooltip.success_rate')
                      ]}
                    />
                    <Line type="monotone" dataKey="rate" stroke="#8b5cf6" strokeWidth={3} name="rate" />
                    <Bar dataKey="sent" fill="#3b82f6" name="sent" />
                  </LineChart>
                </ResponsiveContainer>
              ) : (
                <div className="h-[400px] flex items-center justify-center text-muted-foreground">
                  {t('analytics.charts.no_timing_data')}
                </div>
              )}
            </CardContent>
          </Card>

          {/* Best Timing Insights */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg flex items-center gap-2">
                <TrendingUp className="h-5 w-5" />
                {t('analytics.charts.timing_insights')}
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {analytics.success_by_timing.length > 0 ? (
                  analytics.success_by_timing
                    .sort((a, b) => b.response_rate - a.response_rate)
                    .slice(0, 3)
                    .map((timing, index) => (
                      <div key={index} className="flex items-center justify-between p-4 border rounded-lg">
                        <div className="flex items-center gap-3">
                          <div className={`w-3 h-3 rounded-full ${
                            index === 0 ? 'bg-green-500' : 
                            index === 1 ? 'bg-blue-500' : 'bg-orange-500'
                          }`} />
                          <div>
                            <span className="font-medium">
                              {t('analytics.labels.after_days', {
                                days: timing.trigger_days,
                                unit: timing.trigger_days !== 1 ? t('analytics.labels.days_plural') : t('analytics.labels.day_singular')
                              })}
                            </span>
                            <div className="text-sm text-muted-foreground">
                              {timing.response_count}/{timing.sent_count} {t('analytics.labels.responses')}
                            </div>
                          </div>
                        </div>
                        <Badge variant={index === 0 ? "default" : "secondary"}>
                          {(timing.response_rate * 100).toFixed(1)}%
                        </Badge>
                      </div>
                    ))
                ) : (
                  <div className="text-center py-8 text-muted-foreground">
                    {t('analytics.charts.no_timing_data_yet')}
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};