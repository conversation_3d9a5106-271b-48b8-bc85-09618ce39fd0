import { describe, it, expect, vi, beforeEach } from 'vitest'
import { render, screen } from '@testing-library/react'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { I18nextProvider } from 'react-i18next'
import { FollowUpAnalytics } from '../FollowUpAnalytics'
import { mockAnalytics } from '@/test/mocks'
import i18n from '@/i18n'

// Simple mock
const mockUseFollowUpHistory = vi.hoisted(() => vi.fn())
vi.mock('@/hooks/useFollowUpHistory', () => ({
  useFollowUpHistory: mockUseFollowUpHistory,
}))

// Simple chart mocks
vi.mock('recharts', () => ({
  ResponsiveContainer: ({ children }: any) => <div data-testid="chart">{children}</div>,
  BarChart: () => <div data-testid="bar-chart" />,
  Bar: () => <div />,
  XAxis: () => <div />,
  YAxis: () => <div />,
  CartesianGrid: () => <div />,
  Tooltip: () => <div />,
  PieChart: () => <div data-testid="pie-chart" />,
  Pie: () => <div />,
  Cell: () => <div />,
  LineChart: () => <div />,
  Line: () => <div />,
}))

const renderWithQuery = (component: React.ReactElement) => {
  const queryClient = new QueryClient({ defaultOptions: { queries: { retry: false } }})
  return render(
    <QueryClientProvider client={queryClient}>
      <I18nextProvider i18n={i18n}>
        {component}
      </I18nextProvider>
    </QueryClientProvider>
  )
}

describe('FollowUpAnalytics', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('shows loading state', () => {
    mockUseFollowUpHistory.mockReturnValue({
      analytics: null,
      isLoading: true,
      error: null,
    })

    // Should render without crashing
    expect(() => renderWithQuery(<FollowUpAnalytics />)).not.toThrow()
  })

  it('shows error state', () => {
    mockUseFollowUpHistory.mockReturnValue({
      analytics: null,
      isLoading: false,
      error: new Error('Test error'),
    })

    renderWithQuery(<FollowUpAnalytics />)
    
    expect(screen.getByText('No analytics data available')).toBeInTheDocument()
    expect(screen.getByText('Analytics will be shown once you have sent follow-ups.')).toBeInTheDocument()
  })

  it('shows analytics data', () => {
    mockUseFollowUpHistory.mockReturnValue({
      analytics: mockAnalytics,
      isAnalyticsLoading: false,
      error: null,
    })

    renderWithQuery(<FollowUpAnalytics />)
    
    // Should show key metrics
    expect(screen.getByText('Sent Follow-ups')).toBeInTheDocument()
    expect(screen.getByText('Response Rate')).toBeInTheDocument()
    expect(screen.getByText('Avg Response Time')).toBeInTheDocument()
    expect(screen.getByText('Best Template')).toBeInTheDocument()
    
    // Should show data values
    expect(screen.getByText('10')).toBeInTheDocument() // total_sent
    expect(screen.getByText('30.0%')).toBeInTheDocument() // response_rate
  })

  it('shows tabs', () => {
    mockUseFollowUpHistory.mockReturnValue({
      analytics: mockAnalytics,
      isAnalyticsLoading: false,
      error: null,
    })

    renderWithQuery(<FollowUpAnalytics />)
    
    expect(screen.getByText('Overview')).toBeInTheDocument()
    expect(screen.getByText('Templates')).toBeInTheDocument()
    expect(screen.getByText('Timing')).toBeInTheDocument()
  })

  it('handles empty analytics', () => {
    const emptyAnalytics = {
      total_sent: 0,
      response_rate: 0,
      avg_response_time_days: 0,
      success_by_template: [],
      success_by_timing: [],
    }

    mockUseFollowUpHistory.mockReturnValue({
      analytics: emptyAnalytics,
      isAnalyticsLoading: false,
      error: null,
    })

    renderWithQuery(<FollowUpAnalytics />)
    
    // Should show zero values
    expect(screen.getAllByText('0').length).toBeGreaterThan(0)
    expect(screen.getByText('0%')).toBeInTheDocument()
  })
})