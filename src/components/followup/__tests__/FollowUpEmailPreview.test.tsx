import { describe, it, expect, vi } from 'vitest'
import { render, screen } from '@testing-library/react'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { FollowUpEmailPreview } from '../FollowUpEmailPreview'

const mockUseUserSettings = vi.hoisted(() => vi.fn())
const mockUseFollowUpSchedule = vi.hoisted(() => vi.fn())

vi.mock('@/hooks/useUserSettings', () => ({
  useUserSettings: mockUseUserSettings,
}))

vi.mock('@/hooks/useFollowUpSchedule', () => ({
  useFollowUpSchedule: mockUseFollowUpSchedule,
}))

const renderWithQuery = (component: React.ReactElement) => {
  const queryClient = new QueryClient({ defaultOptions: { queries: { retry: false } }})
  return render(
    <QueryClientProvider client={queryClient}>
      {component}
    </QueryClientProvider>
  )
}

describe('FollowUpEmailPreview', () => {
  const mockTemplate = {
    id: '1',
    name: 'Standard Follow-up',
    subject: 'Follow-up: {project_name}',
    body: 'Hello {contact_person}, I wanted to follow up on my application for {project_name} at {company_name}.',
    trigger_days: 7,
  }

  const mockApplication = {
    id: '1',
    project_name: 'React Developer',
    company_name: 'Tech Corp',
    contact_person: 'Max Mustermann',
    status: 'application_sent' as const,
    contact: null,
    user_id: 'user-1',
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-01T00:00:00Z',
  }

  beforeEach(() => {
    mockUseUserSettings.mockReturnValue({
      settings: { full_name: 'Test User', email: '<EMAIL>' },
      isLoading: false,
      error: null,
    })

    mockUseFollowUpSchedule.mockReturnValue({
      isCreatingFromTemplate: false,
      scheduleFollowUp: vi.fn(),
    })
  })

  it('shows email preview with personalized content', () => {
    renderWithQuery(
      <FollowUpEmailPreview 
        isOpen={true}
        onClose={() => {}}
        template={mockTemplate}
        application={mockApplication}
        onSchedule={() => {}}
        defaultScheduleDate="2024-01-08"
      />
    )
    
    // Should show personalized content - use getAllByText for multiple occurrences
    expect(screen.getAllByText(/React Developer/).length).toBeGreaterThan(0)
    expect(screen.getAllByText(/Tech Corp/).length).toBeGreaterThan(0)
    expect(screen.getAllByText(/Max Mustermann/).length).toBeGreaterThan(0)
  })

  it('renders without crashing', () => {
    expect(() => renderWithQuery(
      <FollowUpEmailPreview 
        isOpen={true}
        onClose={() => {}}
        template={mockTemplate}
        application={mockApplication}
        onSchedule={() => {}}
        defaultScheduleDate="2024-01-08"
      />
    )).not.toThrow()
  })
})