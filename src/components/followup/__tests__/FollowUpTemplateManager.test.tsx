import { describe, it, expect, vi, beforeEach } from 'vitest'
import { render, screen } from '@testing-library/react'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { I18nextProvider } from 'react-i18next'
import { FollowUpTemplateManager } from '../FollowUpTemplateManager'
import i18n from '@/i18n'

const mockUseFollowUpTemplates = vi.hoisted(() => vi.fn())

vi.mock('@/hooks/useFollowUpTemplates', () => ({
  useFollowUpTemplates: mockUseFollowUpTemplates,
}))

const renderWithQuery = (component: React.ReactElement) => {
  const queryClient = new QueryClient({ defaultOptions: { queries: { retry: false } }})
  return render(
    <QueryClientProvider client={queryClient}>
      <I18nextProvider i18n={i18n}>
        {component}
      </I18nextProvider>
    </QueryClientProvider>
  )
}

describe('FollowUpTemplateManager', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('shows templates list', () => {
    const mockTemplates = [
      { 
        id: '1', 
        name: 'Standard Follow-up',
        subject: 'Follow-up: {project_name}',
        body: 'Hello {contact_person}, I wanted to follow up on my application for {project_name}.',
        trigger_days: 7,
        status_trigger: 'application_sent',
        is_active: true
      }
    ]

    mockUseFollowUpTemplates.mockReturnValue({
      templates: mockTemplates,
      createTemplate: { mutate: vi.fn() },
      updateTemplate: { mutate: vi.fn() },
      deleteTemplate: { mutate: vi.fn() },
      isLoading: false,
      error: null,
    })

    renderWithQuery(<FollowUpTemplateManager />)
    
    expect(screen.getByText('Standard Follow-up')).toBeInTheDocument()
    expect(screen.getByText(/7.*day/i)).toBeInTheDocument()
  })

  it('shows empty state', () => {
    mockUseFollowUpTemplates.mockReturnValue({
      templates: [],
      createTemplate: { mutate: vi.fn() },
      updateTemplate: { mutate: vi.fn() },
      deleteTemplate: { mutate: vi.fn() },
      isLoading: false,
      error: null,
    })

    renderWithQuery(<FollowUpTemplateManager />)
    
    expect(screen.getByText(/no templates created yet/i)).toBeInTheDocument()
  })

  it('shows loading state', () => {
    mockUseFollowUpTemplates.mockReturnValue({
      templates: [],
      createTemplate: { mutate: vi.fn() },
      updateTemplate: { mutate: vi.fn() },
      deleteTemplate: { mutate: vi.fn() },
      isLoading: true,
      error: null,
    })

    // Should render without crashing
    expect(() => renderWithQuery(<FollowUpTemplateManager />)).not.toThrow()
  })

  it('shows create template button', () => {
    mockUseFollowUpTemplates.mockReturnValue({
      templates: [],
      createTemplate: { mutate: vi.fn() },
      updateTemplate: { mutate: vi.fn() },
      deleteTemplate: { mutate: vi.fn() },
      isLoading: false,
      error: null,
    })

    renderWithQuery(<FollowUpTemplateManager />)
    
    // Should show some template creation option
    expect(screen.getAllByText(/template/i).length).toBeGreaterThan(0)
  })

  it('handles error state', () => {
    mockUseFollowUpTemplates.mockReturnValue({
      templates: [],
      createTemplate: { mutate: vi.fn() },
      updateTemplate: { mutate: vi.fn() },
      deleteTemplate: { mutate: vi.fn() },
      isLoading: false,
      error: new Error('Failed to load templates'),
    })

    // Should render without crashing
    expect(() => renderWithQuery(<FollowUpTemplateManager />)).not.toThrow()
  })
})