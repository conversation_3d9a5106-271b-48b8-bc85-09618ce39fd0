import { describe, it, expect, vi, beforeEach } from 'vitest'
import { render, screen } from '@testing-library/react'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { FollowUpScheduler } from '../FollowUpScheduler'

// Simple mocks
const mockUseFollowUpTemplates = vi.hoisted(() => vi.fn())
const mockUseFollowUpSchedule = vi.hoisted(() => vi.fn())
const mockUseUserSettings = vi.hoisted(() => vi.fn())

vi.mock('@/hooks/useFollowUpTemplates', () => ({
  useFollowUpTemplates: mockUseFollowUpTemplates,
}))

vi.mock('@/hooks/useFollowUpSchedule', () => ({
  useFollowUpSchedule: mockUseFollowUpSchedule,
}))

vi.mock('@/hooks/useUserSettings', () => ({
  useUserSettings: mockUseUserSettings,
}))

const renderWithQuery = (component: React.ReactElement) => {
  const queryClient = new QueryClient({ defaultOptions: { queries: { retry: false } }})
  return render(
    <QueryClientProvider client={queryClient}>
      {component}
    </QueryClientProvider>
  )
}

describe('FollowUpScheduler', () => {
  const mockApplication = {
    id: '1',
    project_name: 'React Developer',
    company_name: 'Tech Corp',
    status: 'application_sent' as const,
    contact_person: 'John Doe',
    contact: null,
    user_id: 'user-1',
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-01T00:00:00Z',
  }

  beforeEach(() => {
    vi.clearAllMocks()
    
    mockUseUserSettings.mockReturnValue({
      settings: { full_name: 'Test User', email: '<EMAIL>' },
      isLoading: false,
      error: null,
    })
    
    mockUseFollowUpTemplates.mockReturnValue({
      templates: [
        { 
          id: '1', 
          name: 'Standard Follow-up', 
          subject: 'Follow-up: {project_name}',
          body: 'Hello {contact_person}, I wanted to follow up on my application for {project_name}.',
          trigger_days: 7, 
          status_trigger: 'application_sent' 
        }
      ],
      getTemplatesByStatus: vi.fn().mockReturnValue([
        { 
          id: '1', 
          name: 'Standard Follow-up', 
          subject: 'Follow-up: {project_name}',
          body: 'Hello {contact_person}, I wanted to follow up on my application for {project_name}.',
          trigger_days: 7, 
          status_trigger: 'application_sent' 
        }
      ]),
      isLoading: false,
      error: null,
    })

    mockUseFollowUpSchedule.mockReturnValue({
      scheduleFollowUp: vi.fn(),
      isScheduling: false,
      error: null,
    })
  })

  it('renders scheduler form', () => {
    renderWithQuery(<FollowUpScheduler application={mockApplication} />)
    
    // Should show the trigger button to open scheduler
    expect(screen.getByText('Follow-up planen')).toBeInTheDocument()
  })

  it('shows templates for current project status', () => {
    renderWithQuery(<FollowUpScheduler application={mockApplication} />)
    
    // Should render without crashing and show button
    expect(screen.getByText('Follow-up planen')).toBeInTheDocument()
  })

  it('handles no templates available', () => {
    mockUseFollowUpTemplates.mockReturnValue({
      templates: [],
      getTemplatesByStatus: vi.fn().mockReturnValue([]),
      isLoading: false,
      error: null,
    })

    renderWithQuery(<FollowUpScheduler application={mockApplication} />)
    
    // Should render without crashing and show button
    expect(screen.getByText('Follow-up planen')).toBeInTheDocument()
  })

  it('shows loading state', () => {
    mockUseFollowUpTemplates.mockReturnValue({
      templates: [],
      getTemplatesByStatus: vi.fn().mockReturnValue([]),
      isLoading: true,
      error: null,
    })

    renderWithQuery(<FollowUpScheduler application={mockApplication} />)
    
    // Should render without crashing
    expect(() => renderWithQuery(<FollowUpScheduler application={mockApplication} />)).not.toThrow()
  })

  it('handles scheduling error', () => {
    mockUseFollowUpSchedule.mockReturnValue({
      scheduleFollowUp: vi.fn(),
      isScheduling: false,
      error: new Error('Scheduling failed'),
    })

    renderWithQuery(<FollowUpScheduler application={mockApplication} />)
    
    // Should render without crashing
    expect(() => renderWithQuery(<FollowUpScheduler application={mockApplication} />)).not.toThrow()
  })
})