import React from 'react';
import { Bell, Clock, AlertTriangle, CheckCircle, XCircle, Mail, Trash2, MoreHorizontal } from 'lucide-react';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuSeparator, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { format } from 'date-fns';
import { de } from 'date-fns/locale';
import { formatFollowUpDateTime } from '@/lib/dateUtils';
import { useFollowUpSchedule } from '@/hooks/useFollowUpSchedule';
import { FollowUpService } from '@/services/followUpService';
import { useUserSettings } from '@/hooks/useUserSettings';
import { supabase } from '@/integrations/supabase/client';
import { useNavigate } from 'react-router-dom';
import { useTranslation } from '@/hooks/useTranslation';
import type { FollowUpNotification } from '@/types/followup';
import { FollowUpDeleteDialog } from './FollowUpDeleteDialog';
import { useState } from 'react';
interface FollowUpNotificationsProps {
  compact?: boolean;
}

export const FollowUpNotifications = ({ compact = false }: FollowUpNotificationsProps) => {
  const { dueFollowUps, markAsSent, dismissFollowUp, deleteScheduledFollowUp, isDeleting, error } = useFollowUpSchedule();
  const { settings } = useUserSettings();
  const navigate = useNavigate();
  const { t } = useTranslation('followup');
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [followUpToDelete, setFollowUpToDelete] = useState<FollowUpNotification | null>(null);

  // If there's an error (like 403/400), don't render anything
  if (error) {
    console.warn('Follow-up system not available:', error);
    return null;
  }

  const handleOpenInEmailClient = async (followUp: FollowUpNotification) => {
    try {
      // Get the full follow-up with template details
      const { data: fullFollowUp, error } = await supabase
        .from('follow_up_schedule')
        .select(`
          *,
          template:follow_up_templates(*),
          application:project_applications(
            *,
            contact:contacts(*)
          )
        `)
        .eq('id', followUp.id)
        .single();

      if (error) {
        console.error('Error fetching follow-up data:', error);
        throw error;
      }

      if (!fullFollowUp?.template || !fullFollowUp?.application) {
        console.error('Could not load follow-up template or application data');
        throw new Error('Missing template or application data');
      }

      // Create template variables
      const templateVariables = {
        project_name: fullFollowUp.application.project_name || followUp.project_name,
        contact_person: fullFollowUp.application.contact?.name || 'Sehr geehrte Damen und Herren',
        company_name: fullFollowUp.application.company_name || followUp.company_name,
        user_name: settings?.full_name || 'Ihr Name',
        trigger_days: fullFollowUp.template.trigger_days || 7,
        application_date: fullFollowUp.application.application_date || fullFollowUp.application.created_at || '',
        interview_date: fullFollowUp.application.interview_date || ''
      };

      // Generate personalized email content
      const personalizedSubject = FollowUpService.personalizeTemplate(
        fullFollowUp.template.subject,
        templateVariables
      );
      const personalizedBody = FollowUpService.personalizeTemplate(
        fullFollowUp.template.body,
        templateVariables
      );

      const recipientEmail = fullFollowUp.application.contact?.email || '';
      const mailtoLink = FollowUpService.generateMailtoLink(
        recipientEmail,
        personalizedSubject,
        personalizedBody
      );
      
      window.open(mailtoLink, '_blank');
    } catch (error) {
      console.error('Error generating personalized email:', error);
      // Fallback to simple email
      const subject = `Follow-up: ${followUp.project_name}`;
      const body = `Hallo,\n\nich wollte mich nach dem Stand meiner Bewerbung für ${followUp.project_name} bei ${followUp.company_name} erkundigen.\n\nVielen Dank für Ihr Feedback.\n\nBeste Grüße\n${settings?.full_name || 'Ihr Name'}`;
      
      const mailtoLink = FollowUpService.generateMailtoLink('', subject, body);
      window.open(mailtoLink, '_blank');
    }
  };

  const handleMarkAsSent = (followUpId: string, followUp: FollowUpNotification) => {
    const subject = `Follow-up: ${followUp.project_name}`;
    const body = `Hallo,\n\nich wollte mich nach dem Stand meiner Bewerbung für ${followUp.project_name} bei ${followUp.company_name} erkundigen.\n\nVielen Dank für Ihr Feedback.\n\nBeste Grüße\n${settings?.full_name || 'Ihr Name'}`;
    
    markAsSent({
      followUpId,
      actualSubject: subject,
      actualBody: body
    });
  };

  const handleDeleteClick = (followUp: FollowUpNotification) => {
    setFollowUpToDelete(followUp);
    setDeleteDialogOpen(true);
  };

  const handleDeleteConfirm = () => {
    if (followUpToDelete) {
      deleteScheduledFollowUp(followUpToDelete.id);
      setDeleteDialogOpen(false);
      setFollowUpToDelete(null);
    }
  };

  const handleDeleteCancel = () => {
    setDeleteDialogOpen(false);
    setFollowUpToDelete(null);
  };

  const getFollowUpStatus = (notification: FollowUpNotification) => {
    const now = new Date();
    const scheduledDate = new Date(notification.scheduled_date);
    
    // Check if it's the same day
    const nowDate = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    const scheduleOnlyDate = new Date(scheduledDate.getFullYear(), scheduledDate.getMonth(), scheduledDate.getDate());
    const isSameDay = nowDate.getTime() === scheduleOnlyDate.getTime();
    
    const diffMs = scheduledDate.getTime() - now.getTime();
    const diffDays = Math.floor((scheduleOnlyDate.getTime() - nowDate.getTime()) / (1000 * 60 * 60 * 24));
    
    if (diffMs > 0) {
      // Still upcoming
      if (isSameDay) {
        // Due today but not yet overdue
        return {
          status: 'due',
          text: t('notifications.status.due_today'),
          color: 'orange',
          icon: AlertTriangle
        };
      } else {
        // Future days - but dashboard only shows today's items, so this shouldn't appear
        return {
          status: 'upcoming',
          text: `in ${Math.abs(diffDays)} Tag${Math.abs(diffDays) !== 1 ? 'en' : ''}`,
          color: 'blue',
          icon: Clock
        };
      }
    } else {
      // Past due time
      if (isSameDay) {
        // Overdue today
        return {
          status: 'overdue',
          text: t('notifications.status.overdue'),
          color: 'red',
          icon: AlertTriangle
        };
      } else {
        // Overdue by multiple days
        const overdueDays = Math.abs(diffDays);
        return {
          status: 'overdue',
          text: t('notifications.status.overdue_days', { days: overdueDays, plural: overdueDays !== 1 ? 'e' : '' }),
          color: 'red',
          icon: AlertTriangle
        };
      }
    }
  };

  if (dueFollowUps.length === 0) {
    if (compact) return null;
    
    return (
      <div className="rounded-lg border bg-card/30 backdrop-blur-sm">
        <div className="flex items-center gap-2 px-6 pt-6 pb-4">
          <Bell className="h-4 w-4 sm:h-5 sm:w-5 flex-shrink-0" />
          <h3 className="text-base sm:text-lg font-semibold text-foreground">
            {t('notifications.title')}
          </h3>
        </div>
        <div className="px-6 pb-6">
          <div className="text-center py-4 text-muted-foreground">
            <CheckCircle className="w-6 h-6 mx-auto mb-2 opacity-50" />
            <p className="text-sm">{t('notifications.all_up_to_date')}</p>
          </div>
        </div>
      </div>
    );
  }

  const upcomingCount = dueFollowUps.filter(f => {
    const status = getFollowUpStatus(f);
    return status.status === 'upcoming';
  }).length;
  const dueCount = dueFollowUps.filter(f => {
    const status = getFollowUpStatus(f);
    return status.status === 'due';
  }).length;
  const overdueCount = dueFollowUps.filter(f => {
    const status = getFollowUpStatus(f);
    return status.status === 'overdue';
  }).length;

  return (
    <div 
      className="rounded-lg border bg-card/30 backdrop-blur-sm"
      role="region"
      aria-labelledby="followup-notifications-title"
    >
      {/* Minimalistic Header */}
      <div className={`flex items-center justify-between px-6 pt-6 ${compact ? "pb-2" : "pb-4"}`}>
        <div className="flex items-center gap-2 min-w-0 flex-1">
          <Bell className="h-4 w-4 sm:h-5 sm:w-5 flex-shrink-0" aria-hidden="true" />
          <h3 id="followup-notifications-title" className="text-base sm:text-lg font-semibold text-foreground truncate">
            Follow-ups
          </h3>
          <Badge 
            variant="secondary" 
            className="text-xs bg-blue-500/20 text-blue-700 dark:text-blue-300 flex-shrink-0"
            aria-label={`${dueFollowUps.length} Follow-up${dueFollowUps.length !== 1 ? 's' : ''}`}
          >
            {dueFollowUps.length}
          </Badge>
        </div>
      </div>


      {/* Minimalistic Follow-up Items */}
      <div className="space-y-1 px-6 pb-6" role="list">
        {dueFollowUps.slice(0, compact ? 3 : 5).map((notification) => {
          const status = getFollowUpStatus(notification);
          const StatusIcon = status.icon;
          
          return (
            <div 
              key={notification.id} 
              onClick={() => navigate(`/applications/${notification.application_id}`)}
              onKeyDown={(e) => {
                if (e.key === 'Enter' || e.key === ' ') {
                  e.preventDefault();
                  navigate(`/applications/${notification.application_id}`);
                }
              }}
              className="group relative flex items-center justify-between gap-2 sm:gap-3 p-2 sm:p-3 rounded-lg border bg-muted/20 hover:bg-muted/40 transition-colors cursor-pointer min-w-0"
              role="listitem button"
              tabIndex={0}
              aria-label={`Follow-up für ${notification.project_name || 'Projekt'} bei ${notification.company_name || 'Unbekannt'} - ${status.text}`}
            >
              {/* Content Section - Optimized for Mobile */}
              <div className="flex-1 min-w-0">
                {/* Project Name & Status Badge */}
                <div className="flex items-center gap-2 mb-1">
                  <h4 className="font-medium text-sm truncate text-foreground">
                    {notification.project_name || 'Projekt'}
                  </h4>
                  <Badge 
                    variant={status.status === 'overdue' ? 'destructive' : 'secondary'} 
                    className={`text-xs flex-shrink-0 ${
                      status.status === 'upcoming' ? 'bg-blue-500/20 text-blue-700 dark:text-blue-300' :
                      status.status === 'due' ? 'bg-orange-500/20 text-orange-700 dark:text-orange-300' :
                      ''
                    }`}
                    aria-hidden="true"
                  >
                    <StatusIcon className="w-3 h-3 mr-1" aria-hidden="true" />
                    {status.text}
                  </Badge>
                </div>
                
                {/* Company & Template */}
                <div className="flex flex-col sm:flex-row sm:items-center gap-0 sm:gap-1 text-xs text-muted-foreground">
                  <span className="truncate">{notification.company_name || 'Unbekannt'}</span>
                  <span className="hidden sm:inline">•</span>
                  <span className="truncate sm:flex-shrink-0">{notification.template_name || 'Follow-up'}</span>
                </div>
                
                {/* Scheduled Date - Only on larger screens */}
                <div className="hidden sm:block text-xs text-muted-foreground mt-1">
                  {t('notifications.scheduled')} {formatFollowUpDateTime(notification.scheduled_date)}
                </div>
              </div>

              {/* Action Buttons - Desktop: 4 icons, Mobile: Dropdown */}
              <div className="flex items-center gap-1 flex-shrink-0">
                {/* Desktop: All 4 buttons visible */}
                <div className="hidden sm:flex items-center gap-1">
                  <TooltipProvider>
                    {/* Email Button */}
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={(e) => {
                            e.stopPropagation();
                            handleOpenInEmailClient(notification);
                          }}
                          className="h-7 w-7 p-0 hover:bg-blue-500/20 text-blue-600 hover:text-blue-700"
                          aria-label={`E-Mail für Follow-up ${notification.project_name} öffnen`}
                        >
                          <Mail className="w-3 h-3" aria-hidden="true" />
                        </Button>
                      </TooltipTrigger>
                      <TooltipContent>
                        <p>{t('tooltips.open_email')}</p>
                      </TooltipContent>
                    </Tooltip>
                    
                    {/* Mark as Sent Button */}
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={(e) => {
                            e.stopPropagation();
                            handleMarkAsSent(notification.id, notification);
                          }}
                          className="h-7 w-7 p-0 hover:bg-green-500/20 text-green-600 hover:text-green-700"
                          aria-label={`Follow-up ${notification.project_name} als gesendet markieren`}
                        >
                          <CheckCircle className="w-3 h-3" aria-hidden="true" />
                        </Button>
                      </TooltipTrigger>
                      <TooltipContent>
                        <p>{t('tooltips.mark_as_sent')}</p>
                      </TooltipContent>
                    </Tooltip>
                    
                    {/* Delete Button */}
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={(e) => {
                            e.stopPropagation();
                            handleDeleteClick(notification);
                          }}
                          className="h-7 w-7 p-0 hover:bg-red-500/20 text-muted-foreground hover:text-red-600"
                          aria-label={`Follow-up ${notification.project_name} löschen`}
                        >
                          <Trash2 className="w-3 h-3" aria-hidden="true" />
                        </Button>
                      </TooltipTrigger>
                      <TooltipContent>
                        <p>{t('tooltips.delete')}</p>
                      </TooltipContent>
                    </Tooltip>
                    
                    {/* Dismiss Button */}
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={(e) => {
                            e.stopPropagation();
                            dismissFollowUp(notification.id);
                          }}
                          className="h-7 w-7 p-0 hover:bg-orange-500/20 text-muted-foreground hover:text-orange-600"
                        >
                          <XCircle className="w-3 h-3" />
                        </Button>
                      </TooltipTrigger>
                      <TooltipContent>
                        <p>{t('tooltips.dismiss')}</p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                </div>

                {/* Mobile: Dropdown with 3 dots */}
                <div className="sm:hidden">
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={(e) => e.stopPropagation()}
                        className="h-7 w-7 p-0 hover:bg-muted"
                        aria-label={`Aktionen für Follow-up ${notification.project_name}`}
                      >
                        <MoreHorizontal className="w-3 h-3" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end" onClick={(e) => e.stopPropagation()}>
                      <DropdownMenuItem
                        onClick={(e) => {
                          e.stopPropagation();
                          handleOpenInEmailClient(notification);
                        }}
                        className="text-blue-600 focus:text-blue-700"
                      >
                        <Mail className="w-4 h-4 mr-2" />
                        {t('notifications.dropdown.open_email')}
                      </DropdownMenuItem>
                      <DropdownMenuItem
                        onClick={(e) => {
                          e.stopPropagation();
                          handleMarkAsSent(notification.id, notification);
                        }}
                        className="text-green-600 focus:text-green-700"
                      >
                        <CheckCircle className="w-4 h-4 mr-2" />
                        {t('notifications.dropdown.mark_as_sent')}
                      </DropdownMenuItem>
                      <DropdownMenuItem
                        onClick={(e) => {
                          e.stopPropagation();
                          dismissFollowUp(notification.id);
                        }}
                        className="text-orange-600 focus:text-orange-700"
                      >
                        <XCircle className="w-4 h-4 mr-2" />
                        {t('notifications.dropdown.dismiss')}
                      </DropdownMenuItem>
                      <DropdownMenuSeparator />
                      <DropdownMenuItem
                        onClick={(e) => {
                          e.stopPropagation();
                          handleDeleteClick(notification);
                        }}
                        className="text-red-600 focus:text-red-700"
                      >
                        <Trash2 className="w-4 h-4 mr-2" />
                        {t('notifications.dropdown.delete')}
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
              </div>
            </div>
          );
        })}

        {/* Show More Indicator */}
        {dueFollowUps.length > (compact ? 3 : 5) && (
          <div className="text-center pt-1">
            <p className="text-xs text-muted-foreground">
              {t('notifications.more_followups', { count: dueFollowUps.length - (compact ? 3 : 5) })}
            </p>
          </div>
        )}
      </div>

      {/* Delete Confirmation Dialog */}
      <FollowUpDeleteDialog
        isOpen={deleteDialogOpen}
        onClose={handleDeleteCancel}
        onConfirm={handleDeleteConfirm}
        followUp={followUpToDelete}
        isDeleting={isDeleting}
      />
    </div>
  );
};