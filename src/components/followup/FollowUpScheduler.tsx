import { useState } from 'react';
import { Calendar, Clock, Mail, Plus } from 'lucide-react';
import { format } from 'date-fns';
import { de } from 'date-fns/locale';
import { createFollowUpISOString } from '@/lib/dateUtils';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { FollowUpTemplateManager } from './FollowUpTemplateManager';
import { FollowUpEmailPreview } from './FollowUpEmailPreview';
import { FollowUpService } from '@/services/followUpService';
import { useFollowUpSchedule } from '@/hooks/useFollowUpSchedule';
import { useFollowUpTemplates } from '@/hooks/useFollowUpTemplates';
import { useUserSettings } from '@/hooks/useUserSettings';
import type { FollowUpTemplate, TemplateVariables } from '@/types/followup';
import type { ApplicationWithContact } from '@/types/applications';
import { APPLICATION_STATUS_LABELS } from '@/types/applications';

interface FollowUpSchedulerProps {
  application: ApplicationWithContact;
  trigger?: React.ReactNode;
}

export const FollowUpScheduler = ({ application, trigger }: FollowUpSchedulerProps) => {
  const [isOpen, setIsOpen] = useState(false);
  const [selectedTemplate, setSelectedTemplate] = useState<FollowUpTemplate | null>(null);
  const [showEmailPreview, setShowEmailPreview] = useState(false);
  const [customScheduleDate, setCustomScheduleDate] = useState<string>('');

  const { createFromTemplate, isCreatingFromTemplate } = useFollowUpSchedule();
  const { getTemplatesByStatus } = useFollowUpTemplates();
  const { settings } = useUserSettings();

  // Get templates that match the current application status
  const relevantTemplates = getTemplatesByStatus(application.status);
  
  // Create template variables for personalization
  const templateVariables: TemplateVariables = {
    project_name: application.project_name,
    contact_person: application.contact?.name || application.contact_person || 'Sehr geehrte Damen und Herren',
    company_name: application.company_name,
    user_name: settings?.full_name || 'Ihr Name',
    trigger_days: 0, // Will be set per template
    application_date: application.created_at ? format(new Date(application.created_at), 'dd.MM.yyyy', { locale: de }) : '',
    interview_date: application.interview_date ? format(new Date(application.interview_date), 'dd.MM.yyyy', { locale: de }) : undefined
  };

  const handleTemplateSelect = (template: FollowUpTemplate) => {
    setSelectedTemplate(template);
    setShowEmailPreview(true);
  };

  const handleScheduleFollowUp = async (scheduleDate?: string) => {
    if (!selectedTemplate) return;

    try {
      await createFromTemplate({
        applicationId: application.id,
        templateId: selectedTemplate.id,
        customScheduleDate: scheduleDate || customScheduleDate
      });
      
      setIsOpen(false);
      setSelectedTemplate(null);
      setShowEmailPreview(false);
      setCustomScheduleDate('');
    } catch (error) {
      console.error('Error scheduling follow-up:', error);
    }
  };

  const getDefaultScheduleDate = (template: FollowUpTemplate) => {
    const date = new Date();
    date.setDate(date.getDate() + template.trigger_days);
    date.setHours(9, 0, 0, 0); // Set default time to 9:00 AM
    return date.toISOString().split('T')[0]; // YYYY-MM-DD format for date input
  };

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        {trigger || (
          <Button size="sm" variant="outline">
            <Plus className="w-4 h-4" />
            Follow-up planen
          </Button>
        )}
      </DialogTrigger>
      <DialogContent className="w-[95vw] max-w-4xl max-h-[90vh] overflow-y-auto mx-auto">
        <DialogHeader>
          <DialogTitle className="text-base sm:text-lg break-words">Follow-up für "{application.project_name}" planen</DialogTitle>
          <DialogDescription className="text-sm break-words">
            Wähle ein Template aus oder erstelle ein neues Follow-up für diese Bewerbung.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Application Info */}
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-base">Bewerbungsdetails</CardTitle>
            </CardHeader>
            <CardContent className="space-y-2">
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="font-medium">Projekt:</span>
                  <p className="text-muted-foreground break-words">{application.project_name}</p>
                </div>
                <div>
                  <span className="font-medium">Unternehmen:</span>
                  <p className="text-muted-foreground break-words">{application.company_name}</p>
                </div>
                <div>
                  <span className="font-medium">Kontaktperson:</span>
                  <p className="text-muted-foreground break-words">
                    {application.contact?.name || application.contact_person || 'Nicht angegeben'}
                  </p>
                </div>
                <div>
                  <span className="font-medium">Status:</span>
                  <Badge variant="secondary" className="w-fit">
                    {APPLICATION_STATUS_LABELS[application.status] || application.status}
                  </Badge>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Quick Templates for Current Status */}
          {relevantTemplates.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle className="text-base flex items-center gap-2">
                  <Clock className="w-4 h-4" />
                  Empfohlene Templates für aktuellen Status
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {relevantTemplates.map((template) => {
                    const scheduleDate = new Date();
                    scheduleDate.setDate(scheduleDate.getDate() + template.trigger_days);
                    
                    // Personalize template for display
                    const personalizedVariables = { ...templateVariables, trigger_days: template.trigger_days };
                    const personalizedSubject = FollowUpService.personalizeTemplate(template.subject, personalizedVariables);
                    
                    return (
                      <Card key={template.id} className="w-full max-w-full overflow-x-hidden cursor-pointer hover:bg-muted/50 transition-colors">
                        <CardContent className="p-3 sm:pt-4 sm:px-6">
                          <div className="w-full space-y-3">
                            <div className="w-full min-w-0">
                              <h4 className="font-medium text-sm sm:text-base truncate">{template.name}</h4>
                              <p className="text-xs sm:text-sm text-muted-foreground break-words mt-1">{personalizedSubject}</p>
                              <div className="flex flex-col sm:flex-row sm:items-center gap-1 sm:gap-2 mt-2 text-xs text-muted-foreground">
                                <div className="flex items-center gap-1">
                                  <Calendar className="w-3 h-3" />
                                  <span>
                                    Geplant für: {format(scheduleDate, 'dd.MM.yyyy', { locale: de })}
                                  </span>
                                </div>
                                <Badge variant="outline" className="text-xs w-fit">
                                  {template.trigger_days} Tag{template.trigger_days !== 1 ? 'e' : ''}
                                </Badge>
                              </div>
                            </div>
                            <div className="flex items-center justify-end gap-1 sm:gap-2 pt-2 border-t">
                              <Button
                                variant="outline"
                                size="sm"
                                className="px-3 sm:px-4 text-xs sm:text-sm"
                                onClick={() => handleTemplateSelect(template)}
                              >
                                Vorschau
                              </Button>
                              <Button
                                size="sm"
                                className="px-3 sm:px-4 text-xs sm:text-sm"
                                onClick={() => {
                                  setSelectedTemplate(template);
                                  // Use timezone-safe function to create ISO string
                                  const dateString = format(scheduleDate, 'yyyy-MM-dd');
                                  const isoString = createFollowUpISOString(dateString, '09:00');
                                  handleScheduleFollowUp(isoString);
                                }}
                                disabled={isCreatingFromTemplate}
                              >
                                <Calendar className="w-3 h-3 sm:w-4 sm:h-4 mr-1" />
                                Planen
                              </Button>
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    );
                  })}
                </div>
              </CardContent>
            </Card>
          )}

          <Separator />

          {/* All Templates */}
          <div>
            <h3 className="font-medium mb-4 flex items-center gap-2">
              <Mail className="w-4 h-4" />
              Alle Templates
            </h3>
            <FollowUpTemplateManager 
              onTemplateSelect={handleTemplateSelect}
              selectionMode={true}
              application={application}
            />
          </div>
        </div>
      </DialogContent>

      {/* Email Preview Modal */}
      {selectedTemplate && (
        <FollowUpEmailPreview
          isOpen={showEmailPreview}
          onClose={() => {
            setShowEmailPreview(false);
            setSelectedTemplate(null);
          }}
          template={selectedTemplate}
          application={application}
          onSchedule={handleScheduleFollowUp}
          defaultScheduleDate={getDefaultScheduleDate(selectedTemplate)}
        />
      )}
    </Dialog>
  );
};