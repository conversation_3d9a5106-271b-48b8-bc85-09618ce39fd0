import { useState } from 'react';
import { Plus, Edit, Trash2, Power, PowerOff, Copy } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { 
  AlertDialog, 
  AlertDialogAction, 
  AlertDialogCancel, 
  AlertDialogContent, 
  AlertDialogDescription, 
  AlertDialogFooter, 
  AlertDialogHeader, 
  AlertDialogTitle, 
  AlertDialogTrigger 
} from '@/components/ui/alert-dialog';
import { useTranslation } from 'react-i18next';
import { useFollowUpTemplates } from '@/hooks/useFollowUpTemplates';
import { FollowUpTemplateForm } from './FollowUpTemplateForm';
import { FollowUpService } from '@/services/followUpService';
import { useUserSettings } from '@/hooks/useUserSettings';
import type { FollowUpTemplate, TemplateVariables } from '@/types/followup';
import type { ApplicationWithContact } from '@/types/applications';
import { format } from 'date-fns';
import { de } from 'date-fns/locale';

interface FollowUpTemplateManagerProps {
  onTemplateSelect?: (template: FollowUpTemplate) => void;
  selectionMode?: boolean;
  application?: ApplicationWithContact; // Add application data for personalization
}

export const FollowUpTemplateManager = ({ 
  onTemplateSelect, 
  selectionMode = false,
  application 
}: FollowUpTemplateManagerProps) => {
  const [isFormOpen, setIsFormOpen] = useState(false);
  const [editingTemplate, setEditingTemplate] = useState<FollowUpTemplate | null>(null);
  const { t } = useTranslation('followup');
  
  const {
    templates,
    isLoading,
    isDeleting,
    isToggling,
    deleteTemplate,
    toggleTemplateActive,
    createDefaultTemplates
  } = useFollowUpTemplates();
  
  const { settings } = useUserSettings();
  
  // Create template variables for personalization when application is provided
  const getTemplateVariables = (): TemplateVariables | null => {
    if (!application) return null;
    
    return {
      project_name: application.project_name,
      contact_person: application.contact?.name || application.contact_person || t('template_manager.greetings.default'),
      company_name: application.company_name,
      user_name: settings?.full_name || t('template_manager.greetings.your_name'),
      trigger_days: 0, // Will be set per template
      application_date: application.created_at ? format(new Date(application.created_at), 'dd.MM.yyyy', { locale: de }) : '',
      interview_date: application.interview_date ? format(new Date(application.interview_date), 'dd.MM.yyyy', { locale: de }) : undefined
    };
  };
  
  // Helper to get personalized template content
  const getPersonalizedContent = (template: FollowUpTemplate): { subject: string; body: string } => {
    const variables = getTemplateVariables();
    if (!variables) {
      return { subject: template.subject, body: template.body };
    }
    
    const personalizedVariables = { ...variables, trigger_days: template.trigger_days };
    return {
      subject: FollowUpService.personalizeTemplate(template.subject, personalizedVariables),
      body: FollowUpService.personalizeTemplate(template.body, personalizedVariables)
    };
  };

  const handleEdit = (template: FollowUpTemplate) => {
    setEditingTemplate(template);
    setIsFormOpen(true);
  };

  const handleClone = (template: FollowUpTemplate) => {
    setEditingTemplate({
      ...template,
      id: '', // Clear ID to create new template
      name: `${template.name}${t('template_manager.copy_suffix')}`,
      created_at: '',
      updated_at: ''
    });
    setIsFormOpen(true);
  };

  const handleFormClose = () => {
    setIsFormOpen(false);
    setEditingTemplate(null);
  };

  const handleDelete = (templateId: string) => {
    deleteTemplate(templateId);
  };

  const handleToggleActive = (templateId: string, isActive: boolean) => {
    toggleTemplateActive({ templateId, isActive: !isActive });
  };

  const getStatusTriggerLabel = (trigger: string) => {
    return t(`template_manager.status_triggers.${trigger}`, trigger);
  };

  const getStatusTriggerColor = (trigger: string) => {
    const colors: Record<string, string> = {
      'application_sent': 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300',
      'interview_scheduled': 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300',
      'interview_completed': 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300',
      'offer_received': 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300',
      'rejected': 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300',
      'inquiry_received': 'bg-indigo-100 text-indigo-800 dark:bg-indigo-900 dark:text-indigo-300'
    };
    return colors[trigger] || 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300';
  };

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>{t('template_manager.title')}</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <>
      <Card className="w-full max-w-full overflow-x-hidden">
        <CardHeader className="pb-3">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3">
            <CardTitle className="text-base sm:text-lg">{t('template_manager.title')}</CardTitle>
            <div className="flex gap-2 flex-wrap">
            {templates.length === 0 && (
              <Button 
                variant="outline" 
                size="sm"
                onClick={createDefaultTemplates}
              >
                {t('template_manager.create_default_templates')}
              </Button>
            )}
            <Button 
              size="sm" 
              onClick={() => setIsFormOpen(true)}
              disabled={selectionMode}
            >
              <Plus className="w-4 h-4" />
              {t('template_manager.new_template')}
            </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          {templates.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              <p>{t('template_manager.no_templates_title')}</p>
              <p className="text-sm mt-2">{t('template_manager.no_templates_subtitle')}</p>
            </div>
          ) : (
            <div className="space-y-3 sm:space-y-4">
              {(selectionMode ? templates.filter(t => t.is_active) : templates).map((template) => {
                const personalizedContent = getPersonalizedContent(template);
                
                return (
                <Card key={template.id} className="w-full max-w-full overflow-x-hidden">
                  <CardContent className="p-3 sm:pt-6 sm:px-6">
                    <div className="w-full space-y-3">
                      <div className="w-full min-w-0">
                        <div className="flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-3">
                          <h3 className="font-semibold text-sm sm:text-base truncate min-w-0">{template.name}</h3>
                          <div className="flex items-center gap-1 sm:gap-2 flex-wrap">
                            <Badge 
                              variant="secondary" 
                              className={getStatusTriggerColor(template.status_trigger)}
                            >
                              {getStatusTriggerLabel(template.status_trigger)}
                            </Badge>
                            <Badge variant="outline">
                              {t('template_manager.labels.trigger_days', { days: template.trigger_days, plural: template.trigger_days !== 1 ? 'e' : '' })}
                            </Badge>
                            <Badge 
                              variant={template.is_active ? "default" : "secondary"}
                              className={template.is_active ? "" : "opacity-50"}
                            >
                              {template.is_active ? t('template_manager.labels.active') : t('template_manager.labels.inactive')}
                            </Badge>
                            {application && (
                              <Badge variant="outline" className="text-xs">
                                {t('template_manager.personalized')}
                              </Badge>
                            )}
                          </div>
                        </div>
                        
                        <div className="space-y-2 mt-3">
                          <div>
                            <span className="text-xs sm:text-sm font-medium text-muted-foreground">{t('template_manager.subject_label')}</span>
                            <p className="text-xs sm:text-sm mt-1 break-words">{personalizedContent.subject}</p>
                          </div>
                          <div>
                            <span className="text-xs sm:text-sm font-medium text-muted-foreground">{t('template_manager.message_label')}</span>
                            <p className="text-xs sm:text-sm mt-1 line-clamp-3 text-muted-foreground break-words">
                              {personalizedContent.body.substring(0, 120)}
                              {personalizedContent.body.length > 120 && '...'}
                            </p>
                          </div>
                        </div>
                      </div>

                      <div className="flex items-center justify-end gap-1 sm:gap-2 mt-3 pt-3 border-t">
                        {selectionMode ? (
                          <Button
                            size="sm"
                            className="px-3 sm:px-4 text-xs sm:text-sm"
                            onClick={() => onTemplateSelect?.(template)}
                            disabled={!template.is_active}
                          >
                            {t('template_manager.actions.select')}
                          </Button>
                        ) : (
                          <>
                            <Button
                              size="sm"
                              variant="ghost"
                              className="px-2 sm:px-3"
                              onClick={() => handleToggleActive(template.id, template.is_active)}
                              disabled={isToggling}
                            >
                              {template.is_active ? (
                                <PowerOff className="w-3 h-3 sm:w-4 sm:h-4" />
                              ) : (
                                <Power className="w-3 h-3 sm:w-4 sm:h-4" />
                              )}
                            </Button>
                            <Button
                              size="sm"
                              variant="ghost"
                              className="px-2 sm:px-3"
                              onClick={() => handleClone(template)}
                            >
                              <Copy className="w-3 h-3 sm:w-4 sm:h-4" />
                            </Button>
                            <Button
                              size="sm"
                              variant="ghost"
                              className="px-2 sm:px-3"
                              onClick={() => handleEdit(template)}
                            >
                              <Edit className="w-3 h-3 sm:w-4 sm:h-4" />
                            </Button>
                            <AlertDialog>
                              <AlertDialogTrigger asChild>
                                <Button
                                  size="sm"
                                  variant="ghost"
                                  className="text-destructive hover:text-destructive px-2 sm:px-3"
                                  disabled={isDeleting}
                                >
                                  <Trash2 className="w-3 h-3 sm:w-4 sm:h-4" />
                                </Button>
                              </AlertDialogTrigger>
                              <AlertDialogContent className="w-[95vw] max-w-lg mx-auto">
                                <AlertDialogHeader>
                                  <AlertDialogTitle>{t('template_manager.delete_dialog.title')}</AlertDialogTitle>
                                  <AlertDialogDescription className="break-words">
                                    {t('template_manager.delete_dialog.description', { name: template.name })}
                                  </AlertDialogDescription>
                                </AlertDialogHeader>
                                <AlertDialogFooter className="flex-col sm:flex-row gap-2">
                                  <AlertDialogCancel className="w-full sm:w-auto">{t('template_manager.delete_dialog.cancel')}</AlertDialogCancel>
                                  <AlertDialogAction
                                    onClick={() => handleDelete(template.id)}
                                    className="bg-destructive text-destructive-foreground hover:bg-destructive/90 w-full sm:w-auto"
                                  >
                                    {t('template_manager.delete_dialog.confirm')}
                                  </AlertDialogAction>
                                </AlertDialogFooter>
                              </AlertDialogContent>
                            </AlertDialog>
                          </>
                        )}
                      </div>
                    </div>
                  </CardContent>
                </Card>
                );
              })}
            </div>
          )}
        </CardContent>
      </Card>

      <FollowUpTemplateForm
        isOpen={isFormOpen}
        onClose={handleFormClose}
        template={editingTemplate}
      />
    </>
  );
};