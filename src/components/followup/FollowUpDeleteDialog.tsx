import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { Button } from '@/components/ui/button';
import { Trash2 } from 'lucide-react';
import type { FollowUpNotification } from '@/types/followup';

interface FollowUpDeleteDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  followUp: FollowUpNotification | null;
  isDeleting?: boolean;
}

export const FollowUpDeleteDialog = ({
  isOpen,
  onClose,
  onConfirm,
  followUp,
  isDeleting = false
}: FollowUpDeleteDialogProps) => {
  if (!followUp) return null;

  return (
    <AlertDialog open={isOpen} onOpenChange={onClose}>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle className="flex items-center gap-2">
            <Trash2 className="h-5 w-5 text-destructive" />
            Follow-up löschen
          </AlertDialogTitle>
          <AlertDialogDescription>
            Möchtest du das Follow-up für <strong>"{followUp.project_name}"</strong> bei{' '}
            <strong>{followUp.company_name}</strong> wirklich löschen?
            <br />
            <br />
            Diese Aktion kann nicht rückgängig gemacht werden. Das Follow-up wird permanent gelöscht
            und auch der entsprechende Kalendereintrag wird entfernt.
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel disabled={isDeleting}>
            Abbrechen
          </AlertDialogCancel>
          <AlertDialogAction asChild>
            <Button 
              variant="destructive" 
              onClick={onConfirm}
              disabled={isDeleting}
            >
              {isDeleting ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
                  Wird gelöscht...
                </>
              ) : (
                <>
                  <Trash2 className="h-4 w-4 mr-2" />
                  Follow-up löschen
                </>
              )}
            </Button>
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
};