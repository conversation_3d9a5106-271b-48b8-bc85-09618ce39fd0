import { useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Button } from '@/components/ui/button';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Badge } from '@/components/ui/badge';
import { useFollowUpTemplates } from '@/hooks/useFollowUpTemplates';
import type { FollowUpTemplate } from '@/types/followup';

const formSchema = z.object({
  name: z.string().min(1, 'Name ist erforderlich').max(100, 'Name zu lang'),
  subject: z.string().min(1, 'Betreff ist erforderlich').max(200, 'Betreff zu lang'),
  body: z.string().min(10, 'Nachricht muss mindestens 10 Zeichen haben').max(2000, 'Nachricht zu lang'),
  trigger_days: z.number().min(1, 'Mindestens 1 Tag').max(365, 'Maximal 365 Tage'),
  status_trigger: z.string().min(1, 'Status-Trigger ist erforderlich'),
  is_active: z.boolean()
});

type FormData = z.infer<typeof formSchema>;

interface FollowUpTemplateFormProps {
  isOpen: boolean;
  onClose: () => void;
  template?: FollowUpTemplate | null;
}

const statusTriggerOptions = [
  { value: 'application_sent', label: 'Nach Bewerbung' },
  { value: 'interview_scheduled', label: 'Interview geplant' },
  { value: 'interview_completed', label: 'Nach Interview' },
  { value: 'offer_received', label: 'Angebot erhalten' },
  { value: 'rejected', label: 'Absage erhalten' },
  { value: 'inquiry_received', label: 'Anfrage erhalten' }
];

const placeholders = [
  { key: '{project_name}', description: 'Titel des Projekts' },
  { key: '{contact_person}', description: 'Name der Kontaktperson' },
  { key: '{company_name}', description: 'Name des Unternehmens' },
  { key: '{user_name}', description: 'Dein Name' },
  { key: '{trigger_days}', description: 'Anzahl der Tage seit Status-Änderung' },
  { key: '{application_date}', description: 'Bewerbungsdatum' },
  { key: '{interview_date}', description: 'Interview-Datum (falls vorhanden)' }
];

export const FollowUpTemplateForm = ({ isOpen, onClose, template }: FollowUpTemplateFormProps) => {
  const { createTemplate, updateTemplate, isCreating, isUpdating } = useFollowUpTemplates();
  
  const form = useForm<FormData>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: '',
      subject: '',
      body: '',
      trigger_days: 7,
      status_trigger: '',
      is_active: true
    }
  });

  const isEditing = template && template.id !== '';
  const isLoading = isCreating || isUpdating;

  useEffect(() => {
    if (template) {
      form.reset({
        name: template.name,
        subject: template.subject,
        body: template.body,
        trigger_days: template.trigger_days,
        status_trigger: template.status_trigger,
        is_active: template.is_active
      });
    } else {
      form.reset({
        name: '',
        subject: '',
        body: '',
        trigger_days: 7,
        status_trigger: '',
        is_active: true
      });
    }
  }, [template, form]);

  const onSubmit = async (data: FormData) => {
    try {
      if (isEditing && template) {
        await updateTemplate({
          id: template.id,
          ...data
        });
      } else {
        await createTemplate(data);
      }
      form.reset();
      onClose();
    } catch (error) {
      console.error('Error saving template:', error);
    }
  };

  const handleClose = () => {
    form.reset();
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="w-[95vw] max-w-2xl max-h-[90vh] overflow-y-auto mx-auto">
        <DialogHeader>
          <DialogTitle>
            {isEditing ? 'Template bearbeiten' : 'Neues Template erstellen'}
          </DialogTitle>
          <DialogDescription>
            Erstelle wiederverwendbare Email-Templates für deine Follow-ups.
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Template-Name</FormLabel>
                    <FormControl>
                      <Input placeholder="z.B. Nach Bewerbung nachfragen" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="status_trigger"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Trigger bei Status</FormLabel>
                    <Select onValueChange={field.onChange} value={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Status auswählen" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {statusTriggerOptions.map((option) => (
                          <SelectItem key={option.value} value={option.value}>
                            {option.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="trigger_days"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Anzahl Tage nach Status-Änderung</FormLabel>
                    <FormControl>
                      <Input 
                        type="number" 
                        min="1" 
                        max="365"
                        {...field} 
                        onChange={(e) => field.onChange(parseInt(e.target.value) || 1)}
                      />
                    </FormControl>
                    <FormDescription>
                      Follow-up wird automatisch nach dieser Anzahl Tage geplant
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="is_active"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                    <div className="space-y-0.5">
                      <FormLabel className="text-base">Aktiv</FormLabel>
                      <FormDescription>
                        Template für automatische Follow-ups verwenden
                      </FormDescription>
                    </div>
                    <FormControl>
                      <Switch
                        checked={field.value}
                        onCheckedChange={field.onChange}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />
            </div>

            <FormField
              control={form.control}
              name="subject"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Email-Betreff</FormLabel>
                  <FormControl>
                    <Input 
                      placeholder="z.B. Nachfrage zu meiner Bewerbung - {project_name}" 
                      {...field} 
                    />
                  </FormControl>
                  <FormDescription>
                    Du kannst Platzhalter verwenden (siehe unten)
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="body"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Email-Text</FormLabel>
                  <FormControl>
                    <Textarea 
                      placeholder="Liebe/r {contact_person},..."
                      className="min-h-[200px]"
                      {...field} 
                    />
                  </FormControl>
                  <FormDescription>
                    Verwende Platzhalter für personalisierte Emails
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="bg-muted/50 rounded-lg p-4">
              <h4 className="font-medium mb-3">Verfügbare Platzhalter:</h4>
              <div className="grid grid-cols-1 gap-2">
                {placeholders.map((placeholder) => (
                  <div key={placeholder.key} className="flex flex-col sm:flex-row sm:items-center gap-1 sm:gap-2">
                    <Badge variant="outline" className="font-mono text-xs w-fit">
                      {placeholder.key}
                    </Badge>
                    <span className="text-xs sm:text-sm text-muted-foreground break-words">
                      {placeholder.description}
                    </span>
                  </div>
                ))}
              </div>
            </div>

            <DialogFooter className="flex-col sm:flex-row gap-2">
              <Button type="button" variant="outline" onClick={handleClose} className="w-full sm:w-auto">
                Abbrechen
              </Button>
              <Button type="submit" disabled={isLoading} className="w-full sm:w-auto">
                {isLoading && (
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
                )}
                {isEditing ? 'Aktualisieren' : 'Erstellen'}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
};