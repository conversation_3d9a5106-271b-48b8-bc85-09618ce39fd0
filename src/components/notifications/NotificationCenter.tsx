import React, { useState } from 'react';
import { <PERSON>, X, <PERSON><PERSON><PERSON><PERSON>, Eye, EyeOff, Trash2, Filter, More<PERSON><PERSON><PERSON><PERSON>, ArrowRight } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuSeparator, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { useNotifications, useNotificationGroups, useUnreadNotifications } from '@/hooks/useNotifications';
import { NotificationItem } from './NotificationItem';
import { NotificationFilters } from './NotificationFilters';
import type { NotificationFilters as NotificationFiltersType, NotificationType } from '@/types/notifications';
import { format, isToday, isYesterday } from 'date-fns';
import { de } from 'date-fns/locale';
import { useNavigate } from 'react-router-dom';
import { useTranslation } from '@/hooks/useTranslation';
import { cn } from '@/lib/utils';

interface NotificationCenterProps {
  compact?: boolean;
  onNavigate?: () => void;
}

export const NotificationCenter: React.FC<NotificationCenterProps> = ({ compact = false, onNavigate }) => {
  const [activeTab, setActiveTab] = useState<'all' | 'unread' | 'groups'>('all');
  const [filters, setFilters] = useState<NotificationFiltersType>({});
  const [showFilters, setShowFilters] = useState(false);
  const navigate = useNavigate();
  const { t } = useTranslation('common');

  const unreadCount = useUnreadNotifications();
  const { notifications: allNotifications, loading, markAllAsRead, clearAll, markAsRead, markAsUnread } = useNotifications(
    activeTab === 'unread' ? { ...filters, unread_only: true } : filters
  );
  const { notifications: unreadNotifications } = useNotifications({ unread_only: true });
  const { groups } = useNotificationGroups();

  const filteredNotifications = allNotifications;

  const groupNotificationsByDate = (notifications: typeof allNotifications) => {
    const grouped = new Map<string, typeof allNotifications>();
    
    notifications.forEach(notification => {
      const date = new Date(notification.created_at);
      let dateKey: string;
      
      if (isToday(date)) {
        dateKey = t('notifications.today');
      } else if (isYesterday(date)) {
        dateKey = t('notifications.yesterday');
      } else {
        dateKey = format(date, 'dd. MMMM yyyy', { locale: de });
      }
      
      if (!grouped.has(dateKey)) {
        grouped.set(dateKey, []);
      }
      grouped.get(dateKey)!.push(notification);
    });
    
    return Array.from(grouped.entries()).map(([date, items]) => ({
      date,
      notifications: items
    }));
  };

  const getNotificationTypeIcon = (type: NotificationType) => {
    switch (type) {
      case 'follow_up_due':
      case 'follow_up_overdue':
        return '📧';
      case 'application_reminder':
        return '📝';
      case 'interview_reminder':
        return '🤝';
      case 'calendar_event':
        return '📅';
      case 'system_update':
        return '⚙️';
      default:
        return '📢';
    }
  };

  const getNotificationTypeLabel = (type: NotificationType) => {
    switch (type) {
      case 'follow_up_due':
        return t('notifications.types.follow_up_due');
      case 'follow_up_overdue':
        return t('notifications.types.follow_up_overdue');
      case 'application_reminder':
        return t('notifications.types.application_reminder');
      case 'interview_reminder':
        return t('notifications.types.interview_reminder');
      case 'calendar_event':
        return t('notifications.types.calendar_event');
      case 'system_update':
        return t('notifications.types.system_update');
      default:
        return t('notifications.types.general');
    }
  };

  if (loading) {
    return (
      <Card className={compact ? "w-80" : ""}>
        <CardHeader className={compact ? "pb-2" : "pb-3"}>
          <CardTitle className={cn("flex items-center", compact ? "gap-2" : "gap-2")}>
            <Bell className={compact ? "h-4 w-4" : "h-5 w-5"} />
            <span className={compact ? "text-sm" : "text-base"}>{t('notifications.title')}</span>
          </CardTitle>
        </CardHeader>
        <CardContent className={compact ? "px-2" : ""}>
          <div className={cn("animate-pulse", compact ? "space-y-1" : "space-y-2")}>
            {[...Array(compact ? 5 : 3)].map((_, i) => (
              <div key={i} className={cn("bg-muted rounded-lg", compact ? "h-12" : "h-16")} />
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  const currentNotifications = activeTab === 'unread' ? unreadNotifications : filteredNotifications;

  if (compact) {
    return (
      <Card className="w-80 shadow-xl border-0 bg-background rounded-t-xl rounded-b-xl overflow-hidden">
        <CardHeader className="pb-3 rounded-t-xl border-b border-border/50">
          <div className="flex items-center justify-between min-w-0">
            <CardTitle className="flex items-center gap-2 text-foreground min-w-0 flex-1">
              <Bell className="h-4 w-4 text-primary flex-shrink-0" />
              <span className="text-sm sm:text-base font-semibold truncate">{t('notifications.title')}</span>
              {unreadCount > 0 && (
                <Badge variant="destructive" className="text-xs font-bold px-1.5 py-0.5 bg-red-500 text-white shadow-sm flex-shrink-0">
                  {unreadCount}
                </Badge>
              )}
            </CardTitle>
            {unreadCount > 0 && (
              <Button
                variant="ghost"
                size="sm"
                onClick={markAllAsRead}
                className="h-7 w-7 p-0 hover:bg-primary/10 hover:text-primary transition-colors rounded-md flex-shrink-0 ml-2"
                title={t('notifications.mark_all_read')}
              >
                <CheckCircle className="h-4 w-4" />
              </Button>
            )}
          </div>
        </CardHeader>
        <CardContent className="p-0">
          <ScrollArea className="h-96">
            {currentNotifications.length === 0 ? (
              <div className="p-6 text-center">
                <div className="p-2 rounded-full bg-muted/50 w-fit mx-auto mb-2">
                  <Bell className="h-5 w-5 text-muted-foreground/60" />
                </div>
                <p className="text-xs text-muted-foreground font-medium">{t('notifications.no_notifications')}</p>
              </div>
            ) : (
              <div>
                {currentNotifications.slice(0, 12).map((notification) => (
                  <NotificationItem
                    key={notification.id}
                    notification={notification}
                    compact
                  />
                ))}
                {currentNotifications.length > 12 && (
                  <div className="p-3 text-center border-t border-border/30">
                    <Badge variant="outline" className="text-xs text-muted-foreground bg-muted/30">
                      +{currentNotifications.length - 12} {t('notifications.more_notifications')}
                    </Badge>
                  </div>
                )}
              </div>
            )}
          </ScrollArea>
          <div className="p-3 border-t border-border/50 rounded-b-xl">
            <Button
              variant="ghost"
              className="w-full justify-between hover:bg-primary/10 hover:text-primary transition-all duration-200 rounded-lg h-9"
              onClick={() => {
                onNavigate?.();
                navigate('/notifications');
              }}
            >
              <span className="font-medium">{t('notifications.show_all')}</span>
              <ArrowRight className="h-4 w-4" />
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <Bell className="h-5 w-5" />
            {t('notifications.title')}
            {unreadCount > 0 && (
              <Badge variant="destructive">
                {unreadCount}
              </Badge>
            )}
          </CardTitle>
          <div className="flex items-center gap-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setShowFilters(!showFilters)}
              className={showFilters ? "bg-muted" : ""}
            >
              <Filter className="h-4 w-4" />
            </Button>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="sm">
                  <MoreHorizontal className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem onClick={markAllAsRead} disabled={unreadCount === 0}>
                  <CheckCircle className="h-4 w-4 mr-2" />
                  {t('notifications.mark_all_as_read')}
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem onClick={clearAll} className="text-destructive">
                  <Trash2 className="h-4 w-4 mr-2" />
                  {t('notifications.clear_all')}
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>
        {showFilters && (
          <NotificationFilters
            filters={filters}
            onChange={setFilters}
            onClose={() => setShowFilters(false)}
          />
        )}
      </CardHeader>
      <CardContent className="p-0">
        <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as any)}>
          <TabsList className="w-full rounded-none border-b">
            <TabsTrigger value="all" className="flex-1">
              {t('notifications.tabs.all')} ({allNotifications.length})
            </TabsTrigger>
            <TabsTrigger value="unread" className="flex-1">
              {t('notifications.tabs.unread')} ({unreadCount})
            </TabsTrigger>
            <TabsTrigger value="groups" className="flex-1">
              {t('notifications.tabs.groups')} ({groups.length})
            </TabsTrigger>
          </TabsList>

          <TabsContent value="all" className="mt-0">
            <ScrollArea className="h-96">
              {filteredNotifications.length === 0 ? (
                <div className="p-6 text-center text-muted-foreground">
                  <Bell className="h-10 w-10 mx-auto mb-3 opacity-50" />
                  <h3 className="text-sm font-medium mb-1">{t('notifications.no_notifications')}</h3>
                  <p className="text-xs">{t('notifications.up_to_date')}</p>
                </div>
              ) : (
                <div className="space-y-3 p-3">
                  {groupNotificationsByDate(filteredNotifications).map(({ date, notifications }) => (
                    <div key={date}>
                      <h4 className="text-xs font-medium text-muted-foreground mb-1.5 sticky top-0 bg-background/80 backdrop-blur-sm py-1">
                        {date}
                      </h4>
                      <div className="space-y-1">
                        {notifications.map((notification) => (
                          <NotificationItem
                            key={notification.id}
                            notification={notification}
                          />
                        ))}
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </ScrollArea>
          </TabsContent>

          <TabsContent value="unread" className="mt-0">
            <ScrollArea className="h-96">
              {unreadNotifications.length === 0 ? (
                <div className="p-6 text-center text-muted-foreground">
                  <CheckCircle className="h-10 w-10 mx-auto mb-3 opacity-50" />
                  <h3 className="text-sm font-medium mb-1">{t('notifications.all_read')}</h3>
                  <p className="text-xs">{t('notifications.no_unread')}</p>
                </div>
              ) : (
                <div className="space-y-1 p-3">
                  {unreadNotifications.map((notification) => (
                    <NotificationItem
                      key={notification.id}
                      notification={notification}
                    />
                  ))}
                </div>
              )}
            </ScrollArea>
          </TabsContent>

          <TabsContent value="groups" className="mt-0">
            <ScrollArea className="h-96">
              {groups.length === 0 ? (
                <div className="p-6 text-center text-muted-foreground">
                  <Bell className="h-10 w-10 mx-auto mb-3 opacity-50" />
                  <h3 className="text-sm font-medium mb-1">{t('notifications.no_groups')}</h3>
                  <p className="text-xs">{t('notifications.no_notifications_to_group')}</p>
                </div>
              ) : (
                <div className="space-y-2 p-3">
                  {groups.map((group) => (
                    <Card key={group.type} className="border-l-3 border-l-primary">
                      <CardHeader className="pb-1.5">
                        <CardTitle className="text-xs flex items-center gap-1.5">
                          <span className="text-sm">{getNotificationTypeIcon(group.type)}</span>
                          {getNotificationTypeLabel(group.type)}
                          <Badge variant="secondary" className="text-xs h-4 px-1.5">
                            {group.count}
                          </Badge>
                        </CardTitle>
                      </CardHeader>
                      <CardContent className="pt-0">
                        <p className="text-xs text-muted-foreground mb-1">
                          {t('notifications.latest')}: {group.latest_notification.title}
                        </p>
                        <p className="text-xs text-muted-foreground">
                          {format(new Date(group.latest_notification.created_at), 'dd.MM.yyyy HH:mm', { locale: de })}
                        </p>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              )}
            </ScrollArea>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
};