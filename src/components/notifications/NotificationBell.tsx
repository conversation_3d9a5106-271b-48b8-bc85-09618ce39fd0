import React, { useState } from 'react';
import { Bell, BellRing } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { useUnreadNotifications } from '@/hooks/useNotifications';
import { NotificationCenter } from './NotificationCenter';
import { cn } from '@/lib/utils';
import { useNavigate } from 'react-router-dom';

interface NotificationBellProps {
  className?: string;
  showBadge?: boolean;
  size?: 'sm' | 'md' | 'lg';
  mode?: 'popover' | 'navigate';
}

export const NotificationBell: React.FC<NotificationBellProps> = ({ 
  className,
  showBadge = true,
  size = 'md',
  mode = 'popover'
}) => {
  const [open, setOpen] = useState(false);
  const navigate = useNavigate();
  const unreadCount = useUnreadNotifications();

  const iconSizeMap = {
    sm: 'h-4 w-4',
    md: 'h-5 w-5',
    lg: 'h-6 w-6'
  };

  const buttonSizeMap = {
    sm: 'h-8 w-8',
    md: 'h-9 w-9',
    lg: 'h-10 w-10'
  };

  const handleClick = () => {
    if (mode === 'navigate') {
      navigate('/notifications');
    }
  };

  const BellButton = (
    <Button
      variant="ghost"
      className={cn(
        "relative p-0 hover:bg-accent",
        buttonSizeMap[size],
        className
      )}
      aria-label={`Benachrichtigungen${unreadCount > 0 ? ` (${unreadCount} ungelesen)` : ''}`}
      aria-expanded={mode === 'popover' ? 'false' : undefined}
      aria-haspopup={mode === 'popover' ? 'true' : undefined}
      role="button"
      tabIndex={0}
      onClick={mode === 'navigate' ? handleClick : undefined}
      onKeyDown={(e) => {
        if (e.key === 'Enter' || e.key === ' ') {
          e.preventDefault();
          if (mode === 'navigate') handleClick();
        }
      }}
    >
      {unreadCount > 0 ? (
        <BellRing className={cn(iconSizeMap[size], "animate-pulse")} />
      ) : (
        <Bell className={iconSizeMap[size]} />
      )}
      
      {showBadge && unreadCount > 0 && (
        <Badge 
          variant="destructive" 
          className={cn(
            "absolute -top-1 -right-1 h-5 w-5 flex items-center justify-center p-0 text-xs font-bold",
            size === 'sm' && "h-4 w-4 text-xs",
            size === 'lg' && "h-6 w-6 text-sm"
          )}
          aria-hidden="true"
        >
          {unreadCount > 99 ? '99+' : unreadCount}
        </Badge>
      )}
    </Button>
  );

  if (mode === 'navigate') {
    return BellButton;
  }

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        {BellButton}
      </PopoverTrigger>
      <PopoverContent 
        className="w-auto p-0 shadow-2xl border-0 bg-transparent" 
        align="end"
        sideOffset={12}
      >
        <NotificationCenter compact onNavigate={() => setOpen(false)} />
      </PopoverContent>
    </Popover>
  );
};