import React from 'react';
import { X, Filter } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import { Badge } from '@/components/ui/badge';
import type { NotificationFilters as NotificationFiltersType, NotificationType, NotificationPriority } from '@/types/notifications';

interface NotificationFiltersProps {
  filters: NotificationFiltersType;
  onChange: (filters: NotificationFiltersType) => void;
  onClose: () => void;
}

export const NotificationFilters: React.FC<NotificationFiltersProps> = ({
  filters,
  onChange,
  onClose
}) => {
  const notificationTypes: { value: NotificationType; label: string; icon: string }[] = [
    { value: 'follow_up_due', label: 'Fällige Follow-ups', icon: '⏰' },
    { value: 'follow_up_overdue', label: 'Überfällige Follow-ups', icon: '🚨' },
    { value: 'application_reminder', label: 'Bewerbungs-Erinnerungen', icon: '📝' },
    { value: 'interview_reminder', label: 'Interview-Termine', icon: '🤝' },
    { value: 'calendar_event', label: 'Kalender-Ereignisse', icon: '📅' },
    { value: 'system_update', label: 'System-Updates', icon: '⚙️' },
    { value: 'general', label: 'Allgemein', icon: '📢' }
  ];

  const priorities: { value: NotificationPriority; label: string; color: string }[] = [
    { value: 'urgent', label: 'Dringend', color: 'bg-red-500' },
    { value: 'high', label: 'Hoch', color: 'bg-orange-500' },
    { value: 'normal', label: 'Normal', color: 'bg-blue-500' },
    { value: 'low', label: 'Niedrig', color: 'bg-gray-500' }
  ];

  const updateFilters = (key: keyof NotificationFiltersType, value: any) => {
    onChange({
      ...filters,
      [key]: value
    });
  };

  const toggleType = (type: NotificationType) => {
    const currentTypes = filters.types || [];
    const newTypes = currentTypes.includes(type)
      ? currentTypes.filter(t => t !== type)
      : [...currentTypes, type];
    
    updateFilters('types', newTypes.length === 0 ? undefined : newTypes);
  };

  const togglePriority = (priority: NotificationPriority) => {
    const currentPriorities = filters.priorities || [];
    const newPriorities = currentPriorities.includes(priority)
      ? currentPriorities.filter(p => p !== priority)
      : [...currentPriorities, priority];
    
    updateFilters('priorities', newPriorities.length === 0 ? undefined : newPriorities);
  };

  const clearAllFilters = () => {
    onChange({});
  };

  const hasActiveFilters = Boolean(
    filters.types?.length || 
    filters.priorities?.length || 
    filters.unread_only ||
    filters.date_from ||
    filters.date_to
  );

  return (
    <Card className="mt-4 border-l-4 border-l-primary">
      <CardHeader className="pb-3">
        <CardTitle className="flex items-center justify-between text-sm">
          <div className="flex items-center gap-2">
            <Filter className="h-4 w-4" />
            Filter
            {hasActiveFilters && (
              <Badge variant="secondary" className="text-xs">
                Aktiv
              </Badge>
            )}
          </div>
          <div className="flex items-center gap-2">
            {hasActiveFilters && (
              <Button
                variant="ghost"
                size="sm"
                onClick={clearAllFilters}
                className="text-xs h-6"
              >
                Zurücksetzen
              </Button>
            )}
            <Button
              variant="ghost"
              size="sm"
              onClick={onClose}
              className="h-6 w-6 p-0"
            >
              <X className="h-3 w-3" />
            </Button>
          </div>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Quick Filters */}
        <div>
          <h4 className="text-sm font-medium mb-2">Schnellfilter</h4>
          <div className="flex items-center gap-2">
            <Button
              variant={filters.unread_only ? "default" : "outline"}
              size="sm"
              onClick={() => updateFilters('unread_only', !filters.unread_only)}
              className="text-xs"
            >
              Nur ungelesene
            </Button>
          </div>
        </div>

        {/* Notification Types */}
        <div>
          <h4 className="text-sm font-medium mb-2">Benachrichtigungstypen</h4>
          <div className="grid grid-cols-2 gap-2">
            {notificationTypes.map((type) => (
              <div key={type.value} className="flex items-center space-x-2">
                <Checkbox
                  id={`type-${type.value}`}
                  checked={filters.types?.includes(type.value) || false}
                  onCheckedChange={() => toggleType(type.value)}
                />
                <label
                  htmlFor={`type-${type.value}`}
                  className="text-xs font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 flex items-center gap-1"
                >
                  <span>{type.icon}</span>
                  {type.label}
                </label>
              </div>
            ))}
          </div>
        </div>

        {/* Priorities */}
        <div>
          <h4 className="text-sm font-medium mb-2">Prioritäten</h4>
          <div className="flex flex-wrap gap-2">
            {priorities.map((priority) => (
              <Button
                key={priority.value}
                variant={filters.priorities?.includes(priority.value) ? "default" : "outline"}
                size="sm"
                onClick={() => togglePriority(priority.value)}
                className="text-xs flex items-center gap-2"
              >
                <div className={`w-2 h-2 rounded-full ${priority.color}`} />
                {priority.label}
              </Button>
            ))}
          </div>
        </div>

        {/* Date Range */}
        <div>
          <h4 className="text-sm font-medium mb-2">Zeitraum</h4>
          <div className="grid grid-cols-2 gap-2">
            <div>
              <label htmlFor="date-from" className="text-xs text-muted-foreground">Von</label>
              <input
                id="date-from"
                type="date"
                value={filters.date_from || ''}
                onChange={(e) => updateFilters('date_from', e.target.value || undefined)}
                className="w-full px-2 py-1 text-xs border rounded-md bg-background"
              />
            </div>
            <div>
              <label htmlFor="date-to" className="text-xs text-muted-foreground">Bis</label>
              <input
                id="date-to"
                type="date"
                value={filters.date_to || ''}
                onChange={(e) => updateFilters('date_to', e.target.value || undefined)}
                className="w-full px-2 py-1 text-xs border rounded-md bg-background"
              />
            </div>
          </div>
        </div>

        {/* Active Filters Summary */}
        {hasActiveFilters && (
          <div className="pt-2 border-t">
            <div className="flex flex-wrap gap-1">
              {filters.types?.map((type) => (
                <Badge key={type} variant="secondary" className="text-xs">
                  {notificationTypes.find(t => t.value === type)?.label}
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => toggleType(type)}
                    className="h-3 w-3 p-0 ml-1 hover:bg-transparent"
                  >
                    <X className="h-2 w-2" />
                  </Button>
                </Badge>
              ))}
              {filters.priorities?.map((priority) => (
                <Badge key={priority} variant="secondary" className="text-xs">
                  {priorities.find(p => p.value === priority)?.label}
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => togglePriority(priority)}
                    className="h-3 w-3 p-0 ml-1 hover:bg-transparent"
                  >
                    <X className="h-2 w-2" />
                  </Button>
                </Badge>
              ))}
              {filters.unread_only && (
                <Badge variant="secondary" className="text-xs">
                  Nur ungelesene
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => updateFilters('unread_only', false)}
                    className="h-3 w-3 p-0 ml-1 hover:bg-transparent"
                  >
                    <X className="h-2 w-2" />
                  </Button>
                </Badge>
              )}
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
};