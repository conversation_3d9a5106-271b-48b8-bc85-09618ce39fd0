@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom sidebar transition enhancements */
@layer components {
  /* Smooth transitions for sidebar collapsing */
  .group\/sidebar-wrapper [data-sidebar="sidebar"] {
    transition: all 200ms ease-linear;
  }
  
  /* Enhanced tooltip positioning for collapsed sidebar */
  .group-data-[collapsible=icon] [data-sidebar="menu-button"][data-state="open"] + * {
    transition: opacity 150ms ease-in-out;
  }
}

/* Definition of the design system. All colors, gradients, fonts, etc should be defined here. 
All colors MUST be HSL.
*/

@layer base {
  :root {
    /* Light theme - hell/grau/weiß */
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    --primary: 220 15% 12%;
    --primary-foreground: 0 0% 98%;
    --primary-glow: 220 15% 22%;

    --secondary: 210 40% 96%;
    --secondary-foreground: 222.2 84% 4.9%;

    --muted: 210 40% 96%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 210 40% 96%;
    --accent-foreground: 222.2 84% 4.9%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 222.2 84% 4.9%;
    
    /* Disable ring offset globally */
    --tw-ring-offset-width: 0px;
    --tw-ring-offset-color: transparent;

    --success: 142 76% 36%;
    --success-foreground: 0 0% 98%;

    --warning: 38 92% 50%;
    --warning-foreground: 0 0% 98%;

    --radius: 0.75rem;

    /* Light theme gradients */
    --gradient-primary: linear-gradient(135deg, hsl(var(--primary)), hsl(var(--primary-glow)));
    --gradient-card: linear-gradient(145deg, hsl(0 0% 100%), hsl(210 40% 98%));
    --gradient-accent: linear-gradient(145deg, hsl(var(--accent)), hsl(210 40% 94%));
    
    /* Light theme shadows */
    --shadow-sm: 0 1px 2px 0 hsl(0 0% 0% / 0.05);
    --shadow-md: 0 4px 6px -1px hsl(0 0% 0% / 0.1), 0 2px 4px -1px hsl(0 0% 0% / 0.06);
    --shadow-lg: 0 10px 15px -3px hsl(0 0% 0% / 0.1), 0 4px 6px -2px hsl(0 0% 0% / 0.05);
    --shadow-glow: 0 0 20px hsl(var(--primary) / 0.3);

    /* Transitions */
    --transition-smooth: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

    --sidebar-background: 0 0% 98%;

    --sidebar-foreground: 240 5.3% 26.1%;

    --sidebar-primary: 240 5.9% 10%;

    --sidebar-primary-foreground: 0 0% 98%;

    --sidebar-accent: 240 4.8% 95.9%;

    --sidebar-accent-foreground: 240 5.9% 10%;

    --sidebar-border: 220 13% 91%;

    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    --background: 240 10% 3.9%;
    --foreground: 0 0% 98%;

    --card: 240 10% 3.9%;
    --card-foreground: 0 0% 98%;

    --popover: 240 10% 3.9%;
    --popover-foreground: 0 0% 98%;

    --primary: 220 15% 12%;
    --primary-foreground: 0 0% 98%;
    --primary-glow: 220 15% 22%;

    --secondary: 240 4.8% 15.9%;
    --secondary-foreground: 0 0% 98%;

    --muted: 240 4.8% 15.9%;
    --muted-foreground: 240 5% 64.9%;

    --accent: 240 4.8% 15.9%;
    --accent-foreground: 0 0% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;

    --border: 240 3.7% 15.9%;
    --input: 240 3.7% 15.9%;
    --ring: 220 15% 12%;
    
    /* Disable ring offset globally for dark mode */
    --tw-ring-offset-width: 0px;
    --tw-ring-offset-color: transparent;

    --success: 142 76% 36%;
    --success-foreground: 0 0% 98%;

    --warning: 38 92% 50%;
    --warning-foreground: 0 0% 98%;

    /* Dark theme gradients */
    --gradient-primary: linear-gradient(135deg, hsl(var(--primary)), hsl(var(--primary-glow)));
    --gradient-card: linear-gradient(145deg, hsl(240 10% 4.5%), hsl(240 8% 6%));
    --gradient-accent: linear-gradient(145deg, hsl(var(--accent)), hsl(240 4.8% 18%));
    
    /* Dark theme shadows */
    --shadow-sm: 0 1px 2px 0 hsl(240 10% 3.9% / 0.05);
    --shadow-md: 0 4px 6px -1px hsl(240 10% 3.9% / 0.1), 0 2px 4px -1px hsl(240 10% 3.9% / 0.06);
    --shadow-lg: 0 10px 15px -3px hsl(240 10% 3.9% / 0.1), 0 4px 6px -2px hsl(240 10% 3.9% / 0.05);
    --shadow-glow: 0 0 20px hsl(var(--primary) / 0.3);

    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 220 15% 12%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
    /* Prevent horizontal scrolling on mobile */
    overflow-x: hidden;
    max-width: 100vw;
  }

  /* Ensure all containers respect viewport boundaries */
  .container {
    max-width: 100%;
    overflow-x: hidden;
  }
}

@layer utilities {
  /* Mobile-specific utilities */
  .line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  /* Prevent text overflow on mobile */
  .mobile-text-safe {
    word-wrap: break-word;
    overflow-wrap: break-word;
    hyphens: auto;
    word-break: break-word;
  }

  /* Force all text content to break properly on mobile */
  @media (max-width: 640px) {
    h1, h2, h3, h4, h5, h6, p, span, div[class*="text-"], a {
      word-break: break-word;
      overflow-wrap: break-word;
      hyphens: auto;
    }
    
    /* Prevent any element from extending beyond viewport */
    * {
      max-width: 100%;
      box-sizing: border-box;
    }
    
    /* Ensure flex containers don't overflow */
    .flex, [class*="flex"] {
      min-width: 0;
    }
    
  }

  /* Ensure buttons don't cause horizontal overflow */
  .mobile-button-safe {
    min-width: 0;
    flex-shrink: 1;
  }

  /* Safe spacing for mobile */
  .mobile-safe-padding {
    padding-left: max(1rem, env(safe-area-inset-left));
    padding-right: max(1rem, env(safe-area-inset-right));
  }

  /* Completely remove focus rings and outlines */
  input, textarea, select, [role="combobox"], [data-radix-select-trigger], button[role="combobox"], button {
    outline: none !important;
    outline-offset: 0px !important;
    box-shadow: none !important;
    --tw-ring-offset-width: 0px !important;
    --tw-ring-offset-color: transparent !important;
    --tw-ring-width: 0px !important;
    --tw-ring-color: transparent !important;
  }
  
  /* Focus states - completely remove all focus indicators */
  input:focus, 
  textarea:focus, 
  select:focus,
  [role="combobox"]:focus,
  [data-radix-select-trigger]:focus,
  button[role="combobox"]:focus,
  button:focus,
  input:focus-visible,
  textarea:focus-visible,
  select:focus-visible,
  [role="combobox"]:focus-visible,
  [data-radix-select-trigger]:focus-visible,
  button[role="combobox"]:focus-visible,
  button:focus-visible {
    outline: none !important;
    outline-offset: 0px !important;
    box-shadow: none !important;
    --tw-ring-offset-width: 0px !important;
    --tw-ring-offset-color: transparent !important;
    --tw-ring-width: 0px !important;
    --tw-ring-color: transparent !important;
    border: 1px solid hsl(var(--border)) !important;
  }
  
  /* Remove all ring and outline styles globally */
  * {
    --tw-ring-offset-width: 0px !important;
    --tw-ring-offset-color: transparent !important;
    --tw-ring-width: 0px !important;
    --tw-ring-color: transparent !important;
  }
  
  *:focus {
    outline: none !important;
    outline-offset: 0px !important;
    box-shadow: none !important;
  }
  
  *:focus-visible {
    outline: none !important;
    outline-offset: 0px !important;
    box-shadow: none !important;
  }

  /* Auth page animations */
  @keyframes float {
    0%, 100% {
      transform: translateY(0px) rotate(0deg);
    }
    33% {
      transform: translateY(-10px) rotate(1deg);
    }
    66% {
      transform: translateY(5px) rotate(-1deg);
    }
  }

  @keyframes glow {
    0%, 100% {
      opacity: 0.5;
    }
    50% {
      opacity: 0.8;
    }
  }

  @keyframes slide-in-up {
    0% {
      opacity: 0;
      transform: translateY(10px);
    }
    100% {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes scale-in {
    0% {
      opacity: 0;
      transform: scale(0.95);
    }
    100% {
      opacity: 1;
      transform: scale(1);
    }
  }

  .animate-float {
    animation: float 6s ease-in-out infinite;
  }

  .animate-glow {
    animation: glow 4s ease-in-out infinite;
  }

  .animate-slide-in-up {
    animation: slide-in-up 0.5s ease-out;
  }

  .animate-scale-in {
    animation: scale-in 0.3s ease-out;
  }

  /* Grid background pattern */
  .bg-grid-white\/\[0\.02\] {
    background-image: radial-gradient(circle, rgba(255, 255, 255, 0.02) 1px, transparent 1px);
  }

  .dark .bg-grid-white\/\[0\.02\] {
    background-image: radial-gradient(circle, rgba(255, 255, 255, 0.05) 1px, transparent 1px);
  }

  /* Screen reader only content - accessibility */
  .sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
  }


  /* High contrast mode support */
  @media (prefers-contrast: high) {
    .bg-accent {
      border: 1px solid;
    }
    
    .text-muted-foreground {
      opacity: 1;
    }
  }

  /* Reduced motion support */
  @media (prefers-reduced-motion: reduce) {
    * {
      animation-duration: 0.01ms !important;
      animation-iteration-count: 1 !important;
      transition-duration: 0.01ms !important;
    }
  }

  /* Dark mode: Make gradient text white instead of gradient */
  .dark .bg-gradient-primary.bg-clip-text.text-transparent {
    background: none !important;
    -webkit-background-clip: unset !important;
    background-clip: unset !important;
    -webkit-text-fill-color: unset !important;
    color: hsl(0 0% 98%) !important;
  }

}