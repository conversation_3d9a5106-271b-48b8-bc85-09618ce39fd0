import { clsx, type ClassValue } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

/**
 * Normalizes a phone number for search comparison by removing formatting characters
 * @param phoneNumber - The phone number to normalize
 * @returns Normalized phone number with only digits (and optionally leading +)
 */
export function normalizePhoneNumber(phoneNumber: string): string {
  if (!phoneNumber) return ''
  
  // Remove all non-digit characters except + at the beginning
  return phoneNumber.replace(/[^\d+]/g, '').replace(/(?!^)\+/g, '')
}
