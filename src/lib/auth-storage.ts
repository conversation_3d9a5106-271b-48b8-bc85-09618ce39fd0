// Session storage management for "stay logged in" functionality
// Handles switching between localStorage (persistent) and sessionStorage (temporary)

type StorageType = 'localStorage' | 'sessionStorage';

const STORAGE_KEY = 'lanzr-stay-logged-in';

export class AuthStorage {
  private static setStorageType(type: StorageType) {
    try {
      localStorage.setItem(STORAGE_KEY, type);
    } catch (error) {
      console.warn('Failed to set storage preference:', error);
    }
  }

  private static getStorageType(): StorageType {
    try {
      return (localStorage.getItem(STORAGE_KEY) as StorageType) || 'sessionStorage';
    } catch (error) {
      console.warn('Failed to get storage preference:', error);
      return 'sessionStorage';
    }
  }

  static enablePersistentSession() {
    this.setStorageType('localStorage');
    // Move session from sessionStorage to localStorage if it exists
    this.migrateSession('sessionStorage', 'localStorage');
  }

  static enableTemporarySession() {
    this.setStorageType('sessionStorage');
    // Move session from localStorage to sessionStorage if it exists
    this.migrateSession('localStorage', 'sessionStorage');
  }

  private static migrateSession(from: StorageType, to: StorageType) {
    try {
      const fromStorage = from === 'localStorage' ? localStorage : sessionStorage;
      const toStorage = to === 'localStorage' ? localStorage : sessionStorage;
      
      // Supabase stores auth data with specific keys
      const authKeys = [
        'sb-wkmzfqjnlwaogqnkbfgw-auth-token',
        'supabase.auth.token'
      ];
      
      authKeys.forEach(key => {
        const data = fromStorage.getItem(key);
        if (data) {
          toStorage.setItem(key, data);
          fromStorage.removeItem(key);
        }
      });
    } catch (error) {
      console.warn('Failed to migrate session:', error);
    }
  }

  static isPersistentSession(): boolean {
    return this.getStorageType() === 'localStorage';
  }

  static clearStoragePreference() {
    try {
      localStorage.removeItem(STORAGE_KEY);
    } catch (error) {
      console.warn('Failed to clear storage preference:', error);
    }
  }
}