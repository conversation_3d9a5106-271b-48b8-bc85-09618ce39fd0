// AI Rate Limiting Service for preventing abuse and controlling costs
// Implements sliding window rate limiting with multiple tiers

export interface RateLimitConfig {
  windowMs: number; // Time window in milliseconds
  maxRequests: number; // Maximum requests per window
  tier: 'basic' | 'premium' | 'enterprise';
}

export interface RateLimitResult {
  allowed: boolean;
  remaining: number;
  resetTime: number; // Unix timestamp when limit resets
  message?: string;
}

// Rate limit configurations for different user tiers
const RATE_LIMIT_CONFIGS: Record<string, RateLimitConfig> = {
  basic: {
    windowMs: 60 * 1000, // 1 minute
    maxRequests: 5, // 5 requests per minute for basic users
    tier: 'basic'
  },
  premium: {
    windowMs: 60 * 1000, // 1 minute
    maxRequests: 15, // 15 requests per minute for premium users
    tier: 'premium'
  },
  enterprise: {
    windowMs: 60 * 1000, // 1 minute
    maxRequests: 50, // 50 requests per minute for enterprise users
    tier: 'enterprise'
  }
};

// Additional rate limits for security
const SECURITY_LIMITS = {
  // Maximum requests per hour regardless of tier
  maxRequestsPerHour: 100,
  hourlyWindowMs: 60 * 60 * 1000,
  
  // Maximum requests per day
  maxRequestsPerDay: 500,
  dailyWindowMs: 24 * 60 * 60 * 1000,
  
  // Burst protection: max requests in 10 seconds
  burstWindowMs: 10 * 1000,
  maxBurstRequests: 3
};

export class AIRateLimiter {
  private requestHistory: Map<string, number[]> = new Map();
  private hourlyHistory: Map<string, number[]> = new Map();
  private dailyHistory: Map<string, number[]> = new Map();
  private burstHistory: Map<string, number[]> = new Map();
  private cleanupInterval: NodeJS.Timeout | null = null;

  constructor() {
    // Clean up old entries every 5 minutes
    this.cleanupInterval = setInterval(() => this.cleanup(), 5 * 60 * 1000);
  }

  /**
   * Check if a user can make an AI request
   */
  public checkRateLimit(userId: string, userTier: 'basic' | 'premium' | 'enterprise' = 'basic'): RateLimitResult {
    const now = Date.now();
    
    // Get rate limit config for user tier
    const config = RATE_LIMIT_CONFIGS[userTier];
    
    try {
      // Check burst protection first
      const burstCheck = this.checkBurstLimit(userId, now);
      if (!burstCheck.allowed) {
        return burstCheck;
      }

      // Check tier-specific rate limit
      const tierCheck = this.checkTierLimit(userId, now, config);
      if (!tierCheck.allowed) {
        return tierCheck;
      }

      // Check hourly security limit
      const hourlyCheck = this.checkHourlyLimit(userId, now);
      if (!hourlyCheck.allowed) {
        return hourlyCheck;
      }

      // Check daily security limit
      const dailyCheck = this.checkDailyLimit(userId, now);
      if (!dailyCheck.allowed) {
        return dailyCheck;
      }

      // All checks passed - record the request
      this.recordRequest(userId, now);

      return {
        allowed: true,
        remaining: this.getRemainingRequests(userId, config, now),
        resetTime: this.getResetTime(userId, config, now)
      };

    } catch (error) {
      console.error('Rate limiter error:', error);
      // Fail safely - allow the request but log the error
      return {
        allowed: true,
        remaining: 0,
        resetTime: now + config.windowMs,
        message: 'Rate limiter error - request allowed'
      };
    }
  }

  /**
   * Check burst protection (max 3 requests in 10 seconds)
   */
  private checkBurstLimit(userId: string, now: number): RateLimitResult {
    const burstRequests = this.getBurstHistory(userId);
    const recentBurstRequests = burstRequests.filter(
      time => now - time < SECURITY_LIMITS.burstWindowMs
    );

    if (recentBurstRequests.length >= SECURITY_LIMITS.maxBurstRequests) {
      const oldestRequest = Math.min(...recentBurstRequests);
      const resetTime = oldestRequest + SECURITY_LIMITS.burstWindowMs;

      return {
        allowed: false,
        remaining: 0,
        resetTime,
        message: 'Burst limit exceeded. Please wait before making another request.'
      };
    }

    return { allowed: true, remaining: SECURITY_LIMITS.maxBurstRequests - recentBurstRequests.length, resetTime: now };
  }

  /**
   * Check tier-specific rate limit
   */
  private checkTierLimit(userId: string, now: number, config: RateLimitConfig): RateLimitResult {
    const requests = this.getRequestHistory(userId);
    const recentRequests = requests.filter(
      time => now - time < config.windowMs
    );

    if (recentRequests.length >= config.maxRequests) {
      const oldestRequest = Math.min(...recentRequests);
      const resetTime = oldestRequest + config.windowMs;

      return {
        allowed: false,
        remaining: 0,
        resetTime,
        message: `${config.tier} tier limit exceeded (${config.maxRequests} requests per minute).`
      };
    }

    return { allowed: true, remaining: config.maxRequests - recentRequests.length, resetTime: now };
  }

  /**
   * Check hourly security limit
   */
  private checkHourlyLimit(userId: string, now: number): RateLimitResult {
    const hourlyRequests = this.getHourlyHistory(userId);
    const recentHourlyRequests = hourlyRequests.filter(
      time => now - time < SECURITY_LIMITS.hourlyWindowMs
    );

    if (recentHourlyRequests.length >= SECURITY_LIMITS.maxRequestsPerHour) {
      const oldestRequest = Math.min(...recentHourlyRequests);
      const resetTime = oldestRequest + SECURITY_LIMITS.hourlyWindowMs;

      return {
        allowed: false,
        remaining: 0,
        resetTime,
        message: `Hourly security limit exceeded (${SECURITY_LIMITS.maxRequestsPerHour} requests per hour).`
      };
    }

    return { allowed: true, remaining: SECURITY_LIMITS.maxRequestsPerHour - recentHourlyRequests.length, resetTime: now };
  }

  /**
   * Check daily security limit
   */
  private checkDailyLimit(userId: string, now: number): RateLimitResult {
    const dailyRequests = this.getDailyHistory(userId);
    const recentDailyRequests = dailyRequests.filter(
      time => now - time < SECURITY_LIMITS.dailyWindowMs
    );

    if (recentDailyRequests.length >= SECURITY_LIMITS.maxRequestsPerDay) {
      const oldestRequest = Math.min(...recentDailyRequests);
      const resetTime = oldestRequest + SECURITY_LIMITS.dailyWindowMs;

      return {
        allowed: false,
        remaining: 0,
        resetTime,
        message: `Daily security limit exceeded (${SECURITY_LIMITS.maxRequestsPerDay} requests per day).`
      };
    }

    return { allowed: true, remaining: SECURITY_LIMITS.maxRequestsPerDay - recentDailyRequests.length, resetTime: now };
  }

  /**
   * Record a successful request across all tracking mechanisms
   */
  private recordRequest(userId: string, timestamp: number): void {
    // Record in all tracking histories
    this.getRequestHistory(userId).push(timestamp);
    this.getHourlyHistory(userId).push(timestamp);
    this.getDailyHistory(userId).push(timestamp);
    this.getBurstHistory(userId).push(timestamp);
  }

  /**
   * Get remaining requests for a user in the current window
   */
  private getRemainingRequests(userId: string, config: RateLimitConfig, now: number): number {
    const requests = this.getRequestHistory(userId);
    const recentRequests = requests.filter(
      time => now - time < config.windowMs
    );
    
    return Math.max(0, config.maxRequests - recentRequests.length);
  }

  /**
   * Get reset time for the current rate limit window
   */
  private getResetTime(userId: string, config: RateLimitConfig, now: number): number {
    const requests = this.getRequestHistory(userId);
    const recentRequests = requests.filter(
      time => now - time < config.windowMs
    );

    if (recentRequests.length === 0) {
      return now + config.windowMs;
    }

    const oldestRequest = Math.min(...recentRequests);
    return oldestRequest + config.windowMs;
  }

  /**
   * Get request history for a user, creating if it doesn't exist
   */
  private getRequestHistory(userId: string): number[] {
    if (!this.requestHistory.has(userId)) {
      this.requestHistory.set(userId, []);
    }
    return this.requestHistory.get(userId)!;
  }

  /**
   * Get hourly history for a user, creating if it doesn't exist
   */
  private getHourlyHistory(userId: string): number[] {
    if (!this.hourlyHistory.has(userId)) {
      this.hourlyHistory.set(userId, []);
    }
    return this.hourlyHistory.get(userId)!;
  }

  /**
   * Get daily history for a user, creating if it doesn't exist
   */
  private getDailyHistory(userId: string): number[] {
    if (!this.dailyHistory.has(userId)) {
      this.dailyHistory.set(userId, []);
    }
    return this.dailyHistory.get(userId)!;
  }

  /**
   * Get burst history for a user, creating if it doesn't exist
   */
  private getBurstHistory(userId: string): number[] {
    if (!this.burstHistory.has(userId)) {
      this.burstHistory.set(userId, []);
    }
    return this.burstHistory.get(userId)!;
  }

  /**
   * Clean up old entries to prevent memory leaks
   */
  private cleanup(): void {
    const now = Date.now();
    const maxAge = Math.max(
      SECURITY_LIMITS.dailyWindowMs,
      SECURITY_LIMITS.hourlyWindowMs,
      ...Object.values(RATE_LIMIT_CONFIGS).map(config => config.windowMs)
    );

    // Clean up request history
    for (const [userId, requests] of this.requestHistory.entries()) {
      const filtered = requests.filter(time => now - time < maxAge);
      if (filtered.length === 0) {
        this.requestHistory.delete(userId);
      } else {
        this.requestHistory.set(userId, filtered);
      }
    }

    // Clean up hourly history
    for (const [userId, requests] of this.hourlyHistory.entries()) {
      const filtered = requests.filter(time => now - time < SECURITY_LIMITS.hourlyWindowMs);
      if (filtered.length === 0) {
        this.hourlyHistory.delete(userId);
      } else {
        this.hourlyHistory.set(userId, filtered);
      }
    }

    // Clean up daily history
    for (const [userId, requests] of this.dailyHistory.entries()) {
      const filtered = requests.filter(time => now - time < SECURITY_LIMITS.dailyWindowMs);
      if (filtered.length === 0) {
        this.dailyHistory.delete(userId);
      } else {
        this.dailyHistory.set(userId, filtered);
      }
    }

    // Clean up burst history
    for (const [userId, requests] of this.burstHistory.entries()) {
      const filtered = requests.filter(time => now - time < SECURITY_LIMITS.burstWindowMs);
      if (filtered.length === 0) {
        this.burstHistory.delete(userId);
      } else {
        this.burstHistory.set(userId, filtered);
      }
    }
  }

  /**
   * Get current usage statistics for a user
   */
  public getUserStats(userId: string, userTier: 'basic' | 'premium' | 'enterprise' = 'basic') {
    const now = Date.now();
    const config = RATE_LIMIT_CONFIGS[userTier];
    
    const requests = this.getRequestHistory(userId);
    const hourlyRequests = this.getHourlyHistory(userId);
    const dailyRequests = this.getDailyHistory(userId);
    const burstRequests = this.getBurstHistory(userId);

    const recentRequests = requests.filter(time => now - time < config.windowMs);
    const recentHourlyRequests = hourlyRequests.filter(time => now - time < SECURITY_LIMITS.hourlyWindowMs);
    const recentDailyRequests = dailyRequests.filter(time => now - time < SECURITY_LIMITS.dailyWindowMs);
    const recentBurstRequests = burstRequests.filter(time => now - time < SECURITY_LIMITS.burstWindowMs);

    return {
      tier: userTier,
      current: {
        requests: recentRequests.length,
        hourly: recentHourlyRequests.length,
        daily: recentDailyRequests.length,
        burst: recentBurstRequests.length
      },
      limits: {
        requests: config.maxRequests,
        hourly: SECURITY_LIMITS.maxRequestsPerHour,
        daily: SECURITY_LIMITS.maxRequestsPerDay,
        burst: SECURITY_LIMITS.maxBurstRequests
      },
      remaining: {
        requests: config.maxRequests - recentRequests.length,
        hourly: SECURITY_LIMITS.maxRequestsPerHour - recentHourlyRequests.length,
        daily: SECURITY_LIMITS.maxRequestsPerDay - recentDailyRequests.length,
        burst: SECURITY_LIMITS.maxBurstRequests - recentBurstRequests.length
      }
    };
  }

  /**
   * Reset rate limits for a user (admin function)
   */
  public resetUserLimits(userId: string): void {
    this.requestHistory.delete(userId);
    this.hourlyHistory.delete(userId);
    this.dailyHistory.delete(userId);
    this.burstHistory.delete(userId);
  }

  /**
   * Destroy the rate limiter and clean up resources
   * Call this when the rate limiter is no longer needed to prevent memory leaks
   */
  public destroy(): void {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
      this.cleanupInterval = null;
    }
    
    // Clear all maps to free memory
    this.requestHistory.clear();
    this.hourlyHistory.clear();
    this.dailyHistory.clear();
    this.burstHistory.clear();
  }
}

// Singleton instance
export const aiRateLimiter = new AIRateLimiter();

// Cleanup on module unload (for server-side environments)
if (typeof process !== 'undefined' && process.on) {
  process.on('exit', () => {
    aiRateLimiter.destroy();
  });
  
  process.on('SIGINT', () => {
    aiRateLimiter.destroy();
    process.exit(0);
  });
  
  process.on('SIGTERM', () => {
    aiRateLimiter.destroy();
    process.exit(0);
  });
}