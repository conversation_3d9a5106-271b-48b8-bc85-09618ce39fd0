import { toast as sonnerToast } from 'sonner';

// Unified toast utility that uses <PERSON><PERSON> for consistent styling across the app
export const toast = {
  success: (message: string, description?: string) => {
    sonnerToast.success(message, {
      description,
      duration: 4000,
    });
  },
  
  error: (message: string, description?: string) => {
    sonnerToast.error(message, {
      description,
      duration: 6000,
    });
  },
  
  info: (message: string, description?: string) => {
    sonnerToast.info(message, {
      description,
      duration: 4000,
    });
  },
  
  warning: (message: string, description?: string) => {
    sonnerToast.warning(message, {
      description,
      duration: 5000,
    });
  },

  // Generic toast for custom styling
  message: (message: string, description?: string) => {
    sonnerToast(message, {
      description,
      duration: 4000,
    });
  },

  // Promise-based toast for async operations
  promise: <T>(
    promise: Promise<T>,
    {
      loading,
      success,
      error,
    }: {
      loading: string;
      success: string | ((data: T) => string);
      error: string | ((error: any) => string);
    }
  ) => {
    return sonnerToast.promise(promise, {
      loading,
      success,
      error,
      duration: 4000,
    });
  },
};

// Export individual functions for convenience
export const { success, error, info, warning, message, promise } = toast;