import { format } from 'date-fns';
import { de } from 'date-fns/locale';

/**
 * Parse follow-up date from ISO string without timezone conversion
 * This prevents the -2h offset issue when displaying scheduled dates
 */
export function parseFollowUpDate(isoString: string): Date {
  const dateMatch = isoString.match(/^(\d{4}-\d{2}-\d{2})/);
  if (dateMatch) {
    const [year, month, day] = dateMatch[1].split('-').map(Number);
    return new Date(year, month - 1, day); // month is 0-based
  }
  // Fallback to regular parsing
  return new Date(isoString);
}

/**
 * Extract time from ISO string without timezone conversion
 */
export function parseFollowUpTime(isoString: string): string {
  const timeMatch = isoString.match(/T(\d{2}:\d{2})/);
  return timeMatch ? timeMatch[1] : '09:00';
}

/**
 * Format follow-up date correctly without timezone issues
 */
export function formatFollowUpDate(isoString: string, formatString: string = 'dd.MM.yy'): string {
  const date = parseFollowUpDate(isoString);
  return format(date, formatString, { locale: de });
}

/**
 * Format follow-up date and time together
 */
export function formatFollowUpDateTime(isoString: string): string {
  const date = parseFollowUpDate(isoString);
  const time = parseFollowUpTime(isoString);
  return `${format(date, 'dd.MM.yyyy', { locale: de })} ${time}`;
}

/**
 * Parse follow-up date for calendar events (YYYY-MM-DD format)
 */
export function parseFollowUpDateForCalendar(isoString: string): string {
  const dateMatch = isoString.match(/^(\d{4}-\d{2}-\d{2})/);
  return dateMatch ? dateMatch[1] : isoString.split('T')[0];
}

/**
 * Create ISO string from date and time inputs WITHOUT timezone conversion
 * User inputs "09.08.2025 11:00" → Store as "2025-08-09T11:00:00.000Z"
 */
export function createFollowUpISOString(dateInput: string | Date, timeInput: string): string {
  let dateStr: string;
  
  if (dateInput instanceof Date) {
    // Extract YYYY-MM-DD from Date object
    const year = dateInput.getFullYear();
    const month = String(dateInput.getMonth() + 1).padStart(2, '0');
    const day = String(dateInput.getDate()).padStart(2, '0');
    dateStr = `${year}-${month}-${day}`;
  } else {
    // Already a string in YYYY-MM-DD format
    dateStr = dateInput;
  }
  
  // Ensure time is in HH:MM format
  const timeStr = timeInput.includes(':') ? timeInput : `${timeInput}:00`;
  
  // Build ISO string directly without Date object to avoid timezone conversion
  return `${dateStr}T${timeStr}:00.000Z`;
}