// Communication validation utilities for security and data integrity
// All validation functions should be called before database operations

import { CommunicationType, CreateCommunicationData, UpdateCommunicationData, CommunicationFilters } from '@/types/communications';
import i18n from '@/i18n';

// Custom error classes for better error handling
export class ValidationError extends Error {
  constructor(message: string, public field?: string, public code?: string) {
    super(message);
    this.name = 'ValidationError';
  }
}

export class SecurityError extends Error {
  constructor(message: string, public severity: 'low' | 'medium' | 'high' = 'medium') {
    super(message);
    this.name = 'SecurityError';
  }
}

// Constants for validation limits
const VALIDATION_LIMITS = {
  NOTES_MIN_LENGTH: 1,
  NOTES_MAX_LENGTH: 10000,
  SUBJECT_MAX_LENGTH: 500,
  DURATION_MAX: 1440, // 24 hours in minutes
  ID_MIN_LENGTH: 36, // UUID length
  ID_MAX_LENGTH: 36,
  TEXT_FIELD_MAX_LENGTH: 1000,
  SEARCH_TEXT_MIN_LENGTH: 2,
  SEARCH_TEXT_MAX_LENGTH: 200
} as const;

// Security patterns to detect potential attacks
const SECURITY_PATTERNS = {
  SQL_INJECTION: /('|(--)|;|\/\*|\*\/|xp_|sp_|exec|execute|union|select|insert|update|delete|drop|create|alter|declare|cast|convert)/i,
  XSS_BASIC: /<script|javascript:|vbscript:|onload|onerror|onclick|onmouseover/i,
  COMMAND_INJECTION: /(\||;|&|`|\$\(|\$\{|>|<)/,
  PATH_TRAVERSAL: /(\.\.|\/\.\/|\\\.\\|\.\/|\.\\)/,
  EXCESSIVE_REPETITION: /(.)\1{50,}/, // More than 50 repeated characters
} as const;

// Valid communication types
const VALID_COMMUNICATION_TYPES: CommunicationType[] = [
  'call', 'email', 'meeting', 'message', 'whatsapp', 'linkedin', 'sms', 'other'
];

/**
 * Sanitize text input by removing potentially harmful content
 */
export function sanitizeText(input: string): string {
  if (!input || typeof input !== 'string') {
    return '';
  }

  return input
    .trim()
    .replace(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/g, '') // Remove control characters
    .replace(/\s+/g, ' ') // Normalize whitespace
    .substring(0, VALIDATION_LIMITS.TEXT_FIELD_MAX_LENGTH); // Limit length
}

/**
 * Validate text for security threats
 */
export function validateTextSecurity(text: string, fieldName: string): void {
  if (!text || typeof text !== 'string') {
    return;
  }

  // Check for SQL injection patterns
  if (SECURITY_PATTERNS.SQL_INJECTION.test(text)) {
    throw new SecurityError(i18n.t('errors:security.sql_injection', { field: fieldName }), 'high');
  }

  // Check for XSS patterns
  if (SECURITY_PATTERNS.XSS_BASIC.test(text)) {
    throw new SecurityError(i18n.t('errors:security.xss_attack', { field: fieldName }), 'high');
  }

  // Check for command injection
  if (SECURITY_PATTERNS.COMMAND_INJECTION.test(text)) {
    throw new SecurityError(i18n.t('errors:security.command_injection', { field: fieldName }), 'medium');
  }

  // Check for path traversal
  if (SECURITY_PATTERNS.PATH_TRAVERSAL.test(text)) {
    throw new SecurityError(i18n.t('errors:security.path_traversal', { field: fieldName }), 'medium');
  }

  // Check for excessive repetition (potential DoS)
  if (SECURITY_PATTERNS.EXCESSIVE_REPETITION.test(text)) {
    throw new SecurityError(i18n.t('errors:security.excessive_repetition', { field: fieldName }), 'low');
  }
}

/**
 * Validate and sanitize UUID
 */
export function validateUUID(id: string, fieldName: string): string {
  if (!id || typeof id !== 'string') {
    throw new ValidationError(i18n.t('errors:communication.uuid_required', { field: fieldName }), fieldName, 'REQUIRED');
  }

  const sanitized = id.trim();
  
  // Basic UUID format validation
  const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;
  if (!uuidRegex.test(sanitized)) {
    throw new ValidationError(i18n.t('errors:communication.uuid_invalid_format', { field: fieldName }), fieldName, 'INVALID_FORMAT');
  }

  return sanitized;
}

/**
 * Validate communication type
 */
export function validateCommunicationType(type: string): CommunicationType {
  if (!type || typeof type !== 'string') {
    throw new ValidationError(i18n.t('errors:communication.communication_type_required'), 'communication_type', 'REQUIRED');
  }

  const sanitized = type.trim().toLowerCase() as CommunicationType;
  
  if (!VALID_COMMUNICATION_TYPES.includes(sanitized)) {
    throw new ValidationError(
      i18n.t('errors:communication.communication_type_invalid', { types: VALID_COMMUNICATION_TYPES.join(', ') }),
      'communication_type',
      'INVALID_VALUE'
    );
  }

  return sanitized;
}

/**
 * Validate and sanitize notes field
 */
export function validateNotes(notes: string): string {
  if (!notes || typeof notes !== 'string') {
    throw new ValidationError(i18n.t('errors:communication.notes_required'), 'notes', 'REQUIRED');
  }

  const sanitized = sanitizeText(notes);
  
  if (sanitized.length < VALIDATION_LIMITS.NOTES_MIN_LENGTH) {
    throw new ValidationError(
      i18n.t('errors:communication.notes_too_short', { count: VALIDATION_LIMITS.NOTES_MIN_LENGTH }),
      'notes',
      'TOO_SHORT'
    );
  }

  if (sanitized.length > VALIDATION_LIMITS.NOTES_MAX_LENGTH) {
    throw new ValidationError(
      i18n.t('errors:communication.notes_too_long', { count: VALIDATION_LIMITS.NOTES_MAX_LENGTH }),
      'notes',
      'TOO_LONG'
    );
  }

  validateTextSecurity(sanitized, 'notes');
  return sanitized;
}

/**
 * Validate and sanitize subject field
 */
export function validateSubject(subject?: string): string | undefined {
  if (!subject) {
    return undefined;
  }

  if (typeof subject !== 'string') {
    throw new ValidationError('Subject must be a string', 'subject', 'INVALID_TYPE');
  }

  const sanitized = sanitizeText(subject);
  
  if (sanitized.length > VALIDATION_LIMITS.SUBJECT_MAX_LENGTH) {
    throw new ValidationError(
      `Subject cannot exceed ${VALIDATION_LIMITS.SUBJECT_MAX_LENGTH} characters`,
      'subject',
      'TOO_LONG'
    );
  }

  if (sanitized.length > 0) {
    validateTextSecurity(sanitized, 'subject');
  }

  return sanitized || undefined;
}

/**
 * Validate duration in minutes
 */
export function validateDuration(duration?: number): number | undefined {
  if (duration === undefined || duration === null) {
    return undefined;
  }

  if (typeof duration !== 'number') {
    throw new ValidationError('Duration must be a number', 'duration_minutes', 'INVALID_TYPE');
  }

  if (!Number.isInteger(duration) || duration < 0) {
    throw new ValidationError('Duration must be a positive integer', 'duration_minutes', 'INVALID_VALUE');
  }

  if (duration > VALIDATION_LIMITS.DURATION_MAX) {
    throw new ValidationError(
      `Duration cannot exceed ${VALIDATION_LIMITS.DURATION_MAX} minutes (24 hours)`,
      'duration_minutes',
      'TOO_LARGE'
    );
  }

  return duration;
}

/**
 * Validate date string
 */
export function validateDate(dateString: string, fieldName: string): string {
  if (!dateString || typeof dateString !== 'string') {
    throw new ValidationError(`${fieldName} is required`, fieldName, 'REQUIRED');
  }

  const date = new Date(dateString);
  
  if (isNaN(date.getTime())) {
    throw new ValidationError(`Invalid ${fieldName} format`, fieldName, 'INVALID_FORMAT');
  }

  // Check if date is not too far in the future (more than 1 year)
  const oneYearFromNow = new Date();
  oneYearFromNow.setFullYear(oneYearFromNow.getFullYear() + 1);
  
  if (date > oneYearFromNow) {
    throw new ValidationError(`${fieldName} cannot be more than 1 year in the future`, fieldName, 'INVALID_VALUE');
  }

  // Check if date is not too far in the past (more than 10 years)
  const tenYearsAgo = new Date();
  tenYearsAgo.setFullYear(tenYearsAgo.getFullYear() - 10);
  
  if (date < tenYearsAgo) {
    throw new ValidationError(`${fieldName} cannot be more than 10 years in the past`, fieldName, 'INVALID_VALUE');
  }

  return date.toISOString();
}

/**
 * Validate search text
 */
export function validateSearchText(searchText: string): string {
  if (!searchText || typeof searchText !== 'string') {
    throw new ValidationError('Search text is required', 'search_text', 'REQUIRED');
  }

  const sanitized = sanitizeText(searchText);
  
  if (sanitized.length < VALIDATION_LIMITS.SEARCH_TEXT_MIN_LENGTH) {
    throw new ValidationError(
      `Search text must be at least ${VALIDATION_LIMITS.SEARCH_TEXT_MIN_LENGTH} characters long`,
      'search_text',
      'TOO_SHORT'
    );
  }

  if (sanitized.length > VALIDATION_LIMITS.SEARCH_TEXT_MAX_LENGTH) {
    throw new ValidationError(
      `Search text cannot exceed ${VALIDATION_LIMITS.SEARCH_TEXT_MAX_LENGTH} characters`,
      'search_text',
      'TOO_LONG'
    );
  }

  validateTextSecurity(sanitized, 'search_text');
  return sanitized;
}

/**
 * Validate and sanitize CreateCommunicationData
 */
export function validateCreateCommunicationData(data: CreateCommunicationData): CreateCommunicationData {
  if (!data || typeof data !== 'object') {
    throw new ValidationError('Communication data is required', undefined, 'REQUIRED');
  }

  return {
    contact_id: validateUUID(data.contact_id, 'contact_id'),
    communication_type: validateCommunicationType(data.communication_type),
    subject: validateSubject(data.subject),
    notes: validateNotes(data.notes),
    communication_date: validateDate(data.communication_date, 'communication_date'),
    duration_minutes: validateDuration(data.duration_minutes),
    project_id: data.project_id ? validateUUID(data.project_id, 'project_id') : undefined,
    is_project_related: Boolean(data.is_project_related)
  };
}

/**
 * Validate and sanitize UpdateCommunicationData
 */
export function validateUpdateCommunicationData(data: UpdateCommunicationData): UpdateCommunicationData {
  if (!data || typeof data !== 'object') {
    throw new ValidationError('Communication data is required', undefined, 'REQUIRED');
  }

  const result: UpdateCommunicationData = {
    id: validateUUID(data.id, 'id'),
    is_project_related: Boolean(data.is_project_related)
  };

  if (data.communication_type !== undefined) {
    result.communication_type = validateCommunicationType(data.communication_type);
  }

  if (data.subject !== undefined) {
    result.subject = validateSubject(data.subject);
  }

  if (data.notes !== undefined) {
    result.notes = validateNotes(data.notes);
  }

  if (data.summarized_notes !== undefined) {
    result.summarized_notes = data.summarized_notes ? validateNotes(data.summarized_notes) : undefined;
  }

  if (data.communication_date !== undefined) {
    result.communication_date = validateDate(data.communication_date, 'communication_date');
  }

  if (data.duration_minutes !== undefined) {
    result.duration_minutes = validateDuration(data.duration_minutes);
  }

  if (data.project_id !== undefined) {
    result.project_id = data.project_id ? validateUUID(data.project_id, 'project_id') : undefined;
  }

  return result;
}

/**
 * Validate and sanitize CommunicationFilters
 */
export function validateCommunicationFilters(filters: CommunicationFilters): CommunicationFilters {
  if (!filters || typeof filters !== 'object') {
    return {};
  }

  const result: CommunicationFilters = {};

  if (filters.contact_id) {
    result.contact_id = validateUUID(filters.contact_id, 'contact_id');
  }

  if (filters.communication_type) {
    result.communication_type = validateCommunicationType(filters.communication_type);
  }

  if (filters.project_id) {
    result.project_id = validateUUID(filters.project_id, 'project_id');
  }

  if (filters.is_project_related !== undefined) {
    result.is_project_related = Boolean(filters.is_project_related);
  }

  if (filters.date_from) {
    result.date_from = validateDate(filters.date_from, 'date_from');
  }

  if (filters.date_to) {
    result.date_to = validateDate(filters.date_to, 'date_to');
  }

  if (filters.search_text) {
    result.search_text = validateSearchText(filters.search_text);
  }

  return result;
}

/**
 * Validate array of communication IDs for bulk operations
 */
export function validateCommunicationIds(ids: string[]): string[] {
  if (!Array.isArray(ids)) {
    throw new ValidationError('Communication IDs must be an array', 'ids', 'INVALID_TYPE');
  }

  if (ids.length === 0) {
    throw new ValidationError('At least one communication ID is required', 'ids', 'EMPTY_ARRAY');
  }

  if (ids.length > 100) {
    throw new ValidationError('Cannot process more than 100 communications at once', 'ids', 'TOO_MANY');
  }

  return ids.map((id, index) => {
    try {
      return validateUUID(id, `ids[${index}]`);
    } catch (error) {
      throw new ValidationError(`Invalid ID at index ${index}: ${error.message}`, 'ids', 'INVALID_ID');
    }
  });
}

/**
 * Validate limit parameter for queries
 */
export function validateLimit(limit: number): number {
  if (typeof limit !== 'number') {
    throw new ValidationError('Limit must be a number', 'limit', 'INVALID_TYPE');
  }

  if (!Number.isInteger(limit) || limit < 1) {
    throw new ValidationError('Limit must be a positive integer', 'limit', 'INVALID_VALUE');
  }

  if (limit > 1000) {
    throw new ValidationError('Limit cannot exceed 1000', 'limit', 'TOO_LARGE');
  }

  return limit;
}