/**
 * Unified error handling for the application
 * Provides consistent error types and handling patterns
 */

// Supabase error interface
export interface SupabaseError {
  code: string;
  message: string;
  details: string;
  hint: string | null;
}

// PostgreSQL error interface  
export interface PostgresError extends SupabaseError {
  code: string;
  message: string;
}

// HTTP error interface
export interface HttpError {
  status: number;
  statusText: string;
  message: string;
}

// Error types for different categories
export interface AppError {
  type: 'permission' | 'network' | 'validation' | 'not_found' | 'unknown';
  code?: string;
  message: string;
  details?: Record<string, unknown>;
  originalError?: SupabaseError | PostgresError | HttpError | Error | unknown;
}

export interface FollowUpSystemError extends AppError {
  type: 'permission';
  isSystemUnavailable: boolean;
}

/**
 * Type guards for different error types
 */
export function isSupabaseError(error: unknown): error is SupabaseError {
  return typeof error === 'object' && 
         error !== null && 
         'code' in error && 
         'message' in error && 
         'details' in error;
}

export function isPostgresError(error: unknown): error is PostgresError {
  return isSupabaseError(error) && typeof (error as any).code === 'string';
}

export function isHttpError(error: unknown): error is HttpError {
  return typeof error === 'object' && 
         error !== null && 
         'status' in error && 
         'statusText' in error;
}

/**
 * Database error codes that indicate permission issues
 */
const PERMISSION_ERROR_CODES = ['42501', '42P01', '42703'] as const;

/**
 * HTTP status codes that indicate permission issues
 */
const PERMISSION_HTTP_CODES = [403, 401] as const;

/**
 * Check if an error indicates that the follow-up system is unavailable
 * This handles cases where the database tables don't exist or user lacks permissions
 */
export function isFollowUpSystemUnavailable(error: unknown): error is FollowUpSystemError {
  // PostgreSQL permission errors (RLS, table not exists, etc.)
  if (isPostgresError(error) && PERMISSION_ERROR_CODES.includes(error.code as any)) {
    return true;
  }
  
  // HTTP permission errors
  if (isHttpError(error) && PERMISSION_HTTP_CODES.includes(error.status as any)) {
    return true;
  }
  
  // Supabase errors with permission-related codes
  if (isSupabaseError(error)) {
    const code = error.code;
    if (PERMISSION_ERROR_CODES.includes(code as any)) {
      return true;
    }
  }
  
  // Standard Error objects
  if (error instanceof Error) {
    const message = error.message.toLowerCase();
    if (message.includes('permission') || 
        message.includes('403') || 
        message.includes('unauthorized') ||
        (message.includes('relation') && message.includes('does not exist'))) {
      return true;
    }
  }
  
  return false;
}

/**
 * Create a standardized AppError from any error
 */
export function createAppError(error: unknown, context?: string): AppError {
  // Check for follow-up system unavailability first
  if (isFollowUpSystemUnavailable(error)) {
    const errorCode = isPostgresError(error) || isSupabaseError(error) 
      ? error.code 
      : 'SYSTEM_UNAVAILABLE';
    
    return {
      type: 'permission',
      code: errorCode,
      message: `Follow-up system is not available${context ? ` (${context})` : ''}`,
      details: { context, isSystemUnavailable: true },
      originalError: error
    } as FollowUpSystemError;
  }
  
  // HTTP errors
  if (isHttpError(error)) {
    return {
      type: error.status === 404 ? 'not_found' : 'network',
      code: error.status.toString(),
      message: error.message || `HTTP ${error.status} error`,
      details: { status: error.status, context },
      originalError: error
    };
  }
  
  // Supabase/PostgreSQL errors
  if (isSupabaseError(error)) {
    return {
      type: 'network',
      code: error.code,
      message: error.message,
      details: { supabaseDetails: error.details, hint: error.hint, context },
      originalError: error
    };
  }
  
  // Standard Error objects
  if (error instanceof Error) {
    // Validation errors
    if (error.name === 'ValidationError' || error.message.includes('validation')) {
      return {
        type: 'validation',
        code: 'VALIDATION_ERROR',
        message: error.message,
        details: { context },
        originalError: error
      };
    }
    
    // Generic Error
    return {
      type: 'unknown',
      code: 'ERROR',
      message: error.message,
      details: { context },
      originalError: error
    };
  }
  
  // Completely unknown error types
  return {
    type: 'unknown',
    code: 'UNKNOWN_ERROR',
    message: 'An unexpected error occurred',
    details: { context, errorType: typeof error },
    originalError: error
  };
}

/**
 * Handle follow-up system errors consistently
 * Returns empty array if system unavailable, throws for other errors
 */
export function handleFollowUpSystemError<T>(error: unknown, context?: string): T[] {
  const appError = createAppError(error, context);
  
  if (appError.type === 'permission' && (appError as FollowUpSystemError).isSystemUnavailable) {
    console.warn(`Follow-up system unavailable${context ? ` in ${context}` : ''}:`, appError.message);
    return [] as T[];
  }
  
  // For other errors, re-throw as AppError
  throw appError;
}

/**
 * Async wrapper that handles follow-up system errors
 */
export async function withFollowUpErrorHandling<T>(
  operation: () => Promise<T[]>,
  context: string
): Promise<T[]> {
  try {
    return await operation();
  } catch (error) {
    return handleFollowUpSystemError<T>(error, context);
  }
}

/**
 * Log error appropriately based on type
 */
export function logError(error: AppError, operation: string): void {
  const logData = {
    type: error.type,
    code: error.code,
    message: error.message,
    operation,
    details: error.details
  };
  
  switch (error.type) {
    case 'permission':
      if ((error as FollowUpSystemError).isSystemUnavailable) {
        console.warn('System unavailable:', logData);
      } else {
        console.error('Permission error:', logData);
      }
      break;
    case 'validation':
      console.warn('Validation error:', logData);
      break;
    case 'network':
      console.error('Network error:', logData);
      break;
    case 'not_found':
      console.warn('Resource not found:', logData);
      break;
    default:
      console.error('Unknown error:', logData);
  }
}

/**
 * Get user-friendly error message
 */
export function getUserFriendlyErrorMessage(error: AppError): string {
  switch (error.type) {
    case 'permission':
      return (error as FollowUpSystemError).isSystemUnavailable 
        ? 'Das Follow-up System ist derzeit nicht verfügbar.'
        : 'Sie haben keine Berechtigung für diese Aktion.';
    case 'network':
      return 'Netzwerkfehler. Bitte prüfen Sie Ihre Verbindung.';
    case 'validation':
      return 'Eingabedaten sind ungültig. Bitte überprüfen Sie Ihre Eingaben.';
    case 'not_found':
      return 'Die angeforderte Ressource wurde nicht gefunden.';
    default:
      return 'Ein unerwarteter Fehler ist aufgetreten. Bitte versuchen Sie es erneut.';
  }
}

/**
 * Safe error handler for catch blocks - replaces (error: any)
 * Use this in catch blocks instead of typing error as any
 */
export function handleCatchError(error: unknown, context: string): AppError {
  return createAppError(error, context);
}

/**
 * Async wrapper with proper error typing
 */
export async function tryCatch<T>(
  operation: () => Promise<T>,
  context: string
): Promise<[T, null] | [null, AppError]> {
  try {
    const result = await operation();
    return [result, null];
  } catch (error) {
    return [null, handleCatchError(error, context)];
  }
}