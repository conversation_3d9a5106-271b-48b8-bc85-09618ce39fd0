// Translation helper functions for status labels
import { useTranslation } from '@/hooks/useTranslation';
import type { ApplicationStatus } from '@/types/applications';
import type { ProjectStatus, ProjectPriority, ProjectType } from '@/types/projects';
import type { ActivityType } from '@/types/shared';

// Application status translation hook
export const useApplicationStatusLabel = () => {
  const { t } = useTranslation('status');
  
  return (status: ApplicationStatus): string => {
    return t(`project.${status}` as const);
  };
};

// Project status translation hook  
export const useProjectStatusLabel = () => {
  const { t } = useTranslation('status');
  
  return (status: ProjectStatus): string => {
    return t(`active.${status}` as const);
  };
};

// Priority translation hook
export const usePriorityLabel = () => {
  const { t } = useTranslation('status');
  
  return (priority: ProjectPriority): string => {
    return t(`priority.${priority}` as const);
  };
};

// Project type translation hook
export const useProjectTypeLabel = () => {
  const { t } = useTranslation('status');
  
  return (type: ProjectType): string => {
    return t(`project_type.${type}` as const);
  };
};

// Activity label translation hook
export const useActivityLabel = () => {
  const { t } = useTranslation('common');
  
  return (activityType: ActivityType | string): string => {
    return t(`activities.${activityType}` as const, { defaultValue: activityType });
  };
};

// Static utility functions that take a translation function
export const getApplicationStatusLabel = (
  status: ApplicationStatus,
  t: (key: string) => string
): string => {
  return t(`status:project.${status}`);
};

export const getProjectStatusLabel = (
  status: ProjectStatus,
  t: (key: string) => string
): string => {
  return t(`status:active.${status}`);
};

export const getPriorityLabel = (
  priority: ProjectPriority,
  t: (key: string) => string
): string => {
  return t(`status:priority.${priority}`);
};

export const getProjectTypeLabel = (
  type: ProjectType,
  t: (key: string) => string
): string => {
  return t(`status:project_type.${type}`);
};