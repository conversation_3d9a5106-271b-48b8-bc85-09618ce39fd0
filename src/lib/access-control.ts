const ACCESS_CODE_KEY = 'freelance-access-granted';
const EXPECTED_ACCESS_CODE = import.meta.env.VITE_ACCESS_CODE || 'BETA2025';

export const AccessControl = {
  // Check if access has been granted in this session
  hasAccess(): boolean {
    return sessionStorage.getItem(ACCESS_CODE_KEY) === 'true';
  },

  // Validate the access code and grant access
  validateAndGrantAccess(code: string): boolean {
    const isValid = code.trim().toLowerCase() === EXPECTED_ACCESS_CODE.toLowerCase();
    if (isValid) {
      sessionStorage.setItem(ACCESS_CODE_KEY, 'true');
    }
    return isValid;
  },

  // Remove access (for testing purposes)
  revokeAccess(): void {
    sessionStorage.removeItem(ACCESS_CODE_KEY);
  },

  // Get the expected access code (for development/testing)
  getExpectedCode(): string {
    return EXPECTED_ACCESS_CODE;
  }
};