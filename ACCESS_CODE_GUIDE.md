# Access Code System - Beta Launch Protection

## Überblick

Das Access Code System schützt die Anwendung vor unbefugtem Zugriff während der Beta-Phase. Nutzer müssen zunächst einen gültigen Zugriffscode eingeben, bevor sie zur Login-/Registrierungsseite weitergeleitet werden.

## Funktionsweise

### Route-Struktur
```
/access → Code eingeben → /auth → Login/Register → Dashboard
```

### Dateien
- `src/pages/AccessCode.tsx` - Access Code Eingabe-Bildschirm
- `src/lib/access-control.ts` - Access Control Logik
- `src/components/AccessProtectedRoute.tsx` - Route-Schutz Komponente
- `.env.local` - Environment Variable für Code

## Konfiguration

### Access Code ändern
Bearbeiten Sie `.env.local`:
```env
VITE_ACCESS_CODE=BETA2025
```

Nach Änderung Entwicklungsserver neu starten:
```bash
npm run dev
```

### Production Deployment
Für Production muss die Environment Variable im Hosting-Provider gesetzt werden:
```env
VITE_ACCESS_CODE=IHR_PRODUCTION_CODE
```

## Features

✅ **Mobile-responsive Design** - Optimiert für alle Gerätegrößen
✅ **Session-basierte Speicherung** - Code gilt nur für aktuelle Browser-Session  
✅ **Automatische Weiterleitung** - Nach Code-Eingabe direkt zu Auth-Seite
✅ **Fehlerbehandlung** - Benutzerfreundliche Fehlermeldungen
✅ **Case-insensitive** - Code wird automatisch in Großbuchstaben konvertiert
✅ **Loading States** - Smooth User Experience mit Ladezuständen

## Sicherheitsfeatures

- **Session Storage**: Code-Status wird nur in der aktuellen Browser-Session gespeichert
- **Keine permanente Speicherung**: Beim Schließen des Browsers wird der Zugriff zurückgesetzt
- **Environment Variable**: Code ist nicht im Client-Code sichtbar
- **Route Protection**: Alle Auth-Routen sind geschützt

## Nutzung während Beta

### Für Beta-Tester
1. Rufen Sie die Website auf
2. Sie werden automatisch zur Access Code Seite weitergeleitet
3. Geben Sie den bereitgestellten Code ein: `BETA2025`
4. Klicken Sie auf "Zugang freischalten"
5. Sie werden zur Login/Registrierung weitergeleitet

### Für Entwickler
```javascript
// Code-Status prüfen
AccessControl.hasAccess() // true/false

// Code validieren und Zugriff gewähren
AccessControl.validateAndGrantAccess('BETA2025') // true/false

// Zugriff entziehen (für Testing)
AccessControl.revokeAccess()

// Erwarteten Code abrufen (Development only)
AccessControl.getExpectedCode() // 'BETA2025'
```

## Nach dem Launch

### System entfernen
1. Entfernen Sie `VITE_ACCESS_CODE` aus den Environment Variables
2. Entfernen Sie `AccessProtectedRoute` aus den Auth-Routen in `App.tsx`
3. Entfernen Sie `/access` Route
4. Optional: Löschen Sie die Access Code Dateien

### System deaktivieren (temporär)
Setzen Sie `VITE_ACCESS_CODE` auf einen leeren String:
```env
VITE_ACCESS_CODE=
```

## Troubleshooting

### "Code wird nicht akzeptiert"
- Prüfen Sie die .env.local Datei
- Starten Sie den Dev-Server neu
- Code wird automatisch in Großbuchstaben konvertiert

### "Benutzer werden nicht zur Access-Seite weitergeleitet"
- Löschen Sie den Browser-Cache
- Öffnen Sie ein neues Inkognito-Fenster
- Prüfen Sie die Konsole auf JavaScript-Fehler

### "Environment Variable funktioniert nicht"
- Dateiname muss exakt `.env.local` sein
- Variable muss mit `VITE_` beginnen
- Entwicklungsserver nach Änderungen neu starten

## Code-Beispiele

### Aktueller Code
```
BETA2025
```

### Code ändern
```env
# .env.local
VITE_ACCESS_CODE=NEUE_BETA_2025
```

Das System ist vollständig implementiert und einsatzbereit!