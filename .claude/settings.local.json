{"permissions": {"allow": ["Bash(npm run lint)", "Bash(npm run build:*)", "Bash(find:*)", "Bash(npm run dev:*)", "<PERSON><PERSON>(pkill:*)", "Bash(ls:*)", "Bash(timeout 10s npm run dev)", "mcp__sequential-thinking__sequentialthinking", "Bash(rm:*)", "mcp__playwright__browser_navigate", "<PERSON><PERSON>(curl:*)", "mcp__playwright__browser_install", "<PERSON><PERSON>(mv:*)", "Bash(grep:*)", "<PERSON><PERSON>(mkdir:*)", "Bash(cp:*)", "Bash(npm install)", "Bash(npm install:*)", "<PERSON><PERSON>(convert:*)", "Bash(npm run preview:*)", "Bash(npm run lint:*)", "Bash(npm test:*)", "mcp__context7__resolve-library-id", "mcp__context7__get-library-docs", "Bash(npm uninstall:*)", "Bash(npx tsc:*)", "mcp__ide__getDiagnostics"], "deny": []}}