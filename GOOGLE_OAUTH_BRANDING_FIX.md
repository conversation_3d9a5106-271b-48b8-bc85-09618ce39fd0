# Google OAuth Branding Fix - "lanzr.de" statt "supabase.co"

## Das Problem

Im Google-Anmeldescreen steht:
```
Konto auswählen
weiter zu wkmzfqjnlwaogqnkbfgw.supabase.co
```

Stattdessen soll stehen:
```
Konto auswählen  
weiter zu Lanzr
```

## Die Lösung

### 1. Google Cloud Console - OAuth Consent Screen

Gehen Sie zur [Google Cloud Console](https://console.cloud.google.com/):

1. **APIs & Services** > **OAuth consent screen**
2. Klicken Sie auf **EDIT APP** (falls bereits konfiguriert)

### 2. App Information aktualisieren

**Wichtige Felder:**

```
App name: Lanzr
(Das ist der Name, der im Google Login erscheint!)

User support email: <EMAIL>

Application home page: https://lanzr.de
(Ihre echte Domain!)

Application privacy policy link: https://lanzr.de/privacy
(Optional, aber empfohlen)

Application terms of service link: https://lanzr.de/terms  
(Optional, aber empfohlen)
```

### 3. Authorized Domains (WICHTIGSTE EINSTELLUNG!)

**Authorized domains:**
```
lanzr.de
```

**Achtung:**
- ❌ NICHT: `https://lanzr.de` 
- ❌ NICHT: `www.lanzr.de`
- ✅ NUR: `lanzr.de`

**Das sorgt dafür, dass "lanzr.de" statt "supabase.co" angezeigt wird!**

### 4. App Logo (Optional aber professionell)

Laden Sie Ihr Lanzr-Logo hoch:
- **App logo:** 120x120 Pixel, PNG/JPG
- Wird im Google-Anmeldescreen angezeigt

### 5. Scopes (falls nicht schon konfiguriert)

Fügen Sie diese Scopes hinzu:
```
../auth/userinfo.email
../auth/userinfo.profile  
openid
```

### 6. Test Users (bei Development)

Falls Ihre App noch im "Testing"-Modus ist:
- Fügen Sie sich als Test User hinzu
- Oder stellen Sie auf "Production" (nach Review)

### 7. Nach den Änderungen

**Speichern** und dann testen:
1. Loggen Sie sich aus Ihrer App aus
2. Versuchen Sie Google-Anmeldung erneut
3. Sollte jetzt "Lanzr" statt "supabase.co" zeigen

## Troubleshooting

**Falls immer noch "supabase.co" angezeigt wird:**

1. **Cache leeren** - Google cached OAuth-Screens
2. **Incognito-Modus** verwenden zum Testen  
3. **Authorized domains** nochmal prüfen
4. **App name** nochmal prüfen

## Weitere Verbesserungen

**Für noch professionelleres Branding:**
- Logo hochladen (120x120px)
- Privacy Policy erstellen
- Terms of Service erstellen
- App zur Production-Verifizierung einreichen

Nach diesen Änderungen zeigt Google ein sauberes, professionelles Login mit "Lanzr" statt kryptischer Supabase-URLs! 🎉