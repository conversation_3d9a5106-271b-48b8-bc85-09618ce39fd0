# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Development Commands

### Common Commands
```bash
npm run dev          # Start development server with hot-reload
npm run build        # Build for production
npm run build:dev    # Build for development
npm run lint         # Run ESLint
npm run preview      # Preview production build
```

### Testing
This project doesn't have a specific test command configured. Check with the user if they need testing setup.

## CRITICAL DATABASE RULES

### ⚠️ DATABASE MIGRATION POLICY ⚠️
**NEVER execute any database migration commands!**

- ❌ NEVER run `npx supabase db reset`
- ❌ NEVER run `npx supabase db push` 
- ❌ NEVER run `npx supabase migration up`
- ❌ NEVER run any Supabase CLI database commands

**What Claude Code should do:**
- ✅ CREATE migration files in `supabase/migrations/`
- ✅ CREATE edge function files
- ✅ WRITE SQL schema changes
- ✅ UPDATE documentation

**What the user handles:**
- 🔧 User executes all database migrations manually
- 🔧 User runs Supabase CLI commands when ready
- 🔧 User controls database state and timing

## Project Architecture

### Tech Stack
- **Frontend**: React 18 + TypeScript + Vite
- **UI**: shadcn/ui components with Tailwind CSS
- **State Management**: TanStack Query for server state, React hooks for local state
- **Backend**: Supabase (PostgreSQL, Auth, Storage, Edge Functions)
- **AI Integration**: Gemini API for text generation via Supabase Edge Functions

### Core Application Structure
This is a freelance project management application with AI-powered application generation:

**Main Features:**
- Project dashboard with CRUD operations and KPI tracking
- Activity timeline with project change history
- AI-powered application text generation  
- Detailed statistics and analytics
- User settings and CV management with profile pictures
- Authentication via Supabase Auth
- Calendar integration for project dates and events
- Advanced import/export system with duplicate detection
- Mobile-responsive design with modern UI

**Key Components:**
- `src/components/dashboard/Dashboard.tsx`: Main project overview with filtering
- `src/components/dashboard/Statistics.tsx`: Detailed project analytics
- `src/components/dashboard/DashboardStats.tsx`: Essential KPI cards
- `src/components/projects/ProjectCard.tsx`: Individual project display
- `src/components/projects/ProjectForm.tsx`: Project creation/editing
- `src/components/projects/ProjectTimeline.tsx`: Activity timeline and analytics
- `src/components/settings/SettingsModern.tsx`: User profile and settings (modern UI)
- `src/components/layout/AppSidebar.tsx`: Navigation sidebar
- `src/components/calendar/`: Calendar-related components

### Database Schema
**Main Tables:**
- `freelance_projects`: Project data with status tracking and contact information
- `user_settings`: User profile, contact info, availability, CV upload, profile pictures
- `project_activities`: Activity log for project changes (app-side logic only)
- `calendar_events`: Calendar events linked to projects with reminder functionality

**Project Status Flow:**
`not_applied` → `application_sent` → `inquiry_received` → `interview_scheduled` → `interview_completed` → `offer_received` → `rejected`/`project_completed`

### State Management Patterns
- **Server State**: TanStack Query with React hooks pattern
- **Authentication**: Supabase Auth with session management
- **Forms**: React Hook Form with Zod validation
- **UI State**: Local useState/useReducer in components

### Key Custom Hooks
- `useFreelanceProjects()`: Project CRUD operations with activity logging
- `useUserSettings()`: User profile management
- `useExport()`: PDF/Excel export functionality with progress tracking
- `useImport()`: Robust import functionality with duplicate detection and progress modal
- `useProjectActivities()`: Activity tracking and timeline data
- `useCalendarEvents()`: Calendar event management and scheduling

### Supabase Integration
- **Client**: `src/integrations/supabase/client.ts`
- **Types**: `src/integrations/supabase/types.ts` (auto-generated)
- **Edge Functions**: 
  - `analyze-project`: AI project analysis
  - `generate-application`: AI application generation
- **Storage Buckets**:
  - `cv-uploads`: PDF CVs for user profiles
  - `profile-pictures`: User profile images (JPG, PNG, WebP)
- **Row Level Security**: Enabled for all tables

### AI Features
- Project description analysis and data extraction
- Personalized application text generation
- CV-based skill matching
- All AI operations use Gemini API via Edge Functions

### File Structure Conventions
- Components use PascalCase
- Hooks use camelCase with `use` prefix
- Types defined in `src/types/`
- Utility functions in `src/lib/`
- Supabase integration in `src/integrations/supabase/`

### Styling
- Tailwind CSS with custom design system
- Dark/Light mode support via `next-themes`
- Semantic color tokens defined in `src/index.css`
- shadcn/ui components for consistency

### Import/Export Features
- **Export**: PDF export using jsPDF, Excel export using xlsx, JSON backup export
- **Import**: Advanced JSON import with duplicate detection and overwrite options
- **Progress Tracking**: Real-time progress modals for large operations
- **Services**: 
  - `src/services/exportService.ts`: Export functionality with progress tracking
  - `src/services/importService.ts`: Robust import with validation and duplicate handling
- **UI Components**:
  - `src/components/ui/import-progress-modal.tsx`: Import progress display
  - `src/components/ui/import-confirmation-dialog.tsx`: Duplicate handling confirmation

## Important Notes

- All database operations require authentication
- AI features require `GEMINI_API_KEY` environment variable
- CV files are stored in Supabase Storage `cv-uploads` bucket (PDF only)
- Profile pictures stored in Supabase Storage `profile-pictures` bucket (JPG, PNG, WebP, max 5MB)
- Theme defaults to dark mode (`storageKey: "freelance-tracker-theme"`)
- German locale used for date formatting (`date-fns/locale/de`)
- Import files limited to 50MB JSON files with validation
- Database schema uses `contact_person` field (not `contact_name`)

## Code Style Guidelines

### Language Requirements
- **ALL code comments MUST be in English** - Never use German or other languages in code comments
- **ALL documentation MUST be in English** - Including README, inline docs, and technical documentation
- **User-facing text can remain in German** - UI labels, error messages, etc. for German users

### Page Layout Standards
**ALWAYS use the PageLayout component for new pages to ensure consistency:**

```tsx
import { PageLayout } from '@/components/layout/PageLayout';

const MyNewPage = () => {
  return (
    <PageLayout
      title="Page Title"
      description="Optional page description"
      headerActions={<Button>Optional Action</Button>} // Optional
    >
      {/* Your page content here */}
    </PageLayout>
  );
};
```

**Features of PageLayout:**
- Consistent spacing: `px-2 sm:px-6 pb-3 sm:pb-6`
- Gradient title styling: `bg-gradient-primary bg-clip-text text-transparent`
- Responsive design: Mobile-first with `sm:` breakpoints
- Proper container constraints: `max-w-full overflow-x-hidden`
- Optional header actions support

**Never manually implement page layout patterns - always use PageLayout!**

### Code Commenting Standards
```typescript
// ✅ CORRECT - English comments
// Handle user authentication and session management
const handleLogin = async (email: string, password: string) => {
  // Validate input parameters before proceeding
  if (!email || !password) {
    throw new Error('Email and password are required');
  }
  // ... rest of implementation
};

// ❌ INCORRECT - German comments
// Behandle Benutzer-Authentifizierung und Session-Management
const handleLogin = async (email: string, password: string) => {
  // Validiere Eingabeparameter vor Fortsetzung
  // ... implementation
};
```

## Mobile-First Responsive Design Guidelines

**CRITICAL: Always apply these patterns to prevent horizontal overflow on mobile devices**

### Container Hierarchy Standards
```typescript
// ✅ CORRECT - Mobile-first container structure
<AppLayout>
  <div className="w-full max-w-full overflow-x-hidden min-w-0">
    <PageLayout title="Page Title" description="Description">
      {/* Page content with proper constraints */}
    </PageLayout>
  </div>
</AppLayout>

// ❌ INCORRECT - Using responsive container (causes overflow)
<div className="container mx-auto px-4">
  {/* Content may overflow on mobile */}
</div>
```

### Essential Mobile-First CSS Classes
**Apply these classes consistently to prevent overflow:**

```css
/* Container-Level Fixes */
w-full max-w-full          /* Full width with max constraints */
overflow-x-hidden          /* Prevent horizontal scroll */
min-w-0                    /* Allow flex shrinking */

/* Text Handling */
truncate                   /* Single-line text truncation */
break-words                /* Multi-line text wrapping */
text-sm sm:text-base       /* Responsive font sizes */

/* Spacing (Mobile-First) */
p-2 sm:p-4                 /* Responsive padding */
px-1.5 sm:px-3             /* Responsive horizontal padding */
gap-1 sm:gap-2             /* Responsive gaps */
space-y-2 sm:space-y-4     /* Responsive vertical spacing */

/* Button Optimization */
px-1.5 sm:px-3             /* Tighter mobile buttons */
size="sm"                  /* Smaller touch targets on mobile */
flex-shrink-0              /* Prevent button compression */

/* Grid/Flex Layouts */
min-h-[50px] sm:min-h-[80px]  /* Responsive minimum heights */
flex-col sm:flex-row           /* Stack mobile, row desktop */
```

### Navigation Pattern for Mobile
```typescript
// Mobile-first navigation structure
<div className="w-full min-w-0 mb-6">
  {/* Mobile Navigation - Stack vertically */}
  <div className="sm:hidden space-y-3">
    <div className="flex items-center justify-between min-w-0">
      <div className="flex items-center gap-2 min-w-0 flex-1">
        <div className="flex items-center gap-1 flex-shrink-0">
          {/* Navigation buttons with tight spacing */}
        </div>
        <h2 className="text-base font-semibold truncate min-w-0">
          {/* Truncated title */}
        </h2>
      </div>
    </div>
  </div>
  
  {/* Desktop Navigation - Horizontal */}
  <div className="hidden sm:flex items-center justify-between">
    {/* Full desktop navigation */}
  </div>
</div>
```

### Event/Card Component Mobile Pattern
```typescript
<Card className="p-2 sm:p-4 w-full max-w-full overflow-x-hidden">
  <div className="flex items-start justify-between gap-2 min-w-0">
    <div className="flex-1 min-w-0">
      <h3 className="font-medium truncate text-sm sm:text-base">
        {title}
      </h3>
      <p className="text-xs sm:text-sm text-muted-foreground break-words">
        {description}
      </p>
    </div>
    <Button size="sm" className="px-1.5 flex-shrink-0">
      Action
    </Button>
  </div>
</Card>
```

### Common Overflow Sources to Avoid
1. **Fixed widths** without mobile breakpoints
2. **`container mx-auto`** without proper constraints  
3. **Long unbreakable text** without truncation
4. **Large button groups** without responsive spacing
5. **Grid layouts** without mobile adaptations
6. **Missing `min-w-0`** on flex containers
7. **Fixed padding/margins** instead of responsive values

### Testing Checklist for Mobile
- [ ] Test at 320px viewport width (smallest mobile)
- [ ] Verify no horizontal scrollbars appear
- [ ] Check all text truncates/wraps properly
- [ ] Ensure buttons are touch-friendly (44px minimum)
- [ ] Validate responsive spacing works
- [ ] Confirm all containers respect viewport boundaries

**ALWAYS test mobile-first and scale up to desktop, never the reverse!**

## Database Design Principles

- **NO DATABASE FUNCTIONS**: Never use PostgreSQL functions, triggers, or stored procedures
- **APP-SIDE LOGIC**: All business logic must be handled in the React application
- **DATABASE INDEPENDENCE**: Keep database schema simple - only tables, indexes, RLS policies
- **CLIENT-SIDE TRACKING**: Activity logging, status changes, etc. handled via React hooks/services
- **REASON**: Maintain database independence and avoid vendor lock-in

## Documentation Structure

### Main Documentation Files
- **CLAUDE.md** (this file): Development guidance and architecture overview
- **README.md**: Project setup, installation, and general information
- **docs/project.md**: Complete Product Requirements Document (consolidated PRD)
- **docs/frontend.md**: Frontend architecture and component documentation
- **docs/backend.md**: Supabase backend and database schema documentation

### Documentation Standards
- All documentation is in **English only**
- This is a standalone project
- Code comments must be in **English**
- User-facing text can remain in German for German users