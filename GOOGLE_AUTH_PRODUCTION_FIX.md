# Google Auth Produktion Fix - Redirect URL Problem

## Das Problem

Auf der Produktion redirectet Google Auth zu `localhost:3000` statt zu Ihrer echten Produktions-URL.

## Die Lösung

### 1. Google Cloud Console - Authorized redirect URIs aktualisieren

Gehen Si<PERSON> zu <PERSON> [Google Cloud Console](https://console.cloud.google.com/):

1. **APIs & Services** > **Credentials** > Ihr OAuth 2.0 Client
2. **Authorized redirect URIs** komplett ersetzen mit:

```
https://wkmzfqjnlwaogqnkbfgw.supabase.co/auth/v1/callback
http://localhost:8080/
https://IHR-PRODUKTIONS-DOMAIN.com/
```

**Wichtig:** Ersetzen Sie `IHR-PRODUKTIONS-DOMAIN.com` mit Ihrer echten Domain!

### 2. Supabase Dashboard - Site URL & Redirect URLs

Gehen Sie zu Ihrem Supabase Dashboard:

**Authentication > Settings > General:**
- **Site URL:** `https://IHR-PRODUKTIONS-DOMAIN.com`

**Authentication > URL Configuration:**
- **Redirect URLs:**
```
http://localhost:8080/
https://IHR-PRODUKTIONS-DOMAIN.com/
```

### 3. Woher kommt localhost:3000?

Das Problem liegt daran, dass:
1. Google OAuth noch die alte/falsche Produktions-URL gespeichert hat
2. Oder die Supabase "Site URL" noch falsch konfiguriert ist
3. Google standardmäßig zu localhost:3000 redirectet, wenn die Produktions-URL fehlt

### 4. Debug-Schritte

**Aktuelle Konfiguration prüfen:**

1. **Google Cloud Console** > Credentials > Authorized redirect URIs - sollte Ihre echte Domain enthalten
2. **Supabase Dashboard** > Auth > Settings > Site URL - sollte Ihre echte Domain sein
3. **Supabase Dashboard** > Auth > URL Configuration > Redirect URLs - sollte Ihre echte Domain enthalten

### 5. Häufige Fehlerquellen

❌ **Site URL in Supabase ist localhost**
❌ **Google OAuth hat keine Produktions-Domain**  
❌ **Redirect URLs fehlt die Produktions-Domain**
❌ **HTTP statt HTTPS bei der Produktions-URL**

### 6. Nach der Korrektur testen

1. Loggen Sie sich aus der Produktions-App aus
2. Testen Sie Google-Anmeldung erneut
3. Sollte jetzt zu Ihrer echten Domain redirecten

## Wichtiger Hinweis

Die URL `localhost:3000` deutet darauf hin, dass in Ihrer Supabase-Konfiguration noch eine Development-URL als Standard gesetzt ist. Das muss unbedingt auf Ihre Produktions-Domain geändert werden!

Welche Domain ist denn Ihre Produktions-URL? Dann kann ich Ihnen die exakten Einstellungen geben.