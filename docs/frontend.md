# Frontend Documentation

## Technology Stack

### Core Technologies
- **React 18**: Modern React version with Hooks and Concurrent Features
- **TypeScript**: Type-safe development
- **Vite**: Fast build tool and dev server
- **React Router DOM**: Client-side routing

### UI Framework
- **Tailwind CSS**: Utility-first CSS framework
- **shadcn/ui**: Modern, accessible UI components
- **Lucide React**: Icon library
- **next-themes**: Dark/Light mode management

### State Management & Data Fetching
- **TanStack Query**: Server state management
- **React Hook Form**: Form management
- **Zod**: Schema validation

## Project Structure

```
src/
├── components/
│   ├── dashboard/          # Dashboard and statistics components
│   │   ├── Dashboard.tsx   # Main dashboard
│   │   ├── Statistics.tsx  # Detailed analytics
│   │   └── DashboardStats.tsx # KPI cards
│   ├── projects/           # Project management components
│   │   ├── ProjectCard.tsx # Project card component
│   │   ├── ProjectForm.tsx # Project form
│   │   └── ProjectTimeline.tsx # Activity timeline
│   ├── settings/           # Settings components
│   │   └── SettingsModern.tsx # Modern settings UI
│   ├── calendar/           # Calendar components
│   ├── layout/             # Layout components
│   │   └── AppSidebar.tsx  # Navigation sidebar
│   ├── theme-provider.tsx  # Theme context
│   ├── theme-toggle.tsx    # Theme switcher
│   └── ui/                 # shadcn/ui components
│       ├── import-progress-modal.tsx
│       └── import-confirmation-dialog.tsx
├── hooks/                  # Custom React hooks
│   ├── useFreelanceProjects.ts
│   ├── useUserSettings.ts
│   ├── useImport.ts
│   └── useExport.ts
├── services/               # Business logic services
│   ├── importService.ts
│   └── exportService.ts
├── integrations/supabase/  # Supabase integration
├── lib/                    # Utility functions
├── pages/                  # Page components
├── types/                  # TypeScript type definitions
└── main.tsx               # App entry point
```

## Component Architecture

### Dashboard (`src/components/dashboard/Dashboard.tsx`)
- **Purpose**: Main view with project overview and search functionality
- **Features**: Project filtering, status management, bulk actions
- **State**: Uses `useFreelanceProjects` hook

### ProjectCard (`src/components/projects/ProjectCard.tsx`)
- **Purpose**: Individual project card with quick actions
- **Features**: Status badges, quick actions, responsive design
- **Props**: `project`, `onEdit`, `onDelete`, `onGenerateApplication`

### ProjectForm (`src/components/projects/ProjectForm.tsx`)
- **Purpose**: Form for project creation and editing
- **Features**: Validation, auto-save, skill tags
- **Validation**: Zod schema for type safety

### SettingsModern (`src/components/settings/SettingsModern.tsx`)
- **Purpose**: User settings and profile management
- **Features**: CV upload, profile pictures, availability management, contact data
- **State**: Uses `useUserSettings` hook

### Import/Export Components
- **ImportProgressModal**: Real-time progress display for imports
- **ImportConfirmationDialog**: Duplicate handling confirmation
- **Features**: Progress bars, error reporting, user feedback

## Custom Hooks

### `useFreelanceProjects`
```typescript
// Manages all project CRUD operations
const { projects, loading, createProject, updateProject, deleteProject } = useFreelanceProjects();
```

### `useUserSettings`
```typescript
// Manages user settings
const { settings, loading, updateSettings, uploadCV } = useUserSettings();
```

### `useImport`
```typescript
// Handles robust import functionality
const { importFromFile, isImporting, progress, result, showModal } = useImport();
```

### `useExport`
```typescript
// Manages export operations
const { exportToPDF, exportToExcel, exportToJSON, isExporting } = useExport();
```

## Design System

### Colors
- Semantic tokens defined in `src/index.css`
- HSL-based color palette for Dark/Light mode
- Design tokens in `tailwind.config.ts`

### Component Variants
- shadcn/ui components with custom variants
- Consistent spacing and typography
- Responsive breakpoints

## Routing

```typescript
// Main routes
/           # Dashboard (Index)
/auth       # Authentication
/*          # 404 Not Found
```

## Performance Optimizations

- **Code Splitting**: Automatically through Vite
- **Lazy Loading**: React.lazy for components
- **Memoization**: React.memo for expensive components
- **Query Caching**: TanStack Query for server-state

## Development Guidelines

### Component Standards
- Functional components with TypeScript
- Export props interfaces
- Consistent naming (PascalCase for components)
- English comments for complex logic

### State Management
- Server State: TanStack Query
- Client State: useState/useReducer
- Global State: React Context (minimal)

### Styling
- Tailwind-only (no CSS modules)
- Use semantic tokens
- Mobile-first responsive design
- Dark mode support mandatory

### Language Requirements
- **ALL code comments MUST be in English**
- **ALL documentation MUST be in English**
- User-facing text can remain in German for German users