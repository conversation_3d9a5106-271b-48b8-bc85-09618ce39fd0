# Freelance Project Management App - Product Requirements Document

## Overview

A modern web application for managing freelance projects and applications with AI-powered application generation. The app enables freelancers to organize their project inquiries, track the application process, and create personalized application texts through AI.

## Technology Stack

### Core Technologies
- **Frontend**: React 18 + TypeScript + Vite
- **UI Framework**: shadcn/ui with Tailwind CSS
- **Backend**: Supabase (PostgreSQL, Auth, Storage, Edge Functions)
- **State Management**: TanStack Query + React Hooks
- **AI Integration**: Gemini API via Supabase Edge Functions
- **Styling**: Dark/Light Mode Support, Mobile-First Design
- **Deployment**: Vercel Platform

## Database Schema

### Table: `user_settings`
```sql
- id (UUID, Primary Key)
- user_id (UUID, Foreign Key to auth.users)
- full_name (TEXT)
- professional_email (TEXT)
- phone (TEXT)
- address (TEXT)
- website (TEXT)
- hourly_rate_eur (INTEGER)
- availability_start_date (DATE)
- availability_end_date (DATE)
- availability_hours_per_week (INTEGER)
- availability_notes (TEXT)
- cv_pdf_url (TEXT)
- profile_picture_url (TEXT)
- created_at, updated_at (TIMESTAMPTZ)
```

### Table: `freelance_projects`
```sql
- id (UUID, Primary Key)
- user_id (UUID, Foreign Key to auth.users)
- project_name (TEXT, Required)
- company_name (TEXT, Required)
- contact_person (TEXT)
- contact_email (TEXT)
- contact_phone (TEXT)
- project_description (TEXT)
- budget_range (TEXT)
- project_start_date (DATE)
- project_end_date (DATE)
- required_skills (TEXT[])
- application_date (DATE)
- status (ENUM: 'not_applied', 'recommended', 'recruiter_contacted', 'application_sent', 'inquiry_received', 'interview_scheduled', 'interview_completed', 'offer_received', 'rejected', 'project_completed')
- application_text (TEXT)
- notes (TEXT)
- source (TEXT)
- listing_url (TEXT)
- work_location_type (ENUM: 'remote', 'onsite', 'hybrid', 'flexible')
- remote_percentage (INTEGER)
- work_location_notes (TEXT)
- created_at, updated_at (TIMESTAMPTZ)
```

### Table: `project_activities`
```sql
- id (UUID, Primary Key)
- project_id (UUID, Foreign Key to freelance_projects)
- user_id (UUID, Foreign Key to auth.users)
- activity_type (TEXT, Required)
- description (TEXT, Required)
- notes (TEXT)
- notes_date (DATE)
- created_at (TIMESTAMPTZ)
```

### Table: `calendar_events`
```sql
- id (UUID, Primary Key)
- user_id (UUID, Foreign Key to auth.users)
- project_id (UUID, Foreign Key to freelance_projects, Optional)
- title (TEXT, Required)
- description (TEXT)
- start_date (DATE, Required)
- start_time (TIME)
- end_date (DATE)
- end_time (TIME)
- all_day (BOOLEAN, Default: false)
- event_type (TEXT, Default: 'manual')
- color (TEXT, Default: '#3b82f6')
- location (TEXT)
- completed (BOOLEAN, Default: false)
- reminder_enabled (BOOLEAN, Default: false)
- reminder_minutes_before (INTEGER)
- created_automatically (BOOLEAN, Default: false)
- source_status (TEXT)
- created_at, updated_at (TIMESTAMPTZ)
```

## Application Architecture

### Pages/Views
1. **Auth Page** (`/auth`)
   - Login/Register with Supabase Auth
   - Email/Password Authentication

2. **Dashboard** (`/` - Default)
   - Compact overview cards: Total, Success Rate, Running Projects, Offers
   - Project search with phone number normalization
   - Status filters
   - Project list with pagination
   - Mobile-optimized: Minimal scrolling to projects

3. **Statistics** (`/statistics`)
   - Complete status distribution (all statuses)
   - Performance metrics (Application Rate, Success Rate, Interview Rate)
   - Recent Activity (last 30 days)
   - Pipeline analysis with progress bars

4. **Settings** (`/settings`)
   - User profile information
   - Availability management
   - CV upload (PDF, Supabase Storage)
   - Profile picture upload (JPG/PNG/WebP)
   - Data export (PDF, Excel, JSON)
   - Data import with progress modal

### Component Structure
```
src/
├── components/
│   ├── dashboard/
│   │   ├── Dashboard.tsx
│   │   ├── DashboardStats.tsx (compact version)
│   │   └── Statistics.tsx (detailed analytics)
│   ├── projects/
│   │   ├── ProjectCard.tsx
│   │   ├── ProjectForm.tsx
│   │   └── ProjectTimeline.tsx
│   ├── settings/
│   │   └── SettingsModern.tsx
│   ├── calendar/
│   ├── layout/
│   │   └── AppSidebar.tsx
│   └── ui/ (shadcn/ui components + custom components)
│       ├── import-progress-modal.tsx
│       └── import-confirmation-dialog.tsx
├── hooks/
│   ├── useFreelanceProjects.ts
│   ├── useUserSettings.ts
│   ├── useImport.ts
│   ├── useExport.ts
│   └── usePagination.ts
├── services/
│   ├── importService.ts
│   ├── exportService.ts
│   ├── activityService.ts
│   └── calendarService.ts
├── types/
│   ├── freelance.ts
│   ├── settings.ts
│   ├── export.ts
│   └── calendar.ts
└── lib/
    └── utils.ts (incl. normalizePhoneNumber)
```

## Core Functionalities

### 1. Project Management
- **CRUD Operations**: Create, edit, delete, view projects
- **Status Tracking**: Multi-stage workflow from "Not Applied" to "Project Completed"
- **Search/Filter Function**: 
  - Text search across all fields (name, company, contact, skills, etc.)
  - Phone number normalization for better search
  - Status-based filtering
- **Pagination**: Configurable projects per page

### 2. AI Features (via Supabase Edge Functions)
- **Project Analysis**: Automatic extraction of project data from descriptions
- **Application Generation**: Personalized application texts based on:
  - User profile and CV
  - Project requirements
  - Availability
- **Gemini API Integration**: Via Supabase Edge Functions

### 3. User Settings
- **Profile Information**: Name, email, phone, address, website, hourly rate
- **Availability Management**: Time period, hours/week, notes
- **CV Upload**: PDF files in Supabase Storage with RLS
- **Profile Pictures**: Image upload with size and type validation

### 4. Import/Export System
- **Robust Import**: JSON import with duplicate detection and overwrite options
- **Multi-Format Export**: PDF reports, Excel spreadsheets, JSON backups
- **Progress Tracking**: Real-time progress display for large operations
- **Validation**: Automatic file validation (50MB limit, type checks)
- **Duplicate Handling**: Smart duplicate detection using listing URL and project+company name

### 5. Dashboard Analytics
**Compact Overview (Dashboard):**
- Total Projects
- Success Rate (Offers/Applications)
- Running Projects
- Received Offers

**Detailed Statistics (separate page):**
- Complete status distribution with progress bars
- Performance metrics (Application Rate, Interview Rate)
- Recent Activity (last 30 days)
- Pipeline analysis and conversion rates

### 6. Calendar Integration
- **Project Events**: Automatic appointments for project dates
- **Reminders**: Configurable notifications
- **Timeline View**: Chronological display of all activities
- **Activity Logging**: Automatic tracking of all project changes

## UI/UX Design

### Design System
- **shadcn/ui Components**: Card, Button, Input, Select, Dialog, Progress, Badge
- **Color Scheme**: Status-based color coding
  - Primary: Project statistics
  - Blue: Applications/Running projects
  - Green: Success/Offers
  - Red: Rejections
  - Purple: Interviews
- **Dark/Light Mode**: Complete support
- **Mobile-First**: Responsive design for all screen sizes

### Navigation
- **Sidebar Navigation**: Dashboard, Statistics, Settings
- **Mobile Sidebar**: Automatic closing after navigation
- **Breadcrumb/State Management**: Tab-based navigation

### Key UX Principles
- **Minimal Scrolling**: Dashboard optimized for quick access to projects
- **Intuitive Icons**: Status-specific Lucide icons
- **Hover Effects**: Micro-interactions for better usability
- **Progress Visualization**: Progress bars for metrics and status
- **Loading States**: For all async operations
- **Error Handling**: Comprehensive error boundaries

## Status Definitions

### Project Status Pipeline
1. **not_applied** - "Not Applied Yet"
   - Project is recorded but no application sent yet
   
2. **recommended** - "Recommended"
   - Project has been recommended or suggested
   
3. **recruiter_contacted** - "Recruiter Contacted"
   - Initial contact with recruiter or hiring manager
   
4. **application_sent** - "Application Sent"
   - Application has been sent to the company
   
5. **inquiry_received** - "Inquiry Received"
   - Company responded with questions or interest
   
6. **interview_scheduled** - "Interview Scheduled"
   - Interview appointment has been arranged
   
7. **interview_completed** - "Interview Completed"
   - Interview has taken place, result still pending
   
8. **offer_received** - "Offer Received"
   - Project offer received from company
   
9. **rejected** - "Rejected"
   - Rejection received from company
   
10. **project_completed** - "Project Completed"
    - Project was successfully completed

## Supabase Configuration

### Authentication
- Email/Password Authentication
- Row Level Security (RLS) for all tables
- User-based data isolation

### Storage
- **Bucket**: `cv-uploads`
  - Policies: User can only upload/view/delete own CVs
  - File Types: PDF only
  - Size Limit: Configurable

- **Bucket**: `profile-pictures`
  - Policies: User can upload/view/delete own profile pictures
  - File Types: JPG, PNG, WebP
  - Size Limit: 5MB

### Edge Functions
- **analyze-project**: Project analysis via Gemini API
- **generate-application**: Application text generation
- **Environment**: `GEMINI_API_KEY` required

## Performance Requirements
- **Build Size**: Acceptable bundle size with code splitting
- **Loading States**: For all async operations
- **Error Handling**: Comprehensive error boundaries
- **Caching**: TanStack Query for optimized API calls
- **Memory Management**: Limits for large import operations

## Security
- **RLS Policies**: Strict user data separation
- **Input Validation**: Frontend + Backend validation
- **File Upload**: Secure PDF and image upload validation
- **API Keys**: Secure environment variable management
- **Authentication Required**: For all operations

## Mobile Optimization
- **Responsive Grid**: Multi-column layouts adapting to screen size
- **Touch-friendly**: Optimized button sizes and spacing
- **Sidebar**: Collapsible navigation for mobile
- **Search**: Mobile-optimized search bar
- **Forms**: Mobile-friendly form layouts

## Implementation Details

### Search Functionality
- **Normalized Phone Number Search**: Removes spaces, hyphens and other formatting for better matches
- **Multi-Field Search**: Searches project name, company name, contact data, description, skills, notes
- **Real-time Filtering**: Immediate results during input

### Export Features
- **PDF**: Complete report with structured presentation
- **Excel**: Tabular data for further analysis
- **JSON**: Complete backup of all data including settings

### Technical Requirements
- **TypeScript**: Strict typing for all components
- **Error Boundaries**: Graceful error handling
- **Loading States**: Spinners and skeleton loaders
- **Form Validation**: React Hook Form with Zod schema validation
- **Responsive Design**: Breakpoints for mobile, tablet, desktop
- **Accessibility**: ARIA labels, keyboard navigation, screen reader support

### Language Requirements
- **ALL code comments MUST be in English** - Never use German or other languages
- **ALL documentation MUST be in English** - Including README, inline docs
- **User-facing text can remain in German** - UI labels, error messages for German users

## Current Status

✅ **Production** - Fully functional application with all core features implemented, including:
- Complete project management lifecycle
- AI-powered application generation
- Robust import/export system with progress tracking
- Mobile-responsive design
- Dark/light mode support
- Comprehensive user settings and profile management