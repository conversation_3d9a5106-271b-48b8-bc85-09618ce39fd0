# Follow-Up System - Comprehensive QA Test Plan

## 🎯 Test Strategy Overview

**Current State:** No testing framework currently implemented
**Priority Setup:** Vitest + React Testing Library (immediate implementation)
**E2E Tests:** Define test scenarios but don't implement yet
**Database:** Use existing Supabase instance with mocked data (no separate test DB)

---

## 📊 Test Coverage Priorities

### 🔴 **CRITICAL (Must-Have) - 85% Coverage Required**
- Core CRUD operations
- Data integrity and security
- Auto-scheduling functionality  
- Calendar integration
- Template personalization
- Response tracking

### 🟡 **IMPORTANT (Should-Have) - 70% Coverage Target**
- UI component rendering
- Error handling
- Notification system
- Analytics accuracy

### 🟢 **NICE-TO-HAVE (Could-Have) - 50% Coverage Target**  
- Performance tests
- Accessibility
- Edge cases

---

## 🧪 Test Categories & Detailed Test Cases

### **1. HOOK LAYER TESTS (Unit Tests with Mocked Supabase)**
**Focus: Test React hooks with mocked Supabase calls**

#### 1.1 useFollowUpTemplates Hook Tests
**Priority: 🔴 CRITICAL**

```typescript
// Test Case: FUH-001
describe('useFollowUpTemplates', () => {
  beforeEach(() => {
    // Mock Supabase auth and database calls
    vi.mocked(supabase.auth.getUser).mockResolvedValue({
      data: { user: mockUser },
      error: null
    });
  });

  test('should fetch templates successfully', async () => {
    const mockTemplates = [
      { id: '1', name: 'Test Template', subject: '{project_name}', ... },
      { id: '2', name: 'Interview Follow-up', subject: 'Interview: {project_name}', ... }
    ];
    
    vi.mocked(supabase.from).mockReturnValue({
      select: vi.fn().mockReturnThis(),
      eq: vi.fn().mockReturnThis(),
      order: vi.fn().mockResolvedValue({ data: mockTemplates, error: null })
    } as any);

    const { result } = renderHook(() => useFollowUpTemplates());
    
    await waitFor(() => {
      expect(result.current.templates).toEqual(mockTemplates);
      expect(result.current.isLoading).toBe(false);
    });
  });

  test('should create template via mutation', async () => {
    // Mock successful creation
    vi.mocked(supabase.from).mockReturnValue({
      insert: vi.fn().mockReturnThis(),
      select: vi.fn().mockReturnThis(),
      single: vi.fn().mockResolvedValue({ data: mockTemplate, error: null })
    } as any);

    const { result } = renderHook(() => useFollowUpTemplates());
    
    act(() => {
      result.current.createTemplate.mutate({
        name: 'New Template',
        subject: 'Test Subject',
        body: 'Test Body',
        trigger_days: 7,
        status_trigger: 'application_sent'
      });
    });

    // Expected: Mutation called with correct data
    // Expected: Query invalidation triggered
    // Expected: Success toast shown
  });
});
```

#### 1.2 Follow-Up Schedule CRUD
**Priority: 🔴 CRITICAL**

```typescript
// Test Case: FUS-DB-002  
describe('FollowUp Schedule Database Operations', () => {
  test('should create scheduled follow-up with foreign key constraints', async () => {
    // Setup: Valid application and template
    // Expected: Schedule entry created
    // Expected: Foreign keys validated
    // Expected: Default status 'scheduled'
  });

  test('should update follow-up status correctly', async () => {
    // Expected: Status transitions: scheduled → sent → completed
    // Expected: sent_at timestamp set when marked as sent
    // Expected: response_received tracking
  });
});
```

#### 1.3 Follow-Up History CRUD
**Priority: 🔴 CRITICAL**

```typescript
// Test Case: FUH-DB-003
describe('FollowUp History Database Operations', () => {
  test('should create history entry when follow-up is sent', async () => {
    // Expected: History entry with subject/body content
    // Expected: Correct application_id and template_id references
    // Expected: sent_at timestamp
  });

  test('should track response status and date', async () => {
    // Expected: response_received boolean toggle
    // Expected: response_date when marked as responded
    // Expected: Calculate response time correctly
  });
});
```

---

### **2. SERVICE LAYER TESTS (Unit Tests)**

#### 2.1 Template Personalization
**Priority: 🔴 CRITICAL**

```typescript
// Test Case: FUS-SVC-001
describe('FollowUpService.personalizeTemplate', () => {
  test('should replace all placeholders with actual data', async () => {
    const template = 'Hallo {contact_person}, bezüglich {project_name} bei {company_name}. Nach {trigger_days} Tagen...';
    const variables = {
      contact_person: 'Max Mustermann',
      project_name: 'React Developer',
      company_name: 'Tech GmbH',
      trigger_days: 7,
      user_name: 'John Doe',
      application_date: '2024-01-15',
      interview_date: '2024-01-20'
    };

    const result = FollowUpService.personalizeTemplate(template, variables);
    
    // Expected: 'Hallo Max Mustermann, bezüglich React Developer bei Tech GmbH. Nach 7 Tagen...'
    // Expected: No placeholders remaining
    // Expected: Handle missing data gracefully
  });

  test('should handle edge cases in personalization', async () => {
    // Expected: Handle undefined/null values
    // Expected: Handle empty strings  
    // Expected: Handle special characters (ä, ö, ü, ß)
    // Expected: Handle long company names
  });
});
```

#### 2.2 Email Client Integration
**Priority: 🟡 IMPORTANT**

```typescript
// Test Case: FUS-SVC-002  
describe('FollowUpService.generateMailtoLink', () => {
  test('should generate valid mailto URL', async () => {
    const result = FollowUpService.generateMailtoLink(
      '<EMAIL>',
      'Follow-up: React Developer',  
      'Hallo Max Mustermann...'
    );
    
    // Expected: Valid mailto: URL format
    // Expected: Proper URL encoding
    // Expected: Include subject and body parameters
  });
});
```

#### 2.3 Analytics Calculations
**Priority: 🔴 CRITICAL**

```typescript
// Test Case: FUS-SVC-003
describe('FollowUpService.getFollowUpAnalytics', () => {
  test('should calculate response rates correctly', async () => {
    // Setup: 10 sent follow-ups, 3 responses
    // Expected: Response rate = 30%
    // Expected: Average response time calculation
    // Expected: Success by template breakdown
    // Expected: Success by timing analysis
  });

  test('should handle zero division in analytics', async () => {
    // Expected: Handle no sent follow-ups gracefully
    // Expected: Return 0% response rate for empty data
  });
});
```

---

### **3. CALENDAR INTEGRATION TESTS (Integration Tests)**

#### 3.1 Follow-Up to Calendar Event Creation
**Priority: 🔴 CRITICAL**

```typescript
// Test Case: FUC-INT-001
describe('Calendar Integration', () => {
  test('should create calendar event when follow-up is scheduled', async () => {
    // Setup: Schedule follow-up for project application
    // Expected: Calendar event created with correct date
    // Expected: Event title includes project name
    // Expected: Event description includes follow-up details
    // Expected: Event linked to application
  });

  test('should update calendar event when follow-up is rescheduled', async () => {
    // Expected: Calendar event date updated
    // Expected: Original event removed or updated
  });

  test('should remove calendar event when follow-up is deleted', async () => {
    // Expected: Calendar event deleted
    // Expected: No orphaned calendar entries
  });
});
```

---

### **4. AUTO-SCHEDULING TESTS (Integration Tests)**

#### 4.1 Status-Triggered Follow-Up Creation  
**Priority: 🔴 CRITICAL**

```typescript
// Test Case: FUA-INT-001
describe('Auto-Scheduling on Application Status Change', () => {
  test('should auto-schedule follow-up when application status changes', async () => {
    // Setup: Application with status 'application_sent'
    // Setup: Active template for 'application_sent' trigger
    
    // Action: Change application status to 'application_sent'
    // Expected: Follow-up automatically scheduled
    // Expected: Correct template selected
    // Expected: Correct trigger_days applied
    // Expected: Calendar event created
  });

  test('should not create duplicate follow-ups', async () => {
    // Setup: Application already has scheduled follow-up
    // Action: Status change again
    // Expected: No duplicate follow-ups created
  });

  test('should respect inactive templates', async () => {
    // Setup: Template with is_active = false
    // Action: Status change
    // Expected: No follow-up scheduled from inactive template
  });
});
```

---

### **5. NOTIFICATION SYSTEM TESTS (Integration Tests)**

#### 5.1 Follow-Up Due Notifications
**Priority: 🟡 IMPORTANT**

```typescript
// Test Case: FUN-INT-001
describe('Follow-Up Notification System', () => {
  test('should send notification for due follow-ups', async () => {
    // Setup: Follow-up due today
    // Expected: Notification created
    // Expected: Correct notification content
    // Expected: User receives browser notification
  });

  test('should send overdue notifications', async () => {
    // Setup: Follow-up overdue by 2 days
    // Expected: Overdue notification sent
    // Expected: Escalated priority indicated
  });

  test('should not spam notifications', async () => {
    // Expected: One notification per follow-up per day max
    // Expected: Notification deduplication works
  });
});
```

---

### **6. UI COMPONENT TESTS (Component Tests)**

#### 6.1 FollowUpTemplateManager Component
**Priority: 🟡 IMPORTANT**

```typescript
// Test Case: FUI-CMP-001
describe('FollowUpTemplateManager Component', () => {
  test('should render template list correctly', async () => {
    // Setup: Mock templates data
    // Expected: Templates displayed in list
    // Expected: Template names visible
    // Expected: Active/inactive status shown
  });

  test('should create new template via form', async () => {
    // Action: Fill template form and submit
    // Expected: Template created via API call
    // Expected: Form validation works
    // Expected: Success message shown
  });

  test('should edit existing template', async () => {
    // Action: Click edit, modify template, save
    // Expected: Template updated via API
    // Expected: Changes reflected in UI
  });

  test('should delete template with confirmation', async () => {
    // Action: Click delete, confirm
    // Expected: Confirmation dialog shown
    // Expected: Template deleted via API
    // Expected: Template removed from list
  });
});
```

#### 6.2 FollowUpScheduler Component
**Priority: 🟡 IMPORTANT**

```typescript  
// Test Case: FUI-CMP-002
describe('FollowUpScheduler Component', () => {
  test('should schedule follow-up with template selection', async () => {
    // Setup: Mock application and templates
    // Action: Select template, set date, schedule
    // Expected: Follow-up scheduled via API
    // Expected: Calendar event created
    // Expected: Success feedback
  });

  test('should validate scheduling form', async () => {
    // Expected: Require template selection
    // Expected: Require future date
    // Expected: Show validation errors
  });
});
```

#### 6.3 FollowUps Page (3 Tabs)
**Priority: 🟡 IMPORTANT**

```typescript
// Test Case: FUI-PAG-001  
describe('FollowUps Page', () => {
  test('should render planned follow-ups tab correctly', async () => {
    // Setup: Mock scheduled follow-ups
    // Expected: Follow-ups listed with correct data
    // Expected: Status badges accurate
    // Expected: Action buttons functional
  });

  test('should render sent follow-ups tab with response tracking', async () => {
    // Setup: Mock follow-up history
    // Expected: Sent follow-ups displayed
    // Expected: Response status shown correctly
    // Expected: "Mark as responded" button works
  });

  test('should render analytics tab with charts', async () => {
    // Setup: Mock analytics data
    // Expected: Charts render without errors
    // Expected: Data visualized correctly
    // Expected: Responsive design works
  });
});
```

---

### **7. END-TO-END USER FLOW TESTS (DEFINE ONLY - DON'T IMPLEMENT)**
**Note: E2E tests defined for future implementation with Playwright**

#### 7.1 Complete Follow-Up Lifecycle
**Priority: 🔴 CRITICAL**

**Test Scenario: Complete Follow-Up User Journey**
```
User Story: Als Freelancer möchte ich einen Follow-up von Template bis Response durchführen

Test Steps:
1. Navigate to Settings → Follow-up Tab
2. Create new template: "Interview Follow-up" with placeholders
3. Navigate to Applications
4. Create new application: "React Developer" at "Tech GmbH"
5. Change application status to "application_sent" 
6. Navigate to Follow-ups → Planned tab
7. Verify auto-scheduled follow-up appears
8. Click "Open Email" → verify mailto link with personalized content
9. Click "Mark as Sent" → verify follow-up moves to Sent tab
10. Navigate to Follow-ups → Sent tab
11. Click "Mark as Responded" → verify response status
12. Navigate to Follow-ups → Analytics tab
13. Verify analytics show 100% response rate

Expected Outcomes:
- Auto-scheduling works on status change
- Template personalization correct in email
- Response tracking updates analytics
- Calendar events created (verify in calendar view)
- All data persists across page refreshes
```

#### 7.2 Template Management Workflow
**Priority: 🟡 IMPORTANT**

**Test Scenario: Template CRUD Operations**
```
User Story: Als Freelancer möchte ich Follow-up Templates verwalten

Test Steps:
1. Navigate to Settings → Follow-up Tab
2. Create template with all placeholders: {project_name}, {contact_person}, {company_name}
3. Edit template → change trigger days from 7 to 3
4. Activate/Deactivate template toggle
5. Create second template for different status trigger
6. Delete unused template
7. Verify templates are used in follow-up scheduling

Expected Outcomes:
- All CRUD operations work smoothly
- Template validation prevents empty submissions  
- Active/inactive templates affect auto-scheduling
- Deleted templates don't appear in scheduling options
```

#### 7.3 Response Tracking & Analytics Workflow  
**Priority: 🟡 IMPORTANT**

**Test Scenario: Response Tracking Impact on Analytics**
```
User Story: Als Freelancer möchte ich Follow-up Erfolg messen

Test Steps:
1. Create 5 different follow-ups (different templates/timing)
2. Mark 3 as sent, 2 as not sent yet
3. Mark 2 of the sent ones as "received response" 
4. Navigate to Analytics tab
5. Verify response rate shows 66.7% (2/3 sent)
6. Verify template performance comparison
7. Verify timing analysis shows correct data
8. Change response dates → verify average response time changes

Expected Outcomes:
- Analytics update in real-time after response tracking
- Charts render correctly with real data
- Template and timing comparisons accurate
- Response time calculations correct
```

#### 7.4 Auto-Scheduling Integration Test
**Priority: 🔴 CRITICAL**

**Test Scenario: Status-Triggered Auto-Scheduling**
```
User Story: Als Freelancer möchte ich dass Follow-ups automatisch geplant werden

Test Steps:
1. Create active template for "application_sent" trigger (7 days)
2. Create active template for "interview_completed" trigger (3 days)
3. Create application in "not_applied" status
4. Change status to "application_sent"
5. Verify follow-up scheduled for +7 days in Follow-ups
6. Verify calendar event created
7. Change application status to "interview_completed"  
8. Verify second follow-up scheduled for +3 days
9. Verify no duplicates created

Expected Outcomes:
- Correct templates selected based on status
- Correct timing applied (trigger_days)
- Calendar integration creates events
- No duplicate follow-ups on repeated status changes
- Inactive templates ignored
```

---

### **8. DATA INTEGRITY & SECURITY TESTS**

#### 8.1 Row Level Security (RLS) Tests
**Priority: 🔴 CRITICAL**

```typescript
// Test Case: FUS-SEC-001
describe('Row Level Security', () => {
  test('should enforce user data isolation', async () => {
    // Setup: User A and User B with separate data
    // Expected: User A cannot see User B's templates
    // Expected: User A cannot see User B's follow-ups  
    // Expected: User A cannot see User B's history
  });

  test('should prevent unauthorized data modifications', async () => {
    // Expected: User A cannot modify User B's data
    // Expected: Proper 403/401 error responses
  });
});
```

#### 8.2 Foreign Key Constraint Tests
**Priority: 🔴 CRITICAL**

```typescript
// Test Case: FUS-SEC-002
describe('Database Constraint Enforcement', () => {
  test('should enforce foreign key relationships', async () => {
    // Expected: Cannot create follow-up for non-existent application
    // Expected: Cannot create follow-up with non-existent template
    // Expected: Cascade deletes work correctly
  });
});
```

---

### **9. PERFORMANCE TESTS**

#### 9.1 Large Dataset Performance
**Priority: 🟢 NICE-TO-HAVE**

```typescript
// Test Case: FUP-PRF-001
describe('Performance with Large Datasets', () => {
  test('should handle 1000+ follow-ups efficiently', async () => {
    // Setup: Generate 1000 follow-ups
    // Expected: Page loads in < 3 seconds
    // Expected: Analytics calculations complete in < 5 seconds
    // Expected: UI remains responsive
  });
});
```

---

## 🏗️ Test Setup Requirements

### **Vitest Testing Infrastructure (Priority Setup)**
```bash
# Install core testing dependencies  
npm install --save-dev vitest @vitejs/plugin-react
npm install --save-dev @testing-library/react @testing-library/jest-dom
npm install --save-dev @testing-library/user-event @testing-library/react-hooks
npm install --save-dev jsdom happy-dom

# Mock utilities
npm install --save-dev @vitest/ui
```

### **Vitest Configuration Setup**
```javascript
// vitest.config.ts
import { defineConfig } from 'vitest/config'
import react from '@vitejs/plugin-react'
import path from 'path'

export default defineConfig({
  plugins: [react()],
  test: {
    globals: true,
    environment: 'jsdom',
    setupFiles: './src/test/setup.ts',
  },
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src'),
    },
  },
})
```

### **Test Setup & Mocking Strategy**
- **Mock Supabase:** Use vi.mock() for all database calls
- **Mock TanStack Query:** Test hooks in isolation
- **Mock Date/Time:** Consistent timestamps in tests
- **Mock Toast:** Verify user feedback
- **Real Data Structures:** Use actual TypeScript types

### **Mock Data Requirements**
```typescript
// Test fixtures needed
const mockTemplate = {
  name: 'Test Template',
  subject: 'Follow-up: {project_name}',
  body: 'Hallo {contact_person}...',
  trigger_days: 7,
  status_trigger: 'application_sent'
};

const mockApplication = {
  project_name: 'React Developer',
  company_name: 'Tech GmbH', 
  contact_person: 'Max Mustermann',
  status: 'application_sent'
};
```

---

## ✅ Success Criteria

### **Critical Tests (Must Pass):**
- ✅ All CRUD operations work correctly
- ✅ Template personalization 100% accurate
- ✅ Auto-scheduling triggers correctly
- ✅ Calendar integration functional
- ✅ Response tracking accurate
- ✅ RLS security enforced
- ✅ Analytics calculations correct

### **Test Coverage Targets:**
- **Database Layer:** 90%+ coverage
- **Service Layer:** 85%+ coverage  
- **Component Layer:** 75%+ coverage
- **Integration Tests:** Key user flows covered
- **E2E Tests:** Happy path + error scenarios

### **Performance Criteria:**
- Page load times < 3 seconds
- API responses < 1 second
- Analytics calculations < 5 seconds
- No memory leaks in long-running tests

---

## 🚀 Implementation Priority (Vitest Focus)

### **Phase 1 (Week 1): Vitest Setup + Critical Unit Tests**
1. ✅ **Setup Vitest infrastructure** (vitest.config.ts, test setup)
2. ✅ **Service Layer Tests** (FollowUpService personalization + analytics)
3. ✅ **Hook Tests** (useFollowUpTemplates, useFollowUpSchedule, useFollowUpHistory)
4. ✅ **Mock Strategy** (Supabase, TanStack Query, Toast)

### **Phase 2 (Week 2): Component Tests**  
5. **Component Tests** (FollowUpTemplateManager, FollowUpAnalytics)
6. **Page Tests** (FollowUps page with 3 tabs)
7. **Integration Tests** (Calendar + Auto-scheduling with mocks)

### **Phase 3 (Future): E2E Definition Only**
8. **E2E Test Scenarios** (defined but not implemented)
9. **Performance Test Definitions** (for later Playwright implementation)

---

### **Immediate Vitest Focus:**
**Total Priority Test Cases: ~45 Vitest tests**
**Implementation Time: 2 weeks (Vitest only)**  
**Coverage Target: 80%+ for critical paths**

### **E2E Tests:**
**Status: DEFINED ONLY** (4 comprehensive test scenarios)
**Future Implementation:** With Playwright when ready