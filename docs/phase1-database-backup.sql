-- PHASE 1: <PERSON><PERSON><PERSON><PERSON><PERSON> BACKUP - CRITICAL SAFETY FIRST
-- Created: 2025-08-02
-- Purpose: Create complete backup of all critical tables before restructuring
-- 
-- IMPORTANT: Execute this script in Supabase SQL Editor BEFORE any other changes!

-- ================================
-- BACKUP ALL CRITICAL TABLES
-- ================================

-- Backup freelance_projects (will become project_applications)
CREATE TABLE freelance_projects_backup_20250802 AS 
SELECT * FROM freelance_projects;

-- Backup active_projects (will become projects)
CREATE TABLE active_projects_backup_20250802 AS 
SELECT * FROM active_projects;

-- Backup time_entries (linked to active_projects)
CREATE TABLE time_entries_backup_20250802 AS 
SELECT * FROM time_entries;

-- Backup project_activities (activity log)
CREATE TABLE project_activities_backup_20250802 AS 
SELECT * FROM project_activities;

-- Backup calendar_events (linked to projects)
CREATE TABLE calendar_events_backup_20250802 AS 
SELECT * FROM calendar_events;

-- Backup user_settings (user profile data)
CREATE TABLE user_settings_backup_20250802 AS 
SELECT * FROM user_settings;

-- Backup contacts (contact management)
CREATE TABLE contacts_backup_20250802 AS 
SELECT * FROM contacts;

-- Backup project_notes if exists (notes linked to active_projects)
CREATE TABLE project_notes_backup_20250802 AS 
SELECT * FROM project_notes;

-- ================================
-- VERIFICATION QUERIES
-- ================================

-- Run these queries after creating backups to verify data integrity:

-- Check row counts match original tables
SELECT 
  'freelance_projects' as table_name,
  (SELECT COUNT(*) FROM freelance_projects) as original_count,
  (SELECT COUNT(*) FROM freelance_projects_backup_20250802) as backup_count
UNION ALL
SELECT 
  'active_projects',
  (SELECT COUNT(*) FROM active_projects),
  (SELECT COUNT(*) FROM active_projects_backup_20250802)
UNION ALL
SELECT 
  'time_entries',
  (SELECT COUNT(*) FROM time_entries),
  (SELECT COUNT(*) FROM time_entries_backup_20250802)
UNION ALL
SELECT 
  'project_activities',
  (SELECT COUNT(*) FROM project_activities),
  (SELECT COUNT(*) FROM project_activities_backup_20250802)
UNION ALL
SELECT 
  'calendar_events',
  (SELECT COUNT(*) FROM calendar_events),
  (SELECT COUNT(*) FROM calendar_events_backup_20250802)
UNION ALL
SELECT 
  'user_settings',
  (SELECT COUNT(*) FROM user_settings),
  (SELECT COUNT(*) FROM user_settings_backup_20250802)
UNION ALL
SELECT 
  'contacts',
  (SELECT COUNT(*) FROM contacts),
  (SELECT COUNT(*) FROM contacts_backup_20250802)
UNION ALL
SELECT 
  'project_notes',
  (SELECT COUNT(*) FROM project_notes),
  (SELECT COUNT(*) FROM project_notes_backup_20250802);

-- ================================
-- BACKUP TABLE INFORMATION
-- ================================

-- List all backup tables created
SELECT 
  table_name,
  table_schema,
  table_type
FROM information_schema.tables 
WHERE table_name LIKE '%_backup_20250802' 
ORDER BY table_name;

-- ================================
-- ROLLBACK INSTRUCTIONS (if needed)
-- ================================

/*
IF MIGRATION FAILS AND ROLLBACK IS NEEDED:

1. Drop new tables (if any were created during migration):
   DROP TABLE IF EXISTS project_applications;
   DROP TABLE IF EXISTS projects;

2. Restore from backup tables:
   CREATE TABLE freelance_projects AS SELECT * FROM freelance_projects_backup_20250802;
   CREATE TABLE active_projects AS SELECT * FROM active_projects_backup_20250802;
   
3. Restore all other tables similarly if they were modified

4. Re-create any dropped constraints, indexes, and RLS policies
*/

-- ================================
-- SUCCESS CONFIRMATION
-- ================================

-- After running this script successfully, you should see:
-- 1. All backup tables created with "_backup_20250802" suffix
-- 2. Row counts matching between original and backup tables
-- 3. No errors in the SQL execution

COMMENT ON TABLE freelance_projects_backup_20250802 IS 'Backup created before app restructuring on 2025-08-02';
COMMENT ON TABLE active_projects_backup_20250802 IS 'Backup created before app restructuring on 2025-08-02';
COMMENT ON TABLE time_entries_backup_20250802 IS 'Backup created before app restructuring on 2025-08-02';
COMMENT ON TABLE project_activities_backup_20250802 IS 'Backup created before app restructuring on 2025-08-02';
COMMENT ON TABLE calendar_events_backup_20250802 IS 'Backup created before app restructuring on 2025-08-02';
COMMENT ON TABLE user_settings_backup_20250802 IS 'Backup created before app restructuring on 2025-08-02';
COMMENT ON TABLE contacts_backup_20250802 IS 'Backup created before app restructuring on 2025-08-02';
COMMENT ON TABLE project_notes_backup_20250802 IS 'Backup created before app restructuring on 2025-08-02';

-- Phase 1 Complete - Ready for Phase 2 Planning