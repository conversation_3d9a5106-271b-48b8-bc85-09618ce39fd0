# PHASE 2: Detailed Planning & Preparation

## 📋 **Task 2.1: Database Schema Design - COMPLETED**

### Table Name Mapping
```
ALTE TABELLEN → NEUE TABELLEN:
├── freelance_projects → project_applications
├── active_projects → projects
└── project_activities → bleibt project_activities (wird in Phase 4 aufgeteilt)
```

### Foreign Key Relationships
```
ALTE REFERENCES → NEUE REFERENCES:
├── projects.source_project_id → projects.source_application_id
├── calendar_events.reference_type: 'freelance_project' → 'application'
├── calendar_events.reference_type: 'active_project' → 'project'
└── time_entries.project_id → projects.id (ble<PERSON><PERSON> gle<PERSON>, neue <PERSON>)
```

### Migration Scripts Status
- ✅ `013_create_new_tables.sql` - Erstellt neue Tabellen
- ✅ `014_migrate_data_safely.sql` - Migriert Daten sicher
- ✅ `015_update_foreign_keys.sql` - Aktualisiert Foreign Keys
- ✅ `016_update_calendar_events.sql` - Aktualisiert Calendar Events
- ✅ `017_create_new_rls_policies.sql` - Erstellt RLS Policies
- 🟡 `018_drop_old_tables.sql` - Löscht alte Tabellen (NACH vollständiger Validierung)

---

## 📁 **Task 2.2: File Structure Planning**

### Page Components Renaming
```
ALTE DATEIEN → NEUE DATEIEN:
├── src/pages/Dashboard.tsx → src/pages/Applications.tsx
├── src/pages/ProjectDetails.tsx → src/pages/ApplicationDetails.tsx
├── src/pages/ActiveProjects.tsx → src/pages/Projects.tsx
├── src/pages/ActiveProjectDetails.tsx → src/pages/ProjectDetails.tsx
├── src/pages/ActiveProjectEdit.tsx → src/pages/ProjectEdit.tsx
├── src/pages/ActiveProjectCreate.tsx → src/pages/ProjectCreate.tsx
├── src/pages/ActiveProjectsReports.tsx → src/pages/ProjectsReports.tsx
└── src/pages/ActiveProjectsTimer.tsx → src/pages/ProjectsTimer.tsx
```

### Component Folders Restructuring
```
ALTE ORDNER → NEUE ORDNER:
├── src/components/dashboard/ → src/components/applications/
├── src/components/active-projects/ → src/components/projects/
├── src/components/projects/ → src/components/projects/form/ (Unterordner)
└── src/components/shared-dashboard/ (NEU für Combined Dashboard)
```

### Hooks & Services Renaming
```
ALTE HOOKS → NEUE HOOKS:
├── src/hooks/useFreelanceProjects.tsx → src/hooks/useApplications.tsx
├── src/hooks/useActiveProjects.tsx → src/hooks/useProjects.tsx
└── src/hooks/useProjectActivities.tsx → src/hooks/useApplicationActivities.tsx + useProjectActivities.tsx

ALTE TYPES → NEUE TYPES:
├── src/types/freelance.ts → src/types/applications.ts
├── src/types/active-projects.ts → src/types/projects.ts
└── Aktualisierung aller Interface-Namen
```

### Services Updates
```
SERVICES ZU AKTUALISIEREN:
├── src/services/exportService.ts - Beide Tabellen unterstützen
├── src/services/importService.ts - Neue Tabellennamen
├── src/services/timeTrackingExportService.ts - Projektreferenzen
└── src/services/dashboardService.ts (NEU für Combined Dashboard)
```

---

## 🛣️ **Task 2.3: Routing Strategy**

### Route Structure Changes
```
ALTE ROUTES → NEUE ROUTES:
├── / → /dashboard (NEU - Combined Overview)
├── /dashboard → /applications (Akquise & Bewerbungen)
├── /project/:id → /applications/:id
├── /create-project → /applications/create
├── /active-projects → /projects
├── /active-projects/:id → /projects/:id
├── /active-projects/:id/edit → /projects/:id/edit
├── /active-projects/create → /projects/create
├── /active-projects/reports → /projects/reports
└── /active-projects/timer → /projects/timer
```

### Navigation Updates (AppSidebar.tsx)
```
NEUE NAVIGATION STRUKTUR:
├── 🏠 Dashboard (Combined Overview)
├── 🎯 Akquise & Bewerbungen
│   ├── Übersicht
│   ├── Neue Bewerbung
│   └── Statistiken
├── 📁 Projekte
│   ├── Übersicht
│   ├── Neues Projekt
│   ├── Timer
│   └── Berichte
├── 📅 Kalender
└── ⚙️ Einstellungen
```

### Redirect Strategy
```
REDIRECT REGELN (für Backward Compatibility):
├── /dashboard → /applications (temporär)
├── /project/* → /applications/*
├── /active-projects → /projects
└── /active-projects/* → /projects/*
```

---

## 🔧 **Task 2.4: Migration Safety Planning**

### Rollback Mechanism
```
ROLLBACK STRATEGIE:
1. Backup-Tabellen bleiben verfügbar: *_backup_20250802
2. Alte Tabellen erst nach vollständiger Validierung löschen
3. Rollback-Scripts in jeder Migration verfügbar
4. Code-Rollback durch Git-Revert möglich
```

### Testing Strategy
```
TESTING APPROACH:
├── Unit Tests: Hooks und Services einzeln testen
├── Integration Tests: API-Calls und Database-Queries
├── UI Tests: Alle Pages und Navigation
└── End-to-End Tests: Komplette User-Workflows
```

### Data Validation Steps
```
VALIDIERUNG PRO MIGRATION:
├── Row Count Verification (Original vs. Migrated)
├── Foreign Key Integrity Checks
├── RLS Policy Testing
├── Sample Data Comparison
└── Performance Baseline Comparison
```

---

## 📊 **Task 2.5: Combined Dashboard Design**

### Dashboard Layout Structure
```
COMBINED DASHBOARD LAYOUT:
┌─────────────────────────────────────────────────┐
│ 📊 Dashboard - Gesamtübersicht                 │
├─────────────────────────────────────────────────┤
│ [Bewerbungen KPIs] [Projekte KPIs] [Zeit KPIs]  │
├─────────────────────────────────────────────────┤
│ 🎯 Akquise & Bewerbungen  │  📁 Aktive Projekte │
│ ─ Letzte Bewerbungen      │  ─ Laufende Projekte │
│ ─ Status Updates          │  ─ Timer Status      │
│ ─ [Alle anzeigen]         │  ─ [Alle anzeigen]   │
├─────────────────────────────────────────────────┤
│ 📈 Kombinierte Aktivitäts-Timeline             │
└─────────────────────────────────────────────────┘
```

### KPI Cards Design
```
KPI CARDS (2x2 Mobile, 4x1 Desktop):
├── 📊 Offene Bewerbungen (project_applications.status = 'not_applied')
├── 🎯 Aktive Projekte (projects.status = 'in_progress')
├── ⏱️ Heute gearbeitet (time_entries heute)
└── 💰 Monatsumsatz (projects.hourly_rate * hours)
```

---

## ✅ **Phase 2 Completion Checklist**

- [x] Database Schema Design abgeschlossen
- [x] Table Mapping definiert
- [x] Foreign Key Strategy erstellt
- [x] Migration Scripts geschrieben
- [x] File Structure Planning detailliert
- [x] Component Renaming Map erstellt
- [x] Routing Strategy definiert
- [x] Navigation Update geplant
- [x] Rollback Mechanism designed
- [x] Combined Dashboard Layout designed

**✅ PHASE 2 VOLLSTÄNDIG ABGESCHLOSSEN**
**➡️ BEREIT FÜR PHASE 3: DATABASE MIGRATION EXECUTION**

---

**Next Steps:**
1. Du führst Migration Scripts 013-017 in Supabase aus
2. Validierung dass alle Migrationen erfolgreich waren
3. Start von Phase 4: Backend Code Migration