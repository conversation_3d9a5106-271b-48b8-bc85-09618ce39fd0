# Follow-Up System E2E Test Plan

## 🎯 E2E Test Strategy

**Framework:** Playwright 
**Approach:** Real user workflows with minimal mocking
**Data:** Use test project data in Supabase
**Focus:** Critical user journeys and integrations

---

## 🔄 Critical E2E Test Flows

### **E2E-001: Complete Follow-Up Lifecycle**
**User Story:** Als Freelancer möchte ich Follow-Ups für meine Bewerbungen planen und verwalten

#### Test Steps:
1. **Setup:** Login and navigate to projects
2. **Create Template:** 
   - Go to Follow-Up Templates
   - Create new template with placeholders
   - Save template
3. **Schedule Follow-Up:**
   - Select project with status "application_sent"  
   - Choose template
   - Set custom date
   - Schedule follow-up
4. **Email Generation:**
   - Open scheduled follow-up
   - Verify personalized email content
   - Check mailto link works
5. **Mark as Sent:**
   - Mark follow-up as sent
   - Verify history entry created
6. **Response Tracking:**
   - Mark as "responded" 
   - Verify analytics update

#### Expected Results:
- Template created with correct placeholders
- Follow-up scheduled with calendar entry
- Email personalized correctly 
- History tracking works
- Analytics reflect response data

---

### **E2E-002: Auto-Scheduling Workflow**
**User Story:** Als User möchte ich dass Follow-Ups automatisch geplant werden

#### Test Steps:
1. **Template Setup:**
   - Create template with status trigger "application_sent"
   - Set trigger_days to 3
2. **Project Status Change:**
   - Change project status to "application_sent"
   - Verify auto-scheduling triggers
3. **Calendar Integration:**
   - Check calendar entry created
   - Verify correct date calculation  
4. **Notification:**
   - Check system notification appears

#### Expected Results:
- Follow-up auto-scheduled on status change
- Calendar entry with correct date
- User notification shows

---

### **E2E-003: Template Management Flow**
**User Story:** Als User möchte ich Follow-Up Templates verwalten

#### Test Steps:
1. **Create Multiple Templates:**
   - Create 3 different templates
   - Different triggers and timing
2. **Edit Template:**
   - Modify subject and body
   - Change trigger conditions
3. **Delete Template:**
   - Delete unused template
   - Verify removal from lists
4. **Template Usage:**
   - Use template in scheduling
   - Verify changes reflected

#### Expected Results:  
- All CRUD operations work
- Changes persist correctly
- UI updates immediately

---

### **E2E-004: Analytics and Reporting Flow**
**User Story:** Als User möchte ich Follow-Up Erfolg messen

#### Test Steps:
1. **Generate Test Data:**
   - Create 10 follow-ups with different templates
   - Mark 3 as responded, 2 as unanswered
2. **View Analytics:**
   - Navigate to Analytics tab
   - Check overview metrics
3. **Template Performance:**
   - Switch to Templates tab
   - Verify success rates
4. **Timing Analysis:**
   - Check timing tab
   - Verify response time data

#### Expected Results:
- Correct response rates shown
- Template performance accurate  
- Charts render properly
- Real-time updates work

---

### **E2E-005: Integration with Projects**
**User Story:** Als User möchte ich Follow-Ups nahtlos mit Projekten verwenden

#### Test Steps:
1. **Project Context:**
   - Open project details
   - Schedule follow-up from project
2. **Status Dependencies:**
   - Change project status
   - Verify follow-up recommendations
3. **Project Data Usage:**
   - Check email personalization
   - Verify all project fields used
4. **Timeline Integration:**
   - View project timeline
   - Check follow-up activities shown

#### Expected Results:
- Follow-ups contextual to projects
- Seamless status-based scheduling
- Complete project data usage
- Timeline shows follow-up history

---

## 🔧 E2E Test Configuration

### Test Environment Setup:
```typescript
// e2e/followup.setup.ts
export const testUser = {
  email: '<EMAIL>',
  password: 'TestPassword123!'
}

export const testProjects = [
  {
    project_name: 'E2E Test React Dev',
    company_name: 'Test Tech Corp', 
    contact_person: 'Max Testmann',
    status: 'application_sent'
  }
]
```

### Page Objects:
```typescript
// e2e/pages/FollowUpPage.ts
export class FollowUpPage {
  async scheduleFollowUp(templateName: string, date: string) {
    await this.page.click('[data-testid="schedule-followup-btn"]')
    await this.page.selectOption('[data-testid="template-select"]', templateName)
    await this.page.fill('[data-testid="schedule-date"]', date)
    await this.page.click('[data-testid="schedule-confirm"]')
  }

  async markAsSent(followUpId: string) {
    await this.page.click(`[data-testid="followup-${followUpId}-actions"]`)
    await this.page.click('[data-testid="mark-sent"]')
  }
}
```

### Test Data Management:
```typescript
// e2e/helpers/testData.ts
export class TestDataManager {
  async createTestTemplate() {
    // Create template via UI or API
  }

  async cleanupTestData() {
    // Remove test data after tests
  }
}
```

---

## 🎯 E2E Success Criteria

### Performance:
- Page loads < 2 seconds
- Email generation < 500ms
- Analytics render < 1 second

### Reliability:
- 95% test pass rate
- No flaky tests
- Consistent cross-browser

### Coverage:
- All critical user journeys
- Main integration points
- Error scenarios

### Data Integrity:
- All database operations succeed
- No data corruption
- Proper cleanup

---

## 🚀 Implementation Priority

### Phase 1: Core Flows (Week 1)
- E2E-001: Complete lifecycle
- E2E-002: Auto-scheduling  

### Phase 2: Advanced Features (Week 2)
- E2E-003: Template management
- E2E-004: Analytics

### Phase 3: Integration (Week 3)
- E2E-005: Project integration
- Cross-browser testing
- Performance validation

---

## 📝 Test Execution

### Local Development:
```bash
npm run e2e:followup          # Run all follow-up E2E tests
npm run e2e:followup:headed   # Run with browser visible
npm run e2e:followup:debug    # Debug mode
```

### CI/CD Pipeline:
- Run on every PR to main
- Run nightly for full regression
- Generate HTML reports
- Screenshot on failures

This E2E test plan ensures comprehensive testing of the follow-up system's critical user journeys and integrations.