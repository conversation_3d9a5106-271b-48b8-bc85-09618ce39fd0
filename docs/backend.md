# Backend Documentation (Supabase)

## Overview

The backend is based entirely on **Supabase** and provides Authentication, Database, Storage, and Edge Functions as a Service.

## Supabase Configuration

### Project Details
- **Project ID**: `wkmzfqjnlwaogqnkbfgw`
- **URL**: `https://wkmzfqjnlwaogqnkbfgw.supabase.co`
- **Region**: Automatically configured

### Secrets
- `GEMINI_API_KEY`: For AI text generation
- `SUPABASE_URL`: Project URL
- `SUPABASE_ANON_KEY`: Public API Key
- `SUPABASE_SERVICE_ROLE_KEY`: Admin API Key
- `SUPABASE_DB_URL`: Direct database connection

## Database Schema

### Table: `freelance_projects`

```sql
CREATE TABLE freelance_projects (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL,
  project_name TEXT NOT NULL,
  company_name TEXT NOT NULL,
  project_description TEXT,
  required_skills TEXT[],
  budget_range TEXT,
  status TEXT NOT NULL DEFAULT 'not_applied',
  application_text TEXT,
  application_date DATE DEFAULT CURRENT_DATE,
  project_start_date DATE,
  project_end_date DATE,
  contact_person TEXT,
  contact_email TEXT,
  contact_phone TEXT,
  listing_url TEXT,
  source TEXT,
  work_location_type TEXT,
  work_location_notes TEXT,
  remote_percentage INTEGER,
  notes TEXT,
  created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT now()
);
```

**Status Values**: `not_applied`, `recommended`, `recruiter_contacted`, `application_sent`, `inquiry_received`, `interview_scheduled`, `interview_completed`, `offer_received`, `rejected`, `project_completed`

### Table: `user_settings`

```sql
CREATE TABLE user_settings (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL UNIQUE,
  full_name TEXT,
  professional_email TEXT,
  phone TEXT,
  address TEXT,
  website TEXT,
  hourly_rate_eur INTEGER,
  availability_start_date DATE,
  availability_end_date DATE,
  availability_hours_per_week INTEGER DEFAULT 40,
  availability_notes TEXT,
  cv_pdf_url TEXT,
  profile_picture_url TEXT,
  created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT now()
);
```

### Table: `project_activities`
```sql
CREATE TABLE project_activities (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  project_id UUID NOT NULL REFERENCES freelance_projects(id) ON DELETE CASCADE,
  user_id UUID NOT NULL,
  activity_type TEXT NOT NULL,
  description TEXT NOT NULL,
  notes TEXT,
  notes_date DATE,
  created_at TIMESTAMPTZ NOT NULL DEFAULT now()
);
```

### Table: `calendar_events`
```sql
CREATE TABLE calendar_events (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL,
  project_id UUID REFERENCES freelance_projects(id) ON DELETE CASCADE,
  title TEXT NOT NULL,
  description TEXT,
  start_date DATE NOT NULL,
  start_time TIME,
  end_date DATE,
  end_time TIME,
  all_day BOOLEAN DEFAULT false,
  event_type TEXT DEFAULT 'manual',
  color TEXT DEFAULT '#3b82f6',
  location TEXT,
  completed BOOLEAN DEFAULT false,
  reminder_enabled BOOLEAN DEFAULT false,
  reminder_minutes_before INTEGER,
  created_automatically BOOLEAN DEFAULT false,
  source_status TEXT,
  created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT now()
);
```

## Row Level Security (RLS)

### Freelance Projects Policies
```sql
-- Users can only view their own projects
CREATE POLICY "Users can view their own projects" 
ON freelance_projects FOR SELECT 
USING (auth.uid() = user_id);

-- Users can only create their own projects
CREATE POLICY "Users can create their own projects" 
ON freelance_projects FOR INSERT 
WITH CHECK (auth.uid() = user_id);

-- Users can only update their own projects
CREATE POLICY "Users can update their own projects" 
ON freelance_projects FOR UPDATE 
USING (auth.uid() = user_id);

-- Users can only delete their own projects
CREATE POLICY "Users can delete their own projects" 
ON freelance_projects FOR DELETE 
USING (auth.uid() = user_id);
```

### User Settings Policies
```sql
-- Similar RLS policies for user_settings
-- Users can only manage their own settings
```

## Storage

### Bucket: `cv-uploads`
- **Purpose**: CV/Resume files
- **Public**: No (private)
- **Allowed Types**: PDF files
- **Max Size**: Configured for typical CV sizes

### Bucket: `profile-pictures`
- **Purpose**: User profile pictures
- **Public**: Yes (publicly readable)
- **Allowed Types**: JPG, PNG, WebP
- **Max Size**: 5MB per file

### Storage Policies
```sql
-- Users can upload their own CVs
CREATE POLICY "Users can upload their own CV" 
ON storage.objects FOR INSERT 
WITH CHECK (
  bucket_id = 'cv-uploads' 
  AND auth.uid()::text = (storage.foldername(name))[1]
);

-- Users can upload their own profile pictures
CREATE POLICY "Users can upload their own profile pictures" 
ON storage.objects FOR INSERT 
WITH CHECK (
  bucket_id = 'profile-pictures' 
  AND auth.uid()::text = (storage.foldername(name))[1]
);
```

## Edge Functions

### 1. `analyze-project`
**Path**: `/supabase/functions/analyze-project/index.ts`

**Purpose**: Analyzes project descriptions and extracts structured data

**Input**:
```typescript
{
  projectDescription: string;
  listingUrl?: string;
}
```

**Output**:
```typescript
{
  extractedData: {
    company_name: string;
    project_name: string;
    required_skills: string[];
    budget_range?: string;
    work_location_type?: string;
    contact_person?: string;
    contact_email?: string;
    // ... additional fields
  }
}
```

**Technology**: Gemini AI API for Natural Language Processing

### 2. `generate-application`
**Path**: `/supabase/functions/generate-application/index.ts`

**Purpose**: Generates personalized application texts based on project and user data

**Input**:
```typescript
{
  projectId: string;
  userSettings: UserSettings;
  cvContent?: string; // Optional: CV content for better personalization
}
```

**Output**:
```typescript
{
  applicationText: string;
}
```

**Features**:
- CV analysis and skill matching
- Personalized addressing
- Conversion-optimized structure
- Freelancer-specific best practices

## Authentication

### Auth Provider
- **Type**: Email/Password Authentication
- **Sessions**: Persistent with localStorage
- **Auto-Refresh**: Enabled

### User Management
- Automatic user ID generation
- Session management via Supabase Auth
- No custom user tables (uses auth.users)

## Database Triggers

### Update Timestamps
```sql
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = now();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger for freelance_projects
CREATE TRIGGER update_freelance_projects_updated_at
  BEFORE UPDATE ON freelance_projects
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

-- Trigger for user_settings
CREATE TRIGGER update_user_settings_updated_at
  BEFORE UPDATE ON user_settings
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

-- Trigger for calendar_events
CREATE TRIGGER update_calendar_events_updated_at
  BEFORE UPDATE ON calendar_events
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();
```

## API Patterns

### Frontend Integration
```typescript
// Supabase client import
import { supabase } from "@/integrations/supabase/client";

// Type-safe database queries
import type { Database } from "@/integrations/supabase/types";

// Example query
const { data, error } = await supabase
  .from('freelance_projects')
  .select('*')
  .eq('user_id', userId);
```

### Error Handling
- Consistent error responses
- Client-side error boundaries
- Toast notifications for user feedback

## Monitoring & Logs

### Available Logs
- **Database Logs**: SQL queries and performance
- **Auth Logs**: Login/logout events
- **Edge Function Logs**: Function execution and errors
- **Storage Logs**: File upload events

### Debugging
- Supabase Dashboard for live monitoring
- Edge Function logs for AI integration
- Database performance metrics

## Deployment

### Migrations
- Automatic schema migration via Supabase CLI
- Versioned SQL migration files
- Rollback functionality

### Environment
- Production-ready configuration
- Automatic SSL/TLS
- CDN for static assets
- Global edge network

## Language Requirements
- **ALL code comments MUST be in English**
- **ALL documentation MUST be in English**  
- SQL comments and function documentation in English