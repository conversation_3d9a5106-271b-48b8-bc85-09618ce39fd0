# Google Authentication Setup Guide

## Übersicht

Diese Anleitung erklärt, wie Google OAuth in Ihrer Supabase-App konfiguriert wird.

## 1. Google Cloud Console Setup

### 1.1 Neues Projekt erstellen (falls nicht vorhanden)
1. <PERSON><PERSON><PERSON> Sie zur [Google Cloud Console](https://console.cloud.google.com/)
2. Erstellen Sie ein neues Projekt oder wählen Sie ein existierendes

### 1.2 OAuth Consent Screen konfigurieren
1. Navigieren Sie zu **APIs & Services** > **OAuth consent screen**
2. Wählen Sie **External** (für öffentliche Apps) oder **Internal** (nur für Ihre Organisation)
3. Füllen Sie die erforderlichen Felder aus:
   - **App name:** `Lanzr` (Das erscheint im Google Login!)
   - **User support email:** Ihre E-Mail-Adresse
   - **Application home page:** `https://lanzr.de` (Ihre echte Domain!)
   - **Application privacy policy link:** `https://lanzr.de/privacy` (falls vorhanden)
   - **Application terms of service link:** `https://lanzr.de/terms` (falls vorhanden)
   - **Authorized domains:** `lanzr.de` (Ihre Domain ohne https://)
   - **Developer contact information:** Ihre E-Mail-Adresse

**Wichtig:** Das Feld "Authorized domains" sorgt dafür, dass "lanzr.de" statt "supabase.co" angezeigt wird!

### 1.3 OAuth 2.0 Client erstellen
1. Gehen Sie zu **APIs & Services** > **Credentials**
2. Klicken Sie auf **+ CREATE CREDENTIALS** > **OAuth client ID**
3. Wählen Sie **Web application** als Application type
4. Konfigurieren Sie die URIs:

   **Authorized JavaScript origins:**
   ```
   http://localhost:5173
   https://your-domain.com
   ```

   **Authorized redirect URIs:**
   ```
   https://wkmzfqjnlwaogqnkbfgw.supabase.co/auth/v1/callback
   http://localhost:5173/
   http://localhost:8082/
   https://your-domain.com/
   ```

5. Klicken Sie **Create** und notieren Sie sich:
   - **Client ID** (beginnt mit `.apps.googleusercontent.com`)
   - **Client Secret**

## 2. Supabase Configuration

### 2.1 Google Provider aktivieren
1. Gehen Sie zu Ihrem [Supabase Dashboard](https://supabase.com/dashboard)
2. Wählen Sie Ihr Projekt aus
3. Navigieren Sie zu **Authentication** > **Providers**
4. Aktivieren Sie **Google**
5. Geben Sie Ihre Google OAuth Credentials ein:
   - **Client ID**: Ihre Google Client ID
   - **Client Secret**: Ihr Google Client Secret

### 2.2 Redirect URLs konfigurieren
1. Gehen Sie zu **Authentication** > **URL Configuration**
2. Fügen Sie folgende URLs zu **Redirect URLs** hinzu:
   ```
   http://localhost:5173/
   http://localhost:8082/
   https://your-domain.com/
   ```

## 3. Code Integration

Die Google Auth-Integration ist bereits implementiert:

### 3.1 Komponenten
- **`GoogleAuthButton.tsx`**: Wiederverwendbarer Google Sign-in Button
- **`AuthCallback.tsx`**: Callback-Handler für OAuth Redirect
- **`Auth.tsx`**: Aktualisiert mit Google Auth Optionen

### 3.2 Routing
Die `/auth/callback` Route ist in `App.tsx` konfiguriert.

### 3.3 Flow
1. Benutzer klickt auf "Mit Google anmelden"
2. Weiterleitung zu Google OAuth
3. Nach Genehmigung: Redirect zu `/auth/callback`
4. Callback-Handler tauscht Code gegen Session aus
5. Benutzer wird zur Hauptseite weitergeleitet

## 4. Security Best Practices

### 4.1 PKCE Flow
Die Implementation nutzt PKCE (Proof Key for Code Exchange) für erhöhte Sicherheit.

### 4.2 Scopes
Standardmäßig werden folgende Scopes angefragt:
- `https://www.googleapis.com/auth/userinfo.email`
- `https://www.googleapis.com/auth/userinfo.profile`

### 4.3 Nonce
Für erhöhte Sicherheit gegen Replay-Attacks wird ein Nonce verwendet.

## 5. Testing

### 5.1 Development Testing
1. Starten Sie den Development Server: `npm run dev`
2. Gehen Sie zu `http://localhost:5173/auth`
3. Testen Sie die Google Auth Buttons

### 5.2 Production Testing
1. Deployen Sie Ihre App
2. Stellen Sie sicher, dass die Production-URLs in Google Cloud Console und Supabase konfiguriert sind
3. Testen Sie den kompletten Flow

## 6. Troubleshooting

### 6.1 Häufige Probleme

**Error: "redirect_uri_mismatch"**
- Überprüfen Sie, dass alle Redirect URIs korrekt in der Google Cloud Console konfiguriert sind
- Stellen Sie sicher, dass die URLs exakt übereinstimmen (kein trailing slash)

**Error: "OAuth provider not enabled"**
- Überprüfen Sie, dass Google Auth in Supabase aktiviert ist
- Kontrollieren Sie Client ID und Secret in Supabase

**Error: "Invalid client"**
- Überprüfen Sie die Google Client ID in Supabase
- Stellen Sie sicher, dass das OAuth Consent Screen korrekt konfiguriert ist

### 6.2 Debug-Tipps
1. Überprüfen Sie Browser-Konsole für Fehler
2. Kontrollieren Sie Netzwerk-Tab für HTTP-Fehler  
3. Supabase Dashboard zeigt Auth-Logs
4. Google Cloud Console zeigt OAuth-Metriken

## 7. User Experience

### 7.1 Neue Benutzer
- Beim ersten Google Sign-in wird automatisch ein Supabase-Account erstellt
- User Settings werden mit Google-Profildaten befüllt

### 7.2 Existierende Benutzer
- Benutzer können Google Auth zu existierenden Email/Password-Accounts hinzufügen
- Identity Linking wird automatisch gehandhabt

### 7.3 Benutzerinformationen
Über Google erhaltene Daten:
- E-Mail-Adresse
- Vollständiger Name
- Profilbild (optional implementierbar)

## 8. Maintenance

### 8.1 Token Refresh
- Access Tokens werden automatisch von Supabase verwaltet
- Refresh Tokens werden für längere Sessions verwendet

### 8.2 Monitoring
- Überwachen Sie Auth-Metriken in Supabase Dashboard
- Google Cloud Console zeigt OAuth-Nutzung

---

**Wichtiger Hinweis**: Bewahren Sie Ihre Google Client Secret sicher auf und teilen Sie sie niemals öffentlich!