# Migration Guide: Organization-Based to User-Based Architecture

This guide walks you through migrating from the organization-based multi-tenant structure to a simplified user-based architecture.

## Overview

The migration removes the organization layer entirely, implementing direct user-to-project relationships. This simplifies the system architecture while maintaining all core functionality.

### What's Changed

**Removed:**
- `organizations` table
- `organization_members` table
- Organization context and providers
- Organization selection UI
- Organization creation during registration
- Complex multi-tenant RLS policies

**Simplified:**
- Direct user-to-project relationships
- User-based RLS policies
- Streamlined registration process
- Cleaner UI without organization references

## Migration Steps

### 1. Database Migration

#### Step 1.1: Backup Your Data (IMPORTANT!)
```bash
# Create a backup of your current database
pg_dump your_database_name > backup_before_migration.sql
```

#### Step 1.2: Complete Database Cleanup
```bash
# Execute the complete cleanup commands (removes ALL existing data and tables)
psql -d your_database -f database_complete_cleanup.sql
```

#### Step 1.3: Apply Comprehensive Schema
```bash
# Apply the new comprehensive user-based schema
psql -d your_database -f supabase/migrations/001_comprehensive_schema.sql
```

#### Step 1.4: Verify Schema Setup
```sql
-- Check that all required tables exist
SELECT tablename FROM pg_tables WHERE schemaname = 'public'
ORDER BY tablename;
-- Should show: freelance_projects, user_settings

-- Check that freelance_projects has correct structure (no organization_id)
SELECT column_name, data_type FROM information_schema.columns
WHERE table_name = 'freelance_projects'
ORDER BY column_name;

-- Check that user_settings has comprehensive fields
SELECT column_name, data_type FROM information_schema.columns
WHERE table_name = 'user_settings'
ORDER BY column_name;

-- Verify RLS policies are active
SELECT tablename, policyname FROM pg_policies
WHERE schemaname = 'public'
ORDER BY tablename, policyname;

-- Check storage bucket exists
SELECT name, public FROM storage.buckets WHERE id = 'cv-uploads';
```

### 2. Application Code Changes

All application code changes have been completed automatically:

- ✅ Removed `OrganizationContext` and `OrganizationProvider`
- ✅ Updated `useFreelanceProjects` hook for user-based queries
- ✅ Simplified authentication flow
- ✅ Updated UI components to remove organization references
- ✅ Removed organization selector from sidebar

### 3. Testing the Migration

After completing the migration:

1. **Test User Registration:**
   - Register a new user
   - Verify no organization creation is required
   - Confirm user can immediately access the dashboard

2. **Test Project Management:**
   - Create a new project
   - Edit existing projects
   - Verify all projects are scoped to the current user

3. **Test Data Isolation:**
   - Create a second user account
   - Verify users can only see their own projects
   - Confirm no cross-user data leakage

## Key Benefits

### Simplified Architecture
- No complex organization management
- Direct user-project relationships
- Cleaner, more maintainable code

### Better User Experience
- Faster registration (no organization setup)
- Immediate access to project management
- No organization context switching

### Improved Security
- Simpler RLS policies are easier to audit
- Reduced attack surface
- Clear data ownership model

## Database Deployment Options

### Fresh Database Setup (Recommended)
```bash
# Apply the comprehensive schema to an empty database
psql -d your_database -f supabase/migrations/001_comprehensive_schema.sql
```

### Migrating from Existing Schema
```bash
# 1. Complete cleanup of existing data and tables
psql -d your_database -f database_complete_cleanup.sql

# 2. Apply the new comprehensive schema
psql -d your_database -f supabase/migrations/001_comprehensive_schema.sql
```

## Rollback Plan

If you need to rollback the migration:

1. **Restore Database:**
   ```bash
   # Drop current database and restore backup
   dropdb your_database_name
   createdb your_database_name
   psql -d your_database_name < backup_before_migration.sql
   ```

2. **Revert Code Changes:**
   ```bash
   # Use git to revert to the previous commit
   git revert HEAD
   ```

## Troubleshooting

### Common Issues

**Issue: Projects not showing after migration**
- Check that the user_id in freelance_projects matches your auth.users id
- Verify RLS policies are correctly applied

**Issue: Authentication errors**
- Clear browser localStorage and cookies
- Check that user_settings table is properly configured

**Issue: Database connection errors**
- Verify all organization-related functions are removed
- Check that new RLS policies are active

### Support

If you encounter issues during migration:

1. Check the database logs for specific error messages
2. Verify all migration steps were completed in order
3. Ensure your backup is available for rollback if needed

## Post-Migration Cleanup

After successful migration and testing:

1. **Remove Migration Files (Optional):**
   - Keep `002_user_based_schema.sql` for future deployments
   - Archive `001_complete_schema.sql` and `database_cleanup_commands.sql`

2. **Update Documentation:**
   - The database schema documentation has been updated
   - Review and update any custom documentation

3. **Monitor Performance:**
   - The simplified structure should improve query performance
   - Monitor for any unexpected behavior

## Conclusion

This migration simplifies your Lanzr application by removing the over-engineered organization layer while maintaining all core functionality. The result is a cleaner, more maintainable codebase that's easier to understand and extend.

The user-based architecture provides a better foundation for future development while ensuring strong data isolation and security.
