import { test, expect } from '@playwright/test'

// Mock data for E2E tests
const mockProjects = [
  {
    id: '1',
    project_name: 'React Developer Position',
    company_name: 'Tech Corp GmbH',
    status: 'application_sent',
    contact_person: '<PERSON>',
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-01T00:00:00Z',
    user_id: 'user-1'
  }
]

const mockTemplates = [
  {
    id: '1',
    name: 'Standard Follow-up nach 7 Tagen',
    subject: 'Nachfrage zu meiner Bewerbung - {project_name}',
    body: 'Sehr geehrte/r {contact_person},\n\nvor einer Woche habe ich mich auf die Position {project_name} bei {company_name} beworben. Gerne möchte ich nachfragen, ob Sie bereits Gelegenheit hatten, meine Unterlagen zu prüfen.\n\nIch freue mich auf Ihre Rückmeldung.\n\nBeste Grüße,\n{user_name}',
    trigger_days: 7,
    status_trigger: 'application_sent',
    is_active: true,
    user_id: 'user-1',
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-01T00:00:00Z'
  }
]

// Comprehensive E2E Tests für Follow-Up System
test.describe('Follow-Up System Complete E2E Flow', () => {
  
  test.beforeEach(async ({ page }) => {
    // Mock Supabase responses for consistent testing
    await page.route('**/rest/v1/freelance_projects*', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify(mockProjects)
      })
    })

    await page.route('**/rest/v1/follow_up_templates*', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify(mockTemplates)
      })
    })

    await page.route('**/rest/v1/follow_up_schedule*', async route => {
      if (route.request().method() === 'POST') {
        // Mock successful schedule creation
        const scheduleItem = {
          id: 'schedule-1',
          project_id: '1',
          template_id: '1',
          scheduled_date: '2024-01-08T09:00:00Z',
          status: 'scheduled',
          created_at: '2024-01-01T00:00:00Z',
          user_id: 'user-1'
        }
        await route.fulfill({
          status: 201,
          contentType: 'application/json',
          body: JSON.stringify([scheduleItem])
        })
      } else {
        // Mock GET requests
        await route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify([])
        })
      }
    })

    await page.route('**/rest/v1/calendar_events*', async route => {
      if (route.request().method() === 'POST') {
        // Mock calendar event creation
        const calendarEvent = {
          id: 'calendar-1',
          title: 'Follow-up: React Developer Position',
          start_date: '2024-01-08T09:00:00Z',
          end_date: '2024-01-08T09:30:00Z',
          type: 'followup',
          project_id: '1',
          user_id: 'user-1',
          created_at: '2024-01-01T00:00:00Z'
        }
        await route.fulfill({
          status: 201,
          contentType: 'application/json',
          body: JSON.stringify([calendarEvent])
        })
      } else {
        await route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify([])
        })
      }
    })

    await page.route('**/rest/v1/follow_up_history*', async route => {
      if (route.request().method() === 'POST') {
        // Mock history creation
        const historyItem = {
          id: 'history-1',
          project_id: '1',
          template_id: '1',
          scheduled_date: '2024-01-08T09:00:00Z',
          sent_date: '2024-01-08T09:00:00Z',
          status: 'sent',
          response_status: 'pending',
          user_id: 'user-1',
          created_at: '2024-01-08T09:00:00Z'
        }
        await route.fulfill({
          status: 201,
          contentType: 'application/json',
          body: JSON.stringify([historyItem])
        })
      } else {
        await route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify([])
        })
      }
    })

    // Mock auth session
    await page.route('**/auth/v1/user*', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          id: 'user-1',
          email: '<EMAIL>',
          user_metadata: { full_name: 'Test User' }
        })
      })
    })

    // Navigate to app
    await page.goto('/')
    
    // Wait for app to load
    await page.waitForSelector('[data-testid="app-sidebar"], .sidebar, nav', { timeout: 10000 })
  })

  test('E2E-FLOW-001: Kompletter Follow-Up Erstellungs-Flow', async ({ page }) => {
    // 1. Navigiere zu Follow-Ups Seite
    await page.click('text=Follow-Ups')
    await expect(page.locator('text=Follow-up Management')).toBeVisible({ timeout: 10000 })
    
    // 2. Prüfe dass Templates geladen sind
    await expect(page.locator('text=Standard Follow-up nach 7 Tagen')).toBeVisible({ timeout: 5000 })
    
    // 3. Klicke auf Follow-up planen Button
    const scheduleButton = page.locator('button:has-text("Follow-up planen")')
    await expect(scheduleButton.first()).toBeVisible()
    await scheduleButton.first().click()
    
    // 4. Dialog sollte sich öffnen
    await expect(page.locator('text=Follow-up für "React Developer Position" planen')).toBeVisible()
    
    // 5. Template auswählen
    const templateCard = page.locator('.template-card, [data-testid="template-card"]').first()
    if (await templateCard.count() > 0) {
      await templateCard.click()
    }
    
    // 6. Datum und Zeit setzen (falls verfügbar)
    const dateInput = page.locator('input[type="date"]')
    if (await dateInput.count() > 0) {
      await dateInput.fill('2024-01-08')
    }
    
    const timeInput = page.locator('input[type="time"]')
    if (await timeInput.count() > 0) {
      await timeInput.fill('09:00')
    }
    
    // 7. Follow-up planen
    const planButton = page.locator('button:has-text("Follow-up planen"), button:has-text("Planen")')
    if (await planButton.count() > 0) {
      await planButton.first().click()
    }
    
    // 8. Erfolg validieren
    await expect(page.locator('text=Follow-up wurde erfolgreich geplant, text=Successfully scheduled')).toBeVisible({ timeout: 5000 })
  })

  test('E2E-FLOW-002: Follow-Up Status zu "Gesendet" ändern', async ({ page }) => {
    // Setup: Erstelle zuerst einen geplanten Follow-Up
    await page.goto('/follow-ups')
    await expect(page.locator('text=Follow-up Management')).toBeVisible()
    
    // Mock einen geplanten Follow-Up
    await page.route('**/rest/v1/follow_up_schedule*', async route => {
      const scheduledItems = [{
        id: 'schedule-1',
        project_id: '1',
        template_id: '1',
        scheduled_date: '2024-01-08T09:00:00Z',
        status: 'scheduled',
        project: mockProjects[0],
        template: mockTemplates[0],
        created_at: '2024-01-01T00:00:00Z'
      }]
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify(scheduledItems)
      })
    })
    
    await page.reload()
    
    // 1. Geplant Tab sollte Follow-Up anzeigen
    await page.click('text=Geplant')
    await expect(page.locator('text=React Developer Position')).toBeVisible({ timeout: 5000 })
    
    // 2. Als Gesendet markieren
    const sendButton = page.locator('button:has-text("Als gesendet markieren"), button:has-text("Gesendet")')
    if (await sendButton.count() > 0) {
      await sendButton.first().click()
    }
    
    // 3. Bestätigung (falls Dialog erscheint)
    const confirmButton = page.locator('button:has-text("Bestätigen"), button:has-text("Confirm")')
    if (await confirmButton.count() > 0) {
      await confirmButton.click()
    }
    
    // 4. Wechsel zu "Gesendet" Tab
    await page.click('text=Gesendet')
    
    // 5. Follow-Up sollte jetzt in Gesendet erscheinen
    await expect(page.locator('text=React Developer Position')).toBeVisible({ timeout: 5000 })
  })

  test('E2E-FLOW-003: Response Tracking - Als beantwortet markieren', async ({ page }) => {
    // Setup: Mock einen gesendeten Follow-Up
    await page.route('**/rest/v1/follow_up_history*', async route => {
      const sentItems = [{
        id: 'history-1',
        project_id: '1',
        template_id: '1',
        scheduled_date: '2024-01-08T09:00:00Z',
        sent_date: '2024-01-08T09:00:00Z',
        status: 'sent',
        response_status: 'pending',
        project: mockProjects[0],
        template: mockTemplates[0],
        user_id: 'user-1',
        created_at: '2024-01-08T09:00:00Z'
      }]
      
      if (route.request().method() === 'PATCH') {
        // Mock response status update
        sentItems[0].response_status = 'responded'
        await route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify(sentItems)
        })
      } else {
        await route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify(sentItems)
        })
      }
    })
    
    await page.goto('/follow-ups')
    await expect(page.locator('text=Follow-up Management')).toBeVisible()
    
    // 1. Gesendet Tab öffnen
    await page.click('text=Gesendet')
    await expect(page.locator('text=React Developer Position')).toBeVisible({ timeout: 5000 })
    
    // 2. Als beantwortet markieren
    const respondedButton = page.locator('button:has-text("Als beantwortet markieren"), button:has-text("Beantwortet")')
    if (await respondedButton.count() > 0) {
      await respondedButton.first().click()
    }
    
    // 3. Bestätigung
    const confirmButton = page.locator('button:has-text("Bestätigen"), button:has-text("Confirm")')
    if (await confirmButton.count() > 0) {
      await confirmButton.click()
    }
    
    // 4. Status sollte sich zu "Beantwortet" ändern
    await expect(page.locator('text=Beantwortet, [data-status="responded"]')).toBeVisible({ timeout: 5000 })
  })

  test('E2E-FLOW-004: Analytics Dashboard mit Follow-Up Daten', async ({ page }) => {
    // Setup: Mock Analytics Daten
    await page.route('**/rest/v1/follow_up_history*', async route => {
      const analyticsData = [
        {
          id: 'history-1',
          project_id: '1',
          response_status: 'responded',
          sent_date: '2024-01-01T00:00:00Z',
          template: mockTemplates[0]
        },
        {
          id: 'history-2',
          project_id: '1',
          response_status: 'unanswered',
          sent_date: '2024-01-05T00:00:00Z',
          template: mockTemplates[0]
        }
      ]
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify(analyticsData)
      })
    })
    
    await page.goto('/follow-ups')
    await expect(page.locator('text=Follow-up Management')).toBeVisible()
    
    // 1. Analytics Tab öffnen
    await page.click('text=Analytics')
    
    // 2. Analytics Komponenten sollten laden
    await expect(page.locator('text=Follow-up Analytics, text=Statistiken')).toBeVisible({ timeout: 5000 })
    
    // 3. Prüfe dass Statistiken angezeigt werden
    const statsCards = page.locator('.stats-card, [data-testid="stats-card"], .metric')
    await expect(statsCards.first()).toBeVisible({ timeout: 5000 })
    
    // 4. Response Rate sollte berechnet werden
    await expect(page.locator('text=Response Rate, text=Antwortrate')).toBeVisible()
    
    // 5. Charts sollten gerendert werden (falls vorhanden)
    const charts = page.locator('.recharts-wrapper, canvas, svg')
    if (await charts.count() > 0) {
      await expect(charts.first()).toBeVisible()
    }
  })

  test('E2E-FLOW-005: Template Management und Personalisierung', async ({ page }) => {
    await page.goto('/follow-ups')
    await expect(page.locator('text=Follow-up Management')).toBeVisible()
    
    // 1. Template Manager öffnen (falls verfügbar)
    const templateManager = page.locator('button:has-text("Template Manager"), text=Template Management')
    if (await templateManager.count() > 0) {
      await templateManager.first().click()
      
      // 2. Template Liste sollte sichtbar sein
      await expect(page.locator('text=Standard Follow-up nach 7 Tagen')).toBeVisible()
      
      // 3. Template Preview mit personalisierten Daten
      const templateCard = page.locator('.template-preview, [data-testid="template-preview"]')
      if (await templateCard.count() > 0) {
        await expect(templateCard.first()).toBeVisible()
        
        // 4. Personalisierte Inhalte prüfen
        await expect(page.locator('text=React Developer Position')).toBeVisible()
        await expect(page.locator('text=Tech Corp GmbH')).toBeVisible()
        await expect(page.locator('text=Max Mustermann')).toBeVisible()
      }
    }
  })

  test('E2E-FLOW-006: Kalender Integration Validierung', async ({ page }) => {
    // Mock Calendar Events
    await page.route('**/rest/v1/calendar_events*', async route => {
      if (route.request().method() === 'GET') {
        const calendarEvents = [{
          id: 'calendar-1',
          title: 'Follow-up: React Developer Position',
          start_date: '2024-01-08T09:00:00Z',
          end_date: '2024-01-08T09:30:00Z',
          type: 'followup',
          project_id: '1',
          description: 'Automatisch erstellter Follow-up Termin'
        }]
        await route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify(calendarEvents)
        })
      } else {
        await route.fulfill({
          status: 201,
          contentType: 'application/json',
          body: JSON.stringify([])
        })
      }
    })
    
    // 1. Navigiere zum Kalender
    await page.goto('/calendar')
    
    // 2. Warte auf Kalender
    await expect(page.locator('.calendar, [data-testid="calendar"]')).toBeVisible({ timeout: 10000 })
    
    // 3. Follow-up Event sollte im Kalender erscheinen
    await expect(page.locator('text=Follow-up: React Developer Position')).toBeVisible({ timeout: 5000 })
    
    // 4. Event Details prüfen
    const followupEvent = page.locator('[data-event-type="followup"], .followup-event')
    if (await followupEvent.count() > 0) {
      await expect(followupEvent.first()).toBeVisible()
    }
  })

  test('E2E-FLOW-007: Error Handling und Edge Cases', async ({ page }) => {
    // Test Error Scenarios
    
    // 1. API Fehler simulieren
    await page.route('**/rest/v1/follow_up_schedule*', async route => {
      if (route.request().method() === 'POST') {
        await route.fulfill({
          status: 500,
          contentType: 'application/json',
          body: JSON.stringify({ error: 'Internal Server Error' })
        })
      }
    })
    
    await page.goto('/follow-ups')
    await expect(page.locator('text=Follow-up Management')).toBeVisible()
    
    // 2. Versuche Follow-up zu erstellen
    const scheduleButton = page.locator('button:has-text("Follow-up planen")')
    if (await scheduleButton.count() > 0) {
      await scheduleButton.first().click()
      
      // 3. Error sollte behandelt werden
      await expect(page.locator('text=Fehler, text=Error')).toBeVisible({ timeout: 5000 })
    }
    
    // 4. App sollte stabil bleiben
    await expect(page.locator('text=Follow-up Management')).toBeVisible()
  })

  test('E2E-FLOW-008: Kompletter End-to-End User Journey', async ({ page }) => {
    // Simuliere kompletten Workflow von Anfang bis Ende
    
    await page.goto('/follow-ups')
    await expect(page.locator('text=Follow-up Management')).toBeVisible()
    
    // 1. Template Management
    await page.click('text=Analytics') // Switch to different tab first
    await page.click('text=Geplant')   // Back to main tab
    
    // 2. Follow-up planen
    const scheduleButton = page.locator('button:has-text("Follow-up planen")')
    if (await scheduleButton.count() > 0) {
      await scheduleButton.first().click()
      await page.waitForTimeout(1000)
      
      // Close dialog if opened
      const closeButton = page.locator('button:has-text("Schließen"), button[aria-label="Close"], .dialog-close')
      if (await closeButton.count() > 0) {
        await closeButton.first().click()
      }
    }
    
    // 3. Tab Navigation testen
    const tabs = ['Geplant', 'Gesendet', 'Analytics']
    for (const tab of tabs) {
      await page.click(`text=${tab}`)
      await page.waitForTimeout(500)
      
      // Validiere dass Tab Content lädt
      await expect(page.locator('body')).toBeVisible()
    }
    
    // 4. Zurück zum Dashboard
    await page.click('text=Dashboard')
    await expect(page.locator('text=Dashboard, .dashboard').first()).toBeVisible({ timeout: 5000 })
    
    // 5. Workflow erfolgreich abgeschlossen
    console.log('✅ Kompletter E2E Workflow erfolgreich durchlaufen')
  })
})