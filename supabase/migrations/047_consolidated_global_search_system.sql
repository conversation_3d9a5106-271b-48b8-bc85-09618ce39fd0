-- ============================================================================
-- CONSOLIDATED GLOBAL SEARCH SYSTEM - Final Migration
-- ============================================================================
-- Migration: 047_consolidated_global_search_system.sql
-- Purpose: Complete global search system with all fixes and improvements
-- Consolidates: 042, 043, 044, 045, 046 migration scripts
-- Features:
-- - PostgreSQL Full-Text Search with German language support
-- - Fixed UNION ORDER BY with subquery wrapping
-- - Correct data types (double precision for ts_rank)
-- - Partial word matching with prefix queries
-- - Phone and email search for contacts
-- - ILIKE fallback for comprehensive partial matching
-- - Performance optimized with proper indexes
-- - Security with RLS enforcement
-- ============================================================================

-- Enable PostgreSQL Full-Text Search extension (if not already enabled)
CREATE EXTENSION IF NOT EXISTS "unaccent";

-- ============================================================================
-- DROP EXISTING FUNCTIONS AND INDEXES
-- ============================================================================

-- Drop existing functions to avoid conflicts
DROP FUNCTION IF EXISTS global_search(text, uuid, int);
DROP FUNCTION IF EXISTS quick_search(text, uuid, int);

-- Drop old search indexes
DROP INDEX IF EXISTS idx_applications_search_fts;
DROP INDEX IF EXISTS idx_projects_search_fts;
DROP INDEX IF EXISTS idx_contacts_search_fts;
DROP INDEX IF EXISTS idx_calendar_events_search_fts;
DROP INDEX IF EXISTS idx_follow_up_templates_search_fts;
DROP INDEX IF EXISTS idx_project_notes_search_fts;
DROP INDEX IF EXISTS idx_time_entries_search_fts;

-- ============================================================================
-- CREATE OPTIMIZED SEARCH INDEXES
-- ============================================================================

-- Applications search index
CREATE INDEX IF NOT EXISTS idx_applications_search_fts ON project_applications 
USING gin(to_tsvector('german', 
  COALESCE(project_name, '') || ' ' || 
  COALESCE(company_name, '') || ' ' || 
  COALESCE(project_description, '') || ' ' ||
  COALESCE(notes, '') || ' ' ||
  COALESCE(application_text, '')
));

-- Projects search index
CREATE INDEX IF NOT EXISTS idx_projects_search_fts ON projects 
USING gin(to_tsvector('german', 
  COALESCE(title, '') || ' ' || 
  COALESCE(client_name, '') || ' ' || 
  COALESCE(description, '')
));

-- Contacts search index (including phone and email)
CREATE INDEX IF NOT EXISTS idx_contacts_search_fts ON contacts 
USING gin(to_tsvector('german', 
  COALESCE(name, '') || ' ' || 
  COALESCE(email, '') || ' ' || 
  COALESCE(phone, '') || ' ' ||
  COALESCE(company, '') || ' ' ||
  COALESCE(notes, '')
));

-- Calendar events search index
CREATE INDEX IF NOT EXISTS idx_calendar_events_search_fts ON calendar_events 
USING gin(to_tsvector('german', 
  COALESCE(title, '') || ' ' || 
  COALESCE(description, '')
));

-- Follow-up templates search index
CREATE INDEX IF NOT EXISTS idx_follow_up_templates_search_fts ON follow_up_templates 
USING gin(to_tsvector('german', 
  COALESCE(name, '') || ' ' || 
  COALESCE(subject, '') || ' ' ||
  COALESCE(body, '')
));

-- Conditional indexes for optional tables
DO $$
BEGIN
  -- Project notes search index (if table exists)
  IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'project_notes') THEN
    CREATE INDEX IF NOT EXISTS idx_project_notes_search_fts ON project_notes 
    USING gin(to_tsvector('german', 
      COALESCE(title, '') || ' ' || 
      COALESCE(content, '')
    ));
  END IF;

  -- Time entries search index (if table exists)
  IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'time_entries') THEN
    CREATE INDEX IF NOT EXISTS idx_time_entries_search_fts ON time_entries 
    USING gin(to_tsvector('german', COALESCE(description, '')));
  END IF;
END $$;

-- ============================================================================
-- COMPREHENSIVE GLOBAL SEARCH FUNCTION
-- ============================================================================

CREATE OR REPLACE FUNCTION global_search(
  search_query text, 
  user_uuid uuid,
  result_limit int DEFAULT 50
)
RETURNS TABLE(
  entity_type text,
  entity_id uuid,
  title text,
  subtitle text,
  description text,
  url_path text,
  rank double precision,
  highlight text,
  created_at timestamptz
) 
LANGUAGE plpgsql 
SECURITY DEFINER
AS $$
DECLARE
  query_tsquery tsquery;
  prefix_query_tsquery tsquery;
  clean_query text;
BEGIN
  -- Enhanced input sanitization to prevent SQL injection
  clean_query := trim(regexp_replace(search_query, '[^\w\s@\.\+\-]', '', 'g'));
  
  -- Additional security: reject queries with SQL keywords or suspicious patterns
  IF clean_query ~* '\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|UNION|SCRIPT)\b' 
     OR clean_query ~ '(--|\/\*|\*\/|;)' 
     OR length(clean_query) < 2 
     OR length(clean_query) > 100 THEN
    RETURN;
  END IF;
  
  -- Create both full word and prefix queries with safe parameterization
  BEGIN
    query_tsquery := plainto_tsquery('german', clean_query);
  EXCEPTION
    WHEN OTHERS THEN
      query_tsquery := NULL;
  END;
  
  -- Create prefix query with enhanced error handling
  IF length(clean_query) > 0 AND query_tsquery IS NOT NULL THEN
    BEGIN
      WITH safe_words AS (
        SELECT regexp_replace(word, '\W', '', 'g') AS clean_word
        FROM unnest(string_to_array(clean_query, ' ')) AS word
        WHERE length(trim(regexp_replace(word, '\W', '', 'g'))) >= 2
          AND NOT regexp_replace(word, '\W', '', 'g') ~* '\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|UNION|SCRIPT)\b'
      )
      SELECT to_tsquery('german', string_agg(clean_word || ':*', ' & '))
      INTO prefix_query_tsquery
      FROM safe_words
      WHERE clean_word IS NOT NULL AND length(clean_word) >= 2;
    EXCEPTION
      WHEN OTHERS THEN
        prefix_query_tsquery := NULL;
    END;
  END IF;
  
  -- Return if both queries are empty
  IF query_tsquery IS NULL AND prefix_query_tsquery IS NULL THEN
    RETURN;
  END IF;

  RETURN QUERY
  SELECT * FROM (
    -- Search Applications
    SELECT 
      'application'::text as entity_type,
      pa.id as entity_id,
      pa.project_name as title,
      pa.company_name as subtitle,
      COALESCE(pa.notes, LEFT(pa.project_description, 100)) as description,
      '/applications/' || pa.id::text as url_path,
      ts_rank(
        to_tsvector('german', 
          COALESCE(pa.project_name, '') || ' ' || 
          COALESCE(pa.company_name, '') || ' ' || 
          COALESCE(pa.project_description, '') || ' ' ||
          COALESCE(pa.notes, '') || ' ' ||
          COALESCE(pa.application_text, '')
        ), 
        COALESCE(query_tsquery, prefix_query_tsquery)
      ) as rank,
      ts_headline('german', 
        COALESCE(pa.project_name, '') || ' ' || COALESCE(pa.company_name, ''), 
        COALESCE(query_tsquery, prefix_query_tsquery),
        'MaxWords=10, MinWords=1, ShortWord=3, HighlightAll=false, MaxFragments=1'
      ) as highlight,
      pa.created_at
    FROM project_applications pa
    WHERE pa.user_id = user_uuid
      AND (
        (query_tsquery IS NOT NULL AND to_tsvector('german', 
          COALESCE(pa.project_name, '') || ' ' || 
          COALESCE(pa.company_name, '') || ' ' || 
          COALESCE(pa.project_description, '') || ' ' ||
          COALESCE(pa.notes, '') || ' ' ||
          COALESCE(pa.application_text, '')
        ) @@ query_tsquery)
        OR
        (prefix_query_tsquery IS NOT NULL AND to_tsvector('german', 
          COALESCE(pa.project_name, '') || ' ' || 
          COALESCE(pa.company_name, '') || ' ' || 
          COALESCE(pa.project_description, '') || ' ' ||
          COALESCE(pa.notes, '') || ' ' ||
          COALESCE(pa.application_text, '')
        ) @@ prefix_query_tsquery)
        OR
        -- Fallback ILIKE search for partial matches
        (pa.project_name ILIKE '%' || clean_query || '%' 
         OR pa.company_name ILIKE '%' || clean_query || '%'
         OR pa.project_description ILIKE '%' || clean_query || '%'
         OR pa.notes ILIKE '%' || clean_query || '%'
         OR pa.application_text ILIKE '%' || clean_query || '%')
      )
    
    UNION ALL
    
    -- Search Projects
    SELECT 
      'project'::text,
      p.id,
      p.title,
      p.client_name,
      COALESCE(LEFT(p.description, 100), '') as description,
      '/projects/' || p.id::text,
      ts_rank(
        to_tsvector('german', 
          COALESCE(p.title, '') || ' ' || 
          COALESCE(p.client_name, '') || ' ' || 
          COALESCE(p.description, '')
        ), 
        COALESCE(query_tsquery, prefix_query_tsquery)
      ) * 1.2 as rank, -- Boost projects slightly
      ts_headline('german', 
        COALESCE(p.title, '') || ' ' || COALESCE(p.client_name, ''), 
        COALESCE(query_tsquery, prefix_query_tsquery),
        'MaxWords=10, MinWords=1, ShortWord=3, HighlightAll=false, MaxFragments=1'
      ),
      p.created_at
    FROM projects p
    WHERE p.user_id = user_uuid
      AND (
        (query_tsquery IS NOT NULL AND to_tsvector('german', 
          COALESCE(p.title, '') || ' ' || 
          COALESCE(p.client_name, '') || ' ' || 
          COALESCE(p.description, '')
        ) @@ query_tsquery)
        OR
        (prefix_query_tsquery IS NOT NULL AND to_tsvector('german', 
          COALESCE(p.title, '') || ' ' || 
          COALESCE(p.client_name, '') || ' ' || 
          COALESCE(p.description, '')
        ) @@ prefix_query_tsquery)
        OR
        -- Fallback ILIKE search for partial matches
        (p.title ILIKE '%' || clean_query || '%' 
         OR p.client_name ILIKE '%' || clean_query || '%'
         OR p.description ILIKE '%' || clean_query || '%')
      )
          
    UNION ALL
    
    -- Search Contacts (including phone and email)
    SELECT 
      'contact'::text,
      c.id,
      COALESCE(c.name, c.email, 'Unnamed Contact') as title,
      COALESCE(c.company, c.email, c.phone, '') as subtitle,
      COALESCE(LEFT(c.notes, 100), c.phone, c.email, '') as description,
      '/contacts/' || c.id::text,
      ts_rank(
        to_tsvector('german', 
          COALESCE(c.name, '') || ' ' || 
          COALESCE(c.email, '') || ' ' || 
          COALESCE(c.phone, '') || ' ' ||
          COALESCE(c.company, '') || ' ' ||
          COALESCE(c.notes, '')
        ), 
        COALESCE(query_tsquery, prefix_query_tsquery)
      ),
      ts_headline('german', 
        COALESCE(c.name, '') || ' ' || COALESCE(c.company, '') || ' ' || COALESCE(c.email, '') || ' ' || COALESCE(c.phone, ''), 
        COALESCE(query_tsquery, prefix_query_tsquery),
        'MaxWords=10, MinWords=1, ShortWord=3, HighlightAll=false, MaxFragments=1'
      ),
      c.created_at
    FROM contacts c
    WHERE c.user_id = user_uuid
      AND (
        (query_tsquery IS NOT NULL AND to_tsvector('german', 
          COALESCE(c.name, '') || ' ' || 
          COALESCE(c.email, '') || ' ' || 
          COALESCE(c.phone, '') || ' ' ||
          COALESCE(c.company, '') || ' ' ||
          COALESCE(c.notes, '')
        ) @@ query_tsquery)
        OR
        (prefix_query_tsquery IS NOT NULL AND to_tsvector('german', 
          COALESCE(c.name, '') || ' ' || 
          COALESCE(c.email, '') || ' ' || 
          COALESCE(c.phone, '') || ' ' ||
          COALESCE(c.company, '') || ' ' ||
          COALESCE(c.notes, '')
        ) @@ prefix_query_tsquery)
        OR
        -- Fallback ILIKE search for partial matches including phone and email
        (c.name ILIKE '%' || clean_query || '%' 
         OR c.email ILIKE '%' || clean_query || '%'
         OR c.phone ILIKE '%' || clean_query || '%'
         OR c.company ILIKE '%' || clean_query || '%'
         OR c.notes ILIKE '%' || clean_query || '%')
      )
        
    UNION ALL
    
    -- Search Calendar Events
    SELECT 
      'calendar'::text,
      ce.id,
      ce.title,
      CASE 
        WHEN ce.all_day THEN 'Ganztägig am ' || to_char(ce.start_date, 'DD.MM.YYYY')
        ELSE to_char(ce.start_date, 'DD.MM.YYYY') || 
             CASE WHEN ce.start_time IS NOT NULL 
                  THEN ' um ' || ce.start_time 
                  ELSE '' 
             END
      END as subtitle,
      COALESCE(LEFT(ce.description, 100), '') as description,
      '/calendar?date=' || ce.start_date::text,
      ts_rank(
        to_tsvector('german', 
          COALESCE(ce.title, '') || ' ' || 
          COALESCE(ce.description, '')
        ), 
        COALESCE(query_tsquery, prefix_query_tsquery)
      ),
      ts_headline('german', 
        COALESCE(ce.title, '') || ' ' || COALESCE(ce.description, ''), 
        COALESCE(query_tsquery, prefix_query_tsquery),
        'MaxWords=10, MinWords=1, ShortWord=3, HighlightAll=false, MaxFragments=1'
      ),
      ce.created_at
    FROM calendar_events ce
    WHERE ce.user_id = user_uuid
      AND (
        (query_tsquery IS NOT NULL AND to_tsvector('german', 
          COALESCE(ce.title, '') || ' ' || 
          COALESCE(ce.description, '')
        ) @@ query_tsquery)
        OR
        (prefix_query_tsquery IS NOT NULL AND to_tsvector('german', 
          COALESCE(ce.title, '') || ' ' || 
          COALESCE(ce.description, '')
        ) @@ prefix_query_tsquery)
        OR
        -- Fallback ILIKE search for partial matches
        (ce.title ILIKE '%' || clean_query || '%' 
         OR ce.description ILIKE '%' || clean_query || '%')
      )
        
    UNION ALL
    
    -- Search Follow-up Templates
    SELECT 
      'template'::text,
      ft.id,
      ft.name,
      'Follow-up Template • ' || ft.trigger_days::text || ' Tage nach ' || ft.status_trigger,
      COALESCE(LEFT(ft.body, 100), '') as description,
      '/follow-ups?template=' || ft.id::text,
      ts_rank(
        to_tsvector('german', 
          COALESCE(ft.name, '') || ' ' || 
          COALESCE(ft.subject, '') || ' ' ||
          COALESCE(ft.body, '')
        ), 
        COALESCE(query_tsquery, prefix_query_tsquery)
      ),
      ts_headline('german', 
        COALESCE(ft.name, '') || ' ' || COALESCE(ft.subject, ''), 
        COALESCE(query_tsquery, prefix_query_tsquery),
        'MaxWords=10, MinWords=1, ShortWord=3, HighlightAll=false, MaxFragments=1'
      ),
      ft.created_at
    FROM follow_up_templates ft
    WHERE ft.user_id = user_uuid
      AND (
        (query_tsquery IS NOT NULL AND to_tsvector('german', 
          COALESCE(ft.name, '') || ' ' || 
          COALESCE(ft.subject, '') || ' ' ||
          COALESCE(ft.body, '')
        ) @@ query_tsquery)
        OR
        (prefix_query_tsquery IS NOT NULL AND to_tsvector('german', 
          COALESCE(ft.name, '') || ' ' || 
          COALESCE(ft.subject, '') || ' ' ||
          COALESCE(ft.body, '')
        ) @@ prefix_query_tsquery)
        OR
        -- Fallback ILIKE search for partial matches
        (ft.name ILIKE '%' || clean_query || '%' 
         OR ft.subject ILIKE '%' || clean_query || '%'
         OR ft.body ILIKE '%' || clean_query || '%')
      )
  ) subquery
  ORDER BY rank DESC, created_at DESC
  LIMIT result_limit;
END;
$$;

-- ============================================================================
-- OPTIMIZED QUICK SEARCH FUNCTION
-- ============================================================================

CREATE OR REPLACE FUNCTION quick_search(
  search_query text, 
  user_uuid uuid,
  result_limit int DEFAULT 10
)
RETURNS TABLE(
  entity_type text,
  entity_id uuid,
  title text,
  subtitle text,
  url_path text,
  rank double precision
) 
LANGUAGE plpgsql 
SECURITY DEFINER
AS $$
DECLARE
  query_tsquery tsquery;
  prefix_query_tsquery tsquery;
  clean_query text;
BEGIN
  -- Enhanced input sanitization to prevent SQL injection
  clean_query := trim(regexp_replace(search_query, '[^\w\s@\.\+\-]', '', 'g'));
  
  -- Additional security: reject queries with SQL keywords or suspicious patterns
  IF clean_query ~* '\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|UNION|SCRIPT)\b' 
     OR clean_query ~ '(--|\/\*|\*\/|;)' 
     OR length(clean_query) < 2 
     OR length(clean_query) > 100 THEN
    RETURN;
  END IF;
  
  -- Create both full word and prefix queries with safe parameterization
  BEGIN
    query_tsquery := plainto_tsquery('german', clean_query);
  EXCEPTION
    WHEN OTHERS THEN
      query_tsquery := NULL;
  END;
  
  -- Create prefix query with enhanced error handling
  IF length(clean_query) > 0 AND query_tsquery IS NOT NULL THEN
    BEGIN
      WITH safe_words AS (
        SELECT regexp_replace(word, '\W', '', 'g') AS clean_word
        FROM unnest(string_to_array(clean_query, ' ')) AS word
        WHERE length(trim(regexp_replace(word, '\W', '', 'g'))) >= 2
          AND NOT regexp_replace(word, '\W', '', 'g') ~* '\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|UNION|SCRIPT)\b'
      )
      SELECT to_tsquery('german', string_agg(clean_word || ':*', ' & '))
      INTO prefix_query_tsquery
      FROM safe_words
      WHERE clean_word IS NOT NULL AND length(clean_word) >= 2;
    EXCEPTION
      WHEN OTHERS THEN
        prefix_query_tsquery := NULL;
    END;
  END IF;
  
  -- Return if both queries are empty
  IF query_tsquery IS NULL AND prefix_query_tsquery IS NULL THEN
    RETURN;
  END IF;

  RETURN QUERY
  SELECT * FROM (
    -- Quick search Applications
    SELECT 
      'application'::text,
      pa.id,
      pa.project_name,
      pa.company_name,
      '/applications/' || pa.id::text,
      CASE 
        WHEN query_tsquery IS NOT NULL AND to_tsvector('german', COALESCE(pa.project_name, '') || ' ' || COALESCE(pa.company_name, '')) @@ query_tsquery 
        THEN ts_rank(to_tsvector('german', COALESCE(pa.project_name, '') || ' ' || COALESCE(pa.company_name, '')), query_tsquery)
        WHEN prefix_query_tsquery IS NOT NULL AND to_tsvector('german', COALESCE(pa.project_name, '') || ' ' || COALESCE(pa.company_name, '')) @@ prefix_query_tsquery 
        THEN ts_rank(to_tsvector('german', COALESCE(pa.project_name, '') || ' ' || COALESCE(pa.company_name, '')), prefix_query_tsquery) * 0.8
        ELSE 0.5  -- ILIKE fallback gets lower rank
      END
    FROM project_applications pa
    WHERE pa.user_id = user_uuid
      AND (
        (query_tsquery IS NOT NULL AND to_tsvector('german', COALESCE(pa.project_name, '') || ' ' || COALESCE(pa.company_name, '')) @@ query_tsquery)
        OR
        (prefix_query_tsquery IS NOT NULL AND to_tsvector('german', COALESCE(pa.project_name, '') || ' ' || COALESCE(pa.company_name, '')) @@ prefix_query_tsquery)
        OR
        (pa.project_name ILIKE '%' || clean_query || '%' OR pa.company_name ILIKE '%' || clean_query || '%')
      )
    
    UNION ALL
    
    -- Quick search Projects
    SELECT 
      'project'::text,
      p.id,
      p.title,
      p.client_name,
      '/projects/' || p.id::text,
      CASE 
        WHEN query_tsquery IS NOT NULL AND to_tsvector('german', COALESCE(p.title, '') || ' ' || COALESCE(p.client_name, '')) @@ query_tsquery 
        THEN ts_rank(to_tsvector('german', COALESCE(p.title, '') || ' ' || COALESCE(p.client_name, '')), query_tsquery) * 1.2
        WHEN prefix_query_tsquery IS NOT NULL AND to_tsvector('german', COALESCE(p.title, '') || ' ' || COALESCE(p.client_name, '')) @@ prefix_query_tsquery 
        THEN ts_rank(to_tsvector('german', COALESCE(p.title, '') || ' ' || COALESCE(p.client_name, '')), prefix_query_tsquery) * 1.0
        ELSE 0.6  -- ILIKE fallback gets lower rank, but projects still boosted
      END
    FROM projects p
    WHERE p.user_id = user_uuid
      AND (
        (query_tsquery IS NOT NULL AND to_tsvector('german', COALESCE(p.title, '') || ' ' || COALESCE(p.client_name, '')) @@ query_tsquery)
        OR
        (prefix_query_tsquery IS NOT NULL AND to_tsvector('german', COALESCE(p.title, '') || ' ' || COALESCE(p.client_name, '')) @@ prefix_query_tsquery)
        OR
        (p.title ILIKE '%' || clean_query || '%' OR p.client_name ILIKE '%' || clean_query || '%')
      )
          
    UNION ALL
    
    -- Quick search Contacts (including phone and email)
    SELECT 
      'contact'::text,
      c.id,
      COALESCE(c.name, c.email, 'Unnamed Contact'),
      COALESCE(c.company, c.email, c.phone, ''),
      '/contacts/' || c.id::text,
      CASE 
        WHEN query_tsquery IS NOT NULL AND to_tsvector('german', COALESCE(c.name, '') || ' ' || COALESCE(c.company, '') || ' ' || COALESCE(c.email, '') || ' ' || COALESCE(c.phone, '')) @@ query_tsquery 
        THEN ts_rank(to_tsvector('german', COALESCE(c.name, '') || ' ' || COALESCE(c.company, '') || ' ' || COALESCE(c.email, '') || ' ' || COALESCE(c.phone, '')), query_tsquery)
        WHEN prefix_query_tsquery IS NOT NULL AND to_tsvector('german', COALESCE(c.name, '') || ' ' || COALESCE(c.company, '') || ' ' || COALESCE(c.email, '') || ' ' || COALESCE(c.phone, '')) @@ prefix_query_tsquery 
        THEN ts_rank(to_tsvector('german', COALESCE(c.name, '') || ' ' || COALESCE(c.company, '') || ' ' || COALESCE(c.email, '') || ' ' || COALESCE(c.phone, '')), prefix_query_tsquery) * 0.8
        ELSE 0.5  -- ILIKE fallback gets lower rank
      END
    FROM contacts c
    WHERE c.user_id = user_uuid
      AND (
        (query_tsquery IS NOT NULL AND to_tsvector('german', COALESCE(c.name, '') || ' ' || COALESCE(c.company, '') || ' ' || COALESCE(c.email, '') || ' ' || COALESCE(c.phone, '')) @@ query_tsquery)
        OR
        (prefix_query_tsquery IS NOT NULL AND to_tsvector('german', COALESCE(c.name, '') || ' ' || COALESCE(c.company, '') || ' ' || COALESCE(c.email, '') || ' ' || COALESCE(c.phone, '')) @@ prefix_query_tsquery)
        OR
        -- ILIKE search including phone and email
        (c.name ILIKE '%' || clean_query || '%' 
         OR c.company ILIKE '%' || clean_query || '%'
         OR c.email ILIKE '%' || clean_query || '%'
         OR c.phone ILIKE '%' || clean_query || '%')
      )
  ) subquery
  ORDER BY rank DESC
  LIMIT result_limit;
END;
$$;

-- ============================================================================
-- GRANT PERMISSIONS
-- ============================================================================

-- Grant execute permissions to authenticated users
GRANT EXECUTE ON FUNCTION global_search(text, uuid, int) TO authenticated;
GRANT EXECUTE ON FUNCTION quick_search(text, uuid, int) TO authenticated;

-- ============================================================================
-- HELPFUL COMMENTS
-- ============================================================================

COMMENT ON FUNCTION global_search IS 'Consolidated global search system with full-text search, partial matching, phone/email support, and German language optimization. Includes all fixes from migrations 042-046.';
COMMENT ON FUNCTION quick_search IS 'Optimized quick search for instant suggestions with partial matching and phone/email support. Performance-focused version of global search.';

-- Add migration completion comment
COMMENT ON EXTENSION "unaccent" IS 'Global search system migration 047 completed successfully. Consolidates migrations 042, 043, 044, 045, 046 with all fixes and improvements.';