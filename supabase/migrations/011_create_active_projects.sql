-- Migration: 011_create_active_projects.sql
-- Description: Create tables for Active Projects Management System
-- Date: 2025-07-31

-- Create active_projects table
CREATE TABLE active_projects (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid REFERENCES auth.users(id) NOT NULL,
  
  -- Optional reference to original freelance project
  source_project_id uuid REFERENCES freelance_projects(id),
  
  -- Basic Project Info
  title text NOT NULL,
  client_name text NOT NULL,
  description text,
  project_type text DEFAULT 'development', -- development, design, consulting, etc.
  
  -- Financial (basic for now)
  hourly_rate decimal(10,2),
  estimated_hours integer,
  
  -- Timeline
  start_date date,
  planned_end_date date,
  actual_end_date date,
  
  -- Status and Priority
  status text CHECK (status IN ('starting', 'in_progress', 'on_hold', 'completing', 'completed')) DEFAULT 'starting',
  priority text CHECK (priority IN ('low', 'medium', 'high', 'urgent')) DEFAULT 'medium',
  
  -- <PERSON>ada<PERSON>
  created_at timestamptz DEFAULT now() NOT NULL,
  updated_at timestamptz DEFAULT now() NOT NULL
);

-- Create time_entries table for robust time tracking
CREATE TABLE time_entries (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid REFERENCES auth.users(id) NOT NULL,
  project_id uuid REFERENCES active_projects(id) ON DELETE CASCADE NOT NULL,
  
  -- Time Data
  start_time timestamptz NOT NULL,
  end_time timestamptz, -- NULL for active/running timers
  duration_minutes integer, -- Calculated field, but can be manually set
  
  -- Categorization
  category text DEFAULT 'development' CHECK (category IN (
    'development', 'meetings', 'documentation', 'design', 
    'testing', 'deployment', 'communication', 'research', 'other'
  )),
  description text,
  
  -- Status and Meta
  is_running boolean DEFAULT false, -- For active timers
  billable boolean DEFAULT true,
  
  -- Metadata
  created_at timestamptz DEFAULT now() NOT NULL,
  updated_at timestamptz DEFAULT now() NOT NULL
);

-- Create project_notes table for documentation
CREATE TABLE project_notes (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid REFERENCES auth.users(id) NOT NULL,
  project_id uuid REFERENCES active_projects(id) ON DELETE CASCADE NOT NULL,
  
  -- Note Content
  title text,
  content text NOT NULL,
  note_type text DEFAULT 'general' CHECK (note_type IN (
    'general', 'meeting', 'decision', 'issue', 'idea', 'todo'
  )),
  
  -- Organization
  tags text[] DEFAULT '{}',
  is_pinned boolean DEFAULT false,
  
  -- Metadata
  created_at timestamptz DEFAULT now() NOT NULL,
  updated_at timestamptz DEFAULT now() NOT NULL
);

-- Add indexes for better performance
CREATE INDEX idx_active_projects_user_id ON active_projects(user_id);
CREATE INDEX idx_active_projects_status ON active_projects(user_id, status);
CREATE INDEX idx_active_projects_source ON active_projects(source_project_id) WHERE source_project_id IS NOT NULL;

CREATE INDEX idx_time_entries_user_id ON time_entries(user_id);
CREATE INDEX idx_time_entries_project_id ON time_entries(project_id);
CREATE INDEX idx_time_entries_running ON time_entries(user_id, is_running) WHERE is_running = true;
CREATE INDEX idx_time_entries_start_time ON time_entries(project_id, start_time);

CREATE INDEX idx_project_notes_user_id ON project_notes(user_id);
CREATE INDEX idx_project_notes_project_id ON project_notes(project_id);
CREATE INDEX idx_project_notes_pinned ON project_notes(project_id, is_pinned) WHERE is_pinned = true;

-- Grant permissions to anon and authenticated roles
GRANT ALL ON active_projects TO anon, authenticated;
GRANT ALL ON time_entries TO anon, authenticated;
GRANT ALL ON project_notes TO anon, authenticated;

-- Grant permissions on sequences
GRANT USAGE, SELECT ON ALL SEQUENCES IN SCHEMA public TO anon, authenticated;

-- Add RLS (Row Level Security) policies
ALTER TABLE active_projects ENABLE ROW LEVEL SECURITY;
ALTER TABLE time_entries ENABLE ROW LEVEL SECURITY;
ALTER TABLE project_notes ENABLE ROW LEVEL SECURITY;

-- RLS Policies (simplified, using FOR ALL)
CREATE POLICY "active_projects_all" ON active_projects 
  FOR ALL USING (auth.uid() = user_id);

CREATE POLICY "time_entries_all" ON time_entries 
  FOR ALL USING (auth.uid() = user_id);

CREATE POLICY "project_notes_all" ON project_notes 
  FOR ALL USING (auth.uid() = user_id);

-- Add updated_at trigger function if it doesn't exist
DO $$
BEGIN
  IF NOT EXISTS (SELECT 1 FROM pg_proc WHERE proname = 'update_updated_at_column') THEN
    CREATE OR REPLACE FUNCTION update_updated_at_column()
    RETURNS TRIGGER AS $trigger$
    BEGIN
      NEW.updated_at = now();
      RETURN NEW;  
    END;
    $trigger$ LANGUAGE plpgsql;
  END IF;
END
$$;

-- Add updated_at triggers
CREATE TRIGGER update_active_projects_updated_at
  BEFORE UPDATE ON active_projects
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_time_entries_updated_at
  BEFORE UPDATE ON time_entries
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_project_notes_updated_at
  BEFORE UPDATE ON project_notes
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Add constraint to ensure only one running timer per user
CREATE UNIQUE INDEX idx_one_running_timer_per_user 
  ON time_entries(user_id) 
  WHERE is_running = true;

-- Add constraint to ensure end_time > start_time when both are set
ALTER TABLE time_entries ADD CONSTRAINT check_end_time_after_start 
  CHECK (end_time IS NULL OR end_time > start_time);

-- Add constraint to ensure duration_minutes is positive
ALTER TABLE time_entries ADD CONSTRAINT check_positive_duration 
  CHECK (duration_minutes IS NULL OR duration_minutes > 0);