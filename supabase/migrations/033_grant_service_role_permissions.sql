-- Grant service_role SELECT permissions for notification Edge Function
-- This allows the check-notifications Edge Function to read necessary data
-- while preserving existing RLS policies for authenticated users

-- Grant SELECT permissions to service_role on required tables
-- These grants bypass RLS when using service_role, which is safe for system operations
GRANT SELECT ON project_applications TO service_role;
GRANT SELECT ON follow_up_schedule TO service_role; 
GRANT SELECT ON follow_up_templates TO service_role;

-- Also grant INSERT on notifications table to service_role
-- This allows the Edge Function to create notifications for users
GRANT INSERT ON notifications TO service_role;

-- Note: These grants only affect service_role access
-- All existing RLS policies remain unchanged and continue to protect user data
-- from unauthorized access by authenticated users