-- Migration 009: Remove old contact fields from freelance_projects
-- Description: Clean up after successful contact migration
-- IMPORTANT: Only run this AFTER verifying migration 008 was successful!

-- Step 1: Verify migration was successful before cleanup
-- This will raise an error if there are issues, preventing the cleanup
DO $$
DECLARE
  total_projects INTEGER;
  linked_projects INTEGER;
  projects_with_old_contacts INTEGER;
BEGIN
  -- Count totals
  SELECT COUNT(*) INTO total_projects FROM freelance_projects;
  SELECT COUNT(*) INTO linked_projects FROM freelance_projects WHERE contact_id IS NOT NULL;
  SELECT COUNT(*) INTO projects_with_old_contacts 
  FROM freelance_projects 
  WHERE (contact_email IS NOT NULL AND TRIM(contact_email) != '') 
     OR (contact_person IS NOT NULL AND TRIM(contact_person) != '');
  
  RAISE NOTICE 'Pre-cleanup verification:';
  RAISE NOTICE '- Total projects: %', total_projects;
  RAISE NOTICE '- Projects with contact_id: %', linked_projects;
  RAISE NOTICE '- Projects with old contact fields: %', projects_with_old_contacts;
  
  -- Safety check: Don't proceed if we're about to lose contact data
  IF projects_with_old_contacts > 0 AND linked_projects = 0 THEN
    RAISE EXCEPTION 'SAFETY CHECK FAILED: No projects are linked to contacts but % projects have contact data. Migration may have failed!', projects_with_old_contacts;
  END IF;
  
  -- Another safety check: Warn if we have significantly fewer linked projects than expected
  IF projects_with_old_contacts > 0 AND linked_projects < (projects_with_old_contacts * 0.8) THEN
    RAISE EXCEPTION 'SAFETY CHECK FAILED: Only % out of % projects with contact data are linked to contacts. This seems too low!', linked_projects, projects_with_old_contacts;
  END IF;
  
  RAISE NOTICE 'Safety checks passed. Proceeding with cleanup...';
END $$;

-- Step 2: Create backup table with old contact data (just in case)
CREATE TABLE IF NOT EXISTS freelance_projects_contact_backup AS
SELECT 
  id,
  user_id,
  project_name,
  company_name,
  contact_person,
  contact_email,
  contact_phone,
  contact_id,
  created_at
FROM freelance_projects
WHERE (contact_person IS NOT NULL AND TRIM(contact_person) != '')
   OR (contact_email IS NOT NULL AND TRIM(contact_email) != '')
   OR (contact_phone IS NOT NULL AND TRIM(contact_phone) != '');

-- Step 3: Remove old contact columns from freelance_projects
ALTER TABLE freelance_projects DROP COLUMN IF EXISTS contact_person;
ALTER TABLE freelance_projects DROP COLUMN IF EXISTS contact_email;
ALTER TABLE freelance_projects DROP COLUMN IF EXISTS contact_phone;

-- Step 4: Final verification
DO $$
DECLARE
  backup_count INTEGER;
BEGIN
  SELECT COUNT(*) INTO backup_count FROM freelance_projects_contact_backup;
  RAISE NOTICE 'Cleanup completed successfully!';
  RAISE NOTICE '- Backup table created with % records', backup_count;
  RAISE NOTICE '- Old contact columns removed from freelance_projects';
  RAISE NOTICE '- To rollback, you can restore from freelance_projects_contact_backup table';
END $$;