-- Fix service_role permissions for contact_communications table
-- Edge functions need to write AI summaries to the summarized_notes field

-- Grant service_role permissions for contact_communications table
GRANT SELECT, INSERT, UPDATE, DELETE ON contact_communications TO service_role;

-- Add RLS policy to allow service_role to bypass <PERSON><PERSON> for AI operations
CREATE POLICY "Service role can manage all communications" ON contact_communications
FOR ALL TO service_role USING (true) WITH CHECK (true);