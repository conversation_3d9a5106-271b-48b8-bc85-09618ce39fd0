-- ============================================================================
-- ACTIVITY LOG MIGRATION - APP-SIDE LOGIC ONLY
-- ============================================================================
-- Adds activity tracking table without database functions/triggers
-- All business logic handled in React application
-- Migration: 002_activity_log_app_side.sql
-- Date: 2025-01-27
-- ============================================================================

-- Create project activities table (minimal schema)
CREATE TABLE IF NOT EXISTS public.project_activities (
  -- Core identifiers
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  project_id UUID NOT NULL REFERENCES public.freelance_projects(id) ON DELETE CASCADE,
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,

  -- Activity details - KISS approach
  activity_type TEXT NOT NULL,
  description TEXT NOT NULL,
  
  -- Timestamp
  created_at TIMESTAMPTZ NOT NULL DEFAULT now()
);

-- Essential indexes only
CREATE INDEX IF NOT EXISTS idx_project_activities_project_id ON public.project_activities(project_id);
CREATE INDEX IF NOT EXISTS idx_project_activities_project_created ON public.project_activities(project_id, created_at DESC);

-- Enable RLS
ALTER TABLE public.project_activities ENABLE ROW LEVEL SECURITY;

-- RLS Policies - Users can only see their own project activities
CREATE POLICY "project_activities_select_policy"
ON public.project_activities FOR SELECT
USING (
  auth.uid() IS NOT NULL
  AND user_id = auth.uid()
);

CREATE POLICY "project_activities_insert_policy"
ON public.project_activities FOR INSERT
WITH CHECK (
  auth.uid() IS NOT NULL
  AND user_id = auth.uid()
);

-- Users cannot update or delete activities (audit trail)
-- No UPDATE/DELETE policies = read-only after creation

-- Grant permissions
GRANT SELECT, INSERT ON public.project_activities TO authenticated;
GRANT SELECT, INSERT ON public.project_activities TO anon;
GRANT ALL ON public.project_activities TO service_role;

-- Grant usage on sequences
GRANT USAGE ON ALL SEQUENCES IN SCHEMA public TO authenticated;
GRANT USAGE ON ALL SEQUENCES IN SCHEMA public TO anon;
GRANT USAGE ON ALL SEQUENCES IN SCHEMA public TO service_role;

-- ============================================================================
-- COMPLETION MESSAGE
-- ============================================================================

DO $$
BEGIN
  RAISE NOTICE '============================================================================';
  RAISE NOTICE 'ACTIVITY LOG MIGRATION COMPLETE - APP-SIDE LOGIC';
  RAISE NOTICE '============================================================================';
  RAISE NOTICE 'Tables created:';
  RAISE NOTICE '- project_activities (simple schema, no functions/triggers)';
  RAISE NOTICE '';
  RAISE NOTICE 'Features:';
  RAISE NOTICE '- Manual activity logging via React application';
  RAISE NOTICE '- No database functions or triggers';
  RAISE NOTICE '- Database-independent design';
  RAISE NOTICE '';
  RAISE NOTICE 'Activity types supported:';
  RAISE NOTICE '- project_created, status_changed, contact_updated';
  RAISE NOTICE '- project_updated, note_added, application_sent';
  RAISE NOTICE '';
  RAISE NOTICE 'Security: RLS enabled, audit trail (no updates/deletes)';
  RAISE NOTICE 'Logic: All tracking handled in React hooks/services';
  RAISE NOTICE '============================================================================';
END $$;