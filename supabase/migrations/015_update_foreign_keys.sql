-- Migration: Update foreign key relationships
-- Created: 2025-08-02
-- Purpose: Add new foreign key columns and update relationships
-- 
-- Changes:
-- - Add source_application_id to projects table (replacing source_project_id)
-- - Update all foreign key relationships to use new table names

-- ================================
-- ADD NEW FOREIGN KEY COLUMNS
-- ================================

-- Add source_application_id to projects table
-- This will replace source_project_id to point to project_applications instead of freelance_projects
ALTER TABLE projects 
ADD COLUMN source_application_id uuid REFERENCES project_applications(id);

-- ================================
-- MIGRATE EXISTING FOREIGN KEY DATA
-- ================================

-- Update projects.source_application_id with data from source_project_id
-- Since we're renaming freelance_projects to project_applications, 
-- the IDs should match exactly
UPDATE projects 
SET source_application_id = source_project_id
WHERE source_project_id IS NOT NULL;

-- ================================
-- VALIDATION CHECKS
-- ================================

-- Verify that all source_project_id values were successfully copied
DO $$
DECLARE
    original_count INTEGER;
    migrated_count INTEGER;
BEGIN
    SELECT COUNT(*) INTO original_count 
    FROM projects 
    WHERE source_project_id IS NOT NULL;
    
    SELECT COUNT(*) INTO migrated_count 
    FROM projects 
    WHERE source_application_id IS NOT NULL;
    
    IF original_count != migrated_count THEN
        RAISE EXCEPTION 'Foreign key migration failed: original=%, migrated=%', original_count, migrated_count;
    END IF;
    
    RAISE NOTICE 'Foreign key migration successful: % relationships updated', migrated_count;
END $$;

-- Check that all source_application_id references exist in project_applications
DO $$
DECLARE
    invalid_refs INTEGER;
BEGIN
    SELECT COUNT(*) INTO invalid_refs
    FROM projects p
    LEFT JOIN project_applications pa ON p.source_application_id = pa.id
    WHERE p.source_application_id IS NOT NULL 
    AND pa.id IS NULL;
    
    IF invalid_refs > 0 THEN
        RAISE EXCEPTION 'Invalid foreign key references found: % orphaned references', invalid_refs;
    END IF;
    
    RAISE NOTICE 'All foreign key references are valid';
END $$;

-- ================================
-- UPDATE TIME_ENTRIES TABLE
-- ================================

-- Update time_entries to reference projects table instead of active_projects
-- Note: The project_id column already exists, we just need to verify references are valid
DO $$
DECLARE
    invalid_refs INTEGER;
BEGIN
    SELECT COUNT(*) INTO invalid_refs
    FROM time_entries te
    LEFT JOIN projects p ON te.project_id = p.id
    WHERE te.project_id IS NOT NULL 
    AND p.id IS NULL;
    
    IF invalid_refs > 0 THEN
        RAISE EXCEPTION 'Invalid time_entries.project_id references found: % orphaned references', invalid_refs;
    END IF;
    
    RAISE NOTICE 'All time_entries.project_id references are valid';
END $$;

-- ================================
-- UPDATE PROJECT_NOTES TABLE
-- ================================

-- Update project_notes to reference projects table instead of active_projects
-- Note: The project_id column already exists, we just need to verify references are valid
DO $$
DECLARE
    invalid_refs INTEGER;
BEGIN
    SELECT COUNT(*) INTO invalid_refs
    FROM project_notes pn
    LEFT JOIN projects p ON pn.project_id = p.id
    WHERE pn.project_id IS NOT NULL 
    AND p.id IS NULL;
    
    IF invalid_refs > 0 THEN
        RAISE EXCEPTION 'Invalid project_notes.project_id references found: % orphaned references', invalid_refs;
    END IF;
    
    RAISE NOTICE 'All project_notes.project_id references are valid';
END $$;

-- ================================
-- VERIFICATION QUERIES
-- ================================

-- Show foreign key relationship counts
SELECT 
    'projects -> project_applications' as relationship,
    COUNT(*) as total_projects,
    COUNT(source_application_id) as with_source_app,
    COUNT(source_project_id) as with_source_proj
FROM projects
UNION ALL
SELECT 
    'time_entries -> projects',
    COUNT(*) as total_entries,
    COUNT(project_id) as with_project_ref,
    0 as placeholder
FROM time_entries
UNION ALL
SELECT 
    'project_notes -> projects',
    COUNT(*) as total_notes,
    COUNT(project_id) as with_project_ref,
    0 as placeholder
FROM project_notes;

-- ================================
-- SUCCESS CONFIRMATION
-- ================================

SELECT 'Migration 015 completed successfully - Foreign keys updated' as result;