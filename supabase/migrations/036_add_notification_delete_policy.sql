-- Add missing DELETE RLS policy for notifications table
-- This allows users to delete their own notifications

-- Add DELETE policy for notifications
CREATE POLICY "Users can delete own notifications" ON notifications
  FOR DELETE USING (auth.uid() = user_id);

-- Also add missing UPDATE policy for completeness
CREATE POLICY "Users can update own notifications" ON notifications
  FOR UPDATE USING (auth.uid() = user_id);

-- Grant DELETE permission to authenticated users
GRANT DELETE ON notifications TO authenticated;

-- Grant UPDATE permission to authenticated users  
GRANT UPDATE ON notifications TO authenticated;