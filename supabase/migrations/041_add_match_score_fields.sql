-- Add match score fields to project_applications table
-- Migration: 041_add_match_score_fields.sql
-- Date: 2025-01-14
-- Purpose: Add AI-based skill matching functionality

-- Add match score columns to project_applications
ALTER TABLE project_applications ADD COLUMN match_score INTEGER CHECK (match_score >= 0 AND match_score <= 100);
ALTER TABLE project_applications ADD COLUMN match_reasoning TEXT;
ALTER TABLE project_applications ADD COLUMN match_calculated_at TIMESTAMP;

-- Add index for efficient match score queries
CREATE INDEX IF NOT EXISTS idx_project_applications_match_score 
  ON project_applications(user_id, match_score) 
  WHERE match_score IS NOT NULL;

-- Add index for match calculation timestamp
CREATE INDEX IF NOT EXISTS idx_project_applications_match_calculated 
  ON project_applications(user_id, match_calculated_at) 
  WHERE match_calculated_at IS NOT NULL;

-- Add comment for documentation
COMMENT ON COLUMN project_applications.match_score IS 'AI-calculated skill match percentage (0-100) between user CV and project requirements';
COMMENT ON COLUMN project_applications.match_reasoning IS 'AI-generated explanation of the match score calculation';
COMMENT ON COLUMN project_applications.match_calculated_at IS 'Timestamp when the match score was last calculated';