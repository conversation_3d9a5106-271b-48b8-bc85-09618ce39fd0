-- Migration: Update calendar events table references (FIXED VERSION)
-- Created: 2025-08-02
-- Purpose: Add reference columns to calendar_events and update relationships
-- 
-- IMPORTANT: Handle orphaned calendar events gracefully

-- ================================
-- ADD NEW REFERENCE COLUMNS
-- ================================

-- Add reference_type column for semantic reference types
ALTER TABLE calendar_events 
ADD COLUMN IF NOT EXISTS reference_type text;

-- Add reference_id column for generic references
ALTER TABLE calendar_events 
ADD COLUMN IF NOT EXISTS reference_id uuid;

-- ================================
-- MIGRATE EXISTING PROJECT REFERENCES
-- ================================

-- Update existing calendar events that reference valid projects in new projects table
-- Set reference_type to 'project' and copy project_id to reference_id
UPDATE calendar_events 
SET 
    reference_type = 'project',
    reference_id = project_id
WHERE project_id IS NOT NULL 
AND EXISTS (SELECT 1 FROM projects WHERE projects.id = calendar_events.project_id);

-- ================================
-- HANDLE ORPHANED CALENDAR EVENTS
-- ================================

-- For calendar events that reference non-existent projects, clear the project_id
-- and leave them as standalone events (no reference)
UPDATE calendar_events 
SET 
    project_id = NULL
WHERE project_id IS NOT NULL 
AND NOT EXISTS (SELECT 1 FROM projects WHERE projects.id = calendar_events.project_id);

-- ================================
-- VALIDATE MIGRATION
-- ================================

-- Check how many events were migrated vs. orphaned
DO $$
DECLARE
    total_events INTEGER;
    migrated_refs_count INTEGER;
    orphaned_events INTEGER;
    standalone_events INTEGER;
BEGIN
    SELECT COUNT(*) INTO total_events FROM calendar_events;
    
    SELECT COUNT(*) INTO migrated_refs_count 
    FROM calendar_events 
    WHERE reference_type = 'project' AND reference_id IS NOT NULL;
    
    SELECT COUNT(*) INTO orphaned_events
    FROM calendar_events 
    WHERE project_id IS NULL AND reference_id IS NULL AND reference_type IS NULL;
    
    SELECT COUNT(*) INTO standalone_events
    FROM calendar_events 
    WHERE project_id IS NULL AND reference_id IS NULL;
    
    RAISE NOTICE 'Calendar events migration summary:';
    RAISE NOTICE '- Total events: %', total_events;
    RAISE NOTICE '- Successfully migrated project references: %', migrated_refs_count;
    RAISE NOTICE '- Standalone events (no project reference): %', standalone_events;
END $$;

-- ================================
-- VERIFY ALL REFERENCES ARE VALID
-- ================================

-- Verify that all 'project' references point to valid projects table
DO $$
DECLARE
    invalid_proj_refs INTEGER;
BEGIN
    SELECT COUNT(*) INTO invalid_proj_refs
    FROM calendar_events ce
    LEFT JOIN projects p ON ce.reference_id = p.id
    WHERE ce.reference_type = 'project' 
    AND ce.reference_id IS NOT NULL
    AND p.id IS NULL;
    
    IF invalid_proj_refs > 0 THEN
        RAISE EXCEPTION 'Invalid calendar project references found: % orphaned references', invalid_proj_refs;
    END IF;
    
    RAISE NOTICE 'All calendar project references are valid: % events', 
        (SELECT COUNT(*) FROM calendar_events WHERE reference_type = 'project');
END $$;

-- ================================
-- ADD HELPFUL INDEXES
-- ================================

-- Add index for reference lookups
CREATE INDEX IF NOT EXISTS idx_calendar_events_reference 
ON calendar_events(reference_type, reference_id);

-- Add index for project_id (existing usage - now mostly NULL)
CREATE INDEX IF NOT EXISTS idx_calendar_events_project_id 
ON calendar_events(project_id);

-- ================================
-- VERIFICATION QUERIES
-- ================================

-- Show reference type distribution
SELECT 
    COALESCE(reference_type, 'standalone') as reference_type,
    COUNT(*) as event_count,
    COUNT(reference_id) as with_reference_id,
    COUNT(project_id) as with_project_id
FROM calendar_events 
GROUP BY reference_type
ORDER BY reference_type;

-- Show sample calendar events with their references
SELECT 
    ce.id,
    ce.title,
    ce.project_id as legacy_project_id,
    ce.reference_type,
    ce.reference_id,
    p.title as referenced_project_title,
    ce.start_date
FROM calendar_events ce
LEFT JOIN projects p ON ce.reference_type = 'project' AND ce.reference_id = p.id
ORDER BY ce.start_date DESC
LIMIT 10;

-- ================================
-- ADD HELPFUL COMMENTS
-- ================================

COMMENT ON COLUMN calendar_events.reference_type IS 'Type of referenced entity: project (projects table), application (project_applications), or null for standalone events';
COMMENT ON COLUMN calendar_events.reference_id IS 'ID of the referenced entity based on reference_type';
COMMENT ON COLUMN calendar_events.project_id IS 'Legacy: Direct reference to projects table (now mostly NULL, use reference_id when reference_type=project)';

-- ================================
-- SUCCESS CONFIRMATION
-- ================================

SELECT 'Migration 016 completed successfully - Calendar events updated with reference system (orphaned events handled)' as result;