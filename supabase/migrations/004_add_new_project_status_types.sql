-- ============================================================================
-- ADD NEW PROJECT STATUS TYPES
-- ============================================================================
-- This migration adds two new status types to the freelance_projects table:
-- - 'recommended': For projects where the user was recommended
-- - 'recruiter_contacted': For projects where recruiters directly contacted the user
--
-- Migration: 004_add_new_project_status_types.sql
-- Date: 2025-07-28
-- Purpose: Extend project status enum to support recommendation and direct contact scenarios
-- ============================================================================

-- Drop the existing check constraint
ALTER TABLE public.freelance_projects 
DROP CONSTRAINT IF EXISTS freelance_projects_status_check;

-- Add the new check constraint with the additional status types
ALTER TABLE public.freelance_projects 
ADD CONSTRAINT freelance_projects_status_check 
CHECK (status IN (
  'not_applied', 
  'recommended',
  'recruiter_contacted',
  'application_sent', 
  'inquiry_received',
  'interview_scheduled', 
  'interview_completed', 
  'offer_received',
  'rejected', 
  'project_completed'
));

-- ============================================================================
-- COMPLETION MESSAGE
-- ============================================================================

DO $$
BEGIN
  RAISE NOTICE '============================================================================';
  RAISE NOTICE 'PROJECT STATUS TYPES MIGRATION COMPLETE';
  RAISE NOTICE '============================================================================';
  RAISE NOTICE 'Added new status types:';
  RAISE NOTICE '- recommended: For projects where user was recommended';
  RAISE NOTICE '- recruiter_contacted: For direct recruiter contact scenarios';
  RAISE NOTICE '';
  RAISE NOTICE 'Complete status list now includes:';
  RAISE NOTICE '- not_applied (default)';
  RAISE NOTICE '- recommended (NEW)';
  RAISE NOTICE '- recruiter_contacted (NEW)';
  RAISE NOTICE '- application_sent';
  RAISE NOTICE '- inquiry_received';
  RAISE NOTICE '- interview_scheduled';
  RAISE NOTICE '- interview_completed';
  RAISE NOTICE '- offer_received';
  RAISE NOTICE '- rejected';
  RAISE NOTICE '- project_completed';
  RAISE NOTICE '';
  RAISE NOTICE 'Migration ready for application!';
  RAISE NOTICE '============================================================================';
END $$;