-- Migration: Drop old tables (FINAL STEP)
-- Created: 2025-08-02
-- Purpose: Remove old tables after successful migration and validation
-- 
-- ⚠️  CRITICAL WARNING: ONLY RUN AFTER COMPLETE VALIDATION
-- ⚠️  This will permanently delete the original tables
-- ⚠️  Ensure backup tables exist and all functionality works

-- ================================
-- FINAL VALIDATION BEFORE DELETION
-- ================================

-- Perform final data integrity checks before dropping tables
DO $$
DECLARE
    freelance_count INTEGER;
    applications_count INTEGER;
    active_count INTEGER;
    projects_count INTEGER;
BEGIN
    -- Check row counts match between old and new tables
    SELECT COUNT(*) INTO freelance_count FROM freelance_projects;
    SELECT COUNT(*) INTO applications_count FROM project_applications;
    SELECT COUNT(*) INTO active_count FROM active_projects;
    SELECT COUNT(*) INTO projects_count FROM projects;
    
    IF freelance_count != applications_count THEN
        RAISE EXCEPTION 'Data mismatch: freelance_projects=%, project_applications=%', freelance_count, applications_count;
    END IF;
    
    IF active_count != projects_count THEN
        RAISE EXCEPTION 'Data mismatch: active_projects=%, projects=%', active_count, projects_count;
    END IF;
    
    RAISE NOTICE 'Final validation passed: All data counts match';
    RAISE NOTICE 'freelance_projects -> project_applications: % rows', applications_count;
    RAISE NOTICE 'active_projects -> projects: % rows', projects_count;
END $$;

-- ================================
-- VERIFY BACKUP TABLES EXIST
-- ================================

-- Ensure backup tables are available for rollback if needed
DO $$
BEGIN
    -- Check backup tables exist
    IF NOT EXISTS (SELECT FROM pg_tables WHERE tablename = 'freelance_projects_backup_20250802') THEN
        RAISE EXCEPTION 'Backup table freelance_projects_backup_20250802 does not exist - CANNOT PROCEED';
    END IF;
    
    IF NOT EXISTS (SELECT FROM pg_tables WHERE tablename = 'active_projects_backup_20250802') THEN
        RAISE EXCEPTION 'Backup table active_projects_backup_20250802 does not exist - CANNOT PROCEED';
    END IF;
    
    RAISE NOTICE 'Backup tables verified - safe to proceed';
END $$;

-- ================================
-- FINAL FOREIGN KEY VALIDATION
-- ================================

-- Verify all foreign keys are properly updated and working
DO $$
DECLARE
    orphaned_time_entries INTEGER;
    orphaned_project_notes INTEGER;
    orphaned_projects INTEGER;
    orphaned_calendar_events INTEGER;
BEGIN
    -- Check time_entries references
    SELECT COUNT(*) INTO orphaned_time_entries
    FROM time_entries te
    LEFT JOIN projects p ON te.project_id = p.id
    WHERE te.project_id IS NOT NULL AND p.id IS NULL;
    
    -- Check project_notes references  
    SELECT COUNT(*) INTO orphaned_project_notes
    FROM project_notes pn
    LEFT JOIN projects p ON pn.project_id = p.id
    WHERE pn.project_id IS NOT NULL AND p.id IS NULL;
    
    -- Check projects.source_application_id references
    SELECT COUNT(*) INTO orphaned_projects
    FROM projects pr
    LEFT JOIN project_applications pa ON pr.source_application_id = pa.id
    WHERE pr.source_application_id IS NOT NULL AND pa.id IS NULL;
    
    -- Check calendar_events references
    SELECT COUNT(*) INTO orphaned_calendar_events
    FROM calendar_events ce
    LEFT JOIN project_applications pa ON ce.reference_type = 'application' AND ce.reference_id = pa.id
    LEFT JOIN projects p ON ce.reference_type = 'project' AND ce.reference_id = p.id
    WHERE ce.reference_id IS NOT NULL 
    AND ce.reference_type IN ('application', 'project')
    AND pa.id IS NULL AND p.id IS NULL;
    
    IF orphaned_time_entries > 0 THEN
        RAISE EXCEPTION 'Found % orphaned time_entries references', orphaned_time_entries;
    END IF;
    
    IF orphaned_project_notes > 0 THEN
        RAISE EXCEPTION 'Found % orphaned project_notes references', orphaned_project_notes;
    END IF;
    
    IF orphaned_projects > 0 THEN
        RAISE EXCEPTION 'Found % orphaned projects.source_application_id references', orphaned_projects;
    END IF;
    
    IF orphaned_calendar_events > 0 THEN
        RAISE EXCEPTION 'Found % orphaned calendar_events references', orphaned_calendar_events;
    END IF;
    
    RAISE NOTICE 'All foreign key references are valid - safe to drop old tables';
END $$;

-- ================================
-- REMOVE OLD FOREIGN KEY COLUMNS
-- ================================

-- Remove the old source_project_id column from projects table
-- (This should be done before dropping freelance_projects)
ALTER TABLE projects DROP COLUMN IF EXISTS source_project_id;

COMMENT ON COLUMN projects.source_application_id IS 'References project_applications.id (replaced source_project_id)';

-- ================================
-- DROP OLD TABLES
-- ================================

-- Drop old tables in dependency order
-- Note: This will also drop their indexes, constraints, and triggers

-- Drop freelance_projects (now project_applications)
DROP TABLE IF EXISTS freelance_projects CASCADE;

-- Drop active_projects (now projects)  
DROP TABLE IF EXISTS active_projects CASCADE;

-- ================================
-- VERIFICATION AFTER DELETION
-- ================================

-- Verify old tables are gone
DO $$
BEGIN
    IF EXISTS (SELECT FROM pg_tables WHERE tablename = 'freelance_projects') THEN
        RAISE EXCEPTION 'freelance_projects table still exists after drop';
    END IF;
    
    IF EXISTS (SELECT FROM pg_tables WHERE tablename = 'active_projects') THEN
        RAISE EXCEPTION 'active_projects table still exists after drop';
    END IF;
    
    RAISE NOTICE 'Old tables successfully removed';
END $$;

-- Show remaining tables
SELECT 
    table_name,
    CASE 
        WHEN table_name LIKE '%backup%' THEN 'BACKUP'
        WHEN table_name IN ('project_applications', 'projects') THEN 'NEW'
        ELSE 'OTHER'
    END as table_type
FROM information_schema.tables 
WHERE table_schema = 'public' 
AND (table_name LIKE '%project%' OR table_name LIKE '%backup%')
ORDER BY table_type, table_name;

-- ================================
-- CLEANUP OLD POLICIES (if any remain)
-- ================================

-- Remove any orphaned policies that might reference old tables
-- This is a safety measure
DO $$
DECLARE
    policy_record RECORD;
BEGIN
    -- This will not error if policies don't exist
    FOR policy_record IN 
        SELECT policyname 
        FROM pg_policies 
        WHERE tablename IN ('freelance_projects', 'active_projects')
    LOOP
        RAISE NOTICE 'Found orphaned policy: %', policy_record.policyname;
    END LOOP;
END $$;

-- ================================
-- FINAL COMMENTS AND DOCUMENTATION
-- ================================

COMMENT ON TABLE project_applications IS 'Project applications and opportunities (migrated from freelance_projects on 2025-08-02) - columns: project_name, company_name, project_description';
COMMENT ON TABLE projects IS 'Active projects being worked on (migrated from active_projects on 2025-08-02) - columns: title, client_name, description';

-- ================================
-- SUCCESS CONFIRMATION
-- ================================

SELECT 
    'Migration 018 completed successfully - Old tables dropped' as result,
    'App restructuring database migration complete!' as status,
    '⚠️ Remember: Backup tables remain available for emergency rollback' as note;

-- ================================
-- ROLLBACK INSTRUCTIONS (EMERGENCY ONLY)
-- ================================

/*
🚨 EMERGENCY ROLLBACK PROCEDURE (if needed):

1. Recreate original tables from backups:
   CREATE TABLE freelance_projects AS SELECT * FROM freelance_projects_backup_20250802;
   CREATE TABLE active_projects AS SELECT * FROM active_projects_backup_20250802;

2. Recreate indexes and constraints (you'll need to refer to your schema)

3. Update application code to use old table names

4. Recreate RLS policies for original tables

⚠️  This should only be done in extreme emergency situations
⚠️  All changes made after migration will be lost
⚠️  Consider data recovery from new tables if possible
*/