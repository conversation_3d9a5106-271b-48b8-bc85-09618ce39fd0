-- Migration 007: Create contacts table for contact management system
-- Description: Adds contacts table and contact_id to freelance_projects for better contact management

-- Create contacts table
CREATE TABLE IF NOT EXISTS contacts (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES auth.users NOT NULL,
  name TEXT,
  email TEXT,
  phone TEXT, 
  company TEXT,
  notes TEXT,
  total_projects INTEGER DEFAULT 0,
  successful_projects INTEGER DEFAULT 0,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  CONSTRAINT unique_user_email_company UNIQUE(user_id, email, company)
);

-- Add RLS (Row Level Security) for contacts
ALTER TABLE contacts ENABLE ROW LEVEL SECURITY;

-- RLS Policies for contacts
CREATE POLICY "Users can view own contacts" ON contacts
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own contacts" ON contacts
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own contacts" ON contacts
  FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete own contacts" ON contacts
  FOR DELETE USING (auth.uid() = user_id);

-- Add contact_id column to freelance_projects (nullable for now)
ALTER TABLE freelance_projects 
ADD COLUMN IF NOT EXISTS contact_id UUID REFERENCES contacts(id);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_freelance_projects_contact_id ON freelance_projects(contact_id);
CREATE INDEX IF NOT EXISTS idx_contacts_user_email ON contacts(user_id, email);
CREATE INDEX IF NOT EXISTS idx_contacts_user_company ON contacts(user_id, company);

-- Function to update contact statistics automatically
CREATE OR REPLACE FUNCTION update_contact_stats()
RETURNS TRIGGER AS $$
BEGIN
  -- Get the contact_id to update (from NEW or OLD record)
  DECLARE
    contact_to_update UUID;
  BEGIN
    contact_to_update := COALESCE(NEW.contact_id, OLD.contact_id);
    
    IF contact_to_update IS NOT NULL THEN
      -- Update total_projects count
      UPDATE contacts 
      SET 
        total_projects = (
          SELECT COUNT(*) 
          FROM freelance_projects 
          WHERE contact_id = contact_to_update
        ),
        successful_projects = (
          SELECT COUNT(*)
          FROM freelance_projects
          WHERE contact_id = contact_to_update
          AND status IN ('offer_received', 'project_completed')
        ),
        updated_at = NOW()
      WHERE id = contact_to_update;
    END IF;
    
    RETURN COALESCE(NEW, OLD);
  END;
END;
$$ LANGUAGE plpgsql;

-- Trigger to automatically update contact statistics
DROP TRIGGER IF EXISTS update_contact_stats_trigger ON freelance_projects;
CREATE TRIGGER update_contact_stats_trigger
  AFTER INSERT OR UPDATE OR DELETE ON freelance_projects
  FOR EACH ROW
  EXECUTE FUNCTION update_contact_stats();