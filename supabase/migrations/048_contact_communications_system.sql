-- Migration 048: Contact Communications System
-- Created: 2025-08-21
-- Purpose: Create contact_communications table for comprehensive contact interaction tracking
-- 
-- Changes:
-- - Create contact_communications table with proper indexing
-- - Set up Row Level Security policies following existing patterns
-- - Support for call, email, meeting, message, whatsapp, linkedin, sms, other
-- - Optional project linking and AI summary storage
-- - Proper user isolation and security

-- ================================
-- CREATE CONTACT_COMMUNICATIONS TABLE
-- ================================

CREATE TABLE contact_communications (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  contact_id uuid NOT NULL REFERENCES contacts(id) ON DELETE CASCADE,
  user_id uuid NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  
  -- Communication details
  communication_type text NOT NULL CHECK (communication_type IN (
    'call', 'email', 'meeting', 'message', 'whatsapp', 'linkedin', 'sms', 'other'
  )),
  subject text,
  notes text NOT NULL,
  summarized_notes text, -- AI-generated summary for long notes
  
  -- Date/Time information
  communication_date timestamptz NOT NULL,
  duration_minutes integer, -- For calls/meetings
  
  -- Context/Relationship
  project_id uuid REFERENCES project_applications(id) ON DELETE SET NULL,
  is_project_related boolean DEFAULT false,
  
  -- Metadata
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now(),
  
  -- Constraints
  CONSTRAINT valid_duration CHECK (duration_minutes IS NULL OR duration_minutes >= 0),
  CONSTRAINT valid_user_id CHECK (user_id IS NOT NULL)
);

-- ================================
-- CREATE INDEXES FOR PERFORMANCE
-- ================================

-- Primary lookup indexes
CREATE INDEX idx_contact_communications_contact_id ON contact_communications(contact_id);
CREATE INDEX idx_contact_communications_user_id ON contact_communications(user_id);

-- Date-based queries (most recent first)
CREATE INDEX idx_contact_communications_date ON contact_communications(communication_date DESC);

-- Type-based filtering
CREATE INDEX idx_contact_communications_type ON contact_communications(communication_type);

-- Project relationship queries
CREATE INDEX idx_contact_communications_project_id ON contact_communications(project_id) 
  WHERE project_id IS NOT NULL;

-- Composite index for user + contact lookups (most common query pattern)
CREATE INDEX idx_contact_communications_user_contact ON contact_communications(user_id, contact_id, communication_date DESC);

-- ================================
-- ENABLE ROW LEVEL SECURITY
-- ================================

ALTER TABLE contact_communications ENABLE ROW LEVEL SECURITY;

-- ================================
-- CREATE RLS POLICIES
-- (Following the established pattern from contacts and other tables)
-- ================================

-- Policy: Users can view their own contact communications
CREATE POLICY "Users can view own contact communications" ON contact_communications
  FOR SELECT USING (auth.uid() = user_id);

-- Policy: Users can insert their own contact communications
CREATE POLICY "Users can insert own contact communications" ON contact_communications
  FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Policy: Users can update their own contact communications
CREATE POLICY "Users can update own contact communications" ON contact_communications
  FOR UPDATE USING (auth.uid() = user_id) WITH CHECK (auth.uid() = user_id);

-- Policy: Users can delete their own contact communications
CREATE POLICY "Users can delete own contact communications" ON contact_communications
  FOR DELETE USING (auth.uid() = user_id);

-- ================================
-- ADD HELPFUL COMMENTS
-- ================================

COMMENT ON TABLE contact_communications IS 'Contact communication history with RLS enabled for user isolation. Supports calls, emails, meetings, and messaging with optional project linking.';