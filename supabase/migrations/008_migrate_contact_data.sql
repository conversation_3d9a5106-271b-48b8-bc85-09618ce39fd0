-- Migration 008: Migrate existing contact data from freelance_projects to contacts table
-- Description: Extracts unique contacts and links them to projects

-- Step 1: Insert unique contacts from existing projects
-- Only create contacts where we have meaningful contact information
INSERT INTO contacts (user_id, name, email, phone, company, created_at, updated_at)
SELECT DISTINCT 
  fp.user_id,
  fp.contact_person as name,
  fp.contact_email as email,
  fp.contact_phone as phone,
  fp.company_name as company,
  MIN(fp.created_at) as created_at,  -- Use earliest project date as contact creation date
  NOW() as updated_at
FROM freelance_projects fp
WHERE (
  fp.contact_email IS NOT NULL AND TRIM(fp.contact_email) != ''
) OR (
  fp.contact_person IS NOT NULL AND TRIM(fp.contact_person) != '' AND
  fp.company_name IS NOT NULL AND TRIM(fp.company_name) != ''
)
GROUP BY fp.user_id, fp.contact_person, fp.contact_email, fp.contact_phone, fp.company_name
ON CONFLICT (user_id, email, company) DO NOTHING;

-- Step 2: Update freelance_projects with contact_id references
-- Link projects to contacts based on email + company combination
UPDATE freelance_projects fp
SET contact_id = c.id
FROM contacts c
WHERE fp.user_id = c.user_id
  AND COALESCE(fp.contact_email, '') = COALESCE(c.email, '')
  AND COALESCE(fp.company_name, '') = COALESCE(c.company, '')
  AND fp.contact_id IS NULL;  -- Only update if not already set

-- Step 3: Handle contacts with only name + company (no email)
-- Create contacts for projects that have contact_person + company but no email
INSERT INTO contacts (user_id, name, email, phone, company, created_at, updated_at)
SELECT DISTINCT 
  fp.user_id,
  fp.contact_person as name,
  NULL as email,  -- No email available
  fp.contact_phone as phone,
  fp.company_name as company,
  MIN(fp.created_at) as created_at,
  NOW() as updated_at
FROM freelance_projects fp
WHERE fp.contact_id IS NULL  -- Not yet linked
  AND fp.contact_person IS NOT NULL AND TRIM(fp.contact_person) != ''
  AND fp.company_name IS NOT NULL AND TRIM(fp.company_name) != ''
  AND (fp.contact_email IS NULL OR TRIM(fp.contact_email) = '')
GROUP BY fp.user_id, fp.contact_person, fp.contact_phone, fp.company_name
ON CONFLICT (user_id, email, company) DO NOTHING;

-- Link these projects to the newly created contacts
UPDATE freelance_projects fp
SET contact_id = c.id
FROM contacts c
WHERE fp.user_id = c.user_id
  AND fp.contact_id IS NULL  -- Not yet linked
  AND COALESCE(fp.contact_person, '') = COALESCE(c.name, '')
  AND COALESCE(fp.company_name, '') = COALESCE(c.company, '')
  AND c.email IS NULL;  -- Contacts without email

-- Step 4: Trigger the contact stats update for all contacts
-- This will populate total_projects and successful_projects counts
UPDATE contacts 
SET updated_at = NOW()
WHERE id IN (
  SELECT DISTINCT contact_id 
  FROM freelance_projects 
  WHERE contact_id IS NOT NULL
);

-- Verify migration results (this will be shown in the migration log)
DO $$
DECLARE
  total_contacts INTEGER;
  linked_projects INTEGER;
  unlinked_projects INTEGER;
BEGIN
  SELECT COUNT(*) INTO total_contacts FROM contacts;
  SELECT COUNT(*) INTO linked_projects FROM freelance_projects WHERE contact_id IS NOT NULL;
  SELECT COUNT(*) INTO unlinked_projects FROM freelance_projects WHERE contact_id IS NULL;
  
  RAISE NOTICE 'Migration completed:';
  RAISE NOTICE '- Total contacts created: %', total_contacts;
  RAISE NOTICE '- Projects linked to contacts: %', linked_projects;
  RAISE NOTICE '- Projects without contact: %', unlinked_projects;
END $$;