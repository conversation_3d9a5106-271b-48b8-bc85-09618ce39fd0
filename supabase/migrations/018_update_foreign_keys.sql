-- Migration: Update foreign key constraints for new table references
-- Created: 2025-08-02  
-- Purpose: Update time_entries and project_notes to reference new 'projects' table

-- Update time_entries foreign key to reference the new 'projects' table
ALTER TABLE time_entries DROP CONSTRAINT IF EXISTS time_entries_project_id_fkey;
ALTER TABLE time_entries ADD CONSTRAINT time_entries_project_id_fkey 
  FOREIGN KEY (project_id) REFERENCES projects(id) ON DELETE CASCADE;

-- Update project_notes foreign key to reference the new 'projects' table  
ALTER TABLE project_notes DROP CONSTRAINT IF EXISTS project_notes_project_id_fkey;
ALTER TABLE project_notes ADD CONSTRAINT project_notes_project_id_fkey 
  FOREIGN KEY (project_id) REFERENCES projects(id) ON DELETE CASCADE;

-- Success confirmation
SELECT 'Foreign key constraints updated successfully' as result;