-- Revert to app-side logic approach as per CLAUDE.md guidelines
-- Remove database functions and use simple service_role table access

-- Drop all the database functions we created in migration 034
DROP FUNCTION IF EXISTS get_applications_for_notifications();
DROP FUNCTION IF EXISTS get_scheduled_followups_for_notifications();
DROP FUNCTION IF EXISTS create_notification(uuid, text, text, text, text);

-- <PERSON> direct SELECT permissions back to service_role for Edge Function
-- This is the minimal permissions needed for app-side logic
GRANT SELECT ON project_applications TO service_role;
GRANT SELECT ON follow_up_schedule TO service_role; 
GRANT SELECT ON follow_up_templates TO service_role;
GRANT SELECT ON contacts TO service_role;
GRANT SELECT ON notifications TO service_role;
GRANT INSERT ON notifications TO service_role;

-- Note: All duplicate checking and business logic will be handled in the Edge Function
-- This follows CLAUDE.md principle: "APP-SIDE LOGIC: All business logic must be handled in the React application"