-- Add missing foreign key constraints
-- Migration: 019_add_missing_foreign_keys
-- Description: Add foreign key constraint for project_applications.contact_id -> contacts.id

-- Add foreign key constraint for project_applications.contact_id
-- This will link applications to their contact persons
ALTER TABLE project_applications 
ADD CONSTRAINT project_applications_contact_id_fkey 
FOREIGN KEY (contact_id) REFERENCES contacts(id) ON DELETE SET NULL;

-- Verify the constraint was added
SELECT 
    tc.constraint_name, 
    tc.table_name, 
    kcu.column_name, 
    ccu.table_name AS foreign_table_name,
    ccu.column_name AS foreign_column_name 
FROM 
    information_schema.table_constraints AS tc 
    JOIN information_schema.key_column_usage AS kcu
      ON tc.constraint_name = kcu.constraint_name
      AND tc.table_schema = kcu.table_schema
    JOIN information_schema.constraint_column_usage AS ccu
      ON ccu.constraint_name = tc.constraint_name
      AND ccu.table_schema = tc.table_schema
WHERE tc.constraint_type = 'FOREIGN KEY' 
AND tc.table_name = 'project_applications'
ORDER BY tc.constraint_name;