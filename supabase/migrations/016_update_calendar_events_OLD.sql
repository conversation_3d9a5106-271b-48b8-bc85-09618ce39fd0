-- Migration: Update calendar events table references (CORRECTED)
-- Created: 2025-08-02
-- Purpose: Add reference columns to calendar_events and update relationships
-- 
-- IMPORTANT: The current calendar_events table only has project_id column
-- We need to add reference_type and reference_id columns first

-- ================================
-- ADD NEW REFERENCE COLUMNS
-- ================================

-- Add reference_type column for semantic reference types
ALTER TABLE calendar_events 
ADD COLUMN IF NOT EXISTS reference_type text;

-- Add reference_id column for generic references
ALTER TABLE calendar_events 
ADD COLUMN IF NOT EXISTS reference_id uuid;

-- ================================
-- MIGRATE EXISTING PROJECT REFERENCES
-- ================================

-- Update existing calendar events that reference active_projects
-- Set reference_type to 'project' and copy project_id to reference_id
UPDATE calendar_events 
SET 
    reference_type = 'project',
    reference_id = project_id
WHERE project_id IS NOT NULL;

-- ================================
-- VALIDATE MIGRATION
-- ================================

-- Check that all project_id values were copied to reference_id
DO $$
DECLARE
    project_events_count INTEGER;
    migrated_refs_count INTEGER;
BEGIN
    SELECT COUNT(*) INTO project_events_count 
    FROM calendar_events 
    WHERE project_id IS NOT NULL;
    
    SELECT COUNT(*) INTO migrated_refs_count 
    FROM calendar_events 
    WHERE reference_type = 'project' AND reference_id IS NOT NULL;
    
    IF project_events_count != migrated_refs_count THEN
        RAISE EXCEPTION 'Calendar events migration failed: project_events=%, migrated_refs=%', project_events_count, migrated_refs_count;
    END IF;
    
    RAISE NOTICE 'Calendar events migration successful: % project references updated', migrated_refs_count;
END $$;

-- ================================
-- VERIFY REFERENCES POINT TO VALID PROJECTS
-- ================================

-- Verify that all 'project' references point to valid projects table
DO $$
DECLARE
    invalid_proj_refs INTEGER;
BEGIN
    SELECT COUNT(*) INTO invalid_proj_refs
    FROM calendar_events ce
    LEFT JOIN projects p ON ce.reference_id = p.id
    WHERE ce.reference_type = 'project' 
    AND ce.reference_id IS NOT NULL
    AND p.id IS NULL;
    
    IF invalid_proj_refs > 0 THEN
        RAISE EXCEPTION 'Invalid calendar project references found: % orphaned references', invalid_proj_refs;
    END IF;
    
    RAISE NOTICE 'All calendar project references are valid: % events', 
        (SELECT COUNT(*) FROM calendar_events WHERE reference_type = 'project');
END $$;

-- ================================
-- ADD HELPFUL INDEXES
-- ================================

-- Add index for reference lookups
CREATE INDEX IF NOT EXISTS idx_calendar_events_reference 
ON calendar_events(reference_type, reference_id);

-- Add index for project_id (existing usage)
CREATE INDEX IF NOT EXISTS idx_calendar_events_project_id 
ON calendar_events(project_id);

-- ================================
-- VERIFICATION QUERIES
-- ================================

-- Show reference type distribution
SELECT 
    reference_type,
    COUNT(*) as event_count,
    COUNT(reference_id) as with_reference
FROM calendar_events 
GROUP BY reference_type
ORDER BY reference_type;

-- Show sample calendar events with their references
SELECT 
    ce.id,
    ce.title,
    ce.project_id as old_project_id,
    ce.reference_type,
    ce.reference_id,
    p.title as referenced_project_title,
    ce.start_date
FROM calendar_events ce
LEFT JOIN projects p ON ce.reference_type = 'project' AND ce.reference_id = p.id
ORDER BY ce.start_date DESC
LIMIT 10;

-- Check for any events without references
SELECT 
    COUNT(*) as events_without_reference
FROM calendar_events 
WHERE reference_type IS NULL AND reference_id IS NULL AND project_id IS NULL;

-- ================================
-- ADD HELPFUL COMMENTS
-- ================================

COMMENT ON COLUMN calendar_events.reference_type IS 'Type of referenced entity: project (projects table), application (project_applications), or null for standalone events';
COMMENT ON COLUMN calendar_events.reference_id IS 'ID of the referenced entity based on reference_type';
COMMENT ON COLUMN calendar_events.project_id IS 'Legacy: Direct reference to projects table (now also stored in reference_id when reference_type=project)';

-- ================================
-- SUCCESS CONFIRMATION
-- ================================

SELECT 'Migration 016 completed successfully - Calendar events updated with reference system' as result;