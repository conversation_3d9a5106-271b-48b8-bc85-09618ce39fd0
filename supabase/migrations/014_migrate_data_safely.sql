-- Migration: Safely migrate data to new tables (CORRECTED VERSION)
-- Created: 2025-08-02
-- Purpose: Copy all data from old tables to new renamed tables with validation
-- 
-- Changes:
-- - Copy freelance_projects → project_applications
-- - Copy active_projects → projects

-- ================================
-- MIGRATE freelance_projects → project_applications
-- ================================

-- Insert all data from freelance_projects to project_applications
INSERT INTO project_applications 
SELECT * FROM freelance_projects;

-- Validate data migration
DO $$
DECLARE
    original_count INTEGER;
    migrated_count INTEGER;
BEGIN
    SELECT COUNT(*) INTO original_count FROM freelance_projects;
    SELECT COUNT(*) INTO migrated_count FROM project_applications;
    
    IF original_count != migrated_count THEN
        RAISE EXCEPTION 'Data migration failed for project_applications: original=%, migrated=%', original_count, migrated_count;
    END IF;
    
    RAISE NOTICE 'project_applications migration successful: % rows migrated', migrated_count;
END $$;

-- ================================
-- MIGRATE active_projects → projects
-- ================================

-- Insert all data from active_projects to projects
INSERT INTO projects 
SELECT * FROM active_projects;

-- Validate data migration
DO $$
DECLARE
    original_count INTEGER;
    migrated_count INTEGER;
BEGIN
    SELECT COUNT(*) INTO original_count FROM active_projects;
    SELECT COUNT(*) INTO migrated_count FROM projects;
    
    IF original_count != migrated_count THEN
        RAISE EXCEPTION 'Data migration failed for projects: original=%, migrated=%', original_count, migrated_count;
    END IF;
    
    RAISE NOTICE 'projects migration successful: % rows migrated', migrated_count;
END $$;

-- ================================
-- VERIFICATION QUERIES
-- ================================

-- Compare row counts
SELECT 
    'project_applications' as table_name,
    (SELECT COUNT(*) FROM freelance_projects) as original_count,
    (SELECT COUNT(*) FROM project_applications) as migrated_count,
    CASE 
        WHEN (SELECT COUNT(*) FROM freelance_projects) = (SELECT COUNT(*) FROM project_applications) 
        THEN '✓ MATCH' 
        ELSE '✗ MISMATCH' 
    END as status
UNION ALL
SELECT 
    'projects',
    (SELECT COUNT(*) FROM active_projects),
    (SELECT COUNT(*) FROM projects),
    CASE 
        WHEN (SELECT COUNT(*) FROM active_projects) = (SELECT COUNT(*) FROM projects) 
        THEN '✓ MATCH' 
        ELSE '✗ MISMATCH' 
    END as status;

-- Sample data verification (using correct column names)
-- For project_applications (freelance_projects structure)
SELECT 'project_applications - Sample Data Check' as info;
SELECT id, project_name, company_name, status, created_at 
FROM project_applications 
ORDER BY created_at DESC 
LIMIT 3;

-- For projects (active_projects structure)
SELECT 'projects - Sample Data Check' as info;
SELECT id, title, client_name, status, created_at 
FROM projects 
ORDER BY created_at DESC 
LIMIT 3;

-- ================================
-- INTEGRITY CHECKS
-- ================================

-- Check for any NULL values in critical fields
SELECT 
    'project_applications NULL check' as table_name,
    COUNT(*) as total_rows,
    COUNT(*) - COUNT(id) as null_ids,
    COUNT(*) - COUNT(project_name) as null_project_names,
    COUNT(*) - COUNT(company_name) as null_company_names,
    COUNT(*) - COUNT(user_id) as null_user_ids
FROM project_applications
UNION ALL
SELECT 
    'projects NULL check',
    COUNT(*),
    COUNT(*) - COUNT(id),
    COUNT(*) - COUNT(title),
    COUNT(*) - COUNT(client_name),
    COUNT(*) - COUNT(user_id)
FROM projects;

-- ================================
-- SUCCESS CONFIRMATION
-- ================================

SELECT 'Migration 014 completed successfully - Data migrated to new tables' as result;