-- Migration: 012_add_contacts_to_active_projects.sql
-- Description: Add contact mapping support to active projects
-- Date: 2025-08-01

-- Add contact_id column to active_projects table
ALTER TABLE active_projects 
ADD COLUMN IF NOT EXISTS contact_id UUID REFERENCES contacts(id);

-- Create index for better performance
CREATE INDEX IF NOT EXISTS idx_active_projects_contact_id ON active_projects(contact_id);

-- Grant permissions for anon and authenticated roles
GRANT SELECT, INSERT, UPDATE, DELETE ON active_projects TO anon, authenticated;

-- Update existing active_projects to have proper permissions (already granted above)
-- This ensures the contact_id column is accessible through the existing RLS policies

-- Note: We intentionally do NOT create automatic contact statistics updates for active_projects
-- because these are separate from freelance_projects and have different workflow
-- Active projects represent actual work relationships, not recruiting contacts