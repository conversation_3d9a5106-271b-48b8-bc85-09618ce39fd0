-- Setup cron job to trigger notification checks every 5 minutes
-- This uses pg_cron + pg_net to call our Edge Function automatically

-- Store secrets in Vault for cron job authentication
-- Note: Replace with your actual project URL and anon key
-- You can find these in your Supabase Dashboard → Settings → API

-- For production, update these values in Supabase Dashboard → SQL Editor
-- select vault.create_secret('https://your-project-ref.supabase.co', 'project_url');
-- select vault.create_secret('your-anon-key', 'anon_key');

-- For local development (using local stack)
select vault.create_secret('http://host.docker.internal:54321', 'project_url');
-- The anon key can be found in your local supabase/config.toml or .env

-- Schedule notification check every 5 minutes
select
  cron.schedule(
    'check-notifications-every-5-minutes',
    '*/5 * * * *', -- Every 5 minutes
    $$
    select
      net.http_post(
        url := (select decrypted_secret from vault.decrypted_secrets where name = 'project_url') || '/functions/v1/check-notifications',
        headers := jsonb_build_object(
          'Content-Type', 'application/json',
          'Authorization', 'Bearer ' || (select decrypted_secret from vault.decrypted_secrets where name = 'anon_key')
        ),
        body := jsonb_build_object('trigger', 'cron', 'time', now())
      ) as request_id;
    $$
  );