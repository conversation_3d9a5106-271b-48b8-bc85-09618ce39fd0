-- Migration: Add interview date and time fields to project_applications
-- Created: 2025-01-13
-- Purpose: Add interview_date and interview_time fields to support interview scheduling

-- Add interview fields to project_applications table
ALTER TABLE project_applications 
ADD COLUMN interview_date DATE,
ADD COLUMN interview_time TIME;

-- Add index for interview date queries (for notifications and filtering)
CREATE INDEX IF NOT EXISTS idx_project_applications_interview_date 
ON project_applications(interview_date) 
WHERE interview_date IS NOT NULL;

-- Add composite index for user + interview date queries
CREATE INDEX IF NOT EXISTS idx_project_applications_user_interview 
ON project_applications(user_id, interview_date) 
WHERE interview_date IS NOT NULL;

-- Add comment to document the fields
COMMENT ON COLUMN project_applications.interview_date IS 'Primary interview date for the application';
COMMENT ON COLUMN project_applications.interview_time IS 'Primary interview time for the application';

-- Verification
DO $$
BEGIN
    -- Check if columns were added successfully
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'project_applications' 
        AND column_name = 'interview_date'
    ) THEN
        RAISE EXCEPTION 'Column interview_date was not created successfully';
    END IF;
    
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'project_applications' 
        AND column_name = 'interview_time'
    ) THEN
        RAISE EXCEPTION 'Column interview_time was not created successfully';
    END IF;
    
    RAISE NOTICE 'Interview fields added successfully to project_applications table';
END $$;