-- Secure cron job setup without hardcoded secrets
-- This migration only sets up the cron job structure
-- Actual secrets must be configured via Supabase Dashboard or CLI

-- Remove any existing notification check cron jobs to avoid duplicates
SELECT cron.unschedule('check-notifications-every-5-minutes');

-- Create secure cron job that uses vault for secrets
-- Note: Secrets must be manually configured in Supabase Dashboard:
-- 1. Go to Database → Extensions → Vault
-- 2. Add secret 'project_url' with your project URL (e.g., https://your-project.supabase.co)
-- 3. Add secret 'anon_key' with your anon key from Settings → API

SELECT cron.schedule(
  'check-notifications-every-5-minutes',
  '*/5 * * * *', -- Every 5 minutes
  $$
  SELECT
    net.http_post(
      url := (SELECT decrypted_secret FROM vault.decrypted_secrets WHERE name = 'project_url') || '/functions/v1/check-notifications',
      headers := jsonb_build_object(
        'Content-Type', 'application/json',
        'Authorization', 'Bearer ' || (SELECT decrypted_secret FROM vault.decrypted_secrets WHERE name = 'anon_key')
      ),
      body := jsonb_build_object('trigger', 'cron', 'time', now())
    ) as request_id;
  $$
);

-- Create a helper function to check if secrets are properly configured
CREATE OR REPLACE FUNCTION check_notification_cron_setup()
RETURNS TABLE (
  secret_name text,
  is_configured boolean
)
SECURITY DEFINER
SET search_path = public
LANGUAGE plpgsql
AS $$
BEGIN
  RETURN QUERY
  SELECT 
    s.name as secret_name,
    (s.decrypted_secret IS NOT NULL AND s.decrypted_secret != '') as is_configured
  FROM (
    SELECT 'project_url' as name
    UNION ALL
    SELECT 'anon_key' as name
  ) s
  LEFT JOIN vault.decrypted_secrets v ON v.name = s.name;
END;
$$;

-- Grant execute permission to authenticated users (for debugging)
GRANT EXECUTE ON FUNCTION check_notification_cron_setup() TO authenticated;