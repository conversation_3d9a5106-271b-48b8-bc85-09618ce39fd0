-- ============================================================================
-- ACTIVITY NOTES EXTENSION - STATUS NOTES FEATURE
-- ============================================================================
-- Extends project_activities table with notes functionality
-- Adds support for status change notes and timestamps
-- Migration: 003_activity_notes_extension.sql
-- Date: 2025-01-27
-- ============================================================================

-- Add notes functionality to project_activities
ALTER TABLE public.project_activities 
ADD COLUMN IF NOT EXISTS notes TEXT;

-- Add optional custom date for notes (e.g., when email was received)
ALTER TABLE public.project_activities 
ADD COLUMN IF NOT EXISTS notes_date TIMESTAMPTZ;

-- Add index for notes search (for future full-text search)
CREATE INDEX IF NOT EXISTS idx_project_activities_notes ON public.project_activities USING gin(to_tsvector('german', notes)) WHERE notes IS NOT NULL;

-- Add index for notes_date queries
CREATE INDEX IF NOT EXISTS idx_project_activities_notes_date ON public.project_activities(notes_date) WHERE notes_date IS NOT NULL;

-- ============================================================================
-- UPDATE RLS POLICIES (already exist, just verify they work with new columns)
-- ============================================================================
-- The existing RLS policies automatically cover the new columns since they use
-- user_id based filtering, which applies to all columns in the table.

-- ============================================================================
-- COMPLETION MESSAGE
-- ============================================================================

DO $$
BEGIN
  RAISE NOTICE '============================================================================';
  RAISE NOTICE 'ACTIVITY NOTES EXTENSION COMPLETE';
  RAISE NOTICE '============================================================================';
  RAISE NOTICE 'Schema changes:';
  RAISE NOTICE '- Added notes TEXT column to project_activities';
  RAISE NOTICE '- Added notes_date TIMESTAMPTZ column';
  RAISE NOTICE '- Added GIN index for full-text search on notes';
  RAISE NOTICE '- Added index for notes_date queries';
  RAISE NOTICE '';
  RAISE NOTICE 'Features enabled:';
  RAISE NOTICE '- Status change notes with optional custom dates';
  RAISE NOTICE '- Email content copying and documentation';
  RAISE NOTICE '- Rich timeline with expandable notes';
  RAISE NOTICE '- Full-text search capability (future)';
  RAISE NOTICE '';
  RAISE NOTICE 'Next steps:';
  RAISE NOTICE '- Implement StatusNotesDialog UI component';
  RAISE NOTICE '- Extend ProjectTimeline to display notes';
  RAISE NOTICE '- Add AI notes summary edge function';
  RAISE NOTICE '============================================================================';
END $$;