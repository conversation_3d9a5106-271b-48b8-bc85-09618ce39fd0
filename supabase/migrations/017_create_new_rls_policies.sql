-- Migration: Create RLS policies for new tables
-- Created: 2025-08-02
-- Purpose: Set up Row Level Security policies for project_applications and projects tables
-- 
-- Changes:
-- - Enable RLS on new tables
-- - Create policies that mirror the original table policies
-- - Ensure proper user access control

-- ================================
-- ENABLE ROW LEVEL SECURITY
-- ================================

-- Enable RLS on new tables
ALTER TABLE project_applications ENABLE ROW LEVEL SECURITY;
ALTER TABLE projects ENABLE ROW LEVEL SECURITY;

-- ================================
-- PROJECT_APPLICATIONS RLS POLICIES
-- (Based on original freelance_projects policies)
-- ================================

-- Policy: Users can view their own project applications
CREATE POLICY "Users can view own project applications" ON project_applications
    FOR SELECT USING (auth.uid() = user_id);

-- Policy: Users can insert their own project applications
CREATE POLICY "Users can insert own project applications" ON project_applications
    FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Policy: Users can update their own project applications
CREATE POLICY "Users can update own project applications" ON project_applications
    FOR UPDATE USING (auth.uid() = user_id) WITH CHECK (auth.uid() = user_id);

-- Policy: Users can delete their own project applications
CREATE POLICY "Users can delete own project applications" ON project_applications
    FOR DELETE USING (auth.uid() = user_id);

-- ================================
-- PROJECTS RLS POLICIES
-- (Based on original active_projects policies)
-- ================================

-- Policy: Users can view their own projects
CREATE POLICY "Users can view own projects" ON projects
    FOR SELECT USING (auth.uid() = user_id);

-- Policy: Users can insert their own projects
CREATE POLICY "Users can insert own projects" ON projects
    FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Policy: Users can update their own projects
CREATE POLICY "Users can update own projects" ON projects
    FOR UPDATE USING (auth.uid() = user_id) WITH CHECK (auth.uid() = user_id);

-- Policy: Users can delete their own projects
CREATE POLICY "Users can delete own projects" ON projects
    FOR DELETE USING (auth.uid() = user_id);

-- ================================
-- VERIFY EXISTING POLICIES (for reference)
-- ================================

-- Check what policies exist on original tables
SELECT schemaname, tablename, policyname, permissive, roles, cmd, qual, with_check
FROM pg_policies 
WHERE tablename IN ('freelance_projects', 'active_projects')
ORDER BY tablename, policyname;

-- ================================
-- VERIFY NEW POLICIES
-- ================================

-- Check that new policies were created successfully
SELECT schemaname, tablename, policyname, permissive, roles, cmd, qual, with_check
FROM pg_policies 
WHERE tablename IN ('project_applications', 'projects')
ORDER BY tablename, policyname;

-- ================================
-- TEST POLICY FUNCTIONALITY
-- ================================

-- Test that RLS is working (these should return 0 if RLS is properly configured)
-- Note: These tests assume you're running as a user without proper auth context

-- This should return 0 rows if RLS is working (no authenticated user)
SELECT COUNT(*) as should_be_zero_if_rls_working
FROM project_applications;

SELECT COUNT(*) as should_be_zero_if_rls_working  
FROM projects;

-- ================================
-- GRANT NECESSARY PERMISSIONS
-- ================================

-- Grant usage on the tables to authenticated users
GRANT ALL ON project_applications TO authenticated;
GRANT ALL ON projects TO authenticated;

-- Grant usage on sequences (for auto-increment IDs)
GRANT USAGE, SELECT ON ALL SEQUENCES IN SCHEMA public TO authenticated;

-- ================================
-- ADD HELPFUL COMMENTS
-- ================================

COMMENT ON TABLE project_applications IS 'Project applications and opportunities with RLS enabled for user isolation';
COMMENT ON TABLE projects IS 'Active projects being worked on with RLS enabled for user isolation';

-- ================================
-- VERIFICATION SUMMARY
-- ================================

-- Show RLS status for all relevant tables
SELECT 
    schemaname,
    tablename,
    rowsecurity as rls_enabled,
    (SELECT COUNT(*) FROM pg_policies WHERE pg_policies.tablename = t.tablename) as policy_count
FROM pg_tables t
WHERE t.tablename IN ('freelance_projects', 'active_projects', 'project_applications', 'projects')
ORDER BY t.tablename;

-- ================================
-- SUCCESS CONFIRMATION
-- ================================

SELECT 'Migration 017 completed successfully - RLS policies created' as result;