-- Migration: Create new renamed tables
-- Created: 2025-08-02
-- Purpose: Create new tables with semantic naming for app restructuring
-- 
-- Changes:
-- - freelance_projects → project_applications
-- - active_projects → projects

-- ================================
-- CREATE project_applications TABLE
-- (Renamed from freelance_projects)
-- ================================

CREATE TABLE project_applications (
    LIKE freelance_projects INCLUDING ALL
);

-- Copy all indexes, constraints, and triggers from original table
-- Note: RLS policies will be created in a separate migration

-- ================================
-- CREATE projects TABLE  
-- (Renamed from active_projects)
-- ================================

CREATE TABLE projects (
    LIKE active_projects INCLUDING ALL
);

-- ================================
-- VERIFY TABLE CREATION
-- ================================

-- Check that tables were created successfully
DO $$
BEGIN
    -- Check project_applications table exists
    IF NOT EXISTS (SELECT FROM pg_tables WHERE schemaname = 'public' AND tablename = 'project_applications') THEN
        RAISE EXCEPTION 'Table project_applications was not created successfully';
    END IF;
    
    -- Check projects table exists
    IF NOT EXISTS (SELECT FROM pg_tables WHERE schemaname = 'public' AND tablename = 'projects') THEN
        RAISE EXCEPTION 'Table projects was not created successfully';
    END IF;
    
    RAISE NOTICE 'Tables created successfully: project_applications, projects';
END $$;

-- ================================
-- TABLE COMMENTS
-- ================================

COMMENT ON TABLE project_applications IS 'Project applications and opportunities (renamed from freelance_projects)';
COMMENT ON TABLE projects IS 'Active projects being worked on (renamed from active_projects)';

-- ================================
-- VERIFICATION QUERIES
-- ================================

-- Show table structures to verify they match originals
SELECT 
    table_name,
    column_name,
    data_type,
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_name IN ('project_applications', 'projects')
AND table_schema = 'public'
ORDER BY table_name, ordinal_position;

-- Show table exists
SELECT table_name, table_type 
FROM information_schema.tables 
WHERE table_schema = 'public' 
AND table_name IN ('project_applications', 'projects')
ORDER BY table_name;