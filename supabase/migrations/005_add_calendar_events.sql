-- ============================================================================
-- ADD CALENDAR EVENTS TABLE
-- ============================================================================
-- This migration adds a calendar events table to support:
-- - Manual event creation linked to projects
-- - Automatic event creation from project status changes (e.g., interview scheduled)
-- - Full calendar functionality with date/time management
--
-- Migration: 005_add_calendar_events.sql
-- Date: 2025-07-28
-- Purpose: Add comprehensive calendar functionality for project management
-- ============================================================================

-- Calendar events table
CREATE TABLE IF NOT EXISTS public.calendar_events (
  -- Core identifiers
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  project_id UUID REFERENCES public.freelance_projects(id) ON DELETE CASCADE,

  -- Event basic information
  title TEXT NOT NULL CHECK (length(trim(title)) > 0),
  description TEXT,
  
  -- Date and time information
  start_date DATE NOT NULL,
  start_time TIME,
  end_date DATE,
  end_time TIME,
  all_day BOOLEAN NOT NULL DEFAULT false,
  
  -- Event categorization
  event_type TEXT NOT NULL CHECK (event_type IN (
    'manual',           -- Manually created events
    'interview',        -- Auto-created from interview_scheduled status
    'project_start',    -- Auto-created from project start dates
    'project_end',      -- Auto-created from project end dates
    'application_due',  -- Manual deadline events
    'follow_up',        -- Manual follow-up reminders
    'meeting'          -- General meetings
  )) DEFAULT 'manual',
  
  -- Visual and organizational
  color TEXT DEFAULT '#3b82f6', -- Default blue color
  location TEXT,
  
  -- Status and completion
  completed BOOLEAN NOT NULL DEFAULT false,
  
  -- Reminder settings
  reminder_enabled BOOLEAN NOT NULL DEFAULT false,
  reminder_minutes_before INTEGER CHECK (reminder_minutes_before > 0),
  
  -- System tracking
  created_automatically BOOLEAN NOT NULL DEFAULT false,
  source_status TEXT, -- The project status that created this event (if auto-created)
  
  -- Timestamps
  created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT now(),

  -- Data validation constraints
  CONSTRAINT valid_date_range CHECK (
    end_date IS NULL OR
    end_date >= start_date
  ),
  CONSTRAINT valid_time_range CHECK (
    end_time IS NULL OR
    start_time IS NULL OR
    end_date IS NULL OR
    start_date IS NULL OR
    end_date > start_date OR
    (end_date = start_date AND end_time >= start_time)
  ),
  CONSTRAINT valid_reminder_setup CHECK (
    reminder_enabled = false OR
    (reminder_enabled = true AND reminder_minutes_before IS NOT NULL)
  )
);

-- Calendar events indexes for performance
CREATE INDEX IF NOT EXISTS idx_calendar_events_user_id ON public.calendar_events(user_id);
CREATE INDEX IF NOT EXISTS idx_calendar_events_project_id ON public.calendar_events(project_id) WHERE project_id IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_calendar_events_start_date ON public.calendar_events(start_date);
CREATE INDEX IF NOT EXISTS idx_calendar_events_event_type ON public.calendar_events(event_type);
CREATE INDEX IF NOT EXISTS idx_calendar_events_created_at ON public.calendar_events(created_at DESC);

-- Composite indexes for common queries
CREATE INDEX IF NOT EXISTS idx_calendar_events_user_date ON public.calendar_events(user_id, start_date);
CREATE INDEX IF NOT EXISTS idx_calendar_events_user_type ON public.calendar_events(user_id, event_type);

-- ============================================================================
-- TRIGGERS FOR AUTOMATIC TIMESTAMP UPDATES
-- ============================================================================

-- Trigger for calendar_events table
CREATE TRIGGER update_calendar_events_updated_at
  BEFORE UPDATE ON public.calendar_events
  FOR EACH ROW
  EXECUTE FUNCTION public.update_updated_at_column();

-- ============================================================================
-- TABLE PERMISSIONS
-- ============================================================================

-- Grant necessary permissions to Supabase roles
GRANT ALL ON public.calendar_events TO authenticated;
GRANT ALL ON public.calendar_events TO anon;
GRANT ALL ON public.calendar_events TO service_role;

-- Enable RLS on calendar events table
ALTER TABLE public.calendar_events ENABLE ROW LEVEL SECURITY;

-- ============================================================================
-- RLS POLICIES - CALENDAR EVENTS
-- ============================================================================

-- Users can view their own calendar events
CREATE POLICY "calendar_events_select_policy"
ON public.calendar_events FOR SELECT
USING (
  auth.uid() IS NOT NULL
  AND user_id = auth.uid()
);

-- Users can create their own calendar events
CREATE POLICY "calendar_events_insert_policy"
ON public.calendar_events FOR INSERT
WITH CHECK (
  auth.uid() IS NOT NULL
  AND user_id = auth.uid()
);

-- Users can update their own calendar events
CREATE POLICY "calendar_events_update_policy"
ON public.calendar_events FOR UPDATE
USING (
  auth.uid() IS NOT NULL
  AND user_id = auth.uid()
);

-- Users can delete their own calendar events
CREATE POLICY "calendar_events_delete_policy"
ON public.calendar_events FOR DELETE
USING (
  auth.uid() IS NOT NULL
  AND user_id = auth.uid()
);

-- ============================================================================
-- COMPLETION MESSAGE
-- ============================================================================

DO $$
BEGIN
  RAISE NOTICE '============================================================================';
  RAISE NOTICE 'CALENDAR EVENTS TABLE MIGRATION COMPLETE';
  RAISE NOTICE '============================================================================';
  RAISE NOTICE 'Created table: calendar_events';
  RAISE NOTICE '';
  RAISE NOTICE 'Features:';
  RAISE NOTICE '- Manual event creation with project linking';
  RAISE NOTICE '- Automatic event creation from project status changes';
  RAISE NOTICE '- Full date/time support with all-day events';
  RAISE NOTICE '- Event categorization and color coding';
  RAISE NOTICE '- Reminder functionality';
  RAISE NOTICE '- Location and description support';
  RAISE NOTICE '- Completion tracking';
  RAISE NOTICE '';
  RAISE NOTICE 'Event types supported:';
  RAISE NOTICE '- manual: User-created events';
  RAISE NOTICE '- interview: Auto-created from interview_scheduled status';
  RAISE NOTICE '- project_start: Auto-created from project start dates';
  RAISE NOTICE '- project_end: Auto-created from project end dates';
  RAISE NOTICE '- application_due: Manual deadline events';
  RAISE NOTICE '- follow_up: Manual follow-up reminders';
  RAISE NOTICE '- meeting: General meetings';
  RAISE NOTICE '';
  RAISE NOTICE 'Security: Row Level Security enabled with user isolation';
  RAISE NOTICE '============================================================================';
END $$;