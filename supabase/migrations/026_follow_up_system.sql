-- Follow-up System Migration
-- Creates tables for email follow-up tracking and templates

-- follow_up_templates: Reusable email templates for different follow-up scenarios
CREATE TABLE follow_up_templates (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid REFERENCES auth.users(id) ON DELETE CASCADE,
  name text NOT NULL,
  subject text NOT NULL,
  body text NOT NULL,
  trigger_days integer NOT NULL DEFAULT 7,
  status_trigger text NOT NULL, -- 'application_sent', 'interview_completed', etc.
  is_active boolean DEFAULT true,
  created_at timestamp DEFAULT now(),
  updated_at timestamp DEFAULT now()
);

-- follow_up_schedule: Planned follow-ups with timing and status
CREATE TABLE follow_up_schedule (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid REFERENCES auth.users(id) ON DELETE CASCADE,
  application_id uuid REFERENCES project_applications(id) ON DELETE CASCADE,
  template_id uuid REFERENCES follow_up_templates(id) ON DELETE CASCADE,
  scheduled_date timestamp NOT NULL,
  status text DEFAULT 'scheduled', -- 'scheduled', 'sent', 'dismissed', 'cancelled'
  sent_at timestamp,
  response_received boolean DEFAULT false,
  created_at timestamp DEFAULT now()
);

-- follow_up_history: Complete history of all sent follow-ups for analytics
CREATE TABLE follow_up_history (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid REFERENCES auth.users(id) ON DELETE CASCADE,
  application_id uuid REFERENCES project_applications(id) ON DELETE CASCADE,
  template_id uuid REFERENCES follow_up_templates(id) ON DELETE CASCADE,
  sent_at timestamp NOT NULL,
  subject text NOT NULL,
  body text NOT NULL,
  response_received boolean DEFAULT false,
  response_date timestamp,
  created_at timestamp DEFAULT now()
);

-- RLS Policies: Users can only access their own follow-up data
ALTER TABLE follow_up_templates ENABLE ROW LEVEL SECURITY;
ALTER TABLE follow_up_schedule ENABLE ROW LEVEL SECURITY;
ALTER TABLE follow_up_history ENABLE ROW LEVEL SECURITY;

-- follow_up_templates policies
CREATE POLICY "Users can view own templates" ON follow_up_templates
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own templates" ON follow_up_templates
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own templates" ON follow_up_templates
  FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete own templates" ON follow_up_templates
  FOR DELETE USING (auth.uid() = user_id);

-- follow_up_schedule policies
CREATE POLICY "Users can view own schedule" ON follow_up_schedule
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own schedule" ON follow_up_schedule
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own schedule" ON follow_up_schedule
  FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete own schedule" ON follow_up_schedule
  FOR DELETE USING (auth.uid() = user_id);

-- follow_up_history policies
CREATE POLICY "Users can view own history" ON follow_up_history
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own history" ON follow_up_history
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own history" ON follow_up_history
  FOR UPDATE USING (auth.uid() = user_id);

-- Default follow-up templates (will be inserted per user via application logic)
-- These are example templates that can be created via the UI

/*
INSERT INTO follow_up_templates (user_id, name, subject, body, trigger_days, status_trigger) VALUES
  (auth.uid(), 'Nach Bewerbung', 'Nachfrage zu meiner Bewerbung - {project_title}', 
   'Liebe/r {contact_person},\n\nvor {trigger_days} Tagen habe ich mich auf die Position {project_title} beworben und wollte höflich nachfragen, wie der aktuelle Stand des Auswahlverfahrens ist.\n\nFalls Sie weitere Informationen benötigen, stehe ich gerne zur Verfügung.\n\nVielen Dank für Ihr Feedback.\n\nBeste Grüße\n{user_name}', 
   7, 'application_sent'),
   
  (auth.uid(), 'Nach Interview', 'Nachfrage zum Auswahlverfahren - {project_title}', 
   'Liebe/r {contact_person},\n\nvielen Dank für das interessante Gespräch am {interview_date}. Wie besprochen, wollte ich mich nach dem aktuellen Stand des Auswahlverfahrens erkundigen.\n\nIch freue mich auf Ihr Feedback.\n\nBeste Grüße\n{user_name}', 
   3, 'interview_completed');
*/