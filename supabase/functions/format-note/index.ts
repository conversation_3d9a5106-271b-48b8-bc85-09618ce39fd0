import { serve } from "https://deno.land/std@0.168.0/http/server.ts"

const geminiApiKey = Deno.env.get('GEMINI_API_KEY')!

interface FormatNoteRequest {
  raw_note: string;
  context: {
    status_change: string;
    company_name?: string;
    project_name?: string;
  };
}

interface FormatNoteResponse {
  formatted_note: string;
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response(null, {
      status: 200,
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
        'Access-Control-Allow-Methods': 'POST, OPTIONS',
      },
    })
  }

  try {
    const { raw_note, context }: FormatNoteRequest = await req.json()

    if (!raw_note?.trim()) {
      throw new Error('Keine Notiz zum Formatieren vorhanden')
    }

    // Create intelligent prompt for note formatting
    const prompt = `Du bist ein Assistent für die Strukturierung von Geschäftsnotizen. 

AUFGABE: Strukturiere die folgende unformatierte Notiz zu einer klaren, professionellen Form.

KONTEXT:
- Status-Änderung: ${context.status_change}
- Unternehmen: ${context.company_name || 'Unbekannt'}
- Projekt: ${context.project_name || 'Unbekannt'}

ROHE NOTIZ:
"${raw_note}"

ANWEISUNGEN:
1. Strukturiere die Informationen logisch mit Emojis für bessere Lesbarkeit
2. Verwende klare Kategorien (📞 Gespräch, 📅 Termine, 📋 Aufgaben, 💡 Wichtige Punkte, etc.)
3. Trenne Fakten von Einschätzungen
4. Hebe wichtige Deadlines oder nächste Schritte hervor
5. Korrigiere offensichtliche Tippfehler
6. Behalte alle wichtigen Informationen bei
7. Formatiere für maximale Klarheit und Professionalität

FORMAT:
Verwende eine saubere Struktur mit Markdown-ähnlichen Elementen:
- Emojis für Kategorien
- Aufzählungen für Listen
- **Fett** für wichtige Informationen
- Klare Absätze zwischen verschiedenen Themen

AUSGABE NUR DIE FORMATIERTE NOTIZ (ohne Erklärungen oder "Hier ist..." Einleitungen):`

    // Call Gemini API
    const geminiResponse = await fetch(
      `https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash-latest:generateContent?key=${geminiApiKey}`,
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          contents: [{
            parts: [{
              text: prompt
            }]
          }],
          generationConfig: {
            temperature: 0.3, // Lower temperature for more consistent formatting
            topK: 20,
            topP: 0.8,
            maxOutputTokens: 1024,
          }
        })
      }
    )

    if (!geminiResponse.ok) {
      throw new Error('AI-Formatierung fehlgeschlagen')
    }

    const geminiData = await geminiResponse.json()
    const formattedNote = geminiData.candidates[0].content.parts[0].text.trim()

    const response: FormatNoteResponse = {
      formatted_note: formattedNote
    }

    return new Response(
      JSON.stringify(response),
      { 
        headers: { 
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
        } 
      }
    )

  } catch (error) {
    console.error('Error in format-note function:', error)
    return new Response(
      JSON.stringify({ error: error.message }),
      { 
        status: 400,
        headers: { 
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
        }
      }
    )
  }
})