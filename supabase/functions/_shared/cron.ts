// Shared cron configuration for Supabase Edge Functions
// This allows defining cron schedules per code instead of dashboard

export interface CronConfig {
  [functionName: string]: {
    schedule: string;
    timezone?: string;
  };
}

// Cron schedules for all functions
export const cronSchedules: CronConfig = {
  'check-notifications': {
    schedule: '*/5 * * * *', // Every 5 minutes
    timezone: 'Europe/Berlin' // Optional: specify timezone
  }
};

// Helper to validate cron expressions
export function validateCronExpression(expression: string): boolean {
  // Basic validation - real implementation would be more robust
  const parts = expression.split(' ');
  return parts.length === 5;
}

// Helper to get cron config for a function
export function getCronConfig(functionName: string) {
  return cronSchedules[functionName];
}