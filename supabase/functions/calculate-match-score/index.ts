import "https://deno.land/x/xhr@0.1.0/mod.ts";
import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2.7.1';

const geminiApiKey = Deno.env.get('GEMINI_API_KEY');
const supabaseUrl = Deno.env.get('SUPABASE_URL')!;
const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!;

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
};

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    const { applicationId, userCvText, forceRecalculate = false } = await req.json();

    if (!applicationId) {
      return new Response(
        JSON.stringify({ error: 'Application ID ist erforderlich' }),
        { 
          status: 400, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      );
    }

    // Get auth header and validate user
    const authHeader = req.headers.get('Authorization');
    if (!authHeader) {
      return new Response(
        JSON.stringify({ error: 'Authorization header fehlt' }),
        { 
          status: 401, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      );
    }

    const supabase = createClient(supabaseUrl, supabaseServiceKey);

    // Get user from JWT token
    const token = authHeader.replace('Bearer ', '');
    const { data: { user }, error: authError } = await supabase.auth.getUser(token);
    
    if (authError || !user) {
      return new Response(
        JSON.stringify({ error: 'Ungültiger Token' }),
        { 
          status: 401, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      );
    }

    // Get application details (verify ownership)
    const { data: application, error: appError } = await supabase
      .from('project_applications')
      .select('*')
      .eq('id', applicationId)
      .eq('user_id', user.id)
      .single();

    if (appError || !application) {
      console.error('Application fetch error:', appError);
      return new Response(
        JSON.stringify({ error: 'Bewerbung nicht gefunden' }),
        { 
          status: 404, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      );
    }

    // Note: Caching is handled by frontend via normal DB queries

    // Get user CV PDF URL and profile data
    let cvPdfUrl = null;
    let userSettings = null;
    if (!userCvText) {
      const { data: settings } = await supabase
        .from('user_settings')
        .select('cv_pdf_url, availability_start_date, availability_end_date, availability_hours_per_week, availability_notes, hourly_rate_eur, full_name, professional_email')
        .eq('user_id', user.id)
        .single();

      userSettings = settings;

      if (!userSettings?.cv_pdf_url) {
        return new Response(
          JSON.stringify({ error: 'Kein CV gefunden. Bitte laden Sie zuerst Ihren Lebenslauf hoch.' }),
          { 
            status: 400, 
            headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
          }
        );
      }

      cvPdfUrl = userSettings.cv_pdf_url;
    }

    // Prepare project requirements for AI analysis
    const projectRequirements = {
      project_name: application.project_name,
      company_name: application.company_name,
      required_skills: application.required_skills || [],
      project_description: application.project_description || '',
      budget_range: application.budget_range || '',
      work_location_type: application.work_location_type || '',
      remote_percentage: application.remote_percentage || null
    };

    // Build profile context for additional information
    let profileContext = '';
    if (userSettings) {
      profileContext = `
ZUSÄTZLICHE PROFIL-INFORMATIONEN:
• Verfügbarkeit ab: ${userSettings.availability_start_date || 'Nicht angegeben'}
• Verfügbarkeit bis: ${userSettings.availability_end_date || 'Unbegrenzt'}
• Stunden pro Woche: ${userSettings.availability_hours_per_week || 'Nicht angegeben'}
• Verfügbarkeits-Hinweise: ${userSettings.availability_notes || 'Keine'}
• Stundensatz: ${userSettings.hourly_rate_eur ? `${userSettings.hourly_rate_eur}€/h` : 'Nicht angegeben'}
• Name: ${userSettings.full_name || 'Nicht angegeben'}
• E-Mail: ${userSettings.professional_email || 'Nicht angegeben'}
      `;
    }

    // Create AI prompt for skill matching with PDF
    const prompt = `
    Analysiere die Übereinstimmung zwischen dem Lebenslauf (PDF) und den Projektanforderungen. 
    Gib eine Bewertung von 0-100% und eine detaillierte Begründung zurück.

    PROJEKTANFORDERUNGEN:
    Projekt: ${projectRequirements.project_name}
    Firma: ${projectRequirements.company_name}
    Beschreibung: ${projectRequirements.project_description}
    Benötigte Skills: ${projectRequirements.required_skills.join(', ')}
    Budget: ${projectRequirements.budget_range}
    Arbeitsort: ${projectRequirements.work_location_type} ${projectRequirements.remote_percentage ? `(${projectRequirements.remote_percentage}% remote)` : ''}
    ${profileContext}

    Bewerte die Übereinstimmung basierend auf:
    1. Technische Skills (40% der Bewertung)
    2. Relevante Berufserfahrung (30% der Bewertung)  
    3. Branchenerfahrung (20% der Bewertung)
    4. Verfügbarkeit/Arbeitsmodell (10% der Bewertung)

    WICHTIGE BEWERTUNGSRICHTLINIEN:
    - Wenn kein Projekt-Budget angegeben: Bewerte Stundensatz als "marktüblich" oder "angemessen für Seniority-Level"
    - Verfügbarkeit: Auch zukünftige Verfügbarkeit positiv bewerten
    - Remote/Onsite: 100% Remote immer als Vorteil bewerten (flexible Arbeitsweise)
    - Fehlende Skills: Nur abwerten wenn sie absolut kritisch sind

    Gib das Ergebnis als JSON zurück:
    {
      "match_score": [BERECHNETER_GESAMTSCORE],
      "reasoning": "Formatierte Bewertung mit Icons und Struktur (siehe Format unten)"
    }

    WICHTIG ZUR SCORE-BERECHNUNG:
    - Vergib realistische Scores von 0-100 (nicht immer 75!)
    - Berechne den Gesamtscore als SUMME der Teilscores (intern)
    - Bei sehr guter Übereinstimmung: 90-100%
    - Bei guter Übereinstimmung: 70-89% 
    - Bei mittlerer Übereinstimmung: 50-69%
    - Bei schlechter Übereinstimmung: 0-49%

    WICHTIGES FORMAT für 'reasoning':
    Nutze dieses EXAKTE Format mit Emojis und klarer Struktur:

    ## 🎯 Match-Analyse: [SCORE]%

    ### 💻 Technische Skills (40% Gewichtung)
    **Score: [X]/40**
    • ✅ Vorhandene Skills: [Liste der passenden Skills]
    • ❌ Fehlende Skills: [Liste der fehlenden Skills]  
    • 🔄 Übertragbare Skills: [Skills die ähnlich/relevant sind]

    ### 👔 Berufserfahrung (30% Gewichtung)  
    **Score: [X]/30**
    • 📅 Erfahrungsjahre: [Bewertung der Erfahrung]
    • 🎯 Relevante Projekte: [Passende Projekterfahrung]
    • 📈 Seniority Level: [Junior/Mid/Senior Einschätzung]

    ### 🏢 Branchenerfahrung (20% Gewichtung)
    **Score: [X]/20** 
    • 🎯 Branchenpassung: [Relevante Branchenerfahrung]
    • 💼 Unternehmenstyp: [Startup/Corporate/etc.]

    ### ⏰ Verfügbarkeit & Arbeitsmodell (10% Gewichtung)
    **Score: [X]/10**
    • 📍 Remote/Onsite: [Arbeitsmodell-Kompatibilität]
    • 🗓️ Verfügbarkeit: [Zeitliche Verfügbarkeit]
    • 💰 Budget-Match: [Bewerte auch wenn kein Projekt-Budget angegeben ist: "Stundensatz X€/h - marktüblich für Senior-Level" oder "Stundensatz nicht angegeben"]

    ### 📊 Fazit
    **Gesamtscore: [SUMME]%**
    🟢 **Stärken:** [2-3 Hauptstärken]
    🟡 **Verbesserungspotential:** [1-2 Schwächen]
    💡 **Empfehlung:** [Klare Handlungsempfehlung]

    WICHTIG: 
    - Nutze EXAKT diese Emoji-Icons und Struktur
    - Sei konkret und spezifisch
    - Gib realistische Scores für jeden Bereich (nicht immer das Gleiche!)
    - BERECHNE den Gesamtscore korrekt: Summe aller Teilscores
    - Der match_score im JSON MUSS der berechneten Summe entsprechen
    - Gib nur das JSON zurück
    `;

    // Prepare request body for Gemini with PDF
    const requestBody = {
      contents: [{
        parts: []
      }],
      generationConfig: {
        temperature: 0.5,
        topK: 40,
        topP: 0.95,
        maxOutputTokens: 1024,
      },
    };

    // Add PDF if available, otherwise use provided text
    if (cvPdfUrl) {
      try {
        console.log('Loading CV PDF from:', cvPdfUrl);
        
        // Extract file path from URL - handle both public URLs and direct paths
        let filePath = '';
        if (cvPdfUrl.includes('cv-uploads/')) {
          // Handle public URL format
          const urlParts = cvPdfUrl.split('/');
          const bucketIndex = urlParts.findIndex(part => part === 'cv-uploads');
          if (bucketIndex !== -1) {
            filePath = urlParts.slice(bucketIndex + 1).join('/');
          }
        } else {
          // Handle direct path format
          filePath = cvPdfUrl;
        }
        
        console.log('Attempting to download CV from path:', filePath);
        
        const { data: fileData, error: downloadError } = await supabase.storage
          .from('cv-uploads')
          .download(filePath);
          
        if (!downloadError && fileData) {
          // Convert to base64
          const arrayBuffer = await fileData.arrayBuffer();
          const uint8Array = new Uint8Array(arrayBuffer);
          let binaryString = '';
          
          const chunkSize = 8192;
          for (let i = 0; i < uint8Array.length; i += chunkSize) {
            const chunk = uint8Array.slice(i, i + chunkSize);
            binaryString += String.fromCharCode.apply(null, Array.from(chunk));
          }
          
          const pdfBase64 = btoa(binaryString);
          console.log('CV PDF converted to base64, size:', pdfBase64.length);
          
          requestBody.contents[0].parts.push({
            inline_data: {
              mime_type: "application/pdf",
              data: pdfBase64
            }
          });
        } else {
          console.error('Error downloading CV:', downloadError);
          throw new Error('CV-PDF konnte nicht heruntergeladen werden');
        }
      } catch (pdfError) {
        console.error('Error processing CV PDF:', pdfError);
        throw new Error('Fehler beim Verarbeiten der CV-PDF');
      }
    } else if (userCvText) {
      // Add CV text directly to prompt
      const cvPrompt = `
      LEBENSLAUF (Text):
      ${userCvText}
      
      ` + prompt;
      
      requestBody.contents[0].parts.push({
        text: cvPrompt
      });
      // Note: Will use this modified prompt instead of adding prompt again below
    }

    // Add text prompt (only if not already added with userCvText)
    if (!userCvText) {
      requestBody.contents[0].parts.push({
        text: prompt
      });
    }

    const response = await fetch(`https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash-latest:generateContent?key=${geminiApiKey}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(requestBody),
    });

    if (!response.ok) {
      console.error('Gemini API Error:', response.status, response.statusText);
      throw new Error(`Gemini API Error: ${response.status}`);
    }

    const data = await response.json();
    console.log('Gemini API Response:', JSON.stringify(data, null, 2));

    const generatedText = data.candidates?.[0]?.content?.parts?.[0]?.text;
    
    if (!generatedText) {
      throw new Error('Keine Antwort von Gemini API erhalten');
    }

    // Parse AI response
    let matchResult;
    try {
      const cleanText = generatedText.replace(/```json\n?/g, '').replace(/```\n?/g, '').trim();
      matchResult = JSON.parse(cleanText);
    } catch (parseError) {
      console.error('JSON Parse Error:', parseError);
      console.error('Generated Text:', generatedText);
      throw new Error('AI-Antwort konnte nicht geparst werden');
    }

    // Validate match score
    const matchScore = Math.max(0, Math.min(100, parseInt(matchResult.match_score) || 0));
    const matchReasoning = matchResult.reasoning || 'Keine Begründung verfügbar';

    // Return calculated score - let frontend handle DB update
    return new Response(JSON.stringify({ 
      matchScore,
      matchReasoning,
      cached: false
    }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
    });

  } catch (error) {
    console.error('Error in calculate-match-score function:', error);
    return new Response(
      JSON.stringify({ 
        error: error.message || 'Ein unbekannter Fehler ist aufgetreten' 
      }), 
      {
        status: 500,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      }
    );
  }
});