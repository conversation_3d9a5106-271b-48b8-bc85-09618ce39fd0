import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2';

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
};

interface NotificationData {
  user_id: string;
  type: string;
  priority: string;
  title: string;
  message: string;
  metadata: Record<string, unknown>;
  action_url?: string;
  action_label?: string;
  expires_at?: string;
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders });
  }

  const startTime = Date.now();
  console.log('🔔 Starting notification check process');

  try {
    // Initialize Supabase client with service role key
    const supabaseUrl = Deno.env.get('SUPABASE_URL');
    const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY');
    
    if (!supabaseUrl || !supabaseServiceKey) {
      throw new Error('Missing Supabase configuration');
    }

    const supabase = createClient(supabaseUrl, supabaseServiceKey);
    
    const results = {
      followUps: 0,
      interviews: 0,
      applications: 0,
      errors: 0
    };

    // Check follow-ups
    try {
      const followUpCount = await checkFollowUpNotifications(supabase);
      results.followUps = followUpCount;
      console.log(`✅ Follow-ups checked: ${followUpCount} notifications created`);
    } catch (error) {
      console.error('❌ Error checking follow-ups:', error.message);
      results.errors++;
    }

    // Check interviews
    try {
      const interviewCount = await checkInterviewNotifications(supabase);
      results.interviews = interviewCount;
      console.log(`✅ Interviews checked: ${interviewCount} notifications created`);
    } catch (error) {
      console.error('❌ Error checking interviews:', error.message);
      results.errors++;
    }

    // Check application reminders
    try {
      const applicationCount = await checkApplicationReminders(supabase);
      results.applications = applicationCount;
      console.log(`✅ Applications checked: ${applicationCount} notifications created`);
    } catch (error) {
      console.error('❌ Error checking applications:', error.message);
      results.errors++;
    }

    const duration = Date.now() - startTime;
    console.log(`🎉 Notification check completed in ${duration}ms`, results);

    return new Response(
      JSON.stringify({ 
        success: true, 
        duration,
        ...results
      }),
      { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    );

  } catch (error) {
    console.error('💥 Critical error in notification check:', error.message);
    return new Response(
      JSON.stringify({ 
        error: error.message,
        success: false
      }),
      { 
        status: 500,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      }
    );
  }
});

async function checkFollowUpNotifications(supabase: any): Promise<number> {
  const today = new Date();
  const todayStart = new Date(today.getFullYear(), today.getMonth(), today.getDate());
  const todayEnd = new Date(todayStart.getTime() + 24 * 60 * 60 * 1000 - 1);

  // Get scheduled follow-ups that are due today
  const { data: followUps, error } = await supabase
    .from('follow_up_schedule')
    .select(`
      id, user_id, application_id, scheduled_date,
      template:follow_up_templates(name),
      application:project_applications(project_name, company_name, contact_id, contacts(name))
    `)
    .eq('status', 'scheduled')
    .gte('scheduled_date', todayStart.toISOString())
    .lt('scheduled_date', todayEnd.toISOString());

  if (error) {
    throw new Error(`Failed to fetch follow-ups: ${error.message}`);
  }

  let createdCount = 0;

  for (const followUp of followUps || []) {
    try {
      // App-side duplicate check - check if notification already exists
      const { data: existing } = await supabase
        .from('notifications')
        .select('id')
        .eq('user_id', followUp.user_id)
        .eq('type', 'follow_up_due')
        .eq('related_entity_id', followUp.id.toString())
        .gte('created_at', new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString()) // Last 24 hours
        .maybeSingle();

      if (!existing) {
        const notification: NotificationData = {
          user_id: followUp.user_id,
          type: 'follow_up_due',
          priority: 'high',
          title: 'Follow-up fällig',
          message: `Geplantes Follow-up für ${followUp.application?.project_name || 'Bewerbung'} bei ${followUp.application?.company_name || 'Unbekannt'} ist heute fällig`,
          metadata: {
            follow_up_id: followUp.id,
            application_id: followUp.application_id,
            template_name: followUp.template?.name || 'Follow-up',
            scheduled_date: followUp.scheduled_date,
            company_name: followUp.application?.company_name
          },
          action_url: `/applications/${followUp.application_id}`,
          action_label: 'Follow-up senden',
          expires_at: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
          related_entity_id: followUp.id.toString()
        };

        const { error: insertError } = await supabase
          .from('notifications')
          .insert(notification);

        if (insertError) {
          console.error(`Failed to create notification for follow-up ${followUp.id}:`, insertError.message);
        } else {
          createdCount++;
        }
      }
    } catch (error) {
      console.error(`Error processing follow-up ${followUp.id}:`, error.message);
    }
  }

  return createdCount;
}

async function checkInterviewNotifications(supabase: any): Promise<number> {
  const tomorrow = new Date();
  tomorrow.setDate(tomorrow.getDate() + 1);
  const tomorrowStr = tomorrow.toISOString().split('T')[0];

  // Get interviews scheduled for tomorrow
  const { data: interviews, error } = await supabase
    .from('project_applications')
    .select('id, user_id, project_name, company_name, interview_date, interview_time')
    .eq('status', 'interview_scheduled')
    .not('interview_date', 'is', null)
    .lte('interview_date', tomorrowStr);

  if (error) {
    throw new Error(`Failed to fetch interviews: ${error.message}`);
  }

  let createdCount = 0;

  for (const interview of interviews || []) {
    try {
      // App-side duplicate check - check if notification already exists
      const { data: existing } = await supabase
        .from('notifications')
        .select('id')
        .eq('user_id', interview.user_id)
        .eq('type', 'interview_reminder')
        .eq('related_entity_id', interview.id.toString())
        .gte('created_at', new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString()) // Last 24 hours
        .maybeSingle();

      if (!existing) {
        // Calculate hours until interview
        const interviewDateTime = interview.interview_time
          ? new Date(`${interview.interview_date}T${interview.interview_time}`)
          : new Date(`${interview.interview_date}T09:00:00`);
        
        const hoursUntil = (interviewDateTime.getTime() - Date.now()) / (1000 * 60 * 60);

        // Only create notification if interview is within 24 hours
        if (hoursUntil <= 24 && hoursUntil > 0) {
          const timeText = hoursUntil < 1 
            ? 'in weniger als einer Stunde'
            : hoursUntil < 24
              ? `in ${Math.round(hoursUntil)} Stunde${Math.round(hoursUntil) > 1 ? 'n' : ''}`
              : `in ${Math.round(hoursUntil / 24)} Tag${Math.round(hoursUntil / 24) > 1 ? 'en' : ''}`;

          const notification: NotificationData = {
            user_id: interview.user_id,
            type: 'interview_reminder',
            priority: hoursUntil <= 2 ? 'urgent' : hoursUntil <= 24 ? 'high' : 'normal',
            title: 'Interview-Termin',
            message: `Interview für ${interview.project_name} bei ${interview.company_name} ${timeText}`,
            metadata: {
              application_id: interview.id,
              application_name: interview.project_name,
              company_name: interview.company_name,
              interview_date: interview.interview_date,
              hours_until_interview: hoursUntil
            },
            action_url: `/applications/${interview.id}`,
            action_label: 'Vorbereitung',
            expires_at: interviewDateTime.toISOString(),
            related_entity_id: interview.id.toString()
          };

          const { error: insertError } = await supabase
            .from('notifications')
            .insert(notification);

          if (insertError) {
            console.error(`Failed to create notification for interview ${interview.id}:`, insertError.message);
          } else {
            createdCount++;
          }
        }
      }
    } catch (error) {
      console.error(`Error processing interview ${interview.id}:`, error.message);
    }
  }

  return createdCount;
}

async function checkApplicationReminders(supabase: any): Promise<number> {
  const oneWeekAgo = new Date();
  oneWeekAgo.setDate(oneWeekAgo.getDate() - 7);
  
  // Get applications that are at least 1 week old and still waiting for response
  // Include all relevant statuses where we might want to send follow-ups
  const { data: allApplications, error: appsError } = await supabase
    .from('project_applications')
    .select('id, user_id, project_name, company_name, application_date, status, contact_id, contacts(name)')
    .in('status', ['application_sent', 'not_applied']) // Include applications that haven't been sent yet but should be followed up on
    .lte('application_date', oneWeekAgo.toISOString().split('T')[0]); // At least 1 week old, no upper limit

  if (appsError) {
    throw new Error(`Failed to fetch applications: ${appsError.message}`);
  }

  if (!allApplications?.length) {
    return 0;
  }

  // Step 2: Filter out applications that have planned follow-ups
  const appIds = allApplications.map(app => app.id);
  const { data: plannedFollowUps, error: followUpError } = await supabase
    .from('follow_up_schedule')
    .select('application_id')
    .in('application_id', appIds)
    .in('status', ['scheduled', 'sent']);

  if (followUpError) {
    throw new Error(`Failed to check follow-up status: ${followUpError.message}`);
  }

  const appsWithFollowUps = new Set(plannedFollowUps?.map(f => f.application_id) || []);
  const applications = allApplications.filter(app => !appsWithFollowUps.has(app.id));

  let createdCount = 0;

  for (const app of applications || []) {
    try {
      // Calculate notification details first
      const daysSince = Math.floor(
        (Date.now() - new Date(app.application_date).getTime()) / (1000 * 60 * 60 * 24)
      );

      const notificationType = daysSince >= 14 ? 'follow_up_suggestion_urgent' : 'follow_up_suggestion';
      
      // App-side duplicate check - check if notification already exists for this type
      const { data: existing, error: checkError } = await supabase
        .from('notifications')
        .select('id')
        .eq('user_id', app.user_id)
        .eq('type', notificationType)
        .eq('related_entity_id', app.id.toString())
        .gte('created_at', new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString()) // Last 7 days
        .limit(1);

      if (checkError) {
        console.error(`Error checking for duplicates for app ${app.id}:`, checkError.message);
      }

      if (!existing || existing.length === 0) {
        const title = daysSince >= 14 ? 'Follow-up dringend empfohlen' : 'Follow-up erwägen';
        
        let message: string;
        if (app.status === 'not_applied') {
          message = daysSince >= 14 
            ? `Bewerbung "${app.project_name}" bei ${app.company_name || 'Unbekannt'} ist seit ${daysSince} Tagen offen - jetzt bewerben?`
            : `Bewerbung "${app.project_name}" bei ${app.company_name || 'Unbekannt'} ist seit ${daysSince} Tagen offen - Follow-up planen?`;
        } else {
          message = daysSince >= 14 
            ? `Bereits ${daysSince} Tage ohne Antwort für "${app.project_name}" bei ${app.company_name || 'Unbekannt'} - Follow-up jetzt senden?`
            : `${daysSince} Tage ohne Antwort für "${app.project_name}" bei ${app.company_name || 'Unbekannt'} - Follow-up planen?`;
        }

        const notification: NotificationData = {
          user_id: app.user_id,
          type: notificationType,
          priority: daysSince >= 14 ? 'high' : 'normal',
          title,
          message,
          metadata: {
            application_id: app.id,
            application_name: app.project_name,
            company_name: app.company_name,
            contact_name: app.contacts?.name,
            days_since_application: daysSince,
            status: app.status
          },
          action_url: `/applications/${app.id}`,
          action_label: 'Follow-up planen',
          expires_at: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
          related_entity_id: app.id.toString()
        };

        const { error: insertError } = await supabase
          .from('notifications')
          .insert(notification);

        if (insertError) {
          console.error(`Failed to create notification for application ${app.id}:`, insertError.message);
        } else {
          createdCount++;
        }
      }
    } catch (error) {
      console.error(`Error processing application ${app.id}:`, error.message);
    }
  }

  return createdCount;
}