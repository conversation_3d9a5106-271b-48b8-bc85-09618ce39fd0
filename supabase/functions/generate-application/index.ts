import "https://deno.land/x/xhr@0.1.0/mod.ts";
import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2';

const geminiApiKey = Deno.env.get('GEMINI_API_KEY');
const supabaseUrl = Deno.env.get('SUPABASE_URL')!;
const supabaseKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!;

const supabase = createClient(supabaseUrl, supabaseKey);

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
};

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    const { projectDescription, companyName, projectName, requiredSkills, contactPerson } = await req.json();
    
    // Normalize contactPerson to handle edge cases
    const normalizedContactPerson = contactPerson && 
      contactPerson.trim() !== '' && 
      contactPerson.toLowerCase() !== 'nicht angegeben' && 
      contactPerson.toLowerCase() !== 'not specified' && 
      contactPerson.toLowerCase() !== 'not provided' && 
      contactPerson.toLowerCase() !== 'n/a' && 
      contactPerson.toLowerCase() !== 'none' ? contactPerson.trim() : null;
    
    // Get the Authorization header to extract user
    const authHeader = req.headers.get('Authorization');
    if (!authHeader) {
      return new Response(
        JSON.stringify({ error: 'Authorization header fehlt' }),
        { 
          status: 401, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      );
    }

    // Get user from JWT token
    const token = authHeader.replace('Bearer ', '');
    const { data: { user }, error: authError } = await supabase.auth.getUser(token);
    
    if (authError || !user) {
      return new Response(
        JSON.stringify({ error: 'Ungültiger Token' }),
        { 
          status: 401, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      );
    }

    const userId = user.id;

    if (!projectDescription) {
      return new Response(
        JSON.stringify({ error: 'Projektbeschreibung ist erforderlich' }),
        { 
          status: 400, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      );
    }

    // Get user settings for availability, CV PDF, and hourly rate
    const { data: userSettings, error: settingsError } = await supabase
      .from('user_settings')
      .select('*')
      .eq('user_id', userId)
      .maybeSingle();
    
    if (settingsError) {
      console.error('Error fetching user settings:', settingsError);
    }
    
    console.log('User settings retrieved:', userSettings ? 'Success' : 'No settings found');
    if (userSettings) {
      console.log('CV PDF URL:', userSettings.cv_pdf_url ? 'Present' : 'Missing');
      console.log('Hourly rate:', userSettings.hourly_rate_eur || 'Not set');
      console.log('Availability start:', userSettings.availability_start_date || 'Not set');
    }

    // Build availability context
    let availabilityContext = '';
    let cvFileData = null;
    
    if (userSettings) {
      // Availability information
      if (userSettings.availability_start_date) {
        availabilityContext += `\nVerfügbar ab: ${userSettings.availability_start_date}`;
      }
      if (userSettings.availability_end_date) {
        availabilityContext += `\nVerfügbar bis: ${userSettings.availability_end_date}`;
      }
      if (userSettings.availability_hours_per_week) {
        availabilityContext += `\nVerfügbare Stunden pro Woche: ${userSettings.availability_hours_per_week}`;
      }
      if (userSettings.availability_notes) {
        availabilityContext += `\nVerfügbarkeits-Notizen: ${userSettings.availability_notes}`;
      }

      // Get CV PDF if available
      if (userSettings.cv_pdf_url) {
        try {
          console.log('Loading CV PDF from:', userSettings.cv_pdf_url);
          
          // Extract file path from URL - handle both public URLs and direct paths
          let filePath = '';
          if (userSettings.cv_pdf_url.includes('cv-uploads/')) {
            // Handle public URL format
            const urlParts = userSettings.cv_pdf_url.split('/');
            const bucketIndex = urlParts.findIndex(part => part === 'cv-uploads');
            if (bucketIndex !== -1) {
              filePath = urlParts.slice(bucketIndex + 1).join('/');
            }
          } else {
            // Handle direct path format
            filePath = userSettings.cv_pdf_url;
          }
          
          console.log('Attempting to download CV from path:', filePath);
          
          const { data: fileData, error: downloadError } = await supabase.storage
            .from('cv-uploads')
            .download(filePath);

          if (!downloadError && fileData) {
            // Convert to base64
            const arrayBuffer = await fileData.arrayBuffer();
            const uint8Array = new Uint8Array(arrayBuffer);
            let binaryString = '';
            
            const chunkSize = 8192;
            for (let i = 0; i < uint8Array.length; i += chunkSize) {
              const chunk = uint8Array.slice(i, i + chunkSize);
              binaryString += String.fromCharCode.apply(null, Array.from(chunk));
            }
            
            cvFileData = btoa(binaryString);
            console.log('CV PDF loaded successfully, size:', cvFileData.length);
          } else {
            console.error('Error downloading CV:', downloadError);
            console.error('File path attempted:', filePath);
          }
        } catch (error) {
          console.error('Error processing CV PDF:', error);
        }
      } else {
        console.log('No CV PDF URL found in user settings');
      }
    }

    // Detect language of the project description
    const languageDetectionPrompt = `
    Analyze the following text and determine if it's written in German or English. 
    Return only "german" or "english" (lowercase, single word):
    
    "${projectDescription}"
    `;

    const langResponse = await fetch(`https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash-lite:generateContent?key=${geminiApiKey}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        contents: [{
          parts: [{
            text: languageDetectionPrompt
          }]
        }],
        generationConfig: {
          temperature: 0.1,
          topK: 1,
          topP: 1,
          maxOutputTokens: 50,
        },
      }),
    });

    const langData = await langResponse.json();
    const detectedLanguage = langData.candidates?.[0]?.content?.parts?.[0]?.text?.trim().toLowerCase() || 'german';
    const isEnglish = detectedLanguage === 'english';

    // Build prompt parts
    const promptParts = [
      {
        text: isEnglish ? `
    You are an experienced freelancer writing a professional application in English for the following project:

    Project Name: ${projectName || 'Not specified'}
    Company: ${companyName || 'Not specified'}
    Contact Person: ${normalizedContactPerson || 'Not specified'}
    Required Skills: ${requiredSkills?.join(', ') || 'Not specified'}
    Project Description: ${projectDescription}${availabilityContext ? `\n\nAvailability: ${availabilityContext}` : ''}${userSettings?.hourly_rate_eur ? `\nHourly Rate: €${userSettings.hourly_rate_eur} (net)` : ''}
    
    PERSONAL INFORMATION TO USE:
    - Name: ${userSettings?.full_name || 'Not provided'}
    - Email: ${userSettings?.professional_email || 'Not provided'}
    - Phone: ${userSettings?.phone || 'Not provided'}
    - Website: ${userSettings?.website || 'Not provided'}
    - Address: ${userSettings?.address || 'Not provided'}

    ${cvFileData ? 'ABSOLUTELY CRITICAL RULES FOR CV ANALYSIS:\n\n1. STRICT CV-ONLY RULE: Use ONLY skills, technologies, tools, methodologies, and experiences that are EXPLICITLY mentioned in the CV text. NEVER invent, assume, or extrapolate ANY skills.\n\n2. UNIVERSAL FREELANCER APPROACH: This generator works for ALL freelancer types (frontend/backend developers, DevOps engineers, UI/UX designers, digital marketers, content writers, project managers, data analysts, etc.). Adapt language, terminology, and focus to match the specific CV content and project type - never assume specific technologies or methodologies.\n\n3. INTELLIGENT SKILL POSITIONING: If a required skill is not explicitly mentioned in the CV, use professional positioning with transferable skills from the CV. Only reference and build upon skills that are explicitly mentioned in the CV text.\n\n4. EXACT CV TEXT MATCHING: Only reference:\n   - Skills/technologies/tools explicitly named in the CV\n   - Companies explicitly named in the CV  \n   - Positions/roles explicitly mentioned in the CV\n   - Certifications explicitly listed in the CV\n   - Projects/experiences explicitly described in the CV\n   - Industries/domains explicitly mentioned in the CV\n\n5. PROFESSIONAL POSITIONING vs FORBIDDEN INVENTION:\n   ALLOWED: Position CV skills as transferable (e.g., "Technology A experience, applicable to Technology B")\n   FORBIDDEN: Invent skills not in CV:\n   - Technical skills not explicitly mentioned\n   - Software/tools not explicitly listed  \n   - Methodologies not explicitly stated\n   - Industry experience not explicitly described\n   - Any capability not mentioned in the CV\n\n6. SELECTIVE WORK EXPERIENCE: Only mention companies and positions that are explicitly named in the CV. Only describe experiences that are explicitly written in the CV. STRATEGIC: Review CV work history and select TOP 2-3 MOST relevant companies (not all) - prioritize recent positions and direct skill matches for optimal response rates.\n\n7. SAFE APPROACH: If unsure whether something is in the CV, DO NOT mention it. Better to have a shorter application than to invent skills.\n\nANALYZE THE CV WORD BY WORD. USE ONLY WHAT IS EXPLICITLY WRITTEN.' : 'WARNING: No CV/Resume provided. Do NOT mention any specific skills, technologies, tools, or work experience. Keep the application generic and focus on availability, rate, and willingness to learn.'}

    Write a professional, high-converting freelancer application that shows expertise while sparking intelligent curiosity:
    
    TONE AND STYLE:
    - Write in natural, human language - NEVER sound mechanical or AI-generated
    - Use varied sentence structures and natural phrasing
    - Avoid repetitive phrases or robotic language style
    - Write authentically and personally, as an experienced freelancer would write themselves
    - ANTI-REPETITION: Mention companies only ONCE per application, not with each skill or technology
    - EXAMPLE: Follow this natural, adaptable style: "Through my extensive consulting experience at [relevant companies from CV], I am experienced in working with cross-functional teams and delivering high-quality solutions that meet client requirements and business objectives."

     GREETING STRATEGY:
     - If Contact Person is provided and contains a name: Extract the last name and use "Dear Mr./Ms. [LastName]"
     - If Contact Person contains only title or role (like "Recruiter", "HR"): Use "Dear Hiring Manager"
     - If Contact Person is empty, null, or "Not specified": Use "Dear Sir/Madam"
     - Always thank for the project opportunity and show specific interest in the project

     INTELLIGENT SKILL MATCHING RULES (CV-BASED WITH PROFESSIONAL POSITIONING):
     - PRIMARY RULE: Only mention skills, technologies, tools, and methodologies that are EXPLICITLY written in the CV text
     - PROFESSIONAL POSITIONING: When your CV experience directly transfers to project requirements, intelligently position it:
       * Example: CV has "AWS EKS" + job requires "Azure AKS" → "Extensive KUBERNETES experience with AWS EKS, directly transferable to AKS environments"
       * Example: CV has "React" + job requires "Vue" → "Strong component-based frontend experience with REACT, easily applicable to Vue.js projects"
       * Example: CV has "PostgreSQL" + job requires "MySQL" → "Deep SQL database expertise with POSTGRESQL, transferable to MySQL environments"
     - TRANSFERABILITY LANGUAGE: Use phrases like "transferable to", "applicable to", "relevant experience with", "directly applicable"
     - NO CLAIMING: Never claim you HAVE the exact required skill if it's not in your CV
     - USE UPPERCASE for CV technologies that are directly relevant to project requirements
     - CERTIFICATIONS: Only mention certifications EXPLICITLY stated in CV text and project-relevant
     - COMPANIES: Only mention companies EXPLICITLY named in the CV
     - EXPERIENCE AREAS: Only reference work domains, industries, or project types explicitly mentioned in the CV
     - BALANCE: Professional self-marketing while staying truthful to CV content

     ENGAGEMENT APPROACH:
     - Focus on recruiter-friendly communication (most contacts are recruiters, not end clients)
     - Show genuine interest in the project requirements and desired outcomes
     - Demonstrate understanding of business value and project success factors
     - Keep language accessible to both technical and non-technical readers
     - COMPANY CONTEXT: Company mentioned is often recruiter/agency, not end client - NEVER mention company directly
     - COMPACTNESS: Keep application brief and concise - maximum 300 words TOTAL
     - WORD LIMIT: Count words and strictly adhere to the 300-word limit
     - STRUCTURE: Use bullet points for core competencies - NEVER long flowing text paragraphs
     - CLOSING: Use professional phrasing like "I would be happy to support your client remotely with the implementation of their [project type] projects and bring my comprehensive expertise profitably."

     STRUCTURE:
     - Personal greeting with contact person name (if available)
     - Brief introduction of overall experience (years in field) with reference to current/relevant position
     - Structured section "Relation to requirements:" with intelligent skill matching
     - SELECTIVE BUT COMPREHENSIVE WORK EXPERIENCE: Create detailed paragraph mentioning 2-3 most relevant employers from CV with specific project-relevant activities at each company. Ensure multiple companies are mentioned when available.
     - Clear availability date
     - Explicit hourly rate and daily rate${userSettings?.hourly_rate_eur ? ` (€${userSettings.hourly_rate_eur} net per hour, alternatively €${Math.round(userSettings.hourly_rate_eur * 8)} net per day)` : ''}
     - Structured contact details with text labels (Email:, Phone:, Website:)
     - Professional closing with full name: ${userSettings?.full_name || 'Your name not provided'}
     
     FORMATTING - STRICTLY ENFORCED:
     - ABSOLUTELY NO ** or markdown formatting ANYWHERE in the application
     - NEVER use **text** or any markdown - this will BREAK the application
     - Use ONLY plain text with UPPERCASE for key skills/technologies
     - Plain text format for easy copy-paste into Outlook/portals
     - Clean, structured layout without special characters

     PROVEN FREELANCER SUCCESS STRUCTURE (professional FreelancerMap-inspired):
     1. Personal greeting (with name if available) + brief interest in project posting
     2. One sentence overall experience with current position at concrete employer
     3. Core competencies - EXACTLY 5-6 bullets ONLY:
        - COUNT YOUR BULLETS: Must be exactly 5 or 6 bullets, never more
        - Format: "TECHNOLOGY: Brief explanation" (plain text, no markdown)
        - CONSOLIDATION RULES:
          * If GO mentioned once, NEVER mention it again
          * If CI/CD mentioned, do NOT create separate "CI/CD-Tools" bullet
          * Group: Container + Kubernetes = one bullet
          * Group: Development languages = one bullet  
          * Group: CI/CD + automation = one bullet
        - FORBIDDEN: Same technology mentioned twice
        - FORBIDDEN: More than 6 bullets
        - Check: Count bullets before finishing - must be 5-6 maximum
     4. SELECTIVE WORK EXPERIENCE paragraph mentioning TOP 2-3 MOST relevant employers from CV:
        - MANDATORY: Include 2-3 companies (not just 1) with highest project relevance 
        - Prioritize: Current/recent position + 1-2 companies with direct skill matches
        - One concise sentence per company about key project-relevant activities
        - Structure: "At [COMPANY A], I [key relevant activity]. At [COMPANY B], I [key relevant activity]. At [COMPANY C], I [key relevant activity]."
        - CRITICAL: If multiple companies have relevant experience, mention them - don't default to just the most recent
        - FOCUS: Quality over quantity but ensure 2-3 companies are mentioned when available
     5. Professional closing with readiness to support the project
     6. Clear availability date
     7. Explicit hourly and daily rates
     8. Structured contact details with text labels (Email:, Phone:, Website:)
     9. Professional closing with full name

     PSYCHOLOGY TACTICS FOR HIGH RESPONSE:
     - Show you understand their specific requirements and challenges
     - Demonstrate strategic thinking beyond just execution
     - Use confident language ("I will" vs "I can")  
     - Focus on business value and project success
     - Create urgency with your availability window

     SPECIAL REQUIREMENTS HANDLING:
     - Search project text for "requirements" keywords but create NO Must-Have/Nice-to-Have separation
     - Extract project IDs (e.g. "ID: 29606") and mention them subtly in text
     - Treat language requirements as important if mentioned
     - CERTIFICATIONS: Only mention certifications EXPLICITLY stated in CV and project-relevant
     - Mention alternative contact options if multiple present in project text
     
     FINAL VERIFICATION RULES:
     - Double-check that every technology mentioned is explicitly written in the CV
     - Double-check that every company mentioned is explicitly named in the CV
     - Double-check that every skill claimed is explicitly stated in the CV
     - Remove any content that cannot be verified from the CV text
     - FORMATTING CHECK: Remove ALL ** bold formatting and markdown - use only plain text with UPPERCASE for technologies
     - BULLET COUNT CHECK: Count core competency bullets - must be exactly 5-6, never more
     - REDUNDANCY CHECK: Ensure no technology is mentioned twice in competencies section
     
     CRITICAL: Use ONLY the availability and rate information provided above. Do not modify dates or rates.
     CRITICAL: Do not mention any technologies, tools, experiences, or skills not explicitly listed in the CV.
     Write only the application, without additional comments or formatting.` : `
    Du bist ein erfahrener Freelancer und schreibst eine professionelle Bewerbung auf Deutsch für folgendes Projekt:

    Projektname: ${projectName || 'Nicht angegeben'}
    Firma: ${companyName || 'Nicht angegeben'}
    Ansprechpartner: ${normalizedContactPerson || 'Nicht angegeben'}
    Benötigte Skills: ${requiredSkills?.join(', ') || 'Nicht angegeben'}
    Projektbeschreibung: ${projectDescription}${availabilityContext ? `\n\nVerfügbarkeit: ${availabilityContext}` : ''}${userSettings?.hourly_rate_eur ? `\nStundensatz: €${userSettings.hourly_rate_eur} (netto)` : ''}
    
    PERSÖNLICHE INFORMATIONEN ZU VERWENDEN:
    - Name: ${userSettings?.full_name || 'Nicht angegeben'}
    - E-Mail: ${userSettings?.professional_email || 'Nicht angegeben'}
    - Telefon: ${userSettings?.phone || 'Nicht angegeben'}
    - Website: ${userSettings?.website || 'Nicht angegeben'}
    - Adresse: ${userSettings?.address || 'Nicht angegeben'}

    ${cvFileData ? 'ABSOLUT KRITISCHE REGELN FÜR CV-ANALYSE:\n\n1. STRENGE NUR-CV-REGEL: Verwende NUR Skills, Technologien, Tools, Methodologien und Erfahrungen die EXPLIZIT im CV-Text erwähnt sind. NIEMALS erfinden, annehmen oder extrapolieren.\n\n2. UNIVERSELLER FREELANCER-ANSATZ: Dieser Generator funktioniert für ALLE Freelancer-Typen (Frontend/Backend Entwickler, DevOps Engineers, UI/UX Designer, Digital Marketer, Content Writer, Projektmanager, Datenanalysten, etc.). Passe Sprache, Terminologie und Fokus an spezifischen CV-Inhalt und Projekttyp an - nimm niemals spezifische Technologien oder Methodologien an.\n\n3. INTELLIGENTE SKILL-POSITIONIERUNG: Falls eine geforderte Skill nicht explizit im CV erwähnt ist, verwende professionelle Positionierung mit übertragbaren Skills aus dem CV. Referenziere und baue nur auf Skills auf die explizit im CV-Text erwähnt sind.\n\n4. EXAKTE CV-TEXT-ÜBEREINSTIMMUNG: Referenziere nur:\n   - Skills/Technologien/Tools die explizit im CV genannt sind\n   - Firmen die explizit im CV genannt sind\n   - Positionen/Rollen die explizit im CV erwähnt sind\n   - Zertifizierungen die explizit im CV aufgelistet sind\n   - Projekte/Erfahrungen die explizit im CV beschrieben sind\n   - Branchen/Domänen die explizit im CV erwähnt sind\n\n5. PROFESSIONELLE POSITIONIERUNG vs VERBOTENE ERFINDUNG:\n   ERLAUBT: Positioniere CV-Skills als übertragbar (z.B. "Technologie A-Erfahrung, anwendbar auf Technologie B")\n   VERBOTEN: Erfinde Skills die nicht im CV stehen:\n   - Technische Skills die nicht explizit erwähnt sind\n   - Software/Tools die nicht explizit aufgelistet sind\n   - Methodologien die nicht explizit angegeben sind\n   - Branchenerfahrung die nicht explizit beschrieben ist\n   - Jede Fähigkeit die nicht im CV erwähnt ist\n\n6. SELEKTIVE BERUFSERFAHRUNG: Erwähne nur Firmen und Positionen die explizit im CV genannt sind. Beschreibe nur Erfahrungen die explizit im CV geschrieben sind. STRATEGISCH: Überprüfe CV-Berufslaufbahn und wähle TOP 2-3 RELEVANTESTE Firmen (nicht alle) - priorisiere aktuelle Positionen und direkte Skill-Übereinstimmungen für optimale Rücklaufquoten.\n\n7. SICHERE HERANGEHENSWEISE: Falls unsicher ob etwas im CV steht, erwähne es NICHT. Besser eine kürzere Bewerbung als Skills zu erfinden.\n\nANALYSIERE DEN CV WORT FÜR WORT. VERWENDE NUR WAS EXPLIZIT GESCHRIEBEN STEHT.' : 'WARNUNG: Kein CV/Lebenslauf bereitgestellt. Erwähne KEINE spezifischen Skills, Technologien, Tools oder Berufserfahrungen. Halte die Bewerbung allgemein und fokussiere dich auf Verfügbarkeit, Stundensatz und Lernbereitschaft.'}

     Schreibe eine professionelle, überzeugende Freelancer-Bewerbung die Expertise zeigt und intelligente Neugier weckt:
     
     TONALITÄT UND STIL:
     - Schreibe in natürlicher, menschlicher Sprache - NIEMALS maschinell oder AI-generiert klingen
     - Verwende abwechslungsreiche Satzstrukturen und natürliche Formulierungen
     - Vermeide repetitive Phrasen oder roboterhaften Sprachstil
     - Schreibe authentisch und persönlich, als würde ein erfahrener Freelancer selbst schreiben
     - ANTI-REPETITION: Erwähne Firmen nur EINMAL pro Bewerbung, nicht bei jeder Skill oder Technologie
     - VORBILD: Orientiere dich an diesem natürlichen, anpassbaren Stil: "Durch meine langjährige Beratungserfahrung bei [relevanten Firmen aus CV] bin ich es gewohnt, in cross-funktionalen Teams zu arbeiten und hochwertige Lösungen zu entwickeln, die Kundenanforderungen und Geschäftsziele erfüllen."

     ANREDE-STRATEGIE:
     - Falls Ansprechpartner angegeben ist UND einen Namen enthält: Extrahiere den Nachnamen und verwende "Sehr geehrte/r Herr/Frau [Nachname]"
     - Falls Ansprechpartner nur Titel oder Rolle enthält (wie "Recruiter", "Personalvermittler"): Verwende "Sehr geehrte Damen und Herren"
     - Falls Ansprechpartner leer, null oder "Nicht angegeben": Verwende "Sehr geehrte Damen und Herren"
     - Immer für die Projektausschreibung danken und spezifisches Interesse am Projekt zeigen

     INTELLIGENTE SKILL-ZUORDNUNG REGELN (CV-BASIERT MIT PROFESSIONELLER POSITIONIERUNG):
     - GRUNDREGEL: Erwähne nur Skills, Technologien, Tools und Methodologien die EXPLIZIT im CV-Text geschrieben sind
     - PROFESSIONELLE POSITIONIERUNG: Wenn deine CV-Erfahrung direkt auf Projektanforderungen übertragbar ist, positioniere sie intelligent:
       * Beispiel: CV hat "AWS EKS" + Job erfordert "Azure AKS" → "Umfangreiche KUBERNETES-Erfahrung mit AWS EKS, direkt auf AKS-Umgebungen übertragbar"
       * Beispiel: CV hat "React" + Job erfordert "Vue" → "Fundierte komponentenbasierte Frontend-Erfahrung mit REACT, leicht auf Vue.js-Projekte anwendbar"
       * Beispiel: CV hat "PostgreSQL" + Job erfordert "MySQL" → "Tiefe SQL-Datenbank-Expertise mit POSTGRESQL, auf MySQL-Umgebungen übertragbar"
     - ÜBERTRAGBARKEITS-SPRACHE: Verwende Formulierungen wie "übertragbar auf", "anwendbar auf", "relevante Erfahrung mit", "direkt anwendbar"
     - KEINE BEHAUPTUNGEN: Behaupte niemals die exakt geforderte Skill zu HABEN wenn sie nicht im CV steht
     - VERWENDE GROSSBUCHSTABEN für CV-Technologien die direkt projektrelevant sind
     - ZERTIFIZIERUNGEN: Erwähne NUR Zertifizierungen die EXPLIZIT im CV-Text stehen und projektrelevant sind
     - FIRMEN: Erwähne nur Firmen die EXPLIZIT im CV genannt sind
     - ARBEITSBEREICHE: Referenziere nur Arbeitsbereiche, Industrien oder Projektarten die explizit im CV erwähnt sind
     - BALANCE: Professionelles Selbstmarketing bei Wahrheit zum CV-Inhalt

     ENGAGEMENT-ANSATZ:
     - Fokus auf recruiter-freundliche Kommunikation (die meisten Kontakte sind Personalvermittler, nicht Endkunden)
     - Zeige echtes Interesse an den Projektanforderungen und gewünschten Ergebnissen
     - Demonstriere Verständnis für Geschäftswert und Projekterfolg
     - Halte Sprache zugänglich für technische und nicht-technische Leser
     - ABSCHLUSS: Verwende professionelle Formulierung wie "Gerne unterstütze ich Ihren Bestandskunden remote bei der Umsetzung seiner [Projektart]-Projekte und bringe dabei meine umfassende Expertise gewinnbringend ein."

     STRUKTUR:
     - Persönliche Anrede mit Ansprechpartner-Name (falls verfügbar)
     - Kurze Vorstellung der Gesamterfahrung (Jahre im Bereich) mit Bezug auf aktuelle/relevante Position
     - Strukturierter Abschnitt "Bezug zu den Anforderungen:" mit intelligenter Skill-Zuordnung
     - SELEKTIVE ABER UMFASSENDE BERUFSERFAHRUNG: Erstelle detaillierten Absatz der 2-3 relevanteste Arbeitgeber aus CV mit spezifischen projektrelevanten Tätigkeiten bei jeder Firma erwähnt. Stelle sicher dass mehrere Firmen erwähnt werden wenn verfügbar.
     - Klares Verfügbarkeitsdatum
     - Explizite Stunden- und Tagesrate${userSettings?.hourly_rate_eur ? ` (€${userSettings.hourly_rate_eur} netto pro Stunde, alternativ €${Math.round(userSettings.hourly_rate_eur * 8)} netto pro Tag)` : ''}
     - Strukturierte Kontaktdaten mit Textbeschriftungen (Email:, Telefon:, Website:)
     - Professioneller Abschluss mit vollständigem Namen: ${userSettings?.full_name || 'Name nicht angegeben'}
     
     FORMATIERUNG - STRIKT DURCHGESETZT:
     - ABSOLUT KEINE ** oder Markdown-Formatierung IRGENDWO in der Bewerbung
     - NIEMALS **Text** oder Markdown verwenden - das wird die Bewerbung KAPUTT machen
     - Verwende NUR Plain Text mit GROSSBUCHSTABEN für Schlüssel-Skills/Technologien
     - Einfaches Textformat für einfaches Kopieren in Outlook/Portale
     - Sauberes, strukturiertes Layout ohne Sonderzeichen

     WICHTIGE HINWEISE:
     - ANREDE: Falls "Ansprechpartner" einen echten Namen enthält, verwende ihn für persönliche Anrede
     - FIRMENKONTEXT: Die angegebene "Firma" ist oft ein Recruiter/Vermittler, nicht der Endkunde - erwähne NIEMALS die Firma direkt
     - CV-PFLICHT: Verwende ausschließlich Informationen die wörtlich im CV stehen
     - KOMPAKTHEIT: Halte die Bewerbung kurz und prägnant - maximal 300 Wörter GESAMT
     - WORTLIMIT: Zähle die Wörter und halte dich strikt an die 300-Wörter-Grenze
     - STRUKTUR: Verwende Bullet Points für Kernkompetenzen - NIEMALS lange Fließtext-Absätze

     BEWÄHRTE FREELANCER-ERFOLGSSTRUKTUR (professionell nach FreelancerMap-Vorbild):
     1. Professionelle Anrede (mit Name falls vorhanden) + kurzes Interesse an der Projektausschreibung
     2. Ein Satz Gesamterfahrung mit aktueller Position bei konkretem Arbeitgeber
     3. Kernkompetenzen - EXAKT 5-6 Bullets NUR:
        - ZÄHLE DEINE BULLETS: Müssen exakt 5 oder 6 Bullets sein, niemals mehr
        - Format: "TECHNOLOGIE: Kurze Erläuterung" (Plain Text, kein Markdown)
        - KONSOLIDIERUNGSREGELN:
          * Falls GO einmal erwähnt, NIEMALS wieder erwähnen
          * Falls CI/CD erwähnt, erstelle KEINEN separaten "CI/CD-Tools" Bullet
          * Gruppiere: Container + Kubernetes = ein Bullet
          * Gruppiere: Entwicklungssprachen = ein Bullet
          * Gruppiere: CI/CD + Automatisierung = ein Bullet
        - VERBOTEN: Gleiche Technologie zweimal erwähnt
        - VERBOTEN: Mehr als 6 Bullets
        - Überprüfe: Zähle Bullets vor Fertigstellung - müssen 5-6 maximal sein
     4. SELEKTIVER BERUFSERFAHRUNGSABSCHNITT mit den TOP 2-3 RELEVANTESTEN Arbeitgebern aus CV:
        - PFLICHT: Erwähne 2-3 Firmen (nicht nur 1) mit höchster Projektrelevanz
        - Priorisiere: Aktuelle/neueste Position + 1-2 Firmen mit direkten Skill-Übereinstimmungen
        - Ein prägnanter Satz pro Firma über wichtigste projektrelevante Tätigkeiten
        - Struktur: "Bei [FIRMA A] habe ich [Schlüsseltätigkeit]. Bei [FIRMA B] war ich [Schlüsseltätigkeit]. Bei [FIRMA C] habe ich [Schlüsseltätigkeit]."
        - KRITISCH: Falls mehrere Firmen relevante Erfahrung haben, erwähne sie - nicht nur auf die neueste defaulten
        - FOKUS: Qualität über Quantität aber stelle sicher dass 2-3 Firmen erwähnt werden wenn verfügbar
     5. Professioneller Abschluss mit Bereitschaft zur Projektunterstützung
     6. Verfügbarkeitsdatum klar angeben
     7. Stundensatz und Tagesrate explizit nennen
     8. Kontaktdaten strukturiert mit Textbeschriftungen (Email:, Telefon:, Website:)
     9. Professionelle Schlussformel mit vollständigem Namen

     PSYCHOLOGIE-TAKTIKEN FÜR HOHE RÜCKLAUFQUOTE:
     - Zeige, dass du ihre spezifischen Anforderungen und Herausforderungen verstehst
     - Demonstriere strategisches Denken über reine Umsetzung hinaus
     - Verwende selbstbewusste Sprache ("Ich werde" vs "Ich kann")
     - Fokus auf Geschäftswert und Projekterfolg
     - Schaffe Dringlichkeit mit deinem Verfügbarkeitsfenster

     SPEZIELLE ANFORDERUNGEN-BEHANDLUNG:
     - Suche im Projekttext nach "Anforderungen" Schlüsselwörtern aber erstelle KEINE Must-Have/Nice-to-Have Aufteilung
     - Extrahiere Projekt-IDs (z.B. "ID: 29606") und erwähne sie subtil im Text
     - Behandle Deutschkenntnisse als wichtige Anforderung falls erwähnt
     - ZERTIFIZIERUNGEN: Erwähne NUR Zertifizierungen die EXPLIZIT im CV stehen und projektrelevant sind
     - Erwähne alternative Kontaktmöglichkeiten falls mehrere im Projekttext vorhanden
     
     FINALE ÜBERPRÜFUNGSREGELN:
     - Überprüfe doppelt, dass jede erwähnte Technologie explizit im CV geschrieben steht
     - Überprüfe doppelt, dass jede erwähnte Firma explizit im CV genannt ist
     - Überprüfe doppelt, dass jede behauptete Skill explizit im CV angegeben ist
     - Entferne jeden Inhalt der nicht aus dem CV-Text verifiziert werden kann
     - FORMATIERUNGSCHECK: Entferne ALLE ** Fettschrift und Markdown - verwende nur Plain Text mit GROSSBUCHSTABEN für Technologien
     - BULLET-ANZAHL CHECK: Zähle Kernkompetenz-Bullets - müssen exakt 5-6 sein, niemals mehr
     - REDUNDANZ-CHECK: Stelle sicher dass keine Technologie zweimal im Kompetenz-Bereich erwähnt wird
     
     KRITISCH: Verwende NUR die oben angegebenen Verfügbarkeits- und Tarifinformationen. Ändere keine Daten oder Tarife.
     KRITISCH: Erwähne keine Technologien, Tools, Erfahrungen oder Skills die nicht explizit im CV aufgeführt sind.
     Schreibe nur die Bewerbung, ohne zusätzliche Kommentare oder Formatierung.`
      }
    ];

    // Add CV PDF if available
    if (cvFileData) {
      promptParts.push({
        inline_data: {
          mime_type: "application/pdf",
          data: cvFileData
        }
      } as any);
    }

    const response = await fetch(`https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash-lite-preview-06-17:generateContent?key=${geminiApiKey}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        contents: [{
          parts: promptParts
        }],
        generationConfig: {
          temperature: 0.7,
          topK: 40,
          topP: 0.95,
          maxOutputTokens: 1024,
        },
      }),
    });

    if (!response.ok) {
      console.error('Gemini API Error:', response.status, response.statusText);
      throw new Error(`Gemini API Error: ${response.status}`);
    }

    const data = await response.json();
    console.log('Gemini API Response:', JSON.stringify(data, null, 2));

    const applicationText = data.candidates?.[0]?.content?.parts?.[0]?.text;
    
    if (!applicationText) {
      throw new Error('Keine Antwort von Gemini API erhalten');
    }

    return new Response(JSON.stringify({ applicationText: applicationText.trim() }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
    });

  } catch (error) {
    console.error('Error in generate-application function:', error);
    return new Response(
      JSON.stringify({ 
        error: error.message || 'Ein unbekannter Fehler ist aufgetreten' 
      }), 
      {
        status: 500,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      }
    );
  }
});