import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const supabaseUrl = Deno.env.get('SUPABASE_URL')!
const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!
const geminiApiKey = Deno.env.get('GEMINI_API_KEY')!

interface NoteSummaryRequest {
  project_id: string;
  summary_type: 'timeline' | 'insights' | 'action_items';
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response(null, {
      status: 200,
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
        'Access-Control-Allow-Methods': 'POST, OPTIONS',
      },
    })
  }

  try {
    const supabase = createClient(supabaseUrl, supabaseServiceKey)

    // Get user from JWT
    const authHeader = req.headers.get('Authorization')!
    const token = authHeader.replace('Bearer ', '')
    const { data: { user }, error: authError } = await supabase.auth.getUser(token)

    if (authError || !user) {
      throw new Error('Unauthorized')
    }

    const { project_id, summary_type }: NoteSummaryRequest = await req.json()

    // Fetch project and activities with notes
    const { data: project, error: projectError } = await supabase
      .from('project_applications')
      .select('project_name, company_name, status')
      .eq('id', project_id)
      .eq('user_id', user.id)
      .single()

    if (projectError) {
      throw new Error('Project not found')
    }

    const { data: activities, error: activitiesError } = await supabase
      .from('project_activities')
      .select('activity_type, description, notes, notes_date, created_at')
      .eq('project_id', project_id)
      .eq('user_id', user.id)
      .not('notes', 'is', null)
      .order('created_at', { ascending: true })

    if (activitiesError) {
      throw new Error('Failed to fetch activities')
    }

    if (!activities || activities.length === 0) {
      return new Response(
        JSON.stringify({ 
          summary: 'Keine Notizen für dieses Projekt gefunden.',
          activities_count: 0 
        }),
        { 
          headers: { 
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
          } 
        }
      )
    }

    // Prepare context for AI
    const notesContext = activities.map(activity => ({
      type: activity.activity_type,
      description: activity.description,
      notes: activity.notes,
      date: activity.notes_date || activity.created_at
    }))

    // Create prompt based on summary type
    let prompt = ''
    switch (summary_type) {
      case 'timeline':
        prompt = `Erstelle eine chronologische Zusammenfassung der folgenden Projekt-Notizen:

Projekt: ${project.project_name} bei ${project.company_name}
Aktueller Status: ${project.status}

Notizen:
${notesContext.map(note => `[${note.date}] ${note.type}: ${note.description}\nNotizen: ${note.notes}`).join('\n\n')}

Bitte erstelle eine strukturierte Timeline-Zusammenfassung mit den wichtigsten Ereignissen und deren Auswirkungen.`
        break

      case 'insights':
        prompt = `Analysiere die folgenden Projekt-Notizen und extrahiere wichtige Erkenntnisse:

Projekt: ${project.project_name} bei ${project.company_name}

Notizen:
${notesContext.map(note => `${note.type}: ${note.description}\nDetails: ${note.notes}`).join('\n\n')}

Bitte identifiziere:
1. Wichtige Trends oder Muster
2. Potentielle Chancen oder Risiken
3. Kundenverhalten und Präferenzen
4. Lessons Learned`
        break

      case 'action_items':
        prompt = `Extrahiere konkrete Handlungsempfehlungen aus den folgenden Projekt-Notizen:

Projekt: ${project.project_name} bei ${project.company_name}

Notizen:
${notesContext.map(note => `${note.type}: ${note.description}\nDetails: ${note.notes}`).join('\n\n')}

Bitte erstelle eine Liste mit:
1. Offenen To-Dos und nächsten Schritten
2. Follow-up Aktionen mit Kunden
3. Interne Aufgaben und Verbesserungen
4. Deadlines und wichtige Termine`
        break
    }

    // Call Gemini API with timeout
    const controller = new AbortController()
    const timeoutId = setTimeout(() => controller.abort(), 30000) // 30 second timeout
    
    let summary = ''
    
    try {
      const geminiResponse = await fetch(
        `https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash-latest:generateContent?key=${geminiApiKey}`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            contents: [{
              parts: [{
                text: prompt
              }]
            }],
            generationConfig: {
              temperature: 0.7,
              topK: 40,
              topP: 0.95,
              maxOutputTokens: 2048,
            }
          }),
          signal: controller.signal
        }
      )
      
      clearTimeout(timeoutId)

      if (!geminiResponse.ok) {
        const errorText = await geminiResponse.text()
        console.error('Gemini API error:', {
          status: geminiResponse.status,
          statusText: geminiResponse.statusText,
          error: errorText
        })
        throw new Error(`Gemini API error (${geminiResponse.status}): ${errorText}`)
      }

      const geminiData = await geminiResponse.json()
      
      // Check if response has the expected structure
      if (!geminiData.candidates || 
          !geminiData.candidates[0] || 
          !geminiData.candidates[0].content || 
          !geminiData.candidates[0].content.parts || 
          !geminiData.candidates[0].content.parts[0]) {
        console.error('Unexpected Gemini response structure:', geminiData)
        throw new Error('Gemini API returned unexpected response structure')
      }
      
      summary = geminiData.candidates[0].content.parts[0].text
    
    } catch (fetchError) {
      clearTimeout(timeoutId)
      
      // Handle timeout specifically
      if (fetchError.name === 'AbortError') {
        throw new Error('Gemini API timeout - request took longer than 30 seconds')
      }
      
      // Re-throw other errors
      throw fetchError
    }

    // No logging - summary is only displayed, not persisted

    return new Response(
      JSON.stringify({ 
        summary,
        summary_type,
        activities_count: activities.length,
        project_name: project.project_name
      }),
      { 
        headers: { 
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
        } 
      }
    )

  } catch (error) {
    console.error('Error in summarize-notes function:', error)
    return new Response(
      JSON.stringify({ error: error.message }),
      { 
        status: 400,
        headers: { 
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
        }
      }
    )
  }
})