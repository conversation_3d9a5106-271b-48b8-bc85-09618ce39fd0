import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const supabaseUrl = Deno.env.get('SUPABASE_URL')!
const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!
const geminiApiKey = Deno.env.get('GEMINI_API_KEY')!

interface CommunicationSummaryRequest {
  communication_id?: string; // Optional for direct mode
  contact_id?: string; // Optional for direct mode
  summary_type: 'brief' | 'detailed' | 'action_items' | 'follow_up';
  context_include_project?: boolean;
  // Direct mode parameters
  direct_mode?: boolean;
  notes?: string;
  communication_type?: string;
  subject?: string;
  contact_name?: string;
  company_name?: string;
  project_context?: {
    project_name: string;
    company_name: string;
    status: string;
  };
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response(null, {
      status: 200,
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
        'Access-Control-Allow-Methods': 'POST, OPTIONS',
      },
    })
  }

  try {
    const supabase = createClient(supabaseUrl, supabaseServiceKey)

    // Enhanced authentication validation
    const authHeader = req.headers.get('Authorization')
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return new Response(
        JSON.stringify({ error: 'Missing or invalid authorization header' }),
        { 
          status: 401,
          headers: { 
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
          }
        }
      )
    }

    const token = authHeader.replace('Bearer ', '')
    if (!token || token.length < 10) {
      return new Response(
        JSON.stringify({ error: 'Invalid token format' }),
        { 
          status: 401,
          headers: { 
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
          }
        }
      )
    }

    const { data: { user }, error: authError } = await supabase.auth.getUser(token)

    if (authError) {
      console.error('Authentication error:', authError)
      return new Response(
        JSON.stringify({ error: 'Authentication failed', details: authError.message }),
        { 
          status: 401,
          headers: { 
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
          }
        }
      )
    }

    if (!user || !user.id) {
      return new Response(
        JSON.stringify({ error: 'User not found or invalid' }),
        { 
          status: 401,
          headers: { 
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
          }
        }
      )
    }

    const { 
      communication_id, 
      contact_id, 
      summary_type, 
      context_include_project = false,
      direct_mode = false,
      notes: directNotes,
      communication_type: directCommType,
      subject: directSubject,
      contact_name: directContactName,
      company_name: directCompanyName,
      project_context: directProjectContext
    }: CommunicationSummaryRequest = await req.json()

    // Handle direct mode vs database mode
    let communication, contact, project = null
    let communicationContext, contactContext
    
    if (direct_mode) {
      // Enhanced validation for direct mode parameters
      if (!directNotes || typeof directNotes !== 'string') {
        return new Response(
          JSON.stringify({ error: 'Notes are required for direct mode' }),
          { 
            status: 400,
            headers: { 
              'Content-Type': 'application/json',
              'Access-Control-Allow-Origin': '*',
              'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
            }
          }
        )
      }

      const sanitizedNotes = directNotes.trim()
      
      if (sanitizedNotes.length < 10) {
        return new Response(
          JSON.stringify({ error: 'Notes are too short for summarization (minimum 10 characters)' }),
          { 
            status: 400,
            headers: { 
              'Content-Type': 'application/json',
              'Access-Control-Allow-Origin': '*',
              'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
            }
          }
        )
      }

      if (sanitizedNotes.length > 10000) {
        return new Response(
          JSON.stringify({ error: 'Notes are too long (maximum 10,000 characters)' }),
          { 
            status: 400,
            headers: { 
              'Content-Type': 'application/json',
              'Access-Control-Allow-Origin': '*',
              'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
            }
          }
        )
      }

      // Validate communication type
      if (directCommType && typeof directCommType !== 'string') {
        return new Response(
          JSON.stringify({ error: 'Invalid communication type format' }),
          { 
            status: 400,
            headers: { 
              'Content-Type': 'application/json',
              'Access-Control-Allow-Origin': '*',
              'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
            }
          }
        )
      }

      // Validate contact and company names
      if (directContactName && (typeof directContactName !== 'string' || directContactName.length > 200)) {
        return new Response(
          JSON.stringify({ error: 'Invalid contact name format or too long' }),
          { 
            status: 400,
            headers: { 
              'Content-Type': 'application/json',
              'Access-Control-Allow-Origin': '*',
              'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
            }
          }
        )
      }

      if (directCompanyName && (typeof directCompanyName !== 'string' || directCompanyName.length > 200)) {
        return new Response(
          JSON.stringify({ error: 'Invalid company name format or too long' }),
          { 
            status: 400,
            headers: { 
              'Content-Type': 'application/json',
              'Access-Control-Allow-Origin': '*',
              'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
            }
          }
        )
      }

      // Validate subject if provided
      if (directSubject && (typeof directSubject !== 'string' || directSubject.length > 500)) {
        return new Response(
          JSON.stringify({ error: 'Invalid subject format or too long' }),
          { 
            status: 400,
            headers: { 
              'Content-Type': 'application/json',
              'Access-Control-Allow-Origin': '*',
              'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
            }
          }
        )
      }

      // Validate project context if provided
      if (directProjectContext && typeof directProjectContext === 'object') {
        const { project_name, company_name, status } = directProjectContext
        
        if (project_name && (typeof project_name !== 'string' || project_name.length > 200)) {
          return new Response(
            JSON.stringify({ error: 'Invalid project name in project context' }),
            { 
              status: 400,
              headers: { 
                'Content-Type': 'application/json',
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
              }
            }
          )
        }

        if (company_name && (typeof company_name !== 'string' || company_name.length > 200)) {
          return new Response(
            JSON.stringify({ error: 'Invalid company name in project context' }),
            { 
              status: 400,
              headers: { 
                'Content-Type': 'application/json',
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
              }
            }
          )
        }

        if (status && (typeof status !== 'string' || status.length > 100)) {
          return new Response(
            JSON.stringify({ error: 'Invalid status in project context' }),
            { 
              status: 400,
              headers: { 
                'Content-Type': 'application/json',
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
              }
            }
          )
        }
      }
      
      communicationContext = {
        type: directCommType || 'other',
        subject: directSubject || 'Kein Betreff',
        notes: sanitizedNotes,
        date: new Date().toISOString(),
        duration: null,
        isProjectRelated: !!directProjectContext
      }
      
      contactContext = {
        name: directContactName || 'Unbekannt',
        company: directCompanyName || 'Unbekannt',
        email: 'Keine E-Mail'
      }
      
      if (directProjectContext) {
        project = {
          project_name: directProjectContext.project_name,
          company_name: directProjectContext.company_name,
          status: directProjectContext.status
        }
      }
    } else {
      // Database mode: fetch communication data
      if (!communication_id || !contact_id) {
        throw new Error('Communication ID and Contact ID are required for database mode')
      }
      
      const { data: communicationData, error: commError } = await supabase
        .from('contact_communications')
        .select('*')
        .eq('id', communication_id)
        .eq('user_id', user.id)
        .single()

      if (commError || !communicationData) {
        throw new Error('Communication not found')
      }
      
      communication = communicationData
      
      // Fetch contact information for database mode
      const { data: contactData, error: contactError } = await supabase
        .from('contacts')
        .select('name, company, email')
        .eq('id', contact_id)
        .eq('user_id', user.id)
        .single()

      if (contactError || !contactData) {
        throw new Error('Contact not found')
      }
      
      contact = contactData
      
      // Optionally fetch project information if communication is project-related
      if (communication.project_id && context_include_project) {
        const { data: projectData, error: projectError } = await supabase
          .from('project_applications')
          .select('project_name, company_name, status')
          .eq('id', communication.project_id)
          .eq('user_id', user.id)
          .single()

        if (!projectError && projectData) {
          project = projectData
        }
      }
      
      // Prepare context for database mode
      communicationContext = {
        type: communication.communication_type,
        subject: communication.subject || 'Kein Betreff',
        notes: communication.notes,
        date: communication.communication_date,
        duration: communication.duration_minutes,
        isProjectRelated: communication.is_project_related
      }

      contactContext = {
        name: contact.name || 'Unbekannt',
        company: contact.company || 'Unbekannt',
        email: contact.email || 'Keine E-Mail'
      }
    }

    // Fetch recent communications for database mode only (for context)
    let recentCommunications = []
    if (!direct_mode && contact_id) {
      const { data: recentCommsData, error: recentError } = await supabase
        .from('contact_communications')
        .select('communication_type, subject, notes, communication_date')
        .eq('contact_id', contact_id)
        .eq('user_id', user.id)
        .neq('id', communication_id) // Exclude current communication
        .order('communication_date', { ascending: false })
        .limit(5)

      if (!recentError && recentCommsData) {
        recentCommunications = recentCommsData
      }
    }

    // Check if notes are long enough to warrant summarization
    const notesLength = communicationContext.notes?.length || 0
    if (!direct_mode && notesLength < 30 && summary_type === 'brief') {
      return new Response(
        JSON.stringify({ 
          summary: communicationContext.notes,
          summary_type: 'brief',
          communication_id,
          contact_name: contactContext.name || contactContext.company,
          notes_length: notesLength,
          message: 'Notes too short for AI summarization, returning original text'
        }),
        { 
          headers: { 
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
          } 
        }
      )
    }

    // Context already prepared above based on mode

    // Build context string for recent communications
    let recentContext = ''
    if (recentCommunications && recentCommunications.length > 0) {
      recentContext = `\n\nVorherige Kommunikationen mit diesem Kontakt:\n${
        recentCommunications.map(comm => 
          `[${comm.communication_date}] ${comm.communication_type}: ${comm.subject || 'Kein Betreff'}\n${comm.notes?.substring(0, 200)}${comm.notes && comm.notes.length > 200 ? '...' : ''}`
        ).join('\n\n')
      }`
    }

    // Build project context if available
    let projectContext = ''
    if (project) {
      projectContext = `\n\nProjektkontext:\nProjekt: ${project.project_name} bei ${project.company_name}\nStatus: ${project.status}`
    }

    // Create prompt based on summary type
    let prompt = ''
    switch (summary_type) {
      case 'brief':
        prompt = `Erstelle eine kurze, prägnante Zusammenfassung der folgenden Kommunikation:

Kontakt: ${contactContext.name} (${contactContext.company})
Kommunikationsart: ${communicationContext.type}
Betreff: ${communicationContext.subject}
Datum: ${communicationContext.date}${communicationContext.duration ? `\nDauer: ${communicationContext.duration} Minuten` : ''}

Notizen:
${communicationContext.notes}${projectContext}${recentContext}

Bitte erstelle eine kurze Zusammenfassung (max. 2-3 Sätze) mit den wichtigsten Punkten.`
        break

      case 'detailed':
        prompt = `Erstelle eine detaillierte Zusammenfassung der folgenden Kommunikation:

Kontakt: ${contactContext.name} (${contactContext.company})
Kommunikationsart: ${communicationContext.type}
Betreff: ${communicationContext.subject}
Datum: ${communicationContext.date}${communicationContext.duration ? `\nDauer: ${communicationContext.duration} Minuten` : ''}

Notizen:
${communicationContext.notes}${projectContext}${recentContext}

Bitte erstelle eine strukturierte, detaillierte Zusammenfassung mit:
1. Hauptthemen der Kommunikation
2. Wichtige Informationen und Erkenntnisse
3. Diskutierte Punkte und Entscheidungen
4. Stimmung/Ton der Kommunikation`
        break

      case 'action_items':
        prompt = `Extrahiere konkrete Handlungsempfehlungen aus der folgenden Kommunikation:

Kontakt: ${contactContext.name} (${contactContext.company})
Kommunikationsart: ${communicationContext.type}
Betreff: ${communicationContext.subject}
Datum: ${communicationContext.date}

Notizen:
${communicationContext.notes}${projectContext}${recentContext}

Bitte extrahiere und strukturiere:
1. Konkrete To-Dos und Aufgaben
2. Fälligkeiten und Deadlines
3. Vereinbarte nächste Schritte
4. Offene Fragen oder Klärungsbedarf
5. Follow-up Aktionen`
        break

      case 'follow_up':
        prompt = `Erstelle Empfehlungen für die nächste Kommunikation basierend auf:

Kontakt: ${contactContext.name} (${contactContext.company})
Letzte Kommunikation: ${communicationContext.type}
Betreff: ${communicationContext.subject}
Datum: ${communicationContext.date}

Notizen:
${communicationContext.notes}${projectContext}${recentContext}

Bitte erstelle Empfehlungen für:
1. Idealer Zeitpunkt für nächste Kontaktaufnahme
2. Empfohlene Kommunikationsart
3. Wichtige Themen für das nächste Gespräch
4. Vorbereitende Punkte
5. Mögliche Gesprächseinstiege`
        break
    }

    // Call Gemini API with timeout
    const controller = new AbortController()
    const timeoutId = setTimeout(() => controller.abort(), 30000) // 30 second timeout
    
    let summary = ''
    
    try {
      const geminiResponse = await fetch(
        `https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash-latest:generateContent?key=${geminiApiKey}`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            contents: [{
              parts: [{
                text: prompt
              }]
            }],
            generationConfig: {
              temperature: 0.7,
              topK: 40,
              topP: 0.95,
              maxOutputTokens: 2048,
            }
          }),
          signal: controller.signal
        }
      )
      
      clearTimeout(timeoutId)

      if (!geminiResponse.ok) {
        const errorText = await geminiResponse.text()
        console.error('Gemini API error:', {
          status: geminiResponse.status,
          statusText: geminiResponse.statusText,
          error: errorText
        })
        throw new Error(`Gemini API error (${geminiResponse.status}): ${errorText}`)
      }

      const geminiData = await geminiResponse.json()
      
      // Check if response has the expected structure
      if (!geminiData.candidates || 
          !geminiData.candidates[0] || 
          !geminiData.candidates[0].content || 
          !geminiData.candidates[0].content.parts || 
          !geminiData.candidates[0].content.parts[0]) {
        console.error('Unexpected Gemini response structure:', geminiData)
        throw new Error('Gemini API returned unexpected response structure')
      }
      
      summary = geminiData.candidates[0].content.parts[0].text
    
    } catch (fetchError) {
      clearTimeout(timeoutId)
      
      // Handle timeout specifically
      if (fetchError.name === 'AbortError') {
        throw new Error('Gemini API timeout - request took longer than 30 seconds')
      }
      
      // Re-throw other errors
      throw fetchError
    }

    // Auto-save summary back to communication if it's a brief summary and not direct mode
    if (!direct_mode && summary_type === 'brief' && summary.length > 0 && communication_id) {
      try {
        await supabase
          .from('contact_communications')
          .update({ 
            summarized_notes: summary,
            updated_at: new Date().toISOString()
          })
          .eq('id', communication_id)
          .eq('user_id', user.id)
      } catch (updateError) {
        console.warn('Could not auto-save summary to communication:', updateError)
        // Don't fail the request if we can't save the summary
      }
    }

    return new Response(
      JSON.stringify({ 
        summary,
        summary_type,
        communication_id: communication_id || null,
        contact_name: contactContext.name || contactContext.company,
        notes_length: notesLength,
        project_context: !!project,
        recent_communications_count: recentCommunications?.length || 0
      }),
      { 
        headers: { 
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
        } 
      }
    )

  } catch (error) {
    console.error('Error in summarize-communication function:', error)
    return new Response(
      JSON.stringify({ error: error.message }),
      { 
        status: 400,
        headers: { 
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
        }
      }
    )
  }
})